{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d74ba8931f5d89d9924043ff9b70e42b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982429bbc0cb0745ba259f580f77d38ae6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba6b38401a0b68d7b07af48e31c997d0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988c7513d328b09d75c64982559476c315", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba6b38401a0b68d7b07af48e31c997d0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c27deab5ed4b6f9d017b132bf8082d4c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e6aec76afd744bd86845d94957b973c3", "guid": "bfdfe7dc352907fc980b868725387e988fc341ad79f9ab12cd6229c6226b921a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8ad9b63470665d4d26165ae8a1586ba", "guid": "bfdfe7dc352907fc980b868725387e98cb3aa811a275af69173ce8ad3db93d33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890819be665b360376136a5ededac5cdb", "guid": "bfdfe7dc352907fc980b868725387e98f4874e35c41788b6954ff13ce43b36bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c7e89d744cb810271c452e974570e7b", "guid": "bfdfe7dc352907fc980b868725387e982dcc49ae4b8b7defe978aa8ed7d3239c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1020be6243d341514cbc3423ead1509", "guid": "bfdfe7dc352907fc980b868725387e982d81c0d67382aebbac4b51ddfcfc46e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7f4ade5da68b70ef623de6e4273789c", "guid": "bfdfe7dc352907fc980b868725387e98de3f0688a2c2a1d25c1dd5314f3399fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981527b40baedf31ca4fb84f2248907208", "guid": "bfdfe7dc352907fc980b868725387e987d10edff0ad24e177c224e95d4866993", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb01076903df0ab347b1fc5a262ff094", "guid": "bfdfe7dc352907fc980b868725387e9873636e3a297cd557d6efb98c4ae7dda9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801972aa1950026827f701b313d0301c1", "guid": "bfdfe7dc352907fc980b868725387e98dacf7e9010ae55dd65d66fb7e217d450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e5b04e4239b7f3cc4f6598eae5368ca", "guid": "bfdfe7dc352907fc980b868725387e984f220df3be4ed72c5aa6a357c46ee806"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a65f772745a396b62451ab5ee61dc9a", "guid": "bfdfe7dc352907fc980b868725387e9832632727d925903c2d3fc809cfc21562", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98824796121fd6e19b42b7b3b0725bd24b", "guid": "bfdfe7dc352907fc980b868725387e987c6cc569ca74ce1e5663923bf193b61f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880009684a79d2ee214b40679dbccce0e", "guid": "bfdfe7dc352907fc980b868725387e98a1ebf6930af224274faca41cdaa0a312", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98630d3f422bf8b7c8fd7554e0926b444b", "guid": "bfdfe7dc352907fc980b868725387e980954c23ca93151d26fcc5cb3cdebc925"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a10330611d8bafd5d3539c4aaa8764f", "guid": "bfdfe7dc352907fc980b868725387e984880ac481954526e9456ec28a7409e64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9ed37915a027632c6f949a0e9f84b20", "guid": "bfdfe7dc352907fc980b868725387e98b04ccfc7ed8cf15bba55d2983c705227", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98626fb38b758510dc840b4bbc058831a6", "guid": "bfdfe7dc352907fc980b868725387e98c5900cfd8af73a58698e7cfafb15b3c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da0815f3d65743f68da6f733ba96b5d8", "guid": "bfdfe7dc352907fc980b868725387e98428cb430bc120b570417364e8c8d8259"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6f3568528dc17b30b6809fd1a771db2", "guid": "bfdfe7dc352907fc980b868725387e98f3f5b1dddba8aabfbf8659007655fe97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f90df4d54d8b2ee5c777d1a4601b060f", "guid": "bfdfe7dc352907fc980b868725387e98fd9cf487201521f5ab0fe17890b1735b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb689ef734c9099f3ec465fdf808e575", "guid": "bfdfe7dc352907fc980b868725387e988fbfcc404864c7d263c3c99234c31806"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad19f2d22d56e02f6d608675511c640f", "guid": "bfdfe7dc352907fc980b868725387e98424410be7eb89ef84c92e2a27bef2a21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2968dc8344979cd99fdcd8e24ffb6eb", "guid": "bfdfe7dc352907fc980b868725387e982908e9c9596561cf0ede3dcf5ce2ee9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f74a971033a88927ae2520caaf032a51", "guid": "bfdfe7dc352907fc980b868725387e98f68d9f0d35ae3e5d50452151e8ed1492"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983568aa48abca9cbf283e582aeedcc38e", "guid": "bfdfe7dc352907fc980b868725387e98607a46b288e6add7e6aa08824eaa997d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c9c0e2eecb925e78fca446b3dcb5f49", "guid": "bfdfe7dc352907fc980b868725387e9810912a18776f282883e4e7bafa98bdbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828001f3b89646b42a7f4f8fc5c489fa0", "guid": "bfdfe7dc352907fc980b868725387e989a8277d55b956b7fc174547c846ef353"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848e6cffc9f74824c8783265cf16e0016", "guid": "bfdfe7dc352907fc980b868725387e983fbf53ccc3ede81c29cec73be6853d16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843fd1bb786355b1e8567de9463ad0ac8", "guid": "bfdfe7dc352907fc980b868725387e98f883ab7f1bc5d5281381623b26a7574c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6cb0625b3947553e1cf92f24c6b1f41", "guid": "bfdfe7dc352907fc980b868725387e984332be805b513955b98f4559cf98ba56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cd3c1c35c4fa78118d5e6f4b80052c2", "guid": "bfdfe7dc352907fc980b868725387e987791763e192cda8ef1183a43cc74d146"}], "guid": "bfdfe7dc352907fc980b868725387e98d0302dfcd1cf8213148af9be4664afb0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804922949d9c15ffe87e26e8bdfa33b8e", "guid": "bfdfe7dc352907fc980b868725387e984fb4eeba25acb3ea77c2b8a1d47e0b1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d82a48cec496270711e172d30991419", "guid": "bfdfe7dc352907fc980b868725387e9843ed436b7286eb4ce43608c5237426a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838ae0760b41a19e8d79f68e7dbd749bc", "guid": "bfdfe7dc352907fc980b868725387e98772d10d88168e4e7d51ac164477b283d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c55b8ef6c24a95d359c4a3bf1b7ac6f3", "guid": "bfdfe7dc352907fc980b868725387e98fb5d8760a7dbc108d4dbed68e6fa7c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98252eca19aaf0c027a4bbd796bd163adf", "guid": "bfdfe7dc352907fc980b868725387e9882dbf7ee4c12dff29a93d0c0d784178f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd913a5ad4f7cb2ee634e0bfec7df513", "guid": "bfdfe7dc352907fc980b868725387e982cfb4103d8ad3b7d75ceadaef37bef2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98013fac81ea9fe09d0e1d3f9e6759821e", "guid": "bfdfe7dc352907fc980b868725387e98ec4019fa92265f4e6fb7b3ae0c78026a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987436a31600871d6d0d2df500c393d0a0", "guid": "bfdfe7dc352907fc980b868725387e987ebb130f6ce86180df1cfc07d77a40bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e96bd542226712f160c17a720fe2f52b", "guid": "bfdfe7dc352907fc980b868725387e986f5902f310a77a2fa28a3ddaac61c750"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fcc96728bb523cfae3415bc2fad0983", "guid": "bfdfe7dc352907fc980b868725387e984949bcfebeed0a1c722779f2834e44b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2870f3210d52a2562b9eb031c56c432", "guid": "bfdfe7dc352907fc980b868725387e9807528578be274cb01f3223bd887b1e03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c37e1ba0c0e1c74038595dadf6047096", "guid": "bfdfe7dc352907fc980b868725387e980e694c6c3642f17aff093f9eb9d537c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b41bc948f141e812cf96e6f90fc3fd25", "guid": "bfdfe7dc352907fc980b868725387e9809ba6c447d72a60339e652a5a84bc489"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982283570fbea81a151db8dce81c1411c7", "guid": "bfdfe7dc352907fc980b868725387e98be66feac444743b649aa131496388ef8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828147f94fe871d8fde1101d1bf1a5c93", "guid": "bfdfe7dc352907fc980b868725387e981b707ea49e72d243c3fe17f441a8c1ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dcd312ab62316c7407f5179a09fd49a", "guid": "bfdfe7dc352907fc980b868725387e987943f75dbe244db2ba4c1cd54eeeb614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819f4ad264a014af1bc920eb4de110448", "guid": "bfdfe7dc352907fc980b868725387e985ea43987ebf42303323560e282a180d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805b87140b4c0febe694a0600e945a333", "guid": "bfdfe7dc352907fc980b868725387e989288553e3d44553a3dd37c1e433feff4"}], "guid": "bfdfe7dc352907fc980b868725387e98b5cbadfabc508146d47b95642d187fb9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e986d4cc51387ae24a3c42322299c10da9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e98ae0217a5c597fd8f9d7601c29f1eac30"}], "guid": "bfdfe7dc352907fc980b868725387e98a127f507badb280ce9375e6baa1b7785", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d8b8036d77882bdfd362ebc8559f235a", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e981d0b4f37f7ab0d063205afff2b6e6636", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}