import 'dart:developer';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/guesthome/homemodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/ui/catering/cateringrequest.dart';
import 'package:db_eats/ui/catering/sendcateringrequest.dart';
import 'package:db_eats/ui/chef/view_chef2.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:db_eats/widgets/location_header.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';

class DemoCateringPage extends StatefulWidget {
  const DemoCateringPage({super.key});

  @override
  State<DemoCateringPage> createState() => _DemoCateringPageState();
}

class _DemoCateringPageState extends State<DemoCateringPage> {
  bool _loadingChefs = true;
  bool _hasInitialized = false;
  double screenWidth = 0;
  double screenHeight = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final homeBloc = context.read<HomeBloc>();
      if (homeBloc.state is! HomeDataSuccess &&
          homeBloc.state is! HomeDataLoading) {
        final lat = Initializer().getLatitude;
        final lng = Initializer().getLongitude;
        if (lat != null && lng != null) {
          homeBloc.add(GetHomeDataEvent(
            data: <String, dynamic>{
              'latitude': lat,
              'longitude': lng,
            },
          ));
        }
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final mq = MediaQuery.of(context);
    screenWidth = mq.size.width;
    screenHeight = mq.size.height;
  }

  String _formatDistance(double? distance) {
    if (distance == null) return 'Unknown';
    final kilometers = distance / 1000;
    return '${kilometers.toStringAsFixed(1)} km';
  }

  void _openCart() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => const CartPage()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC),
      body: SafeArea(
        child: Column(
          children: [
            const LocationHeader(),
            SizedBox(height: screenHeight * 0.01),
            Expanded(
              child: BlocConsumer<HomeBloc, HomeState>(
                listener: (context, state) {
                  if (state is HomeDataSuccess) {
                    setState(() {
                      _loadingChefs = false;
                      _hasInitialized = true;
                    });
                  } else if (state is HomeDataLoading) {
                    setState(() => _loadingChefs = true);
                  } else if (state is HomeDataFailed) {
                    setState(() {
                      _loadingChefs = false;
                      _hasInitialized = true;
                    });
                  }
                },
                builder: (context, state) {
                  List<ChefData>? chefs;
                  if (state is HomeDataSuccess) {
                    chefs = state.data.data?.popularChefsNear;
                  }

                  if (_loadingChefs ||
                      state is HomeDataLoading ||
                      !_hasInitialized) {
                    return SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: screenHeight * 0.02),
                          _buildSectionHeader('Most Popular Near You'),
                          SizedBox(height: screenHeight * 0.02),
                          _buildChefShimmerLoading(),
                          _buildCateringButton(),
                          _buildMonthlySaverCard(),
                          SizedBox(height: screenHeight * 0.08),
                        ],
                      ),
                    );
                  }

                  if (chefs == null || chefs.isEmpty) {
                    return SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: screenHeight * 0.02),
                          _buildSectionHeader('Most Popular Near You'),
                          SizedBox(height: screenHeight * 0.02),
                          _buildNoDataMessage('No popular chefs in your area'),
                          SizedBox(height: screenHeight * 0.02),
                          _buildCateringButton(),
                          _buildMonthlySaverCard(),
                          SizedBox(height: screenHeight * 0.08),
                        ],
                      ),
                    );
                  }

                  return SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: screenHeight * 0.02),
                        _buildSectionHeader('Most Popular Near You'),
                        SizedBox(height: screenHeight * 0.02),
                        _buildPopularChefs(chefs),
                        SizedBox(height: screenHeight * 0.02),
                        _buildCateringButton(),
                        _buildMonthlySaverCard(),
                        SizedBox(height: screenHeight * 0.08),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: CartFloatingActionButton(
        itemCount: Initializer.cartCount ?? 0,
        onPressed: _openCart,
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.04, vertical: screenWidth * 0.01),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: screenWidth * 0.05,
              fontWeight: FontWeight.w700,
              fontFamily: 'Inter',
              color: const Color(0xFF1F2122),
            ),
          ),
          // GestureDetector(
          //   onTap: _submit,
          //   child: Column(
          //     children: [
          //       Text(
          //         'See All',
          //         style: TextStyle(
          //           fontSize: screenWidth * 0.03,
          //           fontWeight: FontWeight.w600,
          //           fontFamily: 'Inter',
          //           color: const Color(0xFF1F2122),
          //         ),
          //       ),
          //       Container(
          //         height: 1,
          //         width: screenWidth * 0.08,
          //         color: const Color(0xFF66696D),
          //       ),
          //     ],
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildChefShimmerLoading() {
    return SizedBox(
      height: screenHeight * 0.32,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: 3,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, __) => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            width: screenWidth * 0.58,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth * 0.03),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoDataMessage(String message) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.04,
        vertical: screenHeight * 0.1,
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.restaurant_outlined,
              size: screenWidth * 0.15,
              color: const Color(0xFF66696D),
            ),
            SizedBox(height: screenHeight * 0.02),
            Text(
              message,
              style: TextStyle(
                fontSize: screenWidth * 0.04,
                fontWeight: FontWeight.w400,
                fontFamily: 'Inter',
                color: const Color(0xFF66696D),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPopularChefs(List<ChefData> chefs) {
    return SizedBox(
      height: screenHeight * 0.32,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: chefs.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) {
          final chef = chefs[index];
          final name =
              '${chef.chef?.firstName ?? ''} ${chef.chef?.lastName ?? ''}';
          final availability = chef.chef?.operationDays
                  ?.map((day) => day.day?.name?.substring(0, 1) ?? '')
                  .join(', ') ??
              '';
          final cuisines = chef.searchTags?.join(', ') ?? 'Various Cuisines';
          final distance = _formatDistance(chef.distance);
          final rating = chef.averageRating?.toString() ?? '0.0';
          final prepTime = '30-45 mins';

          return _buildChefCard(
            id: chef.chefId ?? 0,
            image: ServerHelper.imageUrl + (chef.profilePhoto ?? ''),
            name: name,
            cuisines: cuisines,
            rating: rating,
            distance: distance,
            availability: availability,
            dishImage: ServerHelper.imageUrl + (chef.coverPhoto ?? ''),
            prepTime: prepTime,
          );
        },
      ),
    );
  }

  Widget _buildChefCard({
    required int id,
    required String image,
    required String name,
    required String cuisines,
    required String rating,
    required String distance,
    required String availability,
    required String dishImage,
    required String prepTime,
  }) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ViewChef2(
              id: id,
              title: name,
              latitude: Initializer().getLatitude ?? 0,
              longitude: Initializer().getLongitude ?? 0,
              distance: distance,
            ),
          ),
        );
      },
      child: Container(
        width: screenWidth * 0.58,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(screenWidth * 0.03),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.01),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(screenWidth * 0.03),
                    topRight: Radius.circular(screenWidth * 0.03),
                  ),
                  child: Image.network(
                    dishImage,
                    width: double.infinity,
                    height: screenHeight * 0.15,
                    fit: BoxFit.cover,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: double.infinity,
                          height: screenHeight * 0.15,
                          color: Colors.white,
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: double.infinity,
                        height: screenHeight * 0.15,
                        color: Colors.grey[300],
                        child: Icon(Icons.image_not_supported,
                            color: Colors.grey[600]),
                      );
                    },
                  ),
                ),
                Positioned(
                  left: screenWidth * 0.04,
                  bottom: -screenWidth * 0.05,
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 3),
                    ),
                    child: CircleAvatar(
                      radius: screenWidth * 0.075,
                      backgroundImage: NetworkImage(image),
                      onBackgroundImageError: (exception, stackTrace) {
                        log('Error loading chef image: $exception');
                      },
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: screenHeight * 0.03),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: TextStyle(
                      fontSize: screenWidth * 0.035,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'Inter',
                      color: const Color(0xFF1F2122),
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.008),
                  Text(
                    cuisines,
                    style: TextStyle(
                      fontSize: screenWidth * 0.03,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: const Color(0xFF414346),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: screenHeight * 0.01),
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: screenWidth * 0.015,
                          vertical: screenWidth * 0.005,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE1E3E6),
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.03),
                        ),
                        child: Row(
                          children: [
                            Image.asset(
                              'assets/icons/thump.png',
                              width: screenWidth * 0.0275,
                              height: screenWidth * 0.025,
                              color: Colors.black54,
                            ),
                            SizedBox(width: screenWidth * 0.01),
                            Text(
                              rating,
                              style: TextStyle(
                                fontSize: screenWidth * 0.025,
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Inter',
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.02),
                      Row(
                        children: [
                          Icon(
                            Icons.location_on_outlined,
                            size: screenWidth * 0.03,
                            color: const Color(0xFF1F2122),
                          ),
                          SizedBox(width: screenWidth * 0.005),
                          Text(
                            distance,
                            style: TextStyle(
                              fontSize: screenWidth * 0.025,
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Inter',
                              color: const Color(0xFF1F2122),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.008),
                  Row(
                    children: [
                      Image.asset(
                        'assets/icons/calender_2.png',
                        width: screenWidth * 0.03,
                        height: screenWidth * 0.0325,
                        color: Colors.black54,
                      ),
                      SizedBox(width: screenWidth * 0.01),
                      Text(
                        availability,
                        style: TextStyle(
                          fontSize: screenWidth * 0.03,
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Inter',
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCateringButton() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.04,
        vertical: screenHeight * 0.01,
      ),
      child: SizedBox(
        height: screenHeight * 0.055,
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (_) => const CateringRequestsPage()),
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF1F2122),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(screenWidth * 0.09),
            ),
          ),
          child: Text(
            'View Catering Requests',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: screenWidth * 0.035,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMonthlySaverCard() {
    return Padding(
      padding: EdgeInsets.all(screenWidth * 0.04),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(screenWidth * 0.04),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(screenWidth * 0.04),
                topRight: Radius.circular(screenWidth * 0.04),
              ),
              child: Image.network(
                'https://images.pexels.com/photos/1128783/pexels-photo-1128783.jpeg',
                width: double.infinity,
                height: screenHeight * 0.2,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    width: double.infinity,
                    height: screenHeight * 0.2,
                    color: Colors.grey[300],
                    child: const Center(
                      child: CupertinoActivityIndicator(
                        color: Color(0xFF1F2122),
                        radius: 5,
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: double.infinity,
                    height: screenHeight * 0.2,
                    color: Colors.grey[300],
                    child: const Icon(Icons.broken_image, color: Colors.grey),
                  );
                },
              ),
            ),
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.03),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFFFBE16),
                          shape: BoxShape.circle,
                        ),
                        width: screenWidth * 0.067,
                        height: screenWidth * 0.067,
                        child: Padding(
                          padding: EdgeInsets.all(screenWidth * 0.02),
                          child: Image.asset(
                            'assets/icons/percent.png',
                            width: screenWidth * 0.04,
                            height: screenWidth * 0.04,
                            fit: BoxFit.contain,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.02),
                      Text(
                        'Catering Requests',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: screenWidth * 0.045,
                          fontWeight: FontWeight.w700,
                          fontFamily: 'Inter',
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.008),
                  Text(
                    'Customise your dream catering experience with our catering requests. Choose from a variety of options and let us handle the rest.',
                    style: TextStyle(
                      color: const Color(0xFFAAADB1),
                      fontSize: screenWidth * 0.03,
                      fontFamily: 'Inter',
                      height: 1.4,
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.01),
                  Padding(
                    padding: EdgeInsets.only(left: screenWidth * 0.02),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.circle,
                              size: screenWidth * 0.015,
                              color: const Color(0xFFAAADB1),
                            ),
                            SizedBox(width: screenWidth * 0.02),
                            Text(
                              'Unlimited Free Delivery (Capped at \$2.0)',
                              style: TextStyle(
                                color: const Color(0xFFAAADB1),
                                fontSize: screenWidth * 0.03,
                                fontFamily: 'Inter',
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.01),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.circle,
                              size: screenWidth * 0.015,
                              color: const Color(0xFFAAADB1),
                            ),
                            SizedBox(width: screenWidth * 0.02),
                            Text(
                              'Up to 30% Off Restaurants',
                              style: TextStyle(
                                color: const Color(0xFFAAADB1),
                                fontSize: screenWidth * 0.03,
                                fontFamily: 'Inter',
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.01),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.circle,
                              size: screenWidth * 0.015,
                              color: const Color(0xFFAAADB1),
                            ),
                            SizedBox(width: screenWidth * 0.02),
                            Text(
                              'Surprise Perks',
                              style: TextStyle(
                                color: const Color(0xFFAAADB1),
                                fontSize: screenWidth * 0.03,
                                fontFamily: 'Inter',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.015),
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (_) => const SendCateringRequestPage()),
                      );
                    },
                    child: Container(
                      width: double.infinity,
                      padding:
                          EdgeInsets.symmetric(vertical: screenWidth * 0.035),
                      decoration: BoxDecoration(
                        color: Colors.black,
                        border: Border.all(color: const Color(0xFFAAADB1)),
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.075),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Start A Catering Request',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: screenWidth * 0.03,
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Inter',
                            ),
                          ),
                          SizedBox(width: screenWidth * 0.02),
                          Icon(
                            Icons.arrow_forward,
                            color: Colors.white,
                            size: screenWidth * 0.04,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
