{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988100370e484b3e03b6684d9044d87bdc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984afd2e13efe56be3c56511b77e892cde", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d9003e093a0109e538a4b771317e145", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e99de1f30f5ee832f6ce22e1a6c59a9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d9003e093a0109e538a4b771317e145", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d9b08cc70361d72a97e4cec3522982ff", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c35be7c6c8e2083be8ea745075cbf13", "guid": "bfdfe7dc352907fc980b868725387e98c288e1c600e05f1d43bbd8f704c4e38a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870cdedbb6f2e5284158be3551e39d352", "guid": "bfdfe7dc352907fc980b868725387e984f545244d2aa8096a300717ddb3de598", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981329831b1a29790e9eb5f82d20904f79", "guid": "bfdfe7dc352907fc980b868725387e988a8a2f976199243e5c00ed68ba33f224"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c8e5dbf235bd64b101ac0f2820349d", "guid": "bfdfe7dc352907fc980b868725387e98b9942a2d8cfe6f8ce4a7d7a054620c76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f43ee1eedeb63d571dfcf470bdf05569", "guid": "bfdfe7dc352907fc980b868725387e98158dede3b186a2784c6acd3c6718b145"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881d125a7635f05aa39a92c096394ba2c", "guid": "bfdfe7dc352907fc980b868725387e986627156ec127339475e0c5b2a586c0e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb40ab29380d9bbdc05d5c9ca76bbde0", "guid": "bfdfe7dc352907fc980b868725387e9869dd9ca44fbf1d2ce514f93c4f1c3de2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb03b5b16edb9d702639fc546da543ae", "guid": "bfdfe7dc352907fc980b868725387e98b54e6b69c75c055f0b55d1c1b3e3b5b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873f789676c9327814ea25c3c556a8fc8", "guid": "bfdfe7dc352907fc980b868725387e9837577af61f83d85a04b1c87b19742aca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bce699addc8e5bfdce438b5aa27310b8", "guid": "bfdfe7dc352907fc980b868725387e98af8cd251c6b41809362c59246e4a2ff6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c006e1b599d34be49a0ead4ace0c6e9", "guid": "bfdfe7dc352907fc980b868725387e986a367b3721a754474cefcd824f2c06d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860aed83cddb33454ddb3a4552a81ad22", "guid": "bfdfe7dc352907fc980b868725387e98a0108f9f1890b60f4eddae763f540a16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98422c3776b6d754c443dd0a7ff311630d", "guid": "bfdfe7dc352907fc980b868725387e98b56b232f7d9d2f6bf840e8fe84c079b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98136bcb9550471ee4f78fb2243fc10084", "guid": "bfdfe7dc352907fc980b868725387e9816eb03f8bd2352061c086946d6840fda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b469b71820171644f1fa71ee3790d595", "guid": "bfdfe7dc352907fc980b868725387e9849725a5fc9c7c206a45e6495a278afa0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7e5c1f21ef640f596dcd6aa2f89dba3", "guid": "bfdfe7dc352907fc980b868725387e98188a3717a853e46cfadd7d745d486f43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898d2b42608b040550ac70b4e47af07a5", "guid": "bfdfe7dc352907fc980b868725387e9897afab17c10c59a06306c20e47fe491b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a73f161d4bf3aeba0a504361dad423", "guid": "bfdfe7dc352907fc980b868725387e9884479262fb52790558da9129dad013b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e141557469be245d288ec9a9b4e24be2", "guid": "bfdfe7dc352907fc980b868725387e98241292371aeff55c1f789271e1ea1b3e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880adaf4508ea3e67325397bf3de8bf1f", "guid": "bfdfe7dc352907fc980b868725387e9876bf29d1030a287ea0634f28853d25aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98304e2a9e1ad5e60ba52cb64baad142fe", "guid": "bfdfe7dc352907fc980b868725387e987ea1accd7115bd6e1879281b95936fe5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984927632949e4ba84669342b57cd53627", "guid": "bfdfe7dc352907fc980b868725387e98034f48bdc7ef8d1640df7a9870c8cf11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0a24cfe66bf3732cd08d4eee5c08fc5", "guid": "bfdfe7dc352907fc980b868725387e98ca618b9726a6a9ed2fc1c58cd8a0086e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9826b1b11183433e0ce9e79589220a8127", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9896ca253cfb966693573df1c0d8d841c3", "guid": "bfdfe7dc352907fc980b868725387e98c020bcf93703f67fddb7863a8762f8a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819c03e0a981100ddc8806afddb30da6e", "guid": "bfdfe7dc352907fc980b868725387e98ff101d8552f787f636b719543fba1e70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b4052f4d9db16af32bddd2ea037eb56", "guid": "bfdfe7dc352907fc980b868725387e98838fc9b93bbc559462e63d6cfbbb00c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831f24ae52833b507d498870f0f08f41f", "guid": "bfdfe7dc352907fc980b868725387e98e085993140ae682257f814cd126d1c5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acebf844a521479be77b7053301a8270", "guid": "bfdfe7dc352907fc980b868725387e98294e7066facb44bef6e28fa282adb1c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3df93b7d9a4edaee75a60b04c5274c6", "guid": "bfdfe7dc352907fc980b868725387e9850be36274eb5cae162fdcc8e73ccf3a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98099eaf6e3fb8a36f695b771f7d1cbf9c", "guid": "bfdfe7dc352907fc980b868725387e9865751db49d1d3ba843c2a7c38351537a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d92c3a2d5beb2365e9f3d1854115fb4", "guid": "bfdfe7dc352907fc980b868725387e98b912d260d4063730f057ed5f9dfdb7ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b786499bcaba8f65ee03d3baa360741c", "guid": "bfdfe7dc352907fc980b868725387e987db037bf28bfabbda08549a5c7a2c847"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807a9558537b488ecebea34ee358ab1a9", "guid": "bfdfe7dc352907fc980b868725387e987ee8888876d8f798c32ecda25ca48da2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb99a24d9e263e567da9b7c9b8d838c2", "guid": "bfdfe7dc352907fc980b868725387e98ba7157fc7b5799003471a54f73e9518d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f9094605aee4b752d27e94def75df05", "guid": "bfdfe7dc352907fc980b868725387e984f396a5865684f51c945d0551e5e3b02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98964ce726dfd0d76a234660a197298c23", "guid": "bfdfe7dc352907fc980b868725387e98f21a651b296d84814ba3766378d7c22c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f0cf5e067d1d9c84189e087b767d478", "guid": "bfdfe7dc352907fc980b868725387e982dfef7f869c05892a31727dde432aee8"}], "guid": "bfdfe7dc352907fc980b868725387e98030aa4f4967b063360d12d1891a0cf91", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9817482f9062310e4d938bd89d91fbf68a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b194461b3d65889636e9bae473ca683", "guid": "bfdfe7dc352907fc980b868725387e9855045dbbe3f7cc08aefeb0597ceda1f6"}], "guid": "bfdfe7dc352907fc980b868725387e98b7ef773c4c5e5a3f1ec244d9a1faa165", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9838f2867d7cc6a6d6a3b78673d42f0064", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98e730dbce27dca55f39ed3010ea3ea079", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}