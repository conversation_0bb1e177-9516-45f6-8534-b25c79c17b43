import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/bloc/newmealplan_bloc.dart';
import 'package:db_eats/ui/meal_plan_new/curated/choose_spice_level_new.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/models/meal_plan/listdiatarymodel.dart';

class ChooseDietryNew extends StatefulWidget {
  final int mealPlanId;
  const ChooseDietryNew({super.key, required this.mealPlanId});

  @override
  State<ChooseDietryNew> createState() => _ChooseDietryNewState();
}

class _ChooseDietryNewState extends State<ChooseDietryNew> {
  int? _selectedPreferenceId;
  List<Dietaries> _preferences = [];
  late final MealplanBloc _mealPlanBloc;
  bool _isLoading = false;
  final TextEditingController _commentsController = TextEditingController();
  final TextEditingController _personalizationTagsController =
      TextEditingController();

  // Map dietary names to image assets
  final Map<String, String> _dietaryIcons = {
    'Organic': 'assets/icons/organic.png',
    'Halal': 'assets/icons/halal.png',
    'Vegan': 'assets/icons/vegan.png',
    'Vegetarian': 'assets/icons/veg.png',
  };

  @override
  void initState() {
    super.initState();
    _mealPlanBloc = BlocProvider.of<MealplanBloc>(context);
    _mealPlanBloc.add(ListDietaryEvent());
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return MultiBlocListener(
      listeners: [
        BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is ListDietaryLoading) {
              setState(() {
                _isLoading = true;
              });
            } else if (state is ListDietarySuccess) {
              final dietaryData = state.data as DietaryListModel;
              setState(() {
                _isLoading = false;
                _preferences = dietaryData.data?.dietaries ?? [];
              });
            } else if (state is ListDietaryFailed) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.message)),
              );
            }
          },
        ),
        BlocListener<NewmealplanBloc, NewMealPlanState>(
          listener: (context, state) {
            if (state is NewMealPlanStep3StateLoading) {
              setState(() {
                _isLoading = true;
              });
            } else if (state is NewMealPlanStep3StateSuccess) {
              setState(() {
                _isLoading = false;
              });
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) =>
                      ChooseSpiceLevelNew(mealPlanId: widget.mealPlanId),
                ),
              );
            } else if (state is NewMealPlanStep3StateLoadFailed) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.error)),
              );
            }
          },
        ),
      ],
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF6F3EC),
          elevation: 0,
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.transparent,
          automaticallyImplyLeading: false,
          centerTitle: true,
          title: Image.asset(
            'assets/logo.png',
            width: 112,
            height: 29,
            fit: BoxFit.contain,
          ),
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(screenHeight * 0.002),
            child: Divider(
              color: Colors.grey[300],
              height: screenHeight * 0.002,
            ),
          ),
        ),
        body: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            // Dismiss any focus to prevent conflicts
            FocusScope.of(context).unfocus();
          },
          child: Column(
            children: [
              // Fixed header section
              SizedBox(height: size.height * 0.01),
              // Close button and header section
              Padding(
                padding: EdgeInsets.fromLTRB(
                  size.width * 0.04,
                  0,
                  size.width * 0.04,
                  size.height * 0.015,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          if (!mounted) return;
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            if (!mounted) return;
                            Navigator.of(context).pop();
                          });
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Icon(
                            Icons.close,
                            size: size.width * 0.06,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Progress bar section
              Padding(
                padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Single continuous progress bar
                    Stack(
                      children: [
                        Container(
                          height: size.height * 0.01,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: const Color(0xFFE1DDD5),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        FractionallySizedBox(
                          widthFactor: 0.75, // 3/4 filled for third step
                          child: Container(
                            height: size.height * 0.01,
                            decoration: BoxDecoration(
                              color: const Color(0xFF007A4D),
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: size.height * 0.015),
                    // Step indicator text
                    Text(
                      '3 of 4',
                      style: TextStyle(
                        fontSize: size.width * 0.035,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: const Color(0xFF414346),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: size.height * 0.015),
              // Title
              Padding(
                padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "Select your dietary preferences",
                    style: TextStyle(
                      fontSize: isTablet ? size.width * 0.05 : twentyFour,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                      color: const Color(0xFF1F2122),
                    ),
                  ),
                ),
              ),
              SizedBox(height: size.height * 0.02),
              // Scrollable content
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // List of preferences
                      _isLoading
                          ? Container(
                              height: size.height * 0.3,
                              child: const Center(
                                child: CircularProgressIndicator(
                                  color: Color(0xFF007A4D),
                                ),
                              ),
                            )
                          : isTablet
                              ? _buildGridView(size, isLandscape)
                              : _buildListView(size),
                      SizedBox(height: size.height * 0.02),
                      // Comments and Personalization Tags section
                      Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: size.width * 0.04),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Comments field
                            Text(
                              "Comments (Optional)",
                              style: TextStyle(
                                fontSize: forteen,
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Inter',
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                            SizedBox(height: size.height * 0.01),
                            TextFormField(
                              controller: _commentsController,
                              maxLines: 3,
                              decoration: InputDecoration(
                                hintText:
                                    "Add any special dietary requirements or preferences...",
                                hintStyle: TextStyle(
                                  fontSize: twelve,
                                  color: const Color(0xFF9E9E9E),
                                  fontFamily: 'Inter',
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFB9B6AD)),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFB9B6AD)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: const BorderSide(
                                      color: Color(0xFF1F2122)),
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: size.width * 0.03,
                                  vertical: size.height * 0.015,
                                ),
                              ),
                            ),
                            SizedBox(height: size.height * 0.02),
                            // Personalization tags field
                            Text(
                              "Personalization Tags (Optional)",
                              style: TextStyle(
                                fontSize: forteen,
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Inter',
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                            SizedBox(height: size.height * 0.01),
                            TextFormField(
                              controller: _personalizationTagsController,
                              decoration: InputDecoration(
                                hintText:
                                    "e.g., low-sodium, gluten-free, keto-friendly...",
                                hintStyle: TextStyle(
                                  fontSize: twelve,
                                  color: const Color(0xFF9E9E9E),
                                  fontFamily: 'Inter',
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFB9B6AD)),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFB9B6AD)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: const BorderSide(
                                      color: Color(0xFF1F2122)),
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: size.width * 0.03,
                                  vertical: size.height * 0.015,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Bottom spacing for buttons
                      SizedBox(height: size.height * 0.1),
                    ],
                  ),
                ),
              ),
              // Fixed bottom navigation buttons
              Container(
                padding: EdgeInsets.all(size.width * 0.04),
                // decoration: const BoxDecoration(
                //   color: Color(0xFFF6F3EC),
                //   border: Border(
                //     top: BorderSide(color: Color(0xFFE1DDD5), width: 1),
                //   ),
                // ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: Color(0xFF1F2122)),
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.07),
                          ),
                          padding: EdgeInsets.symmetric(
                              horizontal: screenWidth * 0.04,
                              vertical: screenHeight * 0.02),
                        ),
                        child: Text(
                          'Back',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            fontSize: size.width * 0.04,
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: size.width * 0.02),
                    Flexible(
                      child: ElevatedButton(
                        onPressed: _isLoading
                            ? null
                            : () {
                                if (_selectedPreferenceId == null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                          'Please select a dietary preference'),
                                      backgroundColor: Color(0xFFE11900),
                                    ),
                                  );
                                  return;
                                }

                                final dietaryData = {
                                  "meal_plan_id": widget.mealPlanId,
                                  "dietary_preference_id":
                                      _selectedPreferenceId,
                                  'comments':
                                      _commentsController.text.trim().isEmpty
                                          ? null
                                          : _commentsController.text.trim(),
                                  "personalization_tags":
                                      _personalizationTagsController.text
                                              .trim()
                                              .isEmpty
                                          ? null
                                          : _personalizationTagsController.text
                                              .trim()
                                };

                                context
                                    .read<NewmealplanBloc>()
                                    .add(NewStep3MEalPlanEvent(dietaryData));
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.07),
                          ),
                          padding: EdgeInsets.symmetric(
                              horizontal: screenWidth * 0.04,
                              vertical: screenHeight * 0.02),
                        ),
                        child: _isLoading
                            ? SizedBox(
                                width: size.width * 0.05,
                                height: size.width * 0.05,
                                child: const CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                'Next',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w400,
                                  fontSize: size.width * 0.04,
                                  color: Colors.white,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _commentsController.dispose();
    _personalizationTagsController.dispose();
    super.dispose();
  }

  Widget _buildListView(Size size) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
      child: Column(
        children: _preferences.map((preference) {
          final isSelected = _selectedPreferenceId == preference.id;
          return Padding(
            padding: EdgeInsets.only(bottom: size.height * 0.015),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  if (!mounted) return;
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (!mounted) return;
                    setState(() {
                      _selectedPreferenceId = preference.id;
                    });
                  });
                },
                borderRadius: BorderRadius.circular(size.width * 0.02),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: size.width * 0.04,
                    vertical: size.height * 0.02,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFFE1DDD5)
                        : const Color(0xFFF6F3EC),
                    borderRadius: BorderRadius.circular(size.width * 0.02),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF1F2122)
                          : const Color(0xFFB9B6AD),
                      width: 1,
                    ),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                  ),
                  child: Row(
                    children: [
                      Image.asset(
                        _dietaryIcons[preference.name] ??
                            'assets/icons/organic.png',
                        width: twentyFour,
                        height: twentyFour,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) => Icon(
                          Icons.error,
                          size: size.width * 0.06,
                          color: Colors.red,
                        ),
                      ),
                      SizedBox(width: size.width * 0.05),
                      Expanded(
                        child: Text(
                          preference.name ?? '',
                          style: TextStyle(
                            fontSize: forteen,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Inter',
                            color: const Color(0xFF1F2122),
                            height: 1.4,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildGridView(Size size, bool isLandscape) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount:
              isLandscape ? 3 : 2, // 3 columns in landscape, 2 in portrait
          crossAxisSpacing: size.width * 0.02,
          mainAxisSpacing: size.height * 0.015,
          childAspectRatio: 3.0, // Adjust for content
        ),
        itemCount: _preferences.length,
        itemBuilder: (context, index) {
          final preference = _preferences[index];
          final isSelected = _selectedPreferenceId == preference.id;
          return Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                if (!mounted) return;
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (!mounted) return;
                  setState(() {
                    _selectedPreferenceId = preference.id;
                  });
                });
              },
              borderRadius: BorderRadius.circular(size.width * 0.02),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: size.width * 0.04,
                  vertical: size.height * 0.02,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(0xFFE1DDD5)
                      : const Color(0xFFF6F3EC),
                  borderRadius: BorderRadius.circular(size.width * 0.02),
                  border: Border.all(
                    color: isSelected
                        ? const Color(0xFF1F2122)
                        : const Color(0xFFB9B6AD),
                    width: 1,
                  ),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Row(
                  children: [
                    Image.asset(
                      _dietaryIcons[preference.name] ??
                          'assets/icons/organic.png',
                      width: size.width * 0.05,
                      height: size.width * 0.05,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) => Icon(
                        Icons.error,
                        size: size.width * 0.05,
                        color: Colors.red,
                      ),
                    ),
                    SizedBox(width: size.width * 0.03),
                    Expanded(
                      child: Text(
                        preference.name ?? '',
                        style: TextStyle(
                          fontSize: size.width * 0.035,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Inter',
                          color: const Color(0xFF1F2122),
                          height: 1.4,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
