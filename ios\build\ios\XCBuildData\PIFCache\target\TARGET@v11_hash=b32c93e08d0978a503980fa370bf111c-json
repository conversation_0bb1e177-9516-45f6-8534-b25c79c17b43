{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989d57d005c8531c1121c1cc62729f1b30", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a3ed79ebeb55cab386355e0da3dd5300", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b1c913001e713820ed6f947c73fd926", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d8913c21988bd6f099bfe51f975001cb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b1c913001e713820ed6f947c73fd926", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9895fe60c039657ce55be8c624fa3e169f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9820df5350723d054e6294bf53401a9fcc", "guid": "bfdfe7dc352907fc980b868725387e98ce8fd2f4762137eba8fba16707e46b58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c3afa80fc1ed90f972100cc470f88a4", "guid": "bfdfe7dc352907fc980b868725387e98ea17b5d506c3c80611eb6aa4e2bed635", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983629f6c7be76f1d4a49fec214284ee44", "guid": "bfdfe7dc352907fc980b868725387e98a5faf7a6e202e941c8f2a4b0181fd4bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c03f91efee9d6e878af6caefbf16169", "guid": "bfdfe7dc352907fc980b868725387e9830e46c236afbb8b8c997a347ddc16a5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc7529eee8281d4392c8db8d268d4ccc", "guid": "bfdfe7dc352907fc980b868725387e98bc0064466b1fd1bd482d7692dd377e74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815f80edab65164cf48866b3a80636acf", "guid": "bfdfe7dc352907fc980b868725387e986abcb4978475316164c68ba0dfc673ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b863944304edcc56a6b738e336b3f6f2", "guid": "bfdfe7dc352907fc980b868725387e9857498122c7f3ba515b6f13e0af8589da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a89421a577365e5de4acec2029f2ca7b", "guid": "bfdfe7dc352907fc980b868725387e9881d55a8c7a002908db9aa125eb7dd13d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98725428406de73808e7c9bdc7f13c2113", "guid": "bfdfe7dc352907fc980b868725387e9865014b7ebf025e3ef5aaf4b279f910d3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98413b46d07cbcc41fa40e17185c237276", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f350c2d64d0af6d98ce8b5b9bc110458", "guid": "bfdfe7dc352907fc980b868725387e9835f44b0b6cdf16ea7887852a86b22eee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fde32525d35ebf13acb5b16c5883525", "guid": "bfdfe7dc352907fc980b868725387e980b336e38bcf2a6bd065c9f121dfb6140"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816f51552bdd925077281706e9673bdac", "guid": "bfdfe7dc352907fc980b868725387e98e4e6c5f6a2086b102da8ef4eb6383925"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985667843c516b465ba1a043af68138796", "guid": "bfdfe7dc352907fc980b868725387e982c5cb0d4995e33f35832e73e84319787"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876c505661d3eeb868fe92df5256f22f3", "guid": "bfdfe7dc352907fc980b868725387e9891a47156faf571ee94d36946e8196171"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a788f607e31baf2eca919be3264c1518", "guid": "bfdfe7dc352907fc980b868725387e987060baea26178cfcf98e82c2c2c6d2b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4ce15e9ea05708a4343b881f0a88180", "guid": "bfdfe7dc352907fc980b868725387e989a72ba03b3671050db734734393cbb2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d217e6f64660eef7d45f5c6764be1d29", "guid": "bfdfe7dc352907fc980b868725387e9888331b366250886048635d9f6e4ef913"}], "guid": "bfdfe7dc352907fc980b868725387e98eedcb8cf93621c12e230102e6b9833b9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e982cbfde9e125fd2a6190f3ac9f0671fef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e982a45967be4394acedd162dbb7210ed45"}], "guid": "bfdfe7dc352907fc980b868725387e98fb83155f40ae8422adb83aa377b8b641", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c58c36395e2d32d8597f478afccc1fff", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e987300cb87e62ccd4af554191e20b648f7", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e982196076c2dfcb5f8c7868fb35090266b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}