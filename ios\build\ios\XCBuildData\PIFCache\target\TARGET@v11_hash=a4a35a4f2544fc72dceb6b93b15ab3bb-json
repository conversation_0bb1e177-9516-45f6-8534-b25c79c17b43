{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9805e99277c6affce6464bba15e2d9f734", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e50facf5b61a5c8300ac15bf12b135b8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983608760497e4ebe8521ce1c08e9b33dd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ef05e4db5dfc95a679b5b4076e37787", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983608760497e4ebe8521ce1c08e9b33dd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985ae1e4c16ea46d12ad3c3307c1d76b73", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989f64e1e88a1fd156b641daa38c6dd222", "guid": "bfdfe7dc352907fc980b868725387e98028a1e419f5f312ee9de31ec2bc8be82", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9883cc1fd68478a267cb1cb3257450eba0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9848a4f4fe53a5f407fea2ab02e25a58cc", "guid": "bfdfe7dc352907fc980b868725387e98dc2bf1f1cfc6cb0c85cfa8de794b310c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800f4f0a330c8856512db67c0afab30fc", "guid": "bfdfe7dc352907fc980b868725387e9866f0a9a65191b023a931f5c7ae7afe71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800e826e6bed45964130400674bb93195", "guid": "bfdfe7dc352907fc980b868725387e9898215cec44ac4f54f0438bebc3b1c049"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98460b53a8253f028e6442c3bbfc522858", "guid": "bfdfe7dc352907fc980b868725387e98300c303cce23aeee91822cdb9b974b1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b893ce1204c2695eb79f1075831f17f", "guid": "bfdfe7dc352907fc980b868725387e98b083149891c97cc78d017e21d13555f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805ea0e957042f7e7a24c48281823d24e", "guid": "bfdfe7dc352907fc980b868725387e982b1039a6e41d83de8f0e8959b0fbf497"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5001a0882078c09c9c2c29bc77eb0e8", "guid": "bfdfe7dc352907fc980b868725387e98d3e842ca8b6e472c0b3db20a16a6e407"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981abfaa87a80572a98818b9929e9c18d3", "guid": "bfdfe7dc352907fc980b868725387e98cd626f0550f42bada94c2ea7a37e1634"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cae8b46e5fc3548799fd0060ec603bf", "guid": "bfdfe7dc352907fc980b868725387e9857d0c5187af3b3dc0747b798f002d698"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c02645cb5a76ae208b345008140f5b9", "guid": "bfdfe7dc352907fc980b868725387e9807c745c6bcc32e3694848cd046467363"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b115e20ef90294e5cb62c2f3ff03673", "guid": "bfdfe7dc352907fc980b868725387e984e658c2860cd679cd32d173815546c59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6cf198cd381b88f48c986fc70f10a68", "guid": "bfdfe7dc352907fc980b868725387e988bca0c787dfe5541b8a7953fcd88183c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cec54863aae14381b2e9ae34ea3ec3e1", "guid": "bfdfe7dc352907fc980b868725387e98b416a01eb7e9f03797a8d041ad55c83b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9a6395f99bf1b7c37e75cc9b26f02b9", "guid": "bfdfe7dc352907fc980b868725387e9895db5536b892ebfee82bb43bf32ace5b"}], "guid": "bfdfe7dc352907fc980b868725387e98a2dfc88974a4ecd4a202a1ae9b6cf0cf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e985403dc36e85a7ccbcb8b64c0418d5592"}], "guid": "bfdfe7dc352907fc980b868725387e98ba600287af92f8b36ff2fb6852626804", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988f48450ec3592365d366817b052786a1", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98a766d80369516de1e3fe9cc0fe625884", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}