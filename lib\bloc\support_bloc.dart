import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/notification/notificationlistmodel.dart';
import 'package:db_eats/data/models/support/issuecategorymodel.dart';
import 'package:db_eats/data/models/support/listissuesmessagesmodel.dart';
import 'package:db_eats/data/models/support/listissuesmodel.dart';
import 'package:db_eats/data/models/verifyrefreshtokenmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/storage/localstorage.dart';

class SupportBloc extends Bloc<SupportEvent, SupportState> {
  SupportBloc() : super(SupportInitial()) {
    on<ListIssueCategoryEvent>(_listIssueCategory);
    on<RaiseTicketEvent>(_raiseTicket);
    on<ListIssuesEvent>(_handleListIssues);
    on<ListIssueMessagesEvent>(_listIssueMessages);
    on<SendIssueMessageEvent>(_sendIssueMessage);
    on<ListIssueMessagesEventSilent>(_listIssueMessagesSilent);
    on<ListNotificationsEvent>(_listNotifications);
    on<MarkNotificationAsReadEvent>(_markNotificationAsRead);
    on<RefreshTokenEvent>(_refreshToken);
  }

  Future<void> _listIssueCategory(
      ListIssueCategoryEvent event, Emitter<SupportState> emit) async {
    emit(ListIssueCategoryLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/common/issuecategory/list', {'role': 'CUSTOMER'});
      log('Issue Category Response: $response');
      // Check for 401
      if (response['status_code'] == 401) {
        final refreshToken = await LocalStorage.getRefreshToken();
        add(RefreshTokenEvent(
            nextEvent: event, refreshToken: refreshToken.toString()));
        return;
      }
      Initializer.issueCatogoryModel = IssueCatogoryModel.fromJson(response);

      if (Initializer.issueCatogoryModel.status == true) {
        emit(ListIssueCategorySuccess(Initializer.issueCatogoryModel.data));
      } else {
        emit(ListIssueCategoryFailed(
            response['message'] ?? 'Failed to list issue categories'));
      }
    } catch (e) {
      log('Error listing issue categories: $e');
      emit(ListIssueCategoryFailed(
          'Error occurred while listing issue categories'));
    }
  }

  Future<void> _raiseTicket(
      RaiseTicketEvent event, Emitter<SupportState> emit) async {
    emit(RaiseTicketLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/support/report-issue', event.data);
      log('Raise Issue Response: $response');
      if (response['status_code'] == 401) {
        final refreshToken = await LocalStorage.getRefreshToken();
        add(RefreshTokenEvent(
            nextEvent: event, refreshToken: refreshToken.toString()));
        return;
      }

      if (response['status'] == true) {
        emit(RaiseTicketSuccess(response['message'] ?? 'Ticket raised'));
      } else {
        emit(
            RaiseTicketFailed(response['message'] ?? 'Failed to raise ticket'));
      }
    } catch (e) {
      log('Error raising ticket: $e');
      emit(RaiseTicketFailed('Error occurred while raising ticket'));
    }
  }

  Future<void> _handleListIssues(
      ListIssuesEvent event, Emitter<SupportState> emit) async {
    try {
      List<dynamic> currentIssues = [];
      if (state is ListIssuesSuccess && event.page > 1) {
        currentIssues = (state as ListIssuesSuccess).allIssues;
        emit(ListIssuesLoadingMore(currentIssues));
      } else {
        emit(ListIssuesLoading());
      }

      final Map<String, dynamic> body = {
        "page": event.page,
        "limit": event.limit,
      };

      // if (event.status != null) body["status"] = event.status;
      // if (event.orderId != null) body["orderid"] = event.orderId;

      final response =
          await ServerHelper.post1('/v1/customer/support/list-issues', body);
      log('List Issues Response: $response');
      if (response['status_code'] == 401) {
        final refreshToken = await LocalStorage.getRefreshToken();
        add(RefreshTokenEvent(
            nextEvent: event, refreshToken: refreshToken.toString()));
        return;
      }

      Initializer.listIssuesModel = ListIssuesModel.fromJson(response);

      if (Initializer.listIssuesModel.status == true) {
        final newIssues = Initializer.listIssuesModel.data?.issues ?? [];
        final allIssues =
            event.page == 1 ? newIssues : [...currentIssues, ...newIssues];

        emit(ListIssuesSuccess(
          Initializer.listIssuesModel.data,
          allIssues: allIssues,
        ));
      } else {
        emit(ListIssuesFailed(response['message'] ?? 'Failed to list issues'));
      }
    } catch (e) {
      log('Error listing issues: $e');
      emit(ListIssuesFailed('Error occurred while listing issues'));
    }
  }

  Future<void> _listIssueMessages(
      ListIssueMessagesEvent event, Emitter<SupportState> emit) async {
    if (!event.loadMore) {
      emit(ListIssueMessagesLoading());
    }
    try {
      final response =
          await ServerHelper.post1('/v1/customer/support/list-messages', {
        'issue_id': event.issueId,
        'page': event.page,
        'limit': event.limit,
      });
      log('List Issue Messages Response: $response'); // Added log statement
      if (response['status_code'] == 401) {
        final refreshToken = await LocalStorage.getRefreshToken();
        add(RefreshTokenEvent(
            nextEvent: event, refreshToken: refreshToken.toString()));
        return;
      }

      Initializer.listIssueMessagesModel =
          ListIssueMessagesModel.fromJson(response);

      if (Initializer.listIssueMessagesModel.status == true) {
        emit(ListIssueMessagesSuccess(
          Initializer.listIssueMessagesModel.data,
          loadMore: event.loadMore,
        ));
      } else {
        emit(ListIssueMessagesFailed(
            response['message'] ?? 'Failed to list issue messages'));
      }
    } catch (e) {
      emit(ListIssueMessagesFailed(
          'Error occurred while listing issue messages'));
    }
  }

  Future<void> _sendIssueMessage(
      SendIssueMessageEvent event, Emitter<SupportState> emit) async {
    emit(SendIssueMessageLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/support/send-message', event.data);
      log('Send Issue Message Response: $response');
      if (response['status_code'] == 401) {
        final refreshToken = await LocalStorage.getRefreshToken();
        add(RefreshTokenEvent(
            nextEvent: event, refreshToken: refreshToken.toString()));
        return;
      }

      if (response['status'] == true) {
        emit(SendIssueMessageSuccess(response['message'] ?? 'Message sent'));
      } else {
        emit(SendIssueMessageFailed(
            response['message'] ?? 'Failed to send message'));
      }
    } catch (e) {
      log('Error sending issue message: $e');
      emit(SendIssueMessageFailed('Error occurred while sending message'));
    }
  }

  Future<void> _listIssueMessagesSilent(
      ListIssueMessagesEventSilent event, Emitter<SupportState> emit) async {
    try {
      final response =
          await ServerHelper.post1('/v1/customer/support/list-messages', {
        'issue_id': event.issueId,
        'page': event.page,
        'limit': event.limit,
      });
      log('Silent List Issue Messages Response: $response');
      if (response['status_code'] == 401) {
        final refreshToken = await LocalStorage.getRefreshToken();
        add(RefreshTokenEvent(
            nextEvent: event, refreshToken: refreshToken.toString()));
        return;
      }

      Initializer.listIssueMessagesModel =
          ListIssueMessagesModel.fromJson(response);

      if (Initializer.listIssueMessagesModel.status == true) {
        emit(ListIssueMessagesSuccessSilent(
            Initializer.listIssueMessagesModel.data));
      }
      // Don't emit failure states for silent refresh
    } catch (e) {
      log('Silent refresh error: $e');
      // Don't emit failure states for silent refresh
    }
  }

  Future<void> _listNotifications(
      ListNotificationsEvent event, Emitter<SupportState> emit) async {
    emit(ListNotificationsLoading());
    try {
      final response = await ServerHelper.get1(
          '/v1/customer/notification/list?page=${event.page}&limit=${event.limit}');
      log('List Notifications Response: $response');
      if (response['status_code'] == 401) {
        final refreshToken = await LocalStorage.getRefreshToken();
        add(RefreshTokenEvent(
            nextEvent: event, refreshToken: refreshToken.toString()));
        return;
      }

      Initializer.notificationListModel =
          NotificationListModel.fromJson(response);

      if (Initializer.notificationListModel.status == true) {
        emit(ListNotificationsSuccess(Initializer.notificationListModel.data));
      } else {
        emit(ListNotificationsFailed(
            response['message'] ?? 'Failed to list notifications'));
      }
    } catch (e) {
      log('Error listing notifications: $e');
      emit(ListNotificationsFailed(
          'Error occurred while listing notifications'));
    }
  }

  Future<void> _markNotificationAsRead(
      MarkNotificationAsReadEvent event, Emitter<SupportState> emit) async {
    emit(MarkNotificationAsReadLoading(event.notificationId));
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/notification/mark-as-read',
          {'id': event.notificationId});
      log('Mark Notification As Read Response: $response');
      if (response['status_code'] == 401) {
        final refreshToken = await LocalStorage.getRefreshToken();
        add(RefreshTokenEvent(
            nextEvent: event, refreshToken: refreshToken.toString()));
        return;
      }

      if (response['status'] == true) {
        emit(MarkNotificationAsReadSuccess(
            response['message'] ?? 'Notification marked as read',
            event.notificationId));
      } else {
        emit(MarkNotificationAsReadFailed(
            response['message'] ?? 'Failed to mark notification as read',
            event.notificationId));
      }
    } catch (e) {
      log('Error marking notification as read: $e');
      emit(MarkNotificationAsReadFailed(
          'Error occurred while marking notification as read',
          event.notificationId));
    }
  }

  Future<void> _refreshToken(
      RefreshTokenEvent event, Emitter<SupportState> emit) async {
    emit(RefreshTokenLoading());
    try {
      Initializer.verifyRefreshTokenModel = VerifyRefreshTokenModel.fromJson(
          await ServerHelper.getrefresh(
              '/v1/customer/auth/verify-refresh-token'));
      if (Initializer.verifyRefreshTokenModel.status == true) {
        await LocalStorage.setAccessToken(
            Initializer.verifyRefreshTokenModel.data?.accessToken ?? "");
        emit(RefreshTokenSuccess());
        if (event.nextEvent != null) {
          add(event.nextEvent!);
        }
      } else if (Initializer.verifyRefreshTokenModel.statusCode == 401) {
        emit(RefreshTokenFailed());
        // await LocalStorage.setAccessToken('');
        // await LocalStorage.setRefreshToken('');
        // Navigator.of(context).pushAndRemoveUntil(
        //   MaterialPageRoute(builder: (context) => const Home()),
        //   (route) => false,
        // );
      }
    } catch (e) {
      emit(RefreshTokenFailed());
    }
  }
}

// Events
class SupportEvent {
  const SupportEvent();
}

class ListIssueCategoryEvent extends SupportEvent {}

class RaiseTicketEvent extends SupportEvent {
  final Map<String, dynamic> data;
  RaiseTicketEvent(this.data);
}

class ListIssuesEvent extends SupportEvent {
  final int page;
  final int limit;
  final bool loadMore; // Add this parameter

  const ListIssuesEvent({
    required this.page,
    required this.limit,
    this.loadMore = false, // Default to false
  });
}

class ListIssueMessagesEvent extends SupportEvent {
  final int issueId;
  final int page;
  final int limit;
  final bool loadMore;

  ListIssueMessagesEvent(
    this.issueId, {
    this.page = 1,
    this.limit = 10,
    this.loadMore = false,
  });
}

class SendIssueMessageEvent extends SupportEvent {
  final Map<String, dynamic> data;
  SendIssueMessageEvent(this.data);
}

class ListIssueMessagesEventSilent extends SupportEvent {
  final int issueId;
  final int page;
  final int limit;

  ListIssueMessagesEventSilent(
    this.issueId, {
    this.page = 1,
    this.limit = 10,
  });
}

class ListNotificationsEvent extends SupportEvent {
  final int page;
  final int limit;

  const ListNotificationsEvent({
    this.page = 1,
    this.limit = 10,
  });
}

class MarkNotificationAsReadEvent extends SupportEvent {
  final int notificationId;
  MarkNotificationAsReadEvent(this.notificationId);
}

class RefreshTokenEvent extends SupportEvent {
  final String refreshToken;
  final SupportEvent? nextEvent;

  RefreshTokenEvent({required this.refreshToken, required this.nextEvent});
}

// States
abstract class SupportState {}

class SupportInitial extends SupportState {}

class ListIssueCategoryLoading extends SupportState {}

class ListIssueCategorySuccess extends SupportState {
  final dynamic data;
  ListIssueCategorySuccess(this.data);
}

class ListIssueCategoryFailed extends SupportState {
  final String message;
  ListIssueCategoryFailed(this.message);
}

class RaiseTicketLoading extends SupportState {}

class RaiseTicketSuccess extends SupportState {
  final String message;
  RaiseTicketSuccess(this.message);
}

class RaiseTicketFailed extends SupportState {
  final String message;
  RaiseTicketFailed(this.message);
}

class ListIssuesLoading extends SupportState {}

class ListIssuesLoadingMore extends SupportState {
  final List<dynamic> currentIssues;
  ListIssuesLoadingMore(this.currentIssues);
}

class ListIssuesSuccess extends SupportState {
  final dynamic data;
  final List<dynamic> allIssues;
  ListIssuesSuccess(this.data, {this.allIssues = const []});
}

class ListIssuesFailed extends SupportState {
  final String message;
  ListIssuesFailed(this.message);
}

class ListIssueMessagesLoading extends SupportState {}

class ListIssueMessagesSuccess extends SupportState {
  final dynamic data;
  final bool loadMore;
  ListIssueMessagesSuccess(this.data, {this.loadMore = false});
}

class ListIssueMessagesFailed extends SupportState {
  final String message;
  ListIssueMessagesFailed(this.message);
}

class SendIssueMessageLoading extends SupportState {}

class SendIssueMessageSuccess extends SupportState {
  final String message;
  SendIssueMessageSuccess(this.message);
}

class SendIssueMessageFailed extends SupportState {
  final String message;
  SendIssueMessageFailed(this.message);
}

class ListIssueMessagesSuccessSilent extends SupportState {
  final dynamic data;
  ListIssueMessagesSuccessSilent(this.data);
}

class ListNotificationsLoading extends SupportState {}

class ListNotificationsSuccess extends SupportState {
  final dynamic data;
  ListNotificationsSuccess(this.data);
}

class ListNotificationsFailed extends SupportState {
  final String message;
  ListNotificationsFailed(this.message);
}

class MarkNotificationAsReadSuccess extends SupportState {
  final String message;
  final int notificationId;
  MarkNotificationAsReadSuccess(this.message, this.notificationId);
}

class MarkNotificationAsReadLoading extends SupportState {
  final int notificationId;
  MarkNotificationAsReadLoading(this.notificationId);
}

class MarkNotificationAsReadFailed extends SupportState {
  final String message;
  final int notificationId;
  MarkNotificationAsReadFailed(this.message, this.notificationId);
}

class RefreshTokenLoading extends SupportState {}

class RefreshTokenSuccess extends SupportState {}

class RefreshTokenFailed extends SupportState {}
