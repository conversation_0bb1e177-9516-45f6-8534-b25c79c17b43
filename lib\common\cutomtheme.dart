import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomTheme {
  static ThemeData get lightTheme {
    return ThemeData.light(useMaterial3: true).copyWith(
      brightness: Brightness.light,
      primaryColor: const Color(0xff029B3A), // Global primary color
      scaffoldBackgroundColor: const Color(0xffFFFFFF),
      colorScheme: ColorScheme.fromSwatch().copyWith(
        secondary: const Color(0xff029B3A),
        surface: Colors.white,
      ),
      appBarTheme: const AppBarTheme(
        elevation: 0, // Remove shadow
        backgroundColor: Colors.transparent, // Ensure transparent AppBar
        titleTextStyle: TextStyle(
          fontWeight: FontWeight.w500,
          color: Colors.white,
          fontSize: 18,
          fontFamily: 'IBM Plex Sans',
        ),
        iconTheme: IconThemeData(color: Colors.white), // Icons color
        systemOverlayStyle:
            SystemUiOverlayStyle.dark, // Control status bar color
      ),
    );
  }
}
