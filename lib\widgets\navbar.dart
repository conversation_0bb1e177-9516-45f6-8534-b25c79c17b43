import 'package:flutter/material.dart';

class CustomBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  
  const CustomBottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);
  
  // Add responsive helper method
  double getResponsiveSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return 8;
    if (width < 600) return 10;
    if (width < 900) return 12;
    return 14;
  }
  
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final baseTextSize = getResponsiveSize(context);
    
    return Container(
      height: isLandscape ? size.height * 0.1 : size.height * 0.08,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      padding: EdgeInsets.symmetric(
        vertical: size.height * 0.01,
        horizontal: size.width * 0.04,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildNavItem(
            context, 
            'assets/icons/home2.png', 
            'assets/icons/home.png', 
            'Home',
            currentIndex == 0, 
            () => onTap(0)
          ),
          _buildNavItem(
            context, 
            'assets/icons/orders.png', 
            'assets/icons/orders1.png', 
            'Orders',
            currentIndex == 1, 
            () => onTap(1)
          ),
          _buildNavItem(
            context, 
            'assets/icons/catering.png', 
            'assets/icons/catering1.png', 
            'Catering',
            currentIndex == 2, 
            () => onTap(2)
          ),
          _buildNavItem(
            context, 
            'assets/icons/message.png', 
            'assets/icons/message1.png', 
            'Messages',
            currentIndex == 3, 
            () => onTap(3)
          ),
          _buildNavItem(
            context, 
            'assets/icons/account.png', 
            'assets/icons/account1.png', 
            'Account',
            currentIndex == 4, 
            () => onTap(4)
          ),
        ],
      ),
    );
  }
  
  Widget _buildNavItem(
    BuildContext context, 
    String inactiveIconPath, 
    String activeIconPath, 
    String label,
    bool isActive, 
    VoidCallback onTap
  ) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final baseTextSize = getResponsiveSize(context);
    final iconSize = isLandscape ? size.height * 0.03 : size.width * 0.045;
    
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            isActive ? activeIconPath : inactiveIconPath,
            width: iconSize,
            height: iconSize,
            // Removed color property to show original icon colors
          ),
          SizedBox(height: size.height * 0.005),
          Text(
            label,
            style: TextStyle(
              fontSize: baseTextSize,
              fontFamily: 'Inter',
              fontWeight: isActive ? FontWeight.w500 : FontWeight.w400,
              color: isActive 
                ? const Color(0xff1f2022)
                : const Color(0xffB5B5B5),
            ),
          ),
        ],
      ),
    );
  }
}