import 'package:flutter/material.dart';

class EmailVerificationPage extends StatelessWidget {
  final String email;

  const EmailVerificationPage({super.key, required this.email});

  String maskedEmail(String email) {
    if (email.isEmpty) return "j*****<EMAIL>";
    var parts = email.split('@');
    if (parts.length != 2) return email;
    final visibleStart = parts[0].substring(0, 1);
    final visibleEnd = parts[0].substring(parts[0].length - 1);
    return "$visibleStart*****$visibleEnd@${parts[1]}";
  }

  @override
  Widget build(BuildContext context) {
    final masked = maskedEmail(email);

    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 20),
              Row(
                children: [
                  IconButton(
                    icon: Image.asset(
                      'assets/icons/left_arrow.png',
                      width: 15.58,
                      height: 15.58,
                    ),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(
                      minWidth: 24,
                      minHeight: 24,
                    ),
                    onPressed: () => Navigator.pop(context),
                    iconSize: 15.19,
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.only(left: 4, top: 4.41),
                    ),
                  ),
                  const SizedBox(width: 6),
                  const Text(
                    'Back',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                      color: Color(0xFF1F2122),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 70),
              const Text(
                'Verify your account',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1F2122),
                ),
              ),
              const SizedBox(height: 20),
              RichText(
                textAlign: TextAlign.left,
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    height: 1.4,
                    color: Color(0xFF414346),
                    fontFamily: 'Inter',
                  ),
                  children: [
                    const TextSpan(text: "Please check your email "),
                    TextSpan(
                      text: masked,
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                    const TextSpan(text: " to verify your account."),
                  ],
                ),
              ),
              const SizedBox(height: 30),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    "Didn't receive the email? ",
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF414346),
                      fontFamily: 'Inter',
                    ),
                  ),
                  TextButton(
                    onPressed: () {},
                    child: const Text(
                      "Resend email",
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        decoration: TextDecoration.underline,
                        color: Color(0xFF414346),
                        fontFamily: 'Inter',
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
