class MealPlanListModel {
  bool? status;
  List<MealPlanListingData>? data;
  int? statusCode;

  MealPlanListModel({this.status, this.data, this.statusCode});

  MealPlanListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    if (json['data'] != null) {
      data = <MealPlanListingData>[];
      json['data'].forEach((v) {
        data!.add(MealPlanListingData.fromJson(v));
      });
    }
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['status_code'] = statusCode;
    return data;
  }
}

class MealPlanListingData {
  int? id;
  String? startDate;
  String? endDate;
  int? dishesPerDay;
  String? mealSelectionType;
  String? status;
  int? stepProgress;
  String? createdAt;
  String? total;
  int? deliveredDays;
  int? totalDays;

  MealPlanListingData({
    this.id,
    this.startDate,
    this.endDate,
    this.dishesPerDay,
    this.mealSelectionType,
    this.status,
    this.stepProgress,
    this.createdAt,
    this.total,
    this.deliveredDays,
    this.totalDays,
  });

  MealPlanListingData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    dishesPerDay = json['dishes_per_day'];
    mealSelectionType = json['meal_selection_type'];
    status = json['status'];
    stepProgress = json['step_progress'];
    createdAt = json['created_at'];
    total = json['total'];
    deliveredDays = json['delivered_days'];
    totalDays = json['total_days'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['dishes_per_day'] = dishesPerDay;
    data['meal_selection_type'] = mealSelectionType;
    data['status'] = status;
    data['step_progress'] = stepProgress;
    data['created_at'] = createdAt;
    data['total'] = total;
    data['delivered_days'] = deliveredDays;
    data['total_days'] = totalDays;
    return data;
  }
}
