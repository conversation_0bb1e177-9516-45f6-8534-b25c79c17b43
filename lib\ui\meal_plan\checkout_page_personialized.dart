import 'package:db_eats/data/models/guesthome/listaddressmodel.dart';
import 'package:db_eats/ui/cart/edit_address.dart';
import 'package:db_eats/ui/home2.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:db_eats/utils/customtoggle.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:db_eats/data/models/meal_plan/mealplanprogressmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/data/models/meal_plan/dropoffoptionmodel.dart';
import 'package:db_eats/data/models/meal_plan/deliverytimemodel.dart';
import 'package:db_eats/bloc/account_bloc.dart';

class CheckoutPagePersonialized extends StatefulWidget {
  final int mealPlanId;

  const CheckoutPagePersonialized({super.key, required this.mealPlanId});

  @override
  State<CheckoutPagePersonialized> createState() =>
      _CheckoutPagePersonializedState();
}

class _CheckoutPagePersonializedState extends State<CheckoutPagePersonialized> {
  String? dropOffOption;
  String? instructions;
  String? selectedDeliveryOption;
  Data? mealPlanData;

  // Add these properties
  List<DropoffOptionItem> _dropoffOptions = [];
  List<DeliveryTimeItem> _deliveryTimeOptions = [];
  DropoffOptionItem? _selectedDropoffOption;
  DeliveryTimeItem? _selectedDeliveryTime;
  String? _dropOffInstructions;

  // Add error states
  bool _showDropoffError = false;
  bool _showDeliveryTimeError = false;
  bool _isPlacingOrder = false; // Add this property

  late TextEditingController _instructionsController;

  // Add these properties
  AddressData? currentAddressData;
  String? currentAddress;

  @override
  void initState() {
    super.initState();
    _instructionsController = TextEditingController();
    context.read<MealplanBloc>().add(ListDropoffOptionEvent());
    context.read<MealplanBloc>().add(ListDeliveryTimeEvent());
    context.read<MealplanBloc>().add(MealPlanProgressEvent(widget.mealPlanId));
    context.read<AccountBloc>().add(ListAddressesEvent());
  }

  @override
  void dispose() {
    _instructionsController.dispose();
    super.dispose();
  }

  String _formatTime(String? time) {
    if (time == null) return '00:00 AM';
    final parts = time.split(':');
    if (parts.length < 2) return '00:00 AM';

    int hour = int.tryParse(parts[0]) ?? 0;
    String minute = parts[1];
    String period = hour >= 12 ? 'PM' : 'AM';

    if (hour > 12) hour -= 12;
    if (hour == 0) hour = 12;

    return '$hour:$minute $period';
  }

  Widget _buildLoadingShimmer() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: 20,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
        ),
      ),
    );
  }

  void _saveDeliveryDetails() {
    // Validate selections
    if (_selectedDropoffOption == null) {
      setState(() => _showDropoffError = true);
      return;
    }
    if (_selectedDeliveryTime == null) {
      setState(() => _showDeliveryTimeError = true);
      return;
    }

    // Get meal_plan_id from mealPlanData
    final mealPlanId = mealPlanData?.id;
    if (mealPlanId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Missing meal plan ID'),
          backgroundColor: Color(0xFFE11900),
        ),
      );
      return;
    }

    final orderData = {
      "meal_plan_id": mealPlanId,
      "drop_off_option_id": _selectedDropoffOption?.id,
      "drop_off_instructions": _dropOffInstructions,
      "delivery_time_id": _selectedDeliveryTime?.id,
      "address_id": currentAddressData?.id,
    };

    // Call Step7MealPlanEvent
    context.read<MealplanBloc>().add(Step7MealPlanEvent(orderData));
  }

  // Update the delivery option dropdown
  Widget _buildDropoffOptionDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Drop-Off Options',
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: 14,
            fontWeight: FontWeight.w500,
            height: 1.14,
            letterSpacing: 0.28,
            color: Color(0xFF1F2122),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 40,
          padding: const EdgeInsets.symmetric(horizontal: 18),
          decoration: BoxDecoration(
            border: Border.all(
                color:
                    _showDropoffError ? Colors.red : const Color(0xFFE1E3E6)),
            borderRadius: BorderRadius.circular(35),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<DropoffOptionItem>(
              value: _selectedDropoffOption,
              isExpanded: true,
              hint: Text(
                'Select drop-off option',
                style: TextStyle(
                  color:
                      _showDropoffError ? Colors.red : const Color(0xFF66696D),
                ),
              ),
              icon: const Icon(Icons.keyboard_arrow_down,
                  color: Color(0xFF1F2122)),
              items: _dropoffOptions
                  .map<DropdownMenuItem<DropoffOptionItem>>((item) {
                return DropdownMenuItem<DropoffOptionItem>(
                  value: item,
                  child: Text(
                    item.name ?? '',
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: 16,
                      height: 1.5,
                      color: Color(0xFF66696D),
                    ),
                  ),
                );
              }).toList(),
              onChanged: (DropoffOptionItem? newValue) {
                setState(() {
                  _selectedDropoffOption = newValue;
                  _showDropoffError = false;
                });
              },
            ),
          ),
        ),
        if (_showDropoffError)
          const Padding(
            padding: EdgeInsets.only(top: 8),
            child: Text(
              'Please select a drop-off option',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  // Update the delivery time options
  Widget _buildDeliveryTimeOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Delivery Time',
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Color(0xFF1F2122),
          ),
        ),
        const SizedBox(height: 12),
        ..._deliveryTimeOptions.map((option) => Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: _selectedDeliveryTime?.id == option.id
                        ? const Color(0xFF1F2122)
                        : _showDeliveryTimeError
                            ? Colors.red
                            : const Color(0xFFE1E3E6),
                  ),
                  color: _selectedDeliveryTime?.id == option.id
                      ? const Color(0xFFE1DDD5)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: RadioListTile<DeliveryTimeItem>(
                  value: option,
                  groupValue: _selectedDeliveryTime,
                  onChanged: (DeliveryTimeItem? value) {
                    setState(() {
                      _selectedDeliveryTime = value;
                      _showDeliveryTimeError = false;
                    });
                  },
                  title: Text(option.name ?? ''),
                  subtitle: Text(option.description ?? ''),
                  secondary: Text('+\$${option.cost ?? '0.00'}'),
                ),
              ),
            )),
        if (_showDeliveryTimeError)
          const Padding(
            padding: EdgeInsets.only(top: 8),
            child: Text(
              'Please select a delivery time',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  // Update the place order button to call _saveDeliveryDetails
  Widget _buildPlaceOrderButton() {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: _isPlacingOrder ? null : _saveDeliveryDetails,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1F2122),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: _isPlacingOrder ? 32 : 20,
            vertical: 17,
          ),
        ),
        child: _isPlacingOrder
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : const Text(
                'Place Order',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  // Add this method to get current address
  String _getCurrentAddress(List<AddressData>? addresses) {
    if (addresses == null || addresses.isEmpty) return 'No address';
    final address = addresses.firstWhere(
      (address) => address.isCurrent == true,
      orElse: () => addresses.first,
    );
    currentAddressData = address;
    return address.addressText ?? 'No address';
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<AccountBloc, AccountState>(
          listener: (context, state) {
            if (state is ListAddressesSuccess) {
              setState(() {
                currentAddress = _getCurrentAddress(state.data);
              });
            }
          },
        ),
      ],
      child: BlocConsumer<MealplanBloc, MealPlanState>(
        listener: (context, state) {
          if (state is MealPlanProgressSuccess) {
            setState(() {
              mealPlanData = state.data;

              _dropOffInstructions = _instructionsController.text;
            });
          } else if (state is ListDropoffOptionSuccess) {
            final dropoffData = state.data as DropoffOptionsModel;
            setState(() {
              _dropoffOptions = dropoffData.data?.data ?? [];
            });
          } else if (state is ListDeliveryTimeSuccess) {
            final deliveryTimeData = state.data as DeliveryTimeModel;
            setState(() {
              _deliveryTimeOptions = deliveryTimeData.data?.data ?? [];
            });
          } else if (state is Step7MealPlanLoading) {
            setState(() {
              _isPlacingOrder = true;
            });
          } else if (state is Step7MealPlanSuccess) {
            setState(() {
              _isPlacingOrder = false;
            });
            Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(
                  builder: (context) => const MainNavigationScreen()),
              (route) => false,
            );
          } else if (state is Step7MealPlanFailed) {
            setState(() {
              _isPlacingOrder = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: const Color(0xFFE11900),
              ),
            );
          }
        },
        builder: (context, state) {
          return Scaffold(
            backgroundColor: const Color(0xFFF6F3EC),
            appBar: AppBar(
              backgroundColor: const Color(0xFFF6F3EC),
              scrolledUnderElevation: 0,
              surfaceTintColor: Colors.transparent,
              elevation: 0,
              automaticallyImplyLeading: false,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Color(0xFF1F2122)),
                onPressed: () => Navigator.of(context).pop(),
              ),
              title: const Text(
                'Checkout',
                style: TextStyle(
                  color: Color(0xFF1F2122),
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Inter',
                  height: 1.24,
                ),
              ),
            ),
            body: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Delivery Details Card with loading state
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        )
                      ],
                    ),
                    padding: const EdgeInsets.all(16),
                    child: state is MealPlanProgressLoading
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: List.generate(
                                5,
                                (index) => Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8),
                                      child: _buildLoadingShimmer(),
                                    )),
                          )
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Delivery Details',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: 20,
                                  fontWeight: FontWeight.w600,
                                  height: 1.24,
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Address
                              const Text(
                                'Address',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  height: 1.14,
                                  letterSpacing: 0.28,
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                              const SizedBox(height: 15),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.location_on_outlined,
                                        size: 12.88,
                                        color: const Color(0xFF414346),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        currentAddress ?? 'No address',
                                        style: const TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          height: 1.43,
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => EditAddressPage(
                                            currentAddress: currentAddress,
                                            currentLatLng: currentAddressData
                                                ?.location?.coordinates,
                                          ),
                                        ),
                                      );
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      minimumSize: const Size(30, 20),
                                      tapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                    ),
                                    child: const Text(
                                      'Edit',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w600,
                                        fontSize: 12,
                                        height: 1.0,
                                        color: Color(0xFF1F2122),
                                        decoration: TextDecoration.underline,
                                        decorationThickness: 1.5,
                                        letterSpacing: 0.5,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),

                              // // Delivery Time
                              // const Text(
                              //   'Delivery',
                              //   style: TextStyle(
                              //     fontFamily: 'Inter',
                              //     fontSize: 14,
                              //     fontWeight: FontWeight.w500,
                              //     height: 1.14,
                              //     letterSpacing: 0.28,
                              //     color: Color(0xFF1F2122),
                              //   ),
                              // ),
                              // const SizedBox(height: 15),
                              // Row(
                              //   children: [
                              //     SizedBox(
                              //       child: Icon(
                              //         Icons.access_time_rounded,
                              //         size: 12,
                              //         color: Color(0xFF1F2122),
                              //       ),
                              //     ),
                              //     const SizedBox(width: 8),
                              //     const Text(
                              //       'Today, 8:30AM',
                              //       style: TextStyle(
                              //         fontFamily: 'Inter',
                              //         fontSize: 14,
                              //         fontWeight: FontWeight.w500,
                              //         height: 1.43,
                              //         color: Color(0xFF1F2122),
                              //       ),
                              //     ),
                              //   ],
                              // ),
                              // const SizedBox(height: 16),
                              Divider(
                                color: const Color(0xFFE1E3E6),
                                thickness: 1,
                              ),
                              const SizedBox(height: 16),

                              // Drop-Off Options
                              _buildDropoffOptionDropdown(),
                              const SizedBox(height: 16),

                              // Instructions
                              const Text(
                                'Drop-Off Instructions',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  height: 1.14,
                                  letterSpacing: 0.28,
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                              const SizedBox(height: 8),
                              TextField(
                                controller: _instructionsController,
                                onChanged: (value) {
                                  _dropOffInstructions = value;
                                },
                                textAlign: TextAlign.left,
                                decoration: InputDecoration(
                                  contentPadding: const EdgeInsets.all(12),
                                  hintText: 'Add drop-off instructions...',
                                  hintStyle: const TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w400,
                                    fontSize: 16,
                                    height: 1.5,
                                    color: Color(0xFF66696D),
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                        color: Color(0xFFE1E3E6)),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                        color: Color(0xFFE1E3E6)),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(
                                        color: Color(0xFF1F2122), width: 1.5),
                                  ),
                                ),
                                minLines: 5,
                                maxLines: 8,
                                cursorColor: const Color(0xFF1F2122),
                                style: const TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w400,
                                  fontSize: 16,
                                  height: 1.5,
                                  color: Color(0xFF66696D),
                                ),
                              ),
                              const SizedBox(height: 16),
                              Divider(
                                color: const Color(0xFFE1E3E6),
                                thickness: 1,
                              ),
                              const SizedBox(height: 16),

                              // Delivery Time Options
                              _buildDeliveryTimeOptions(),
                            ],
                          ),
                  ),
                  const SizedBox(height: 24),

                  // Payment Card with loading state
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    padding: const EdgeInsets.all(16),
                    child: state is MealPlanProgressLoading
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: List.generate(
                                4,
                                (index) => Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8),
                                      child: _buildLoadingShimmer(),
                                    )),
                          )
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Payment',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                  height: 1.24,
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Default Card
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        width: 30,
                                        height: 20,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: Image.asset(
                                          'assets/icons/Visa.png',
                                          fit: BoxFit.contain,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              const Text(
                                                'Card ending with 0001',
                                                style: TextStyle(
                                                  fontFamily: 'Inter',
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                  color: Color(0xFF1F2122),
                                                ),
                                              ),
                                              const SizedBox(width: 8),
                                              Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                  horizontal: 8,
                                                  vertical: 3,
                                                ),
                                                decoration: BoxDecoration(
                                                  color:
                                                      const Color(0xFFCEF8E0),
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                ),
                                                child: const Text(
                                                  'Default',
                                                  style: TextStyle(
                                                    fontFamily: 'Inter',
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.w500,
                                                    height:
                                                        1.0, // 12px line height
                                                    letterSpacing:
                                                        0.24, // 2% of 12px
                                                    color: Color(0xFF007A4D),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const Text(
                                            'Expires 03/2028',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                              height:
                                                  1.43, // 20px line height (20/14)
                                              color: Color(0xFF1F2122),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.more_vert,
                                        color: Color(0xFF1F2122)),
                                    onPressed: () {},
                                    constraints: const BoxConstraints(),
                                    padding: EdgeInsets.zero,
                                    iconSize: 20,
                                  ),
                                ],
                              ),
                              const Divider(
                                  height: 20, color: Color(0xFFE1E3E6)),

                              // Amount
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        width: 30,
                                        height: 20,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: Image.asset(
                                          'assets/icons/db.png',
                                          fit: BoxFit.contain,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      const Text(
                                        '\$ 200.00',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          height: 1.43, // 20px line height
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.more_vert,
                                        color: Color(0xFF1F2122)),
                                    onPressed: () {},
                                    constraints: const BoxConstraints(),
                                    padding: EdgeInsets.zero,
                                    iconSize: 20,
                                  ),
                                ],
                              ),
                              const Divider(
                                  height: 20, color: Color(0xFFE1E3E6)),

                              // Secondary Card
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        width: 30,
                                        height: 20,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: Image.asset(
                                          'assets/icons/Visa.png',
                                          fit: BoxFit.contain,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const Text(
                                            'Card ending with 0001',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                              height:
                                                  1.43, // 20px line height (20/14)
                                              color: Color(0xFF1F2122),
                                            ),
                                          ),
                                          const Text(
                                            'Expires 03/2028',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                              height:
                                                  1.43, // 20px line height (20/14)
                                              color: Color(0xFF1F2122),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.more_vert,
                                        color: Color(0xFF1F2122)),
                                    onPressed: () {},
                                    constraints: const BoxConstraints(),
                                    padding: EdgeInsets.zero,
                                    iconSize: 20,
                                  ),
                                ],
                              ),
                              const Divider(
                                  height: 20, color: Color(0xFFE1E3E6)),
                              const SizedBox(height: 12),

                              // Add Payment Method
                              TextButton.icon(
                                onPressed: () {},
                                icon: const Icon(
                                  Icons.add,
                                  size: 20,
                                  color: Color(0xFF1F2122),
                                ),
                                label: const Text(
                                  'Add Payment Method',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    color: Color(0xFF414346),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    height: 1.0, // 16px line height
                                    letterSpacing: 0.32, // 2% of 16px
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                                style: TextButton.styleFrom(
                                  padding: EdgeInsets.zero,
                                  minimumSize: const Size(30, 20),
                                  tapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                  alignment: Alignment.centerLeft,
                                ),
                              ),
                            ],
                          ),
                  ),
                  const SizedBox(height: 24),

                  // Order Summary Card with loading state
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    padding: const EdgeInsets.all(16),
                    child: state is MealPlanProgressLoading
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: List.generate(
                                6,
                                (index) => Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8),
                                      child: _buildLoadingShimmer(),
                                    )),
                          )
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Order Summary',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: 20,
                                  fontWeight: FontWeight.w600,
                                  height: 1.24,
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Delivery Time Slot
                              Row(
                                children: List.generate(50, (index) {
                                  return Container(
                                    width: 2.5,
                                    height: 1,
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 2),
                                    color: const Color(0xFFE1E3E6),
                                  );
                                }),
                              ),
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text(
                                    'Delivery Time Slot',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xFF1F2122),
                                    ),
                                  ),
                                  Row(
                                    children: [
                                      const Icon(
                                        Icons.access_time_rounded,
                                        size: 16,
                                        color: Color(0xFF1F2122),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '${_formatTime(mealPlanData?.timeSlot?.startTime)} - ${_formatTime(mealPlanData?.timeSlot?.endTime)}',
                                        style: const TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: List.generate(50, (index) {
                                  return Container(
                                    width: 2.5,
                                    height: 1,
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 2),
                                    color: const Color(0xFFE1E3E6),
                                  );
                                }),
                              ),
                              const SizedBox(height: 16),

                              // Order Details
                              Text(
                                'Order Details (${mealPlanData?.mealPlanDays?.length ?? 0} Days)',
                                style: const TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  height: 1.0, // 16px line height
                                  letterSpacing: 0.32, // 2% of 16px
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Daily Orders
                              ...?mealPlanData?.mealPlanDays
                                  ?.map((day) => _buildDailyOrderItem(
                                        date: '${day.date}, ${day.dayOfWeek}',
                                        chef:
                                            '${day.chef?.firstName ?? ''} ${day.chef?.lastName ?? ''}',
                                        price: double.tryParse(
                                                day.dayTotal ?? '') ??
                                            9.00, // Replace 9.00 fallback with actual logic if needed
                                        chefImage: day.chef?.profilePhoto ?? '',
                                      ))
                                  .toList(),

                              const SizedBox(height: 16),
                              Row(
                                children: List.generate(50, (index) {
                                  return Container(
                                    width: 2.5,
                                    height: 1,
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 2),
                                    color: const Color(0xFFE1E3E6),
                                  );
                                }),
                              ),
                              const SizedBox(height: 16),

                              // Promo Code
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: const [
                                      Icon(
                                        Icons.percent,
                                        size: 16,
                                        color: Color(0xFF1F2122),
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        'Add promo code',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  TextButton(
                                    onPressed: () {},
                                    style: TextButton.styleFrom(
                                      minimumSize: Size.zero,
                                      padding: EdgeInsets.zero,
                                      tapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                    ),
                                    child: Row(
                                      children: [
                                        const Icon(
                                          Icons.add,
                                          size: 15,
                                          color: Color(0xFF1F2122),
                                        ),
                                        const SizedBox(width: 4),
                                        Column(
                                          children: [
                                            const Text(
                                              'Add',
                                              style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontWeight: FontWeight.w600,
                                                fontSize: 12,
                                                height: 1.0, // 12px line height
                                                letterSpacing:
                                                    0.24, // 2% of 12px
                                                color: Color(0xFF414346),
                                              ),
                                            ),
                                            const SizedBox(height: 1),
                                            Container(
                                              height: 1,
                                              width:
                                                  21, // Width matching "Add" text
                                              color: Color(0xFF1F2122),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),

                              Row(
                                children: [
                                  Container(
                                    width: 24,
                                    height: 17,
                                    decoration: BoxDecoration(
                                      color: const Color(0xFF1F2122),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    alignment: Alignment.center,
                                    child: const Text(
                                      'DB',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  CustomToggle(
                                    value: false,
                                    onChanged: (bool newValue) {
                                      // Handle toggle
                                    },
                                  ),
                                ],
                              ),

                              const SizedBox(height: 16),
                              Row(
                                children: List.generate(50, (index) {
                                  return Container(
                                    width: 2.5,
                                    height: 1,
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 2),
                                    color: const Color(0xFFE1E3E6),
                                  );
                                }),
                              ),
                              const SizedBox(height: 16),

                              // Order Total Section
                              Column(
                                children: [
                                  // Order Total Header
                                  const Row(
                                    children: [
                                      Text(
                                        'Order Total',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),

                                  // Subtotal
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        'Subtotal',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          height:
                                              1.43, // 20px line height (20/14)
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                      Text(
                                        '\$${mealPlanData?.subtotal ?? '_'}',
                                        style: const TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 12,
                                          fontWeight: FontWeight.w400,
                                          height:
                                              1.33, // 16px line height (16/12)
                                          color: Color(0xFF414346),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),

                                  // Delivery fee
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        'Delivery fee',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          height:
                                              1.43, // 20px line height (20/14)
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                      Text(
                                        '\$${mealPlanData?.deliveryFee ?? '_'}',
                                        style: const TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),

                                  // Discounts
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        'Discounts',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          height:
                                              1.43, // 20px line height (20/14)
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                      Text(
                                        '-\$${mealPlanData?.discount ?? '_'}',
                                        style: const TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xFFD31510),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),

                                  // DB Wallet Credits
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        'DB Wallet Credits',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          height:
                                              1.43, // 20px line height (20/14)
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                      Text(
                                        '-\$${mealPlanData?.walletCredits ?? '_'}',
                                        style: const TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xFFD31510),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),

                                  // Taxes & Fees
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        'Taxes & Fees',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          height:
                                              1.43, // 20px line height (20/14)
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                      Text(
                                        '\$${mealPlanData?.taxesAndFees ?? '_'}',
                                        style: const TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),

                                  // Total Row with thicker divider above
                                  const Divider(
                                      height: 1,
                                      thickness: 1,
                                      color: Color(0xFFE1E3E6)),
                                  const SizedBox(height: 16),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        'Total',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          height:
                                              1.5, // 24px line height (24/16)
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                      Text(
                                        '\$${mealPlanData?.total ?? '_'}',
                                        style: const TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          height:
                                              1.5, // 24px line height (24/16)
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 20),

                                  // Place Order Button
                                  _buildPlaceOrderButton(),
                                  const SizedBox(height: 18),

                                  // Disclaimer Text
                                  const Text(
                                    "By clicking the 'Place Order' button, a one-time payment for the 5-day meal plan will be charged.",
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      height: 1.43, // 20px line height (20/14)
                                      color: Color(0xFF66696D),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 25),
                            ],
                          ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // Add the helper method for daily order items
  Widget _buildDailyOrderItem({
    required String date,
    required String chef,
    required double price,
    String? chefImage,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          Text(
            date,
            style: const TextStyle(
              fontFamily: 'Inter',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1F2122),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: const Color(0xFFE1DDD5),
                    ),
                    child: ClipOval(
                      child: chefImage != null && chefImage.isNotEmpty
                          ? FadeInImage.assetNetwork(
                              placeholder: 'assets/images/chef_placeholder.png',
                              image: '${ServerHelper.imageUrl}$chefImage',
                              fit: BoxFit.cover,
                              imageErrorBuilder: (context, error, stackTrace) {
                                return Image.asset(
                                  'assets/images/chef_placeholder.png',
                                  fit: BoxFit.cover,
                                );
                              },
                            )
                          : Image.asset(
                              'assets/images/chef_placeholder.png',
                              fit: BoxFit.cover,
                            ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFE1E3E6),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      '6×',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        height: 16 / 14, // Line height of 16px
                        letterSpacing: 0.28, // 2% of 14px
                        color: Color(0xFF1F2122),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Chef $chef',
                      style: const TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Padding(
                      padding: const EdgeInsets.only(left: 9.0),
                      child: const Text(
                        '2 servings, 3 dishes',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF1F2122),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.only(left: 9.0),
                      child: TextButton(
                        onPressed: () {},
                        style: TextButton.styleFrom(
                          minimumSize: Size.zero,
                          padding: EdgeInsets.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: const Text(
                          'Edit',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            height: 1.0, // 12px line height
                            letterSpacing: 0.24, // 2% of 12px
                            color: Color(0xFF414346),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '\$${price.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF1F2122),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
