import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/data/models/cart/listcartmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/cart/cart-subitem.dart';
import 'package:db_eats/ui/chef/popular_chefs_near.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CartPage extends StatefulWidget {
  const CartPage({super.key});

  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> with WidgetsBindingObserver {
  bool isEditing = false;
  List<CartItem> cartItems = [];
  Map<int, bool> selectedItems = {};
  bool selectAll = false;
  bool _isRemoving = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    context.read<AccountBloc>().add(ListCartEvent());
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Refresh cart when app comes back to foreground
      context.read<AccountBloc>().add(ListCartEvent());
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return BlocListener<AccountBloc, AccountState>(
      listener: (context, state) {
        if (state is ListCartSuccess) {
          setState(() {
            cartItems = _convertToCartItems(state.data);
            _isRemoving = false;
          });
        } else if (state is RemoveFromCartLoading) {
          setState(() => _isRemoving = true);
        } else if (state is RemoveFromCartSuccess) {
          context.read<AccountBloc>().add(ListCartEvent());
          setState(() {
            selectedItems.clear();
            selectAll = false;
            isEditing = false;
            _isRemoving = false;
          });
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: Colors.black,
              size: screenWidth * 0.06,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: SafeArea(
          child: BlocBuilder<AccountBloc, AccountState>(
            builder: (context, state) {
              if (state is ListCartLoading) {
                return const Center(
                  child: CupertinoActivityIndicator(
                    radius: 10.0,
                  ),
                );
              }
              if (state is ListCartSuccess && (state.data?.isEmpty ?? true)) {
                return _buildEmptyState(context, screenWidth, screenHeight);
              }
              return _buildFilledState(context, screenWidth, screenHeight);
            },
          ),
        ),
        bottomNavigationBar: cartItems.isEmpty || !isEditing
            ? null
            : _buildRemoveButton(context, screenWidth, screenHeight),
      ),
    );
  }

  List<CartItem> _convertToCartItems(List<Data>? cartData) {
    if (cartData == null) return [];
    return cartData
        .map((data) => CartItem(
              chefImage: ServerHelper.imageUrl + (data.chefPhoto ?? ''),
              chefName: data.chefName ?? '',
              timeRange: _formatTimeRange(
                data.chefOperationTime?.startTime,
                data.chefOperationTime?.endTime,
              ),
              days: _formatDays(data.chefOperationDays),
              itemCount: data.itemsCount ?? 0,
              status: 'Preparing',
              chefId: data.chefId,
            ))
        .toList();
  }

  String _formatTimeRange(String? startTime, String? endTime) {
    if (startTime == null || endTime == null) return 'N/A';
    String formatTime(String time) {
      try {
        final parts = time.split(':');
        int hours = int.parse(parts[0]);
        final period = hours >= 12 ? 'PM' : 'AM';
        hours = hours > 12 ? hours - 12 : hours;
        hours = hours == 0 ? 12 : hours;
        return '$hours$period';
      } catch (e) {
        return 'N/A';
      }
    }

    return 'Open ${formatTime(startTime)}-${formatTime(endTime)}';
  }

  String _formatDays(List<String>? days) {
    if (days == null || days.isEmpty) return 'N/A';
    return days.map((day) => day[0]).join(', ');
  }

  bool get isEmpty => cartItems.isEmpty;

  int get selectedCount =>
      selectedItems.values.where((selected) => selected).length;

  void toggleEditMode() {
    setState(() {
      isEditing = !isEditing;
      selectedItems.clear();
      selectAll = false;
    });
  }

  void toggleSelectAll() {
    setState(() {
      selectAll = !selectAll;
      for (int i = 0; i < cartItems.length; i++) {
        selectedItems[i] = selectAll;
      }
    });
  }

  Widget _buildFilledState(
      BuildContext context, double screenWidth, double screenHeight) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.03),
      child: Column(
        children: [
          _buildHeader(context, screenWidth, screenHeight),
          if (isEditing) _buildSelectAllRow(context, screenWidth, screenHeight),
          SizedBox(height: isEditing ? 6 : screenHeight * 0.02),
          Expanded(
            child: ListView.builder(
              itemCount: cartItems.length,
              itemBuilder: (context, index) {
                return _buildCartItem(
                    index, context, screenWidth, screenHeight);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(
      BuildContext context, double screenWidth, double screenHeight) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Your cart',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w600,
            fontSize: screenWidth > 600 ? ten*3.2 : screenWidth * 0.0514 // ≈ 18

            ,
          ),
        ),
        if (!isEmpty)
          GestureDetector(
            onTap: toggleEditMode,
            child: Row(
              children: [
                if (!isEditing)
                  Image.asset(
                    'assets/icons/edit_2.png',
                    width: screenWidth > 600 ? twenty : screenWidth * 0.04,
                    height: screenWidth > 600 ? twenty : screenWidth * 0.04,
                  ),
                SizedBox(width: screenWidth * 0.01),
                IntrinsicWidth(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isEditing ? 'Cancel' : 'Manage cart',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w600,
                          fontSize: screenWidth > 600 ? eighteen : screenWidth * 0.04,
                        ),
                      ),
                      Transform.translate(
                        offset: Offset(0, -2),
                        child: Container(
                          height: 1,
                          width: double.infinity,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildSelectAllRow(
      BuildContext context, double screenWidth, double screenHeight) {
    return Padding(
      padding: EdgeInsets.only(top: screenHeight * 0.01),
      child: Row(
        children: [
          Checkbox(
            value: selectAll,
            onChanged: (value) => toggleSelectAll(),
            activeColor: Colors.black,
          ),
          Text(
            'Select all',
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              fontSize: screenWidth > 600 ? eighteen : screenWidth * 0.035,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCartItem(int index, BuildContext context, double screenWidth,
      double screenHeight) {
    final item = cartItems[index];
    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: screenHeight * 0.015),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              if (isEditing)
                Checkbox(
                  value: selectedItems[index] ?? false,
                  onChanged: (value) {
                    setState(() {
                      selectedItems[index] = value ?? false;
                      if (value == true) {
                        bool allSelected = true;
                        for (int i = 0; i < cartItems.length; i++) {
                          if (!(selectedItems[i] ?? false)) {
                            allSelected = false;
                            break;
                          }
                        }
                        selectAll = allSelected;
                      } else {
                        selectAll = false;
                      }
                    });
                  },
                  activeColor: Colors.black,
                ),
              Expanded(
                flex: 1,
                child: CircleAvatar(
                  radius: screenWidth > 600 ? ten*3 : screenWidth * 0.06,
                  backgroundImage: NetworkImage(item.chefImage),
                  backgroundColor: Colors.transparent,
                  onBackgroundImageError: (exception, stackTrace) {},
                ),
              ),
              Expanded(
                flex: 4,
                child: Padding(
                  padding: EdgeInsets.only(left: screenWidth * 0.025),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.chefName,
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w600,
                          fontSize:
                              screenWidth > 600 ? twenty : screenWidth * 0.045,
                        ),
                      ),
                      SizedBox(height: screenHeight * 0.007),
                      Row(
                        children: [
                          Image.asset(
                            'assets/icons/calender_3.png',
                            width: screenWidth > 600 ? forteen : screenWidth * 0.03,
                            height: screenWidth > 600 ? forteen : screenWidth * 0.03,
                          ),
                          SizedBox(width: screenWidth * 0.01),
                          Text(
                            item.timeRange,
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w400,
                              fontSize:
                                  screenWidth > 600 ? twelve : screenWidth * 0.025,
                              color: const Color(0xFF414346),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: screenHeight * 0.007),
                      Text(
                        item.days,
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          fontSize:
                              screenWidth > 600 ? twelve : screenWidth * 0.025,
                          color: const Color(0xFF414346),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (!isEditing)
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => CartPage2(
                                chef_id: cartItems[index].chefId ?? 0,
                              ),
                            ),
                          ).then((result) {
                            // Always refresh cart when returning from CartPage2
                            context.read<AccountBloc>().add(ListCartEvent());
                          });
                        },
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IntrinsicWidth(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${item.itemCount} items',
                                    style: TextStyle(
                                      color: Color(0xff1F2122),
                                      fontWeight: FontWeight.w400,
                                      fontSize: twelve,
                                    ),
                                  ),
                                  Transform.translate(
                                    offset: Offset(
                                        0, -2), // Move underline up by 2 pixels
                                    child: Container(
                                      height: 1,
                                      width: double
                                          .infinity, // underline matches text width
                                      color: Color(0xff1F2122),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Text(
                            //   '${item.itemCount} items',
                            //   style: TextStyle(
                            //     fontFamily: 'Inter',
                            //     fontWeight: FontWeight.w600,
                            //     fontSize:
                            //         screenWidth > 600 ? 14 : screenWidth * 0.03,
                            //     letterSpacing: 0.3,
                            //     decoration: TextDecoration.underline,
                            //   ),
                            // ),
                            SizedBox(width: screenWidth * 0.01),
                            Image.asset(
                              'assets/icons/right_arrow_blk.png',
                              width:
                                  screenWidth > 600 ? sixteen : screenWidth * 0.035,
                              height:
                                  screenWidth > 600 ? sixteen : screenWidth * 0.035,
                            ),
                          ],
                        ),
                      )
                    else
                      Text(
                        '${item.itemCount} items',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w600,
                          fontSize: screenWidth > 600 ? forteen : screenWidth * 0.03,
                        ),
                      ),
                    // if (!isEditing) SizedBox(height: screenHeight * 0.03),
                    // if (!isEditing)
                    //   Container(
                    //     padding: EdgeInsets.symmetric(
                    //       horizontal: screenWidth * 0.015,
                    //       vertical: 0,
                    //     ),
                    //     decoration: BoxDecoration(
                    //       color: const Color(0xFFE1E3E6),
                    //       borderRadius: BorderRadius.circular(20),
                    //     ),
                    //     child: Text(
                    //       item.status,
                    //       style: TextStyle(
                    //         fontFamily: 'Inter',
                    //         fontWeight: FontWeight.w500,
                    //         fontSize:
                    //             screenWidth > 600 ? 14 : screenWidth * 0.03,
                    //         color: const Color(0xFF1F2122),
                    //       ),
                    //     ),
                    //   ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(vertical: screenHeight * 0.002),
          child: const Divider(
            height: 1,
            thickness: 1,
            color: Color(0xFFE1E3E6),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(
      BuildContext context, double screenWidth, double screenHeight) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.075),
      child: Column(
        children: [
          _buildHeader(context, screenWidth, screenHeight),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/icons/cart.png',
                  width: screenWidth > 600 ? ten*8 : screenWidth * 0.15,
                  height: screenWidth > 600 ? ten*8 : screenWidth * 0.15,
                ),
                SizedBox(height: screenHeight * 0.02),
                Text(
                  'Your cart is empty',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                    fontSize: screenWidth > 600
                        ? twenty
                        : screenWidth * 0.0457 // clean and gives ≈ 16
                    ,
                  ),
                ),
                SizedBox(height: screenHeight * 0.01),
                Text(
                  'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi non dictum felis. Vestibulum nec eros velit.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: screenWidth > 600 ? 16 : screenWidth * 0.035,
                    letterSpacing: -0.2,
                    height: 1.35,
                    color: const Color(0xFF414346),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: screenHeight * 0.04),
          _buildBrowseMenuButton(context, screenWidth, screenHeight),
          SizedBox(height: screenHeight * 0.06),
        ],
      ),
    );
  }

  Widget _buildBrowseMenuButton(
      BuildContext context, double screenWidth, double screenHeight) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PopularChefsNearPage(),
          ),
        ).then((_) {
          context.read<AccountBloc>().add(ListCartEvent());
        });
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: screenHeight * 0.017),
        decoration: BoxDecoration(
          color: Colors.black,
          border: Border.all(color: const Color(0xFFAAADB1)),
          borderRadius: BorderRadius.circular(ten*3),
        ),
        child: Text(
          'Browse Menu',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white,
            fontSize: screenWidth > 600 ? eighteen : screenWidth * 0.04,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }

  Widget _buildRemoveButton(
      BuildContext context, double screenWidth, double screenHeight) {
    return Padding(
      padding: EdgeInsets.fromLTRB(
        screenWidth * 0.05,
        screenHeight * 0.025,
        screenWidth * 0.05,
        screenHeight * 0.06,
      ),
      child: GestureDetector(
        onTap: _isRemoving
            ? null
            : () {
                if (selectedCount > 0) {
                  final List<int> selectedChefIds = [];
                  selectedItems.forEach((index, isSelected) {
                    if (isSelected && index < cartItems.length) {
                      final chefId = cartItems[index].chefId;
                      if (chefId != null) {
                        selectedChefIds.add(chefId);
                      }
                    }
                  });
                  if (selectedChefIds.isNotEmpty) {
                    context.read<AccountBloc>().add(
                          RemoveFromCartEvent(selectedChefIds),
                        );
                  }
                }
              },
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: screenHeight * 0.017),
          decoration: BoxDecoration(
            color: Colors.black,
            border: Border.all(color: const Color(0xFFAAADB1)),
            borderRadius: BorderRadius.circular(30),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (_isRemoving)
                SizedBox(
                  width: screenWidth * 0.05,
                  height: screenWidth * 0.05,
                  child:  CupertinoActivityIndicator(
                    radius: ten,
                  ),
                )
              else
                Text(
                  'Remove ($selectedCount)',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: screenWidth > 600 ? eighteen : screenWidth * 0.04,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class CartItem {
  final String chefImage;
  final String chefName;
  final String timeRange;
  final String days;
  final int itemCount;
  final String status;
  final int? chefId;

  CartItem({
    required this.chefImage,
    required this.chefName,
    required this.timeRange,
    required this.days,
    required this.itemCount,
    required this.status,
    this.chefId,
  });
}
