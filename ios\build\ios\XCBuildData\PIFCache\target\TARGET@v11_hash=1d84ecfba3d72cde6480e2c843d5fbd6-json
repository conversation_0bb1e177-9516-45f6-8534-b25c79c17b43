{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f3dbbd8724e25b43c147940f9cf2d791", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ebd2729e17cf60ab02692c28edf7402d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ebd2729e17cf60ab02692c28edf7402d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9853065275f61d2817fbc16ba293d553cf", "guid": "bfdfe7dc352907fc980b868725387e98e37370358cdd8bd852c6ecd29d1c6699", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862a76bd43891be7e8a064ec7a7c84cf9", "guid": "bfdfe7dc352907fc980b868725387e98c65ba688da72266cc96899ded9209799", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98078c182e575d828137aebea826ba5c5b", "guid": "bfdfe7dc352907fc980b868725387e980b056ed7b6b99f8533fec1d37701b716", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c286669f8be17fa3eebe3561ce5033a9", "guid": "bfdfe7dc352907fc980b868725387e9815ab9e1123f6342cc131b55219cae95e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fdce00f61efd2f7d933093a9aa9ebf0", "guid": "bfdfe7dc352907fc980b868725387e98728fa087c1df983a5776c90c2ad32f0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981387f049504922a4aaafb217bf85be28", "guid": "bfdfe7dc352907fc980b868725387e9839a0717184882b751f041be102c19634", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f4112dae5194f06bad2f03e2b5d95ad", "guid": "bfdfe7dc352907fc980b868725387e9890e24b099a4a172ddf354c0bc757e22b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ba133f04e31cfda7c7df77f9709100f", "guid": "bfdfe7dc352907fc980b868725387e98ff295f0cbbbfff612a1e6f0e7954818d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890207cab316a5de994377e1b0fa3690c", "guid": "bfdfe7dc352907fc980b868725387e98072a14fe78636dcbdda404cfbb314ea4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a9a6166b067b64c74765ff8e1911c6b", "guid": "bfdfe7dc352907fc980b868725387e98a482eaf54f776bc628053d383c33a7a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2d227d906c8460ba9865e1fed0aac79", "guid": "bfdfe7dc352907fc980b868725387e98a9f7fdd85aca408013c7e62caebfdfa4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d4e4c0b03d51d3bd0ba4b3ca72d1e96", "guid": "bfdfe7dc352907fc980b868725387e9814deb33597fcd96bb77bf5ee3918a74b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a24e0e5e609a97b18c2352b87c59428", "guid": "bfdfe7dc352907fc980b868725387e98cb363a67bc779b208d6c566751fa38c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b29cc40dfbc6f34c25858f0fd44735b0", "guid": "bfdfe7dc352907fc980b868725387e98716042837ddd330b76e4d1536dc5312e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f0944b2830704473f912d7b6ce12ddd", "guid": "bfdfe7dc352907fc980b868725387e982e54e546bc4a81e9db10cc68946fbe94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98711d521792cbd5d878f451ecc25a60f6", "guid": "bfdfe7dc352907fc980b868725387e98c20b87d9fb4d46d9a2b74b6dbe0cac0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e8c3f53b88895573f7cdbd64677825d", "guid": "bfdfe7dc352907fc980b868725387e98ff08a61b058b5ec2e421ebefc03205cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb203e70e61c623846a65dd3fd0aa699", "guid": "bfdfe7dc352907fc980b868725387e98243dc5e59d20b619b46287f6653881ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c796cfc6b49e7d3e6f2c050227591c8", "guid": "bfdfe7dc352907fc980b868725387e98b7419b4b5b3e5c0c3fdd93fb14972121", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffecc35f33ad2f13fe5a39683cef2f24", "guid": "bfdfe7dc352907fc980b868725387e9851d632d6a2d05235d081558e2f536733", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d7c89e3db3cc132a6537943ca2931af2", "guid": "bfdfe7dc352907fc980b868725387e984d34f4c346c4f1da8e7fc2e72b19d4f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8427d5a8b76bfa37d75801fd1e829da", "guid": "bfdfe7dc352907fc980b868725387e9896fae0012b96ffd3543779cd02b0050b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d1c9577479b8b05287db2b015e97f69", "guid": "bfdfe7dc352907fc980b868725387e988cb4dcd87b2f0b7364b4d4163de5a067"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eea77e92967e9ccf4a23675761f90b26", "guid": "bfdfe7dc352907fc980b868725387e98f3dcb2fcce36b41792f1f08053ca2c08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98452d58af83f035a2b71df03627bc0a42", "guid": "bfdfe7dc352907fc980b868725387e98e8c897a93b6a47264dd51ac9e2c6a04d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f273fb678e88275edf11cf1909d437b", "guid": "bfdfe7dc352907fc980b868725387e985f1e7ae11f39dd25ad8b1164f365222e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f88f4b28a4c335080b981e0fd7340446", "guid": "bfdfe7dc352907fc980b868725387e98a11730584e196fde0a5136c7d4362936"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981af57da64e63064048fb99ecc99f1307", "guid": "bfdfe7dc352907fc980b868725387e9881c667a42d9ccb55da19e7d57ed6785c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885779bda91211a515c48bae4c93547ae", "guid": "bfdfe7dc352907fc980b868725387e9849d41e022dfad9f58c9ba2e8695065dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98384cb65c54a3a964ac191b5b372f68ed", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cec329581167926267ec404e821963d5", "guid": "bfdfe7dc352907fc980b868725387e98048d6aa51e941f690dd97c41690fe291"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98209222f2b079a5f078ce8d3501ec119e", "guid": "bfdfe7dc352907fc980b868725387e9889e5900cd748b720870db624f23b8de5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816dfe4dc4d3eefa50ad3c8e48c64839d", "guid": "bfdfe7dc352907fc980b868725387e9825f7bcc81a598ac43b43d6acd38fb779"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847a3e0acf63a834d59814086a0658e35", "guid": "bfdfe7dc352907fc980b868725387e98bc4c66aa1954c19129acd8de26620a19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b20aa54650251c2a905100c530ee1b7", "guid": "bfdfe7dc352907fc980b868725387e9805765735996844fda4b63fcc155f1c21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985842f1f97dfa785d9e19127ad36d576b", "guid": "bfdfe7dc352907fc980b868725387e9832bea6933864bce1eeb84097f0ba450c"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}