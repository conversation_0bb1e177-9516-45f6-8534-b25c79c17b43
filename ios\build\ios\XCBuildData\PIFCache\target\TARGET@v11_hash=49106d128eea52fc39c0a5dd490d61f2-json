{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d53086f9bc9e27c4384487d48ecaa65", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9878a2b45094a9bcee1fca1f10c42d38b8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eaec5e5c8dd01159403a6cd3b0a1dd0d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989516ba823a6c3adc8b8d32ab13d756bd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eaec5e5c8dd01159403a6cd3b0a1dd0d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9898fe1b1c0e2ae04e441ee7dabe73feb0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98985a6b8243172593c9569b494428b6b5", "guid": "bfdfe7dc352907fc980b868725387e9883d9caa8b848d8410d53fb1012d6948b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e19605d643d1d2a03b7414aeeb424e0d", "guid": "bfdfe7dc352907fc980b868725387e98257b61017082f33794744095759483b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2b2c71ca617955b2d9017cc27423b37", "guid": "bfdfe7dc352907fc980b868725387e987cc5c07a8c87df817dbe472aac598897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df885793686fc1075301decb2b396b1a", "guid": "bfdfe7dc352907fc980b868725387e98238089a0102521eda54ed05797106615", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed1bcffec308bbe782711fceae31c475", "guid": "bfdfe7dc352907fc980b868725387e9867f601e9b3818858eb9dbbac7153fc72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f48a175c1ee811cbeaf0ca0e42276912", "guid": "bfdfe7dc352907fc980b868725387e98ff8021defd32e79b08b171f7b7bb4bc2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c563db6b1d2cb2caae13be72fbc50e1d", "guid": "bfdfe7dc352907fc980b868725387e9886c8e39fdf62eec4e87c3a8145683e7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eb7750d3b02f3a227e3b293e6f3f589", "guid": "bfdfe7dc352907fc980b868725387e98c892bd90d3dffc0136935708eac7b592", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f0a057d7b7c405ae60d0afd92ff309e", "guid": "bfdfe7dc352907fc980b868725387e98aff9f9b1a383b70173a12392a074b9a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ef9baea0f560c4fc61b658d3f8d02e5", "guid": "bfdfe7dc352907fc980b868725387e982b2abe91cd02e0ef78d633ef94e85463", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5092118f4bef7838d466583334ee244", "guid": "bfdfe7dc352907fc980b868725387e9890228db2e8e53b0cfa0e79c778159bb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea0dfa6e9dbd7228563290440d6c48f6", "guid": "bfdfe7dc352907fc980b868725387e9808aba3607bed1bda717f2c91e4fd76ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c029d754ba2c18ec8a93b096e8bb0e4c", "guid": "bfdfe7dc352907fc980b868725387e98a403c875266496d53b68175dc296a97b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982513dccffdffd006eac607f5b0826608", "guid": "bfdfe7dc352907fc980b868725387e986159f3971e2cdefae860a312f8cf4360", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf19a29c94a71aea41cc8c1d3d4a720b", "guid": "bfdfe7dc352907fc980b868725387e982642b021442a0749d4e04f55cea3fbfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832eecbe7c503961daca0c03f7a51bc53", "guid": "bfdfe7dc352907fc980b868725387e98c030787a8393c4521e012348bd32abbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983763c4d2768e47fdc8a18d75f2f10e66", "guid": "bfdfe7dc352907fc980b868725387e9808247e1c38ff20f6fd290a00beeb9a46", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a2a0e77afc34e248ae3ff8b708f44ba", "guid": "bfdfe7dc352907fc980b868725387e98620767eec97a79bf1fb5d7125c64f82e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848e34c4fd9bbcce878e41eaad195db07", "guid": "bfdfe7dc352907fc980b868725387e98831b5ea161868bbf6e69c06a59eb481d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837754b17a3f149c7bf5de6e77ff56b40", "guid": "bfdfe7dc352907fc980b868725387e98ea0d7cb48077b5c6948f5c5c9e595a23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e5c223c5bcb1bb57d73b3941bd50149", "guid": "bfdfe7dc352907fc980b868725387e98c487c3de02b08f2efd77d820eddf1ae8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e992c24651a1dec2f4692b1fced7a9f1", "guid": "bfdfe7dc352907fc980b868725387e98637d7a6c58b8a973c83119b3dcccdc27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcf636d7209eb8aecef961c5e965a177", "guid": "bfdfe7dc352907fc980b868725387e9853c0e12f1accd1180603b0bf3236ea06", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb58fd8e476026f7e68552c853dd901f", "guid": "bfdfe7dc352907fc980b868725387e9882b2c93a6eefdfbe074af0b01a4163ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a699d1401023d3754071c4cc285fbdb5", "guid": "bfdfe7dc352907fc980b868725387e98fdadd39dc6a4b5ff9254a5665c099f78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0323f2ab40209fe14672b13f8c8e9c9", "guid": "bfdfe7dc352907fc980b868725387e981e4db11d4c01f2cb2fbfb7985121b130"}], "guid": "bfdfe7dc352907fc980b868725387e983efaab90da4af011367db7ab693bf123", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9858f75aadaa8e62a709333d81fd43ced6", "guid": "bfdfe7dc352907fc980b868725387e98cb3aded5ca0d522a3a131678a4d2d37c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982074b2166667d192f0fc1e8bd66da50c", "guid": "bfdfe7dc352907fc980b868725387e98934121f1ae3b2a88ebe93a6f4d69385d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98583c82365a0a48c9cae385a0a11050c8", "guid": "bfdfe7dc352907fc980b868725387e98f62bdb7e18a24253b65070dfbfb1ba85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887af7868c042a365c229c972b8844713", "guid": "bfdfe7dc352907fc980b868725387e9881f3ec9db51b527d8447a02a77693edc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817cef0bc42178adc6e8011b5d12bd422", "guid": "bfdfe7dc352907fc980b868725387e98f90e645b6d71917b847c7123d2df79da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800819c8faaa9d789cff471d3494b87ad", "guid": "bfdfe7dc352907fc980b868725387e98aa245282544fa1069d2034847908a595"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985365d183a43975c08587f5a372af8462", "guid": "bfdfe7dc352907fc980b868725387e98cf051a85f1ca80753936b721d1da0cac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af039e21294b36f99587e1232a1bcfd5", "guid": "bfdfe7dc352907fc980b868725387e986e1696fbe5a056e381f928cd16d6c8c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825f5f481790d13255815592fcace18c6", "guid": "bfdfe7dc352907fc980b868725387e98b21284244a8a29a1d2f5fbabf2d46113"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98211c3fd3a34fb6cbfdde8deda599db44", "guid": "bfdfe7dc352907fc980b868725387e98231d213bf18287ae79c8ae4c25f2c8a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c64591d5d7750dd174ee6658a3e680a", "guid": "bfdfe7dc352907fc980b868725387e98d51d9dea1ddc868074c57b9ca65552ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8ebfef32e154a3c2c8983724ac9f00c", "guid": "bfdfe7dc352907fc980b868725387e982adabc54fd8fc28ce17d829d1256d194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a00eeb05dd72445f824c2f5ca732d1c", "guid": "bfdfe7dc352907fc980b868725387e989866894f02d7b85bb154c73e013e75b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa3a89b9937327c2a83136e0a185f480", "guid": "bfdfe7dc352907fc980b868725387e98c1725018006a0ff86ec029c67f6e09b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de3a1dfbb72c3a862d774fd63e86b35f", "guid": "bfdfe7dc352907fc980b868725387e98ea78056a82ef0827d87cd86d4d167581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5df668a9c3daa0a13f08377cf53d95f", "guid": "bfdfe7dc352907fc980b868725387e98ddf847eff2c351dd9cb8faef784c7eb1"}], "guid": "bfdfe7dc352907fc980b868725387e98f41018b609797ef85009c063ae8246c1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e984b91b94502aab010fd621086091ce1d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e98924122dc4bc2a1c698f2e6524518fb32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e28c510bcdc3834514b09ab79a067e08", "guid": "bfdfe7dc352907fc980b868725387e9867dc93310eb1306370c9df56794e0c01"}], "guid": "bfdfe7dc352907fc980b868725387e98d5cd42bce17bd72e75a1d9cf4852ad53", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987ead6fd73e84deeaf2509fcf41220113", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98b2c42fec9c8290c757c1f7198eab4362", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}