{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a928405a57ace130017afee8112bf6e6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98974067e61b648fd9784b765b4d14987b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98974067e61b648fd9784b765b4d14987b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982e8ad9af91d4c7ba6662426c88910275", "guid": "bfdfe7dc352907fc980b868725387e98e37370358cdd8bd852c6ecd29d1c6699", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985403adfaff1cba855dde906d3af3a927", "guid": "bfdfe7dc352907fc980b868725387e98c65ba688da72266cc96899ded9209799", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5d19d25fa40920b7e3a0d6f4e12e284", "guid": "bfdfe7dc352907fc980b868725387e980b056ed7b6b99f8533fec1d37701b716", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98343527903bbe24018d7b9302a51220b8", "guid": "bfdfe7dc352907fc980b868725387e9815ab9e1123f6342cc131b55219cae95e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0167a352b31615b12231bde88495638", "guid": "bfdfe7dc352907fc980b868725387e98728fa087c1df983a5776c90c2ad32f0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b642325eb6c2074ec60a00e244a59cf", "guid": "bfdfe7dc352907fc980b868725387e9839a0717184882b751f041be102c19634", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c2fb180412ce404769f8dccb2d83cf2", "guid": "bfdfe7dc352907fc980b868725387e9890e24b099a4a172ddf354c0bc757e22b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f105a235fd19cf09e22d8588cc53e20", "guid": "bfdfe7dc352907fc980b868725387e98ff295f0cbbbfff612a1e6f0e7954818d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800dc1e5ad5e5df6e1243827a767b16fa", "guid": "bfdfe7dc352907fc980b868725387e98072a14fe78636dcbdda404cfbb314ea4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983646cb191803bc095e8568c5b1570c71", "guid": "bfdfe7dc352907fc980b868725387e98a482eaf54f776bc628053d383c33a7a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a87ca1451771ab6f43288bf6e9f9b4f", "guid": "bfdfe7dc352907fc980b868725387e98a9f7fdd85aca408013c7e62caebfdfa4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883265153d6d4e637cd8ecedcc5d3ebfd", "guid": "bfdfe7dc352907fc980b868725387e9814deb33597fcd96bb77bf5ee3918a74b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827c7dd22a6c6f0f4f636f8f4b8841ef9", "guid": "bfdfe7dc352907fc980b868725387e98cb363a67bc779b208d6c566751fa38c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98805678846c2c1a79c26722382c054ce3", "guid": "bfdfe7dc352907fc980b868725387e98716042837ddd330b76e4d1536dc5312e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c8ae6fdecb0f5d0b94aa3642ba48da4", "guid": "bfdfe7dc352907fc980b868725387e982e54e546bc4a81e9db10cc68946fbe94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0f9b8df7318a164342a853703c8ba68", "guid": "bfdfe7dc352907fc980b868725387e98c20b87d9fb4d46d9a2b74b6dbe0cac0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899e6e46258b75b51e8503a5974186321", "guid": "bfdfe7dc352907fc980b868725387e98ff08a61b058b5ec2e421ebefc03205cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef2bd259f2eafa24a0b9140d5f7be182", "guid": "bfdfe7dc352907fc980b868725387e98243dc5e59d20b619b46287f6653881ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989214f189794308567433e4559f75a2c8", "guid": "bfdfe7dc352907fc980b868725387e98b7419b4b5b3e5c0c3fdd93fb14972121", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98348741dc847b4d67ad34c98ca14aba69", "guid": "bfdfe7dc352907fc980b868725387e9851d632d6a2d05235d081558e2f536733", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98975f5dcd8cc46d87a9de2de2ee9fad9e", "guid": "bfdfe7dc352907fc980b868725387e984d34f4c346c4f1da8e7fc2e72b19d4f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d84d347d221b78e9b11f73c68387c3f4", "guid": "bfdfe7dc352907fc980b868725387e9896fae0012b96ffd3543779cd02b0050b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859324e43d873b1fb98f7e36de7529f0f", "guid": "bfdfe7dc352907fc980b868725387e988cb4dcd87b2f0b7364b4d4163de5a067"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be92b39b1474f9411cec350344dea2a0", "guid": "bfdfe7dc352907fc980b868725387e98f3dcb2fcce36b41792f1f08053ca2c08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a7a6cb92ca62cc5cd324ebdd3a23ad9", "guid": "bfdfe7dc352907fc980b868725387e98e8c897a93b6a47264dd51ac9e2c6a04d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e341be9448e6a72eaaea2ddd0cce12d3", "guid": "bfdfe7dc352907fc980b868725387e985f1e7ae11f39dd25ad8b1164f365222e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98975dd052652785c7cdf4a3ba2be88097", "guid": "bfdfe7dc352907fc980b868725387e98a11730584e196fde0a5136c7d4362936"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bdbcb0488eafa1b0030f1b6a02f60f7", "guid": "bfdfe7dc352907fc980b868725387e9881c667a42d9ccb55da19e7d57ed6785c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98387244886537a875b6455cfdf5172070", "guid": "bfdfe7dc352907fc980b868725387e9849d41e022dfad9f58c9ba2e8695065dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98546dda824377e4d31ad5598465a7313c", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf3bca00589a2d08cf854cc21cf04503", "guid": "bfdfe7dc352907fc980b868725387e98048d6aa51e941f690dd97c41690fe291"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866b801e979b020911909434b852f248d", "guid": "bfdfe7dc352907fc980b868725387e9889e5900cd748b720870db624f23b8de5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983612e1ce3d1677244c3e59d9550f49ec", "guid": "bfdfe7dc352907fc980b868725387e9825f7bcc81a598ac43b43d6acd38fb779"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865b26498ca1012c4622b2abfab8d449d", "guid": "bfdfe7dc352907fc980b868725387e98bc4c66aa1954c19129acd8de26620a19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980032aeea3a4ae43d5b87a2ba99ec16af", "guid": "bfdfe7dc352907fc980b868725387e9805765735996844fda4b63fcc155f1c21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ad667251d2be70e9897a4207639d9f6", "guid": "bfdfe7dc352907fc980b868725387e9832bea6933864bce1eeb84097f0ba450c"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}