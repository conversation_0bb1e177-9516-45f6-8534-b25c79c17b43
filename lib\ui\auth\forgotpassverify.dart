import 'package:db_eats/bloc/main_bloc.dart';
import 'package:db_eats/ui/auth/confirm_new_password.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ForgotPassVerify extends StatefulWidget {
  final String email; // unmasked email for API calls
  final String maskedEmail; // masked email for display
  final String phone;
  final bool isPhone;

  const ForgotPassVerify({
    super.key,
    required this.email,
    required this.maskedEmail,
    this.phone = "",
    required this.isPhone,
  });

  @override
  State<ForgotPassVerify> createState() => _ForgotPassVerifyState();
}

class _ForgotPassVerifyState extends State<ForgotPassVerify> {
  // Controllers and focus nodes for OTP fields
  final List<TextEditingController> controllers =
      List.generate(6, (index) => TextEditingController());
  final List<FocusNode> focusNodes = List.generate(6, (index) => FocusNode());

  // Store OTP values
  List<String> otpValues = List.filled(6, '');
  final TextEditingController _passwordController = TextEditingController();

  // Add responsive helper methods
  double getResponsiveSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return 12;
    if (width < 600) return 14;
    if (width < 900) return 16;
    return 18;
  }

  @override
  void dispose() {
    for (var controller in controllers) {
      controller.dispose();
    }
    for (var node in focusNodes) {
      node.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final baseTextSize = getResponsiveSize(context);

    // Get the contact info directly from widget params
    final String maskedContact =
        widget.isPhone ? widget.phone : widget.maskedEmail;

    // Determine verification message based on method
    final String verificationMessage = widget.isPhone
        ? "Enter the 6-digit code sent to your phone number $maskedContact to change your password."
        : "Enter the 6-digit code sent to your email $maskedContact to change your password.";

    return BlocListener<MainBloc, MainState>(
      listener: (context, state) {
        if (state is ResetAltEmailOTPSuccess ||
            state is ResetAltPhoneOTPSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(state is ResetAltEmailOTPSuccess
                    ? state.message
                    : (state as ResetAltPhoneOTPSuccess).message)),
          );
        } else if (state is ResetEmailOTPFailed ||
            state is ResetPhoneOTPFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(state is ResetEmailOTPFailed
                    ? state.message
                    : (state as ResetPhoneOTPFailed).message)),
          );
        } else if (state is ResetEmailOTPVerifySuccess ||
            state is ResetPhoneOTPVerifySuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(state is ResetEmailOTPVerifySuccess
                    ? state.message
                    : (state as ResetPhoneOTPVerifySuccess).message)),
          );
          Navigator.popUntil(context, (route) => route.isFirst);
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        body: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: size.width * 0.02),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: size.height * 0.02),
                // Back button
                Row(
                  children: [
                    IconButton(
                      icon: Icon(Icons.arrow_back, size: baseTextSize * 1.5),
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(
                        minWidth: baseTextSize * 2.5,
                        minHeight: baseTextSize * 2.5,
                      ),
                      onPressed: () => Navigator.pop(context),
                      style: IconButton.styleFrom(
                        padding: EdgeInsets.only(
                          left: 0, // Removed left padding
                          top: size.height * 0.005,
                        ),
                      ),
                    ),
                    // SizedBox(width: size.width * 0.01),
                    Text(
                      'Back',
                      style: TextStyle(
                        fontSize: baseTextSize * 1.2,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                        color: Color(0xFF1F2122),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: size.height * 0.08),
                // Title
                Text(
                  'Enter security code',
                  style: TextStyle(
                    fontSize: isLandscape
                        ? size.height * 0.04
                        : baseTextSize * 1.7 // gives 23.8
                    ,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Inter',
                    color: Color(0xFF1F2122),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: size.height * 0.01),
                // Verification message
                Text(
                  verificationMessage,
                  style: TextStyle(
                    fontSize: baseTextSize,
                    fontWeight: FontWeight.w400,
                    height: 1.4,
                    fontFamily: 'Inter',
                    color: Color(0xFF414346),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: size.height * 0.04),
                // OTP input fields
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: List.generate(
                    6,
                    (index) => SizedBox(
                      width:
                          isLandscape ? size.height * 0.08 : size.width * 0.12,
                      height:
                          isLandscape ? size.height * 0.08 : size.width * 0.12,
                      child: Focus(
                        child: TextField(
                          controller: controllers[index],
                          focusNode: focusNodes[index],
                          textAlign: TextAlign.center,
                          keyboardType: TextInputType.number,
                          maxLength: 1,
                          style: TextStyle(fontSize: baseTextSize * 1.2),
                          decoration: InputDecoration(
                            counterText: "",
                            contentPadding: EdgeInsets.zero,
                            hintText: "-",
                            hintStyle: TextStyle(
                              color: Color(0xFFD2D4D7),
                              fontSize: baseTextSize * 1.2,
                              fontWeight: FontWeight.w600,
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.circular(size.width * 0.06),
                              borderSide: BorderSide(color: Color(0xFFD2D4D7)),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.circular(size.width * 0.06),
                              borderSide:
                                  BorderSide(color: Colors.black, width: 2),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                          textInputAction: index < 5
                              ? TextInputAction.next
                              : TextInputAction.done,
                          onChanged: (value) {
                            if (value.isNotEmpty) {
                              otpValues[index] = value;
                              if (index < 5) {
                                focusNodes[index + 1].requestFocus();
                              }
                            } else {
                              otpValues[index] = '';
                              // If user clears the field, don't move focus automatically
                            }
                          },
                          onSubmitted: (_) {
                            if (index < 5) {
                              focusNodes[index + 1].requestFocus();
                            }
                          },
                          onEditingComplete: () {},
                          // Add onKey to handle backspace for continuous deletion
                          onTap: () {
                            controllers[index].selection =
                                TextSelection.fromPosition(
                              TextPosition(
                                  offset: controllers[index].text.length),
                            );
                          },
                        ),
                        onKey: (FocusNode node, RawKeyEvent event) {
                          if (event is RawKeyDownEvent &&
                              event.logicalKey ==
                                  LogicalKeyboardKey.backspace) {
                            if (controllers[index].text.isEmpty && index > 0) {
                              focusNodes[index - 1].requestFocus();
                              controllers[index - 1].text = '';
                              otpValues[index - 1] = '';
                              controllers[index - 1].selection =
                                  TextSelection.fromPosition(
                                TextPosition(offset: 0),
                              );
                              return KeyEventResult.handled;
                            }
                          }
                          return KeyEventResult.ignored;
                        },
                      ),
                    ),
                  ),
                ),
                SizedBox(height: size.height * 0.04),
                // Continue button
                SizedBox(
                  width: double.infinity,
                  height: isLandscape ? size.height * 0.08 : size.height * 0.06,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(size.width * 0.08),
                      ),
                    ),
                    onPressed: () {
                      final code = otpValues.join();
                      if (code.length == 6) {
                        if (widget.isPhone) {
                          // Navigate to password confirmation screen for phone verification
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ConfirmNewPassword(
                                email: widget.email,
                                otp: code,
                              ),
                            ),
                          );
                        } else {
                          // Email verification remains the same
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ConfirmNewPassword(
                                email: widget.email,
                                otp: code,
                              ),
                            ),
                          );
                          // context.read<MainBloc>().add(
                          //       ResetEmailOTPVerify(
                          //         email: widget.email,
                          //         newPassword: _passwordController.text,
                          //         otp: code,
                          //       ),
                          //     );
                        }
                      }
                    },
                    child: Text(
                      "Continue",
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize:
                            baseTextSize * 1.15 // = 16.1 (very close to 16)
                        ,
                        color: Colors.white,
                        fontFamily: 'Inter',
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: size.height * 0.02),
                // Resend option
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Didn't receive a code? ",
                      style: TextStyle(
                        fontSize: baseTextSize,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF414346),
                        fontFamily: 'Inter',
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        if (widget.isPhone) {
                          context.read<MainBloc>().add(
                                ResetAltPhoneOTPEvent(
                                  email: widget.email,
                                ),
                              );
                        } else {
                          context.read<MainBloc>().add(
                                ResetAltEmailOTPEvent(
                                  email: widget.email,
                                ),
                              );
                        }
                      },
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.zero,
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: Text(
                        "Resend code",
                        style: TextStyle(
                          fontSize: baseTextSize * 0.8,
                          fontWeight: FontWeight.w600,
                          decoration: TextDecoration.underline,
                          color: Color(0xFF414346),
                          fontFamily: 'Inter',
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
