{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c329620c51892527db69ac984ef9321b", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e986eaba3bbf34fffc52894406988f981b0", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9804db47a3ceef83edd118018eb43bf272", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988f27378b21081f0ddd8dafe5153573c7", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources/firebase_auth/firebase_auth_messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98be62f4a228baeab66a4469f8cfd6ee9b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources/firebase_auth/FLTAuthStateChannelStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98076a56e45d08ef60caa0c8697aaab1a4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources/firebase_auth/FLTFirebaseAuthPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e6cbe7d8b6c9774efd017578627a0113", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources/firebase_auth/FLTIdTokenChannelStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989a792ba4cc8488663a1430242bf1ebe0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources/firebase_auth/FLTPhoneNumberVerificationStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98363ab9b5f24ae6b82960f4691d8bcd8a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources/firebase_auth/PigeonParser.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c95a47495eb814292bb851943e89a3b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources/firebase_auth/include/Private/FLTAuthStateChannelStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6927924c666ec24129adcfced8aca7b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources/firebase_auth/include/Private/FLTIdTokenChannelStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9831c79b4e813e64779ba7e0a0f95b2c0b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources/firebase_auth/include/Private/FLTPhoneNumberVerificationStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d172bb4fb83963a2f6b18d9d76e4e328", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources/firebase_auth/include/Private/PigeonParser.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981b3703d7d227c0db9d01dc6033b03b87", "name": "Private", "path": "Private", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b4afcae7f040ddfc327ab02a7511db35", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources/firebase_auth/include/Public/CustomPigeonHeader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986685ebc908694226c41420bce123693c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources/firebase_auth/include/Public/firebase_auth_messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981328ff4fb7d4c55850d13b315383c09d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources/firebase_auth/include/Public/FLTFirebaseAuthPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982dfd01bedb1ae67dd71848d0f161a3f5", "name": "Public", "path": "Public", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898d628dfb0ecf926a095f767fbc4a726", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5a82a6a3df5e210874f3d3792d81aa7", "name": "firebase_auth", "path": "firebase_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4ae11c006250a12029e0f612cc36766", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc67ebca72a55a4a679167d0bcccb85d", "name": "firebase_auth", "path": "firebase_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f606c37806802becd1a76563e47b0990", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984253b62615e0cb45497dd33b1191a8f9", "name": "firebase_auth", "path": "firebase_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980abe43fe2be92e225604b7da389cf74a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844a7c1413c05bf57c1f1977471380b93", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985819801964e3fbe326fc2a1e2b8b7bbc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d0a2bbd7c128b7c32f719afb5f59ffc1", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983697795ec9928c23baca81f314710f71", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9863459718389f44401e40a1ed706bdfdd", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980da21eec830ecb966c6039a3bd06bcf9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869f1249ee41e0169b30a3ca16b99fbf6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98942700d1a9a11c58dc3058b18d423968", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd7fc99fd075ffe9aed26a9e9d0a47b4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9805cbb675dc8aa67476c404e5c901ee40", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd88ec1bfc22eb473d776186966cee57", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98846b7d7f52b3de1768fe140196f14711", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f788524cc1dcbfb9b180b8363f87de59", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d58a0f6ccf42507b7770d927286df40d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/ios/firebase_auth.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c79af750cb0167aa1f2c4f7dca747b12", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d61c7e865472828693dbf53a628d103d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986478bf2cd3955be0d2a061450f280ea4", "path": "firebase_auth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983bf790944b84c38de757c46fa7af643e", "path": "firebase_auth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9806fb65267cd8e06076ad3d66a0984dbb", "path": "firebase_auth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984676f61eb46a80396ccb35f92414d216", "path": "firebase_auth-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981182b7e8eadb39af879298a45326335a", "path": "firebase_auth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9875e5afab22672f66e1e31bdd0115ef59", "path": "firebase_auth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9846e84d391e325e0ad1213e0298d4d8bb", "path": "firebase_auth.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d05f4cc031eed7803e3e05e64b95dce8", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e963bc800557a7d8a1e10e51feeba40", "name": "firebase_auth", "path": "../.symlinks/plugins/firebase_auth/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98999cd85f9db75f6dadc37ca9f917e4be", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f424c22c439d53174f74e443bcb5151a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/FLTFirebaseCorePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9846f31036fb9c9fa863c8c676ef0ea181", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/FLTFirebasePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981f009e3d5007427b7ea83398e741e560", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/FLTFirebasePluginRegistry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98674d04f6aa5f7386e2f03abd7916a46c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bc0045466ac048473a281910cce4850e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/dummy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984960eaee1e2d9ac6739ffd20486756ac", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebaseCorePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c6350a11e78be7363fb3e93ab609ba0", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebasePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98065c3d6c80d39f3217639574672ea6b3", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebasePluginRegistry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e7b152b308e3e310b14ffcfff0b2eb9c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98145b983505659ab5faea9d1ae58902ff", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987915324bf5a8656c0b65090af6e40e4c", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5c53c5c4628ac0e157c734f8647efcf", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98919b8d6d75d79f791a7b23e303241410", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852198fbfb2174e181c9864505de4192f", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98200ed7477913d519c7a941dccf186840", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9dd40fe2d36029fc4956723b0cfe112", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c71f06a72dd1f72eb6c18e88f84cebbb", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eca1294d890cd691cf8cbeea310b5b87", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895a3b56282f1742b530273015a852b81", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b69cc3d9ba879fa2c4bd594283c98792", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897ebc6c5905d53f14afffa4217562c6d", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ea39cae1a3e4747c56c9923d30eac6e", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c222a66c6c339e9e0cf36f853a454345", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6ce23adc3accc8ce94c312380f7f381", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b500f67ba2a53ed75ddc560d20bd80e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98152f45071ea35c4c15c21624ec5e4b68", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98460adbee60e78dbf46fad5c3206ee2c2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb2a22d49e1685c0b878e73e7f71d8e9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875a4bc8a41a1106a3d7300c588d186d1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98732300e8f5fab70fa2ea02ed7f9d63bb", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e986d25d5e8e3a1065e55c4235d3678c4b6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98315fadad852cfb4c715ff53bbfc86381", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9899a235028a6fd4f5fb6fc2d7c1a18d13", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9849dd5c88876e0290b79fa102e72de98e", "path": "firebase_core.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ae2b9dbdc49421cbf0037d2d68c0d00", "path": "firebase_core-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9869f0205ce487740ad07acb9587b7ce2c", "path": "firebase_core-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804c675e2fbe3f6e557b2c892c8eab319", "path": "firebase_core-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1023aa6d8dc0b77e0bd7e0f7ffc2b17", "path": "firebase_core-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9884a544551fa47247cd4b5125dfc1c30b", "path": "firebase_core.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98621c9ca28f16341af4f4a12cd67859ee", "path": "firebase_core.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980e770444d8caa914480f4f7f28e7c876", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982bead4546b38b5bfef0d99a8e71c4a60", "name": "firebase_core", "path": "../.symlinks/plugins/firebase_core/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4d3ddcafc548489a39980fd6f88420e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/ios/firebase_messaging/Sources/firebase_messaging/FLTFirebaseMessagingPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98db59382f944851c6b950b544f1e2e9df", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/ios/firebase_messaging/Sources/firebase_messaging/include/FLTFirebaseMessagingPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988f6b3347e3f310df9bd43999ce5748a8", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e9aa56cb65ddf8bb31b43f500bdceab", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989bca30caf9b51929f23f44becbfd7c42", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9496f4b41e532810fc6a64c3d081286", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e4ff494999c9ca15b704844b9248b28", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cde4c776fd9eb0030830293db0dbf8a6", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3a901d42172bb52a3904b1a982ea74c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a65caf2df93e34b99d72e34c2b27bc63", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3810dbc1e3a6c1add9b18da874fa791", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d9de29c77e679bc7c602ac2d3f0b9b9", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98633c6b06055204927c6296d09cf37709", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f0bb198217fb47e9861ae982f95e98f", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983fe852f10700b360bedef81dc3898aff", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815cabb8e11e0fc24162134d94325a005", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880f391bf76762518e186f611cdf8c868", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9817514f60a8394123f68e06eeca22dbd9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98650ff5cfa30812e81696c4554710eabe", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982dabce08d1eab69374e4206b0596c7ac", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989dff20a3cd9041bda9570b4f8986f274", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c8eaf4af3ada19232f9dc2f70280896", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/ios/firebase_messaging/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98c5bdb382f5a6e2e736df7c657f0fad5d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/ios/firebase_messaging.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e986f6046948579696a4118e9fe768dbdea", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986eafcdb97c7893da71f987aaa17cacb8", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9818a3430ee9965fc6768964073431cdbe", "path": "firebase_messaging.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989e05da432c6773f42b7839fc7a581971", "path": "firebase_messaging-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984a6c47eba891bf59aece019697a34d42", "path": "firebase_messaging-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849f50465b071264cf38359d9cba82fce", "path": "firebase_messaging-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98553f994559c72b4c4b7905f14e39a9c2", "path": "firebase_messaging-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985856721aae5809c90ba86e50631f91b1", "path": "firebase_messaging.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9856e2eaf0cb257780a07c90479214311e", "path": "firebase_messaging.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d3b0a90620bc6173517bb9ee931c088b", "path": "ResourceBundle-firebase_messaging_Privacy-firebase_messaging-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b4d3fc3afd04e949edce8e079b774d3e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d61f32c49f281f932f3b4ff22694aed", "name": "firebase_messaging", "path": "../.symlinks/plugins/firebase_messaging/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9888247715f47fdbdee1df17c824917c5a", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f99e33b3ef5233648c8e608277306315", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986fa279bcda890d7a27f5d2d2bc478e01", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989939c938c2f6e4f147378d99bd4b293f", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dd5755b43f9547193caeb29e471cb927", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989cdb0bc196f73e348901b8328652da82", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9867726dc318fdb117f9fed99a7546bf7c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Classes/FluttertoastPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815dcbc080f2dc565994d9d7c965656e2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Classes/FluttertoastPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de597d40809378588acf660ad4e4909c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Classes/UIView+Toast.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eed9552914c6d3f874587d4c447c5bd2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Classes/UIView+Toast.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ad5a9fde371a6e26e8ace58df67415e3", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98577aa27eed412cf5c04e5c74e3fe6c06", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98facd5c0ab8c13e7ddc15ab371d229c8e", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f789aef05ba693bcb212b3054ba9678", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0d94cf03837626631d127e2449f0fa7", "name": "fluttertoast", "path": "fluttertoast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985965581a54f0b8b1d454befd07427c21", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986740de45c101b7394f62c839a0207160", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856d5de373fa6e1a10d7b125c2d2c99a6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b077f49784ee2f3cfaffa0f9da11308", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b89dee7ebcb01bcc08bd01ddf75fe0e", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98066997c0698f11f4966f030b8909b99a", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a709e77e34c435bc5e092f544327809", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d33ed6ae7fc019f73c9112e19247a752", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c5be86fec2d044553bfd2841ed6b0bb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839a1a0554cf8b63ea100f374bfa3abe5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6e5ba618087ec095bf7038ae2036b86", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8fde3c3419bc15ac361f3caa7f5733e", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98819b22dfaf744ab06db7198097e32578", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/fluttertoast.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9880d0e2420447b9c3515b9989ac1bc73a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989ff2488df2367e8683b6e538f4bb3f67", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98250a6ee51f378bb982524a97f9a70263", "path": "fluttertoast.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884d370bfa91e05a39455e971537e5293", "path": "fluttertoast-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9879e1090cda9069f32ff00d8c703bda4e", "path": "fluttertoast-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989671414ec95ce7f7404e24a58130ebeb", "path": "fluttertoast-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9806d7afcab25908667f288682fed8e646", "path": "fluttertoast-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9833480c80a3fee47c125fdf227a6aefe9", "path": "fluttertoast.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fd29f3e8b1f5717ecbfd31524a17d44c", "path": "fluttertoast.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984e3cc00c10106aabc3b83d7a77f77d97", "path": "ResourceBundle-fluttertoast_privacy-fluttertoast-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e506d6e8aedd1174273ddaaddec5a6cb", "name": "Support Files", "path": "../../../../Pods/Target Support Files/fluttertoast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b80af3b8949b7d676f5af54256032fb", "name": "fluttertoast", "path": "../.symlinks/plugins/fluttertoast/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b7fe65d50e307edd56a7b5581ec0c9c1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Classes/GeocodingHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98871df0177e7f5908b595e0f5b1c2d6df", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Classes/GeocodingHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98648f52e8b7e7f2c63bd23b128a7e29b9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Classes/GeocodingPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988716b73059cfbd56e9fe516b3df137c7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Classes/GeocodingPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d286b20ad5d5c30dd9d53186b13c71f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Classes/Extensions/CLPlacemarkExtensions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9851d191f010fd4fbaf409d93ab6b99897", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Classes/Extensions/CLPlacemarkExtensions.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9835cdbb698f2ed49b1e6e187f27eb352d", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820b8ed9c659859a426b90c6438fd907d", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a90dab816ff04a24ebbedf59d5dc22d8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b11939567b17dcecebde5026108150cc", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b370e3ef0e7746e3b49540e7576d0b2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851cff93ccd9acbc665dc07d74a7b94fb", "name": "geocoding_ios", "path": "geocoding_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98600b1390bba0b0afb4c176f5e658aa99", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e57bce99f513d76c3a82f4841a69ba25", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9ebbeae0eef9d33218dda24f276fcbf", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a83739fd64eff534fdfd9319f58e699", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98621cfc530532d525274ddef99575127c", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1e4eab5433e67f917a51cd4a7db0c59", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db53d329413bd8edc51f6cf300e6d047", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0120d5d76174f20ca5d607c72842385", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989697db58630eedc569619f928701f748", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987fb63356c80bf329bde72faafe5aef64", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98391f57e324db67c9c7a72a62c2e74b25", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca3d3612878346ba9a8d1874cfa275f1", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98950106eb9673610c020f114f730d09e5", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/geocoding_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985119132703cfd92f7268513fc947ff97", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983501530cf9d43fe724843fce42e82788", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b9c03da71459cab96c73aa591a4e3b93", "path": "geocoding_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9899e9d265b52c41ace2833f83d283ebd6", "path": "geocoding_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9897b0999b59faea03e401bdecb37ba99c", "path": "geocoding_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a8051e958ed31b9c1bf2864124911eb", "path": "geocoding_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae8a71c9a6aea20f431365a38270e20c", "path": "geocoding_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984d9f3de421e14799715f5e8a28a88bcb", "path": "geocoding_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9833797b4dc3dcecfb690ac67af527ff7d", "path": "geocoding_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9886cd8d77ced9333f9fba6b3c443b30d8", "path": "ResourceBundle-geocoding_ios_privacy-geocoding_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b88540de8869bc8fc6bf0dd970b0c1f8", "name": "Support Files", "path": "../../../../Pods/Target Support Files/geocoding_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98798aecaae9142844211bc717426f077e", "name": "geocoding_ios", "path": "../.symlinks/plugins/geocoding_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980b0ddf27ca023f0780062d4708dc1c78", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/GeolocatorPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9839c01bc0e365303212a41b7e15caf6f6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986698a9b878102db85330b51e36470ae1", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Constants/ErrorCodes.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98025f989111b8cef015d1757f01373778", "name": "Constants", "path": "Constants", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9837f8ca96433aa9eaa64032153047a8e3", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/GeolocationHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98060d1dd3c09f3fcf7ac31b6be958c6d9", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/LocationAccuracyHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98833f3ccfd9f770c18977831f573d4fc4", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/LocationServiceStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b96d69dbd5e7460ffb50d771283638d4", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/PermissionHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98011d589029e8b47dc068685b8a306d57", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/PositionStreamHandler.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d4330e771206222d5d07b4bad75d4aeb", "name": "Handlers", "path": "Handlers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860c89d6cb48f206f1801e5ae941af650", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea84a482f072cf5726df4b0e7a734867", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/GeolocatorPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98728a0cb081187d126656a108afbf9cf7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/GeolocatorPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bbb9241872369207083c9a4943d78d5a", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Constants/ErrorCodes.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98758bc8cdceb3019044fe4078cf140436", "name": "Constants", "path": "Constants", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989564460610e388392bb30380924d0917", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/GeolocationHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98150226a4c3d7c74aedee25da8ea473c7", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/GeolocationHandler_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c7f6a827f307c9c6017222416d183c9d", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/LocationAccuracyHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984aa682a64882e57e09618c670abe2adf", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/LocationServiceStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d557ee4a6109e1867463c5ef15081139", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/PermissionHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9800ad22c677f0f09527eb822aed3ef71b", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/PositionStreamHandler.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e4ce640a45f3ce51ebbfe9c633bbdf32", "name": "Handlers", "path": "Handlers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c213e4c744d0ea7607817272e8f5f83b", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/ActivityTypeMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9866321825ef50691e1005fdbe76d40a8f", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/AuthorizationStatusMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f784cdaccbcd8689ca68a633d6b6ef7", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/LocationAccuracyMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98babc489f4b62947919987e9e25194a22", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/LocationDistanceMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4b095c3bd53b85a1a6dd27efb3cbbd7", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/LocationMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988186624a9ee58b137a6d9789d0726fe0", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/PermissionUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9862cd2c5fa2149e49502b3543f48fe571", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/ServiceStatus.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983153ce565c75bd261e087d99c1d04d9b", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a0b43bf4da6d32e2e175289765e1abc", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e4709a56a938b55e261e74d91d78c50", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983d60815c677371657c38bf86d8b5a847", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/ActivityTypeMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986fd76e14f324307282f02a25241e71f0", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/AuthorizationStatusMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9866b8132d80d8e75a8bb5478b6ae7e5bb", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/LocationAccuracyMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e66d907c4b5db68b772cfc97e849e798", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/LocationDistanceMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9898681ed94d91f3aab4f68d58701220e4", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/LocationMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e71d70a909a9b9176dfbc92fcc8e76e9", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/PermissionUtils.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a3f975b3a5017d44ac6abc012ae6decc", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832828074ff85f1455b16c965791877b9", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801aa5eac5bdab1c3d747383dc560bf4e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a414a2b6c14a30f31e096a59913df20e", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871d6a4852d37462758506a6ea56ee210", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f702e9dc71b39a73b2fd2a846d472d6", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98875c1faae07cf0c48e0844a6a2751cdf", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c89885055f78ff63b99d3bff1ec4117", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c0a2ec0ee245934c0fe1598b4e92ead8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9840c82eb0cd4bf2dc4a0d66d88c0431e5", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2e3ab3f8bb2902a4b7b03bf337d2c2b", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6624ed1492d4e2db2329c868b88163c", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984bf610ac7f9ffe8a692d7575b80d33da", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9886bb95085b95bdb7a11f47189ca1d55b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9f36a0c3ac19eeb337643a8729d9d7c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98598d269c7a4657dbbd0407fd102c98fd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cce7ba2d47c197c61e285748e971ce9f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fed0f9f11bbd59ba35d233d363eca215", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ed1525fe1b17d2e35d8b10599a0b2b4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f4aa517efb2c5496440df7a7064cb93", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e980c829d2a7d203b2bc7665d48e434da1b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f2942550be33fcadb7aa977858e75316", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/GeolocatorPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98eec723ce4c6bf2653954ac5f876f4c87", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b8c9946005d6805f6d996d5840a73875", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98edd9697ed82845db8756ff7eddb5afbe", "path": "geolocator_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98afc19a2b6eb8fe20ce786086bf3d4033", "path": "geolocator_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9826ce9ae94030294dbda3429566db7103", "path": "geolocator_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838df26f6b3997cfa2f1658b66be201c5", "path": "geolocator_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9800dc10c8252aece7c0216728249a2b83", "path": "geolocator_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98aa3c245f0406c64e600a7df440b23c1e", "path": "geolocator_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fae7ebacfe6869508cf2b55682d11574", "path": "ResourceBundle-geolocator_apple_privacy-geolocator_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9828805391e9c4094e6ad5af03c8619b91", "name": "Support Files", "path": "../../../../Pods/Target Support Files/geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff6983d60ab2894ce571685bb356f442", "name": "geolocator_apple", "path": "../.symlinks/plugins/geolocator_apple/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9853065275f61d2817fbc16ba293d553cf", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FGMCATransactionWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d7c89e3db3cc132a6537943ca2931af2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FGMCATransactionWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9862a76bd43891be7e8a064ec7a7c84cf9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FGMClusterManagersController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c8427d5a8b76bfa37d75801fd1e829da", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FGMClusterManagersController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98078c182e575d828137aebea826ba5c5b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FGMGroundOverlayController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983d1c9577479b8b05287db2b015e97f69", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FGMGroundOverlayController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c286669f8be17fa3eebe3561ce5033a9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FGMGroundOverlayController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988fdce00f61efd2f7d933093a9aa9ebf0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FGMImageUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eea77e92967e9ccf4a23675761f90b26", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FGMImageUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981387f049504922a4aaafb217bf85be28", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FGMMarkerUserData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98452d58af83f035a2b71df03627bc0a42", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FGMMarkerUserData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985f4112dae5194f06bad2f03e2b5d95ad", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FLTGoogleMapHeatmapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986f273fb678e88275edf11cf1909d437b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FLTGoogleMapHeatmapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ba133f04e31cfda7c7df77f9709100f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FLTGoogleMapJSONConversions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f88f4b28a4c335080b981e0fd7340446", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FLTGoogleMapJSONConversions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9890207cab316a5de994377e1b0fa3690c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FLTGoogleMapsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981af57da64e63064048fb99ecc99f1307", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FLTGoogleMapsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a9a6166b067b64c74765ff8e1911c6b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FLTGoogleMapTileOverlayController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9885779bda91211a515c48bae4c93547ae", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/FLTGoogleMapTileOverlayController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f2d227d906c8460ba9865e1fed0aac79", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/google_maps_flutter_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d4e4c0b03d51d3bd0ba4b3ca72d1e96", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/GoogleMapCircleController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cec329581167926267ec404e821963d5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/GoogleMapCircleController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a24e0e5e609a97b18c2352b87c59428", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/GoogleMapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98209222f2b079a5f078ce8d3501ec119e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/GoogleMapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b29cc40dfbc6f34c25858f0fd44735b0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/GoogleMapController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f0944b2830704473f912d7b6ce12ddd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/GoogleMapMarkerController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9816dfe4dc4d3eefa50ad3c8e48c64839d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/GoogleMapMarkerController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98711d521792cbd5d878f451ecc25a60f6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/GoogleMapMarkerController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e8c3f53b88895573f7cdbd64677825d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/GoogleMapPolygonController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9847a3e0acf63a834d59814086a0658e35", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/GoogleMapPolygonController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb203e70e61c623846a65dd3fd0aa699", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/GoogleMapPolylineController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981b20aa54650251c2a905100c530ee1b7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/GoogleMapPolylineController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c796cfc6b49e7d3e6f2c050227591c8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/GoogleMapPolylineController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ffecc35f33ad2f13fe5a39683cef2f24", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985842f1f97dfa785d9e19127ad36d576b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9871c8ffc2eedbb8b2eee056249f8b3381", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e555ced738749e73bb28627ff14ba9d1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c34c8acc2a337ba351f3f690ad2072a4", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842b6458c5984e6510aa46fc429bf0798", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980588011ffb8c1372de7b009aa28a15e1", "name": "google_maps_flutter_ios", "path": "google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98303242b3636d17cd0c57953aa23a4294", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853c68621513056b3cfc3cbf8817bbf18", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984bf7d2b3b2090ac7b16a8d36b4dde030", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a1fb697fb7df37ae58918c8b96b5b4b0", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ec052c6ad0719f383efebf33596fb7c", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98888175c71a85f27d6c5061ff84596fc5", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98177068cb1cd7795666573346cea5db78", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb85b0e27edb8d756d996bf68d3c8542", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983907379f23a888c4ee390a2d127c1ced", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834d749c9f89f12116c5bd73b9e5c7e94", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824f8898ecf32d3d0126522c878f7fc67", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de6efaceb02cdb4d3268ab469bbb67db", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d6d2ab3cd9a04ffed5e08c4845884f75", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/Classes/google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9871daeeeb8ca3faa370aa2b3731544951", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/ios/google_maps_flutter_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e986d984d9ccdc55bea37a9ad0c9e6738a6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f94aa5964d2a9ba0feace7121ec6ca20", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9804bc1fc7d237d24fe561cfda861582de", "path": "google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98384cb65c54a3a964ac191b5b372f68ed", "path": "google_maps_flutter_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9867bb261b3701f8af76028c74a9ccf319", "path": "google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982530c5bc346a96ecacd3248f6ca097e8", "path": "google_maps_flutter_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f3dbbd8724e25b43c147940f9cf2d791", "path": "google_maps_flutter_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ebd2729e17cf60ab02692c28edf7402d", "path": "google_maps_flutter_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9815dfd7fd8b508d006ee5055e87adc944", "path": "ResourceBundle-google_maps_flutter_ios_privacy-google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989c0475908a7468b78cc850f2bf2e426e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e0d556a44f4f42bd7344d85519898b72", "name": "google_maps_flutter_ios", "path": "../.symlinks/plugins/google_maps_flutter_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98da102723b3e5fec401dc278838d3197a", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a3deb0b00e424c9621a66fc7088e72f5", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b495caf7f97ef2b29ae734be241bba1", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835501ed08fa4d59501cef488572648ec", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb41c55ade8451b9f23337f1db3177ab", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8ba3dcb4158eca6415bd854e8bcd35a", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98437923580448b19e4ef8b84ace4c667a", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986628c29ab65efc780792422f502931ba", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98364ccfc1540de27048f72464808a4933", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98044fe205277c92e7f25479bdf6ae7b5a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3e9566bd3a04d87a12d90913fe392f9", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b55d6b4eb6ee45bc5d75fd3b0354753b", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db86333c783ce95f905ae0880971414c", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9147011df8f6fa681f143dca9fb95fe", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989d3b3342647c0d56d143af81754f43f2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/FLTGoogleSignInPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982032838dbaba0ceae79747e908c95fb5", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ad558d82569177fab6e24c3c5e46c8b", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98caaf9ee85931f1a5695e09120e5ae193", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios/FLTGoogleSignInPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804e853f644bac798eb0156c0f90f41e7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios/FLTGoogleSignInPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855b72ea8eb8d1c1b1953078531a30363", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f4566f1dc67538b1c91772bb2805ce71", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9858213bc956a5d7cfae15eea90d8259b5", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5cd50f67a8925f51b9344798a84e6fc", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f67e5512d5198a2eba8be569916d476", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f35dfc9ec23b1943abe76d244024d1ad", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be4b4c53e2037fc443cf37fdd77fea88", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f408e03e24b3149a677e40c737fa7fd", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879f2c1c74880da827ff948894ba884b6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4d852f9c67be142a750d0a91de53095", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a904e95989584eae0364ca5bfac7dbd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829c2bb97519f86b52f60eae90de731cb", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9823f6b07cad20d0d1d5570de4d17cca8f", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fbe84d21f438353c8f625049ebb5f17d", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a15e4f8bc7c8bbb1ffe9ddf64783e49", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee96664ccad9e343cda8c78f9ef83f04", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d582015acabbb566067fedb04fff1347", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851b259ecdb5398456d23761a23c666ed", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8009ff9627fccea7f21bcb1ce6227f8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee28b02fef45cc1924a8a416c0ea95eb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831eaac8bcbf90bb25a30c8af98502231", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984681d021f2a173edd81568d451c3e5c9", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e56e0fa9d4e88735cf5f73114130d3ea", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/FLTGoogleSignInPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e981993c19a3b47f43fb8b0d0c4aa17abc7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98d5f3ba675d6d4043c8bf80211d95d3d9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98edd1790186cd840294bdcd9d01ae1399", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98bbb905f8644dd363cea63c66a90f1bb8", "path": "google_sign_in_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9868fbd08499372098451b8cae37cabf83", "path": "google_sign_in_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cc5a637fb7278504d99110ec564e81bf", "path": "google_sign_in_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bc651a8946d373591a3a45a0e96ae4cf", "path": "google_sign_in_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989503a03deace42c6aa900e12e233e4b9", "path": "google_sign_in_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98052a28ce1c1d8e11006cb5565d7467e7", "path": "google_sign_in_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a052cfb243b48f4b147821e85ebbca4e", "path": "ResourceBundle-google_sign_in_ios_privacy-google_sign_in_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f254ba9dfedf9cda8ba3c796e9fecff8", "name": "Support Files", "path": "../../../../Pods/Target Support Files/google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d56a0bead488aaac06746f3c71d26da6", "name": "google_sign_in_ios", "path": "../.symlinks/plugins/google_sign_in_ios/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98aca3925f94ecfc60f278f260db15fda7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f892821b78f24eff92b3f907adba19f5", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98680dbe7983671a87713e795cfaf572e2", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98796e9d14aab10ad7d4dd7e8cc6a8e2d0", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d901bca95a9921229d2d23d074a1c103", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d0740639b9d21b6a133ea276c5450fe", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d0c3c02ed21a1e96833591454ba148b2", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9448eeac068586306997df28ec7f133", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984554d1a82320a777da6ab5baefd59d2b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833db9c2caa8a109a5b9d91dd2cbcc94b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a1069af05cb353123e3dc1d13fc44d9e", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecd5cc4378ac705c622abd58a66c12d6", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce38d40933fad309c41e68e900794dce", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98afadd63a751f98c86cef76d24a965417", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c9d264d656765ac4afe8786e61a96776", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98059fd7110c463fa02b601a0e0387578f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987dd2e89e1bdf40efc12754b072e1a051", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b5f9f29c4f2427cdb40cfcb2228f72f0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f567125d143bbe18a87b8b6511270497", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c9893acbd8efbb1c48a76e9b97277340", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d012d17b034a6b6b0ec6d44aa4b0108c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987da5ba8b21235c38634d046f1392da91", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888819e5477a59961c4455904a4d563ba", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98687bd788de86fd9fe280780f36eb3102", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7ad6cfea18b2423fd8ba5f31af4b2a7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9896151aa78cb4b493ba331bb7ddc854af", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985391ae0d4faf39d228afbdb446d51999", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c3bf70de0cb4e48ca7d7e2313410e6c8", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d5c0b5c75ea819d159af5377fdf65e49", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98925f12fde487f89b6616121d6deecc10", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c57f7ff13bb35b9b615db99ba8f6dd82", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98519c570cfc553f23e528941bde367164", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b455695ca9248a1dce9dbb4c2bd33807", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856d253396caffab40c81b85b046e7663", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca29120b8156447aa1d9a84f7cce056d", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbb9f431e8b0cd338d943526703fc14e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985958593dba8884d995f6d51fdfddeb6d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848adc74a615f4e34a626289f339db059", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854b78df20e3251393c26cd9483dd641d", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c8a77992f726b976fc7d589165e68d3", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e9c3a5799b3f7ed424a0dce30e58fab1", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98acc103efdc3265053f21c0b7bf7de778", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98edb0c02f054c92071e04368f518c9890", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe5a92b290f84a32515640337dd91fdf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e831ae5c4c97de59ddb2e4a04dc250a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98960288f20f995f846f2258143a1eaa6e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b60d92177c2da5cce5a3dd85de06bab", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983891aad951f37526dbb2fde498860635", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845cdd5b46c1855f64777ffddf7e94ae6", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9802ca6430cde3eb55bec3240217046198", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b83124047090b339ec6b4115af9876f1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9891b40bf88cdd7104c5b8a6ddbe6e064f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9830c60c66ef2f5e235632293757bec413", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c42f6271e44cad2f806d2a8bd5f0972c", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d209faacc19b564e82cdafb866c52fcc", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f9859dee06b688d450e1575a44712cde", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988670d80f3bca4349bc097e4d32e5fbed", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9821682e55fb314ece2ecccad40de2561b", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988c6c00023046a91c2dddff81ede11edc", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98548126738b3d13b1449ceaa2e3ae01ea", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985e204f7543e0fd01f420d8b11c81f12c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980207a7f699438099294d5d6815ee01b2", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a291c57efb9fc77ed02316f8c80c414", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/ios/Classes/FPPPackageInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985aa87652e49a1e308e9a9aa6db8c9500", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/ios/Classes/FPPPackageInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984719af7ffb3f5349e48dd4a4f4cfcf16", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980788745803ac2c2f473fae427920592a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d64fc9ba9388fad187979657abc8d4c", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98845335c59d54f5a270d90eb1508828c6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ba2976c02909d1ecbe3b6cd968319f2b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a02c13f809ab007d52254f379e25733", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984219f3b88460136a28286dc72063f49c", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e41caeedbf15aa70def2649df3fb189", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d7fc352ece4982afd1cc4962076b7bc0", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a873e1a34cc25ae4b16368b7d8478675", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bacfaa798703b1479caca64cf99f9d8b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ba9b910a2902d5c7af6781f430dd7730", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d7503713b65c7986ae4323a8849d3b0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a938373850bc6b2c902168e3644c2df7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98efe9786e762fafb50cd364f0cd243d73", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9846607b897d5e7bdaeff78a7f95111b3c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987ced2312c025c8857f005276faf026bf", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/ios/package_info_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98071da8cf2af9d07bc7c8153209e6394e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984958460224691efdd37c5a90bec745fd", "path": "package_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982a4cdb01843c1eb0da2ca39888c79fc5", "path": "package_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982414aad780282241669d45a0ec697629", "path": "package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a83540f1a3cbd81bc15cdbdae827cfdd", "path": "package_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9818354a426c82e4d0655e15d475e00f8e", "path": "package_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9830831800139642223316e9d2cfd355cd", "path": "package_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98401568e7d79b5d445a354846025eece0", "path": "package_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dba770408ebc7ffc07c77021e56a9f9f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988bfeae0cd1bf52b3b37affabd626cf81", "name": "package_info_plus", "path": "../.symlinks/plugins/package_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9857aca3693bf81de26ee3983afada49e9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d8725e8da06371709ac010293f7f3be6", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d3dd9e949331bcbfd3334a8926469bc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf6a3591c89b111ce7c30edf8cddcbcd", "name": "rive_common", "path": "rive_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98516ed36297d24e513abdd1f0c13a5d72", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4fd39bbcd597bca2ef80bc3c11f97d1", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c06901b0ca0837e6b2d6e8d296c3aa9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de9b905e56dce1d4567c91abd7db9e6f", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd5d7d90bf17721fbd306f8b318c9dbf", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98762c78135bcea8e383ea6fb1176111bc", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98525e7733cb22154d866c3e6c4b39a8ca", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e982fe1a1308c6cdd8b5ba3f9640355fe45", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/common.cpp", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d4b560ebf130154d298fb2f3deacec2d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/Classes/RivePlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989482c57a28a17344cba249750d2fa6d4", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98047fd5106c8de8b85c925da4f8eac7f1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-aat-layout.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98821bd9400a891492a174e00eb6e46a73", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-aat-map.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e987753233ee7c43a1cf39ccf60a8c24b9e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-blob.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98e10eacd1654842f8be5dab26a4c53a95", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-buffer.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98ce2454eac627f6a1f59cf86592e66135", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-buffer-serialize.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98bf91796e2598b0d6d631abe185d68499", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-buffer-verify.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98de6063b2efdc2ca15d93ed0e4aa4125b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-common.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e988b423d7013a4f7cc60f02ac8b52bb2e6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-draw.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e981fe0b28e853f8de63c941b7150766f02", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-face.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e982896b0c65fb8be7643859b314873aab8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-face-builder.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e989fa4e8e20d92f03c10c1ba73258e1c66", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-font.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e988891b051b3a72cb0eecb88dab06bcad5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-map.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9859919fb1423dbbfdebe5ac25d41ffaac", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-number.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98d92ece10b6cdf07319512356cbb8fbd0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-cff1-table.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9803f9f3bf15427ff50bd4d7233145d11b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-cff2-table.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e985cb8e09ac50a0c491779f98c18819a89", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-color.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98791ca7cf97131f30421917b4237fa8fc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-face.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e989a9bc0af46010cf09240ee6d3eca98b3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-font.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e988aaf7173c61e683871e3afdd3b40783e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-layout.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9825a5a6bdc3c90b5866435fcc25c3cbd9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-map.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98b81356f8afc04928e60238447da4758a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-math.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98a76f6bbfb4f418b597056e139018d5c6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-meta.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98cd4992e67f262f5acec0c691aef0b0b5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-metrics.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e987f90206a17ac146a4dc25ed0e56a91c9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-name.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98477f4208fcf014d852f96286fbc8e503", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shape.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98836afe613d2bfbcb57f6198cae2bf0a3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shape-fallback.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98d65523b2ec49aa302cda56aa728b62aa", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shape-normalize.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98a7aed0575edb3e7793eeaace1cf15950", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-arabic.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98ff033dd6adcf5a52fa14523c12eff165", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-default.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e985959ebddb87419658af342c176070964", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-hangul.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98d490835468d22e1c937c0ea8efe0b057", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-hebrew.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e986aca2d5c4fe4c8ec511c1444ce07d3a4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-indic.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98249c863fac6c95b4132ddb7bc71998b4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-indic-table.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e984745594fc553280ee36985ffd4844f46", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-khmer.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98f594de370f976663dbc5e89156a17a03", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-myanmar.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98ad1aa9c448615e94fc4721cb7ef2ac60", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-syllabic.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98544c57f1d25b8a24180c9902ace86642", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-thai.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e986b94a1023bcdd1d991bfdf67d3183465", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-use.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e983059d57e5c0f88121a6f9ed52256b31c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-vowel-constraints.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98e3f513a12cfa6a56b357051ab9065927", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-tag.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e989bd2d34da4c3b68da6c0f1df7ebc94c1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-var.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98a8d504fabf9e030a228eec0402702aeb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-outline.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9833332cdd5b541f60048927b07dc2164e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-paint.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98dfa88ff8dd5056a9a4d844148acc4f3f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-paint-extents.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98f699fb33a4910e065c2a6b63870024bc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-set.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98fba8d82021364a8fb0057438e8de7030", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-shape.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e981f6b1265693cc51b7a8748686aa4a645", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-shape-plan.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98f1892028bf8f5f553b965c6d5f527bda", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-shaper.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e983b386e07dc1f767172e1a3aa599abf09", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-static.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98d9c8431f414a741a5bd9947d4354bf19", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-style.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98c3fec798c25804fd17ac305999787b9d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e989b3559248901d1c3a7222fda9279eb07", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-cff-common.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98fabe7d9e1e9efa3e70b5fc54af1fdc41", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-cff1.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98e332f2e859736507c455cfdae98426be", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-cff2.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e986a0bbfeade12e817d63b6f40a562dca9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-input.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98c4bb65c59cc03819f4029ca6e723bf8e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-instancer-solver.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98ce25b091e297d66b3dfa5ed3eb75fbe3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-plan.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98cd8e45a6c5fd3f8d203caf91bb865d7a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-repacker.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9834e3ca85efd6e19d4561d2081c134478", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ucd.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98dc9f65acc048dc9ff0f4c4613c2d1811", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-unicode.cc", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98601c8a88e271521839e36c913c0683f8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/graph/gsubgpos-context.cc", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98be44ccee8c9af49504836f2611df74d5", "name": "graph", "path": "graph", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98065416527c38ffdfb0ad062b5a85b683", "name": "src", "path": "src", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98192739923755f85688dedbb9131141a4", "name": "harfbuzz", "path": "harfbuzz", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98b60122683ac268b38b3f6a6d3030ef20", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/renderer.cpp", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98a930d523350f891502197f238efaea05", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/audio/audio_engine.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe1b2414a282ab15dabb76c2b1b81f8d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/audio/audio_engine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e983e7547dff40c765d3e44574c378d85aa", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/audio/audio_reader.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98f52d3aea1104e78298517cb5083975ee", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/audio/audio_sound.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9882bbb778a74506aec8ad3c8abd669ccf", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/audio/audio_source.cpp", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988aabd3882f36e2daa029ef85178c3401", "name": "audio", "path": "audio", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98c033fcc413d19e98c1bdfa677259846a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/math/mat2d.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e989a52e1b1fa66e3d32f09576c1785b64b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/math/raw_path.cpp", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e1d355aa6228cd593970a8860df71c5b", "name": "math", "path": "math", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98f9d2bbcd79e20f8742f5ebab75003d4c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/text/font_hb.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98aefb1579c76f5305543e95a5af3ebb57", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/text/line_breaker.cpp", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d430f9a1f02a85f0324cb49e71688274", "name": "text", "path": "text", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a419432f68cf7eccc9e461cd03c907b3", "name": "src", "path": "src", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b4daf55d2e4c6d53e1425932a703aa2", "name": "rive-cpp", "path": "rive-cpp", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98bea2608a75c5dbbf50985bc9b9c9a8d8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive_text/rive_text.cpp", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e2e536cfe24257991f42ed1d6536c5", "name": "rive_text", "path": "rive_text", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f3aa146fffa1974a01679b666f0bb3f3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/SheenBidi/Source/SheenBidi.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e09274cbc3b432fce882dbe0f0f69767", "name": "Source", "path": "Source", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899ba2d96c4e9af0d24056351653c0938", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98e917163f1e8f53cca1774028141cca0e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/log.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98ec392a5de269947a9913382dc87aa73c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/Utils.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98580c300aa251b0cba0f6cddd5b732062", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGConfig.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9835bec0128be743eb4684aac47752102b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGEnums.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98ada42a524a868353823a75abcc887ed6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGLayout.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98b22d5575cffd60d82ae3838017f4881b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGNode.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e980ca4a967bab2398813d14b093df0a22f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGNodePrint.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e987934ec707d143cba5405ffbb6b0102b6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGStyle.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98b148808321f8fa13c7e5edfcfc1b7f64", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGValue.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98a2f3af399ab45003a5f96e8551f07017", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/Yoga.cpp", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98f6f01817d5bebdd943ad022d161c44c0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/event/event.cpp", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bc0b7818216e11b910c9d303188cfe1e", "name": "event", "path": "event", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985bf8af24cd94185c442ff51e8c3ef4eb", "name": "yoga", "path": "yoga", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980560af1f47335fc25a6bf5d9facfca68", "name": "yoga", "path": "yoga", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98118458bfbeed4fa6b1bb4b4121f5ed14", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb97590e81cc457c849e300e25df499a", "name": "rive_common", "path": "rive_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9813ef48cd03c45d5f695707b2b16231f1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98229d9da3c276bda7c0bb4183f9d2edb7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cccabc079e4b6d10a9700e890582e922", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9d586a8a19ac9e16849445e6b0c5678", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4fcd21e4e0c1db11705b8cce17ccf7c", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa3e4646dddf62d13eb889e75519a124", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a19287ba1723b9dac7cb1441bc4b32d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d7025e93b610eb714e83b64c63cc61f6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981afd149eca9bffc73a39c2ffd3597436", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c3e13e6ec03c822941a11852f5dee6e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6b7711f005e7cfb1e153146ffa926b6", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9817439e627cdbfcecd94c380ac3c694ba", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9828be9f241a1ba408282796bf3bd377b9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive_common.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a03fd125e95324b11f49bc6f084ec47e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d3281ba50705a34c7d926a4f1687d0c4", "path": "ResourceBundle-rive_common_privacy-rive_common-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985ca29c2e3ddc0356e8821b4948edeedf", "path": "rive_common.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98675e171c32e99dad988a778fadcd9c2f", "path": "rive_common-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e55214d47c7ddd6e5d5bfb310f0b12c0", "path": "rive_common-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98feed0a1d24d53ddf7b8680fb0d4b3fe3", "path": "rive_common-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ac7f42ee129229c9d4f08f7b219ce733", "path": "rive_common-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988a4ea4acf5aefca6da2778e13accec24", "path": "rive_common.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a59ce6597694e4a750821e9739006558", "path": "rive_common.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98790751c3f91e8c5833abda9a6d791731", "name": "Support Files", "path": "../../../../Pods/Target Support Files/rive_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e8736414b3dfe44fc028927e1224f11", "name": "rive_common", "path": "../.symlinks/plugins/rive_common/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9869295625adbdc4e552056a0a1f0e6453", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fa65a660390cbcc496137ea3950423ba", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f55dd8a70d3b062aa8331585100f7a62", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be85a784e0ca9b4a7294154d18c6c2d5", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852efe99a0e9b19dd5099c5cba71aaa44", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988dc949796176dc302ff58564c3f804bf", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6e1bbbb6e26cd5f424c3b5a580a7af1", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985021749e1bd06d137003c2f7b32b02a1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889502063cd95966357f4110b0b8afba9", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98736a21d922c9fcde0c62fd1263388757", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98abc039f7cfbde8ea90908a8d44a2274a", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4b2bd409c5a07853638838fdcf045f9", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9819926a0ae399b252d3791aea9a935c7f", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc2f1e4873317ed577556a8846b00101", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f08c6dbcb50a8a9810d32635d7ea5de9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9889e81c3a52d07ede73fe82633810bcde", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9873ae30fb39b5e55ee39445b006f8bd8d", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98097455aebb81bebf3ae8a4f511ac76ad", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4b69b48d300a068255a687668932cdb", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896ef4e13ed1956fcd8667a6cd44da070", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a00ea683afe750ca9193828079de6d7f", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e87582c223724ac81cfee44eae80de8b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9817030adc99465a3447e196271ca46f81", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894c6c50fe09bf0690ecef8afcd784beb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc622439ede2c81ef5704e4f0528b080", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e067695172e4091aba5f544f532482", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848ce4c9b4709b20cee93b61bc6cedb26", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986086ebe8dafae4aca22cb7cba36c428a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987977b857d64c3d0efe8b78469d752de2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9fd4e3daf90a8093169f54c760856ba", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98138b5df71e30911c2df8a97e8db07f78", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4043a483ab35cd705e97dd8f895660d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985177a6abe61aaf6d80808ed2bce955d5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860969c6a2d5720ff8bd38c156f72e7a9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98363efd8850590a137d145958b9003190", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9898a351fcdf473923c419a2967605f22c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9853178accf295879090276f404fd18787", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98569f82c7873bbec2230e6c7bd83538bd", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c0cee0a25a16016ebf99ededa845d4a9", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d5587ac35967b90d87ab9e59bf6a519e", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988855f8a51ee8fd1d2e1bd0e6a4072b1c", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9803dad173b631d83e26dccd2863763338", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98834c4dbe190a4d2bf010f7d12eb98c9c", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9839940466cbb9dbc1d95951b37bea96b0", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e48e0f9055efd1a9e66a3450bf110a77", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98be1383f6f5482af47e489eeb11aea8cc", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986bf290707f3213d3d34540a9f9bb1350", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef74c7a896d4e47bb6bddd21e4ae0fb9", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a62991ef59dedd15b0205f3231df01f1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithAppleAvailablePlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9821706a7d832fd133af52e7e4910a6a44", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithAppleError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e032fd72e4b4139a2109c6d7781efdd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithApplePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fd4575193b6eab5413af9429dd455eab", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithApplePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984e6709095d8791f5561cee2743862fbd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithAppleUnavailablePlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986880ebcfee97846bef5385152158d962", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SwiftSignInWithApplePlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981677d97b1eec5973fd4c681e7927d015", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6c09c1886277415e133f9c68a000c67", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e386e6bebd8ab96da34e9c4b0a35100", "name": "sign_in_with_apple", "path": "sign_in_with_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbf85ff8d478cdc2730725076a4e689a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98244a297f2926cf40c37697321716591f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7068370a4363efdcc58be9ddbce5f92", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ba3193b6da84b05041c94a47fd856b06", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98856ce1e782bf4135ab40f83dca116545", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875a05f5b2a4b09128da2f6ee3f3b77dd", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d34c61caccabf82f6a92007d081ee97", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc6d3369eda00fab8c9e122cde9e31c8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989475cb40c6ea85901f61d90fa41341ca", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d436e0c0a6fa0054e501e53c31fbf70f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa569ffff411b747c40f6fc1ec4c94d7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a3b89d89cf33bf8bea8883363a1e2383", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e988038ffff701ce813c01a5b8ed7a43153", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b2e632504ce4a6417b616367f7123f8e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/sign_in_with_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984cdd1f3257d31b151ad8ae9f4f01a655", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98032710cd84943498a9d7eb73d85dd079", "path": "sign_in_with_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989e1fbe133bc4a4cd8a9ede7c43b4b7d3", "path": "sign_in_with_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f557006584fa201da952779933116650", "path": "sign_in_with_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c39ac2625b7c7bc38857cc8ece2f0c6d", "path": "sign_in_with_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f333de5da19a394997a82d41a40e6c3f", "path": "sign_in_with_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9893cb12b6cc76dd2b73b8673930f7c040", "path": "sign_in_with_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987acb15252422c82a1398a17560677a23", "path": "sign_in_with_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982353e04bf75d91313ba05765d515f07f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sign_in_with_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bef5b50348adcb38922c6957e97b0d61", "name": "sign_in_with_apple", "path": "../.symlinks/plugins/sign_in_with_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c42a69c24ce4ca1a938b86e2acb3789e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989d513b42e4b0a37825ecf06b5516696b", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984217349339626ef3b077f2ecf04a5044", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983fb38aa3eb35b1c8bfc64eec9dcdbc82", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cade7d5646e1ad8bad4a0c6d35f33a66", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985507200843db051e08c748c5b0a5ad12", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985762c03e3d6071ecf1fd9c31e4150d61", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a307652ad33a60fa3221247c042fa2b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e14d27fcff86d9934fdc9be4564d2cd", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa3be0e49d6bcbce84903ffab9e8d1a5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9d95428e44a889d4af032f89513c53c", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac0fe256d553132e9a751234604b7896", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866285440c26d10c05bfd605a61d8f0e5", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839672b0b11279a242ccfb2a6425e6b22", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be36f81c8ba2c39e8586e909437b53a2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e336f960d19f8d8e094e39d84e6e7901", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ac0fc27e98abf7b51bad603068acee2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9839bb8005da730bf9fde6bd9568e586ab", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d37d34db8d3c4faa3fd4bfc3a999e8eb", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c7816a3892dec37ef77af194b8c02ad7", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893af0c27f089bbc8284443cca8f4d599", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b127bdbbd23574e6c997eb218c3de757", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d2692627de49dbb8200eae8a643a1107", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c8ce1c9de851d2247b73ac52bba5d68", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98686843bc3026ec8072313a817e971182", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98544b267f2410844b877e2bcf90259e57", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9811a3ad27250c7c12b7185c392015f06a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c7debe77a9ae989d60ed91ffa1e2031", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9836244f53feb3b88c9e32034900e91675", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d70fac231a4f1bb174aadc7dba234551", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a8b02423422e954e7edb69aaa5de0853", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98061790d5254690e3c62ebcf1addb5f40", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a71a7fa4f17efc59ce133542ee2a122b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989fdcfb82c913a1bf34d2fdd23f6ea455", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98970063afb4221553620808015e0e7703", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982bc4d5148afbb8c744b3f939efafb8a9", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98464321ae2879726d130ac780ad04ea7c", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e0441d42997e59f55523167d392de7d7", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98050d5a2baa0ab396eac818c901d3dd1a", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef452e2c626bf05cd066d32e891742e5", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2185ae7ea854a5b1784054e732bacf7", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4c02c6ee803325aa8a2fa0bcfa893c7", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98868db1b5ff46428cb8b408f9d683f701", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867b6e8c2bc0ac8907badcf9be71f0333", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98408d971d4063728636689a09e5ab325d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c29dce8542f4f8e44b2cc2e44af23f29", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aaf09d0b360238da925174001da0b8a9", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b6dd0ab0f7da8603102eef654320648", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e851ccfa8e718f11cfe1f2e998ac9af1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984da34abc658c2a3cba41a1899e12c911", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834959d1ffe6fad63135849b27d8d0f4d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f9126a719b3bda97c051e1338076e15", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985a8b5de511bb2de4d246cd1dcc47b909", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e8857d2eaf2f074aa51a8353be44fa3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987af79e23e789f1fc5a198b5d1ecf79c1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989013853cf36b9f968fd38eeb290ca306", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98b03bfc719016ace3cf256a53484507b5", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e9899623a01fec3b6fee6492825889405a7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98abaa5829bf223ba0e8a3fc2401651966", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9805516c47aa6888b8ecdcf0c18ae6f1c6", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987e49529cc5ae4b7891462c36da02aecf", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e982dc985ef8c03c7593077a0ab5e9b3e32", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981361220abeb47e2b0a402f1882d5df00", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981706b935880a069e91d45fa5f6225fb6", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98948c76620e0731e29af5123ae0a8e278", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d84c5c60f06442cc02916cc4cb6005e", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9878468445af06cf3d315b9e2183d7fe26", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98563b7cc6919ab2a2dd60a3e5a1fb8027", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b49dea0d13bdcf88a72d743d73eeb0bd", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811e94507895974169529c27c718610a1", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880edf33a9ac5115567ae48c186d9ca7a", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98f682db8b9537d169509768c065a6cc9e", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreGraphics.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98df8ab8f5eb777d3551c3bbc04edf992e", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreTelephony.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98f888991ae5b24f9f379f21bce18f2a85", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreText.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98b13654359f4e9c63153b5fd1f8fd8617", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/LocalAuthentication.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98e9c353e05162843dd115e1ee3b6cfa1c", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/SafariServices.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Security.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98e28c510bcdc3834514b09ab79a067e08", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/SystemConfiguration.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e985b194461b3d65889636e9bae473ca683", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987a819a33b096257e70fa923828343a29", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f57df5597ed36b645cb934c885be56d", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983bb3a0ef109cb557423194e0782bb1f5", "path": "Sources/AppAuthCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c0e7d8f49a1998e86e64154fb93c38a9", "path": "Sources/AppAuthCore/OIDAuthorizationRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98994bcaa5d8f95d139bdb400a6aa3ac00", "path": "Sources/AppAuthCore/OIDAuthorizationRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e77241118b97876322a4d418e2addc3c", "path": "Sources/AppAuthCore/OIDAuthorizationResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b9a591e6824aeaf91b4dc3413afc4b6e", "path": "Sources/AppAuthCore/OIDAuthorizationResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6a031942358ea701add2c403753067b", "path": "Sources/AppAuthCore/OIDAuthorizationService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983722ad97173e544615e297f38dde69fa", "path": "Sources/AppAuthCore/OIDAuthorizationService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981e421ccc6ccacb40f78dc1562c16b08e", "path": "Sources/AppAuthCore/OIDAuthState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986fe87a683938dfe8ce8bedc70a77e299", "path": "Sources/AppAuthCore/OIDAuthState.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b88a7e7941c3afcbcd13a0d9bca1d95b", "path": "Sources/AppAuthCore/OIDAuthStateChangeDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989105341b532f0f8bcbe3cd1689df11fa", "path": "Sources/AppAuthCore/OIDAuthStateErrorDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c26c0a44a51297a26280367d77b71f33", "path": "Sources/AppAuthCore/OIDClientMetadataParameters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981277822f2b7f5db583f1ab5ac9c0b754", "path": "Sources/AppAuthCore/OIDClientMetadataParameters.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ee6df9e90db221c983961bd683fc899", "path": "Sources/AppAuthCore/OIDDefines.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f9b6e7f878847ce5dfe966ff57fad49", "path": "Sources/AppAuthCore/OIDEndSessionRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9881ce3c2463d159fa53ac67f41592b5ef", "path": "Sources/AppAuthCore/OIDEndSessionRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9889f4c06aa0818422fb53db1309b83fc6", "path": "Sources/AppAuthCore/OIDEndSessionResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aa7467cf893f36fa502d42be2543e286", "path": "Sources/AppAuthCore/OIDEndSessionResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b973bfdc104f8903a6e856179c05170", "path": "Sources/AppAuthCore/OIDError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9807e0e373c9b33a98d490134a0684e024", "path": "Sources/AppAuthCore/OIDError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f98e92beecb32c4bffe2b92d61efa7ae", "path": "Sources/AppAuthCore/OIDErrorUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ce4080bb17c794b2e01f0c152c8735f4", "path": "Sources/AppAuthCore/OIDErrorUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989db926d49da367fa05175c89a12fa095", "path": "Sources/AppAuthCore/OIDExternalUserAgent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b684e61c1ccd666154cf972805a61e6", "path": "Sources/AppAuthCore/OIDExternalUserAgentRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9815a906bb1995b1e8e01593cd32d07693", "path": "Sources/AppAuthCore/OIDExternalUserAgentSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d804d4499e7cf287c227ef3aa123af4", "path": "Sources/AppAuthCore/OIDFieldMapping.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f6cd733c5ac8e02dc081bc3785f2ba80", "path": "Sources/AppAuthCore/OIDFieldMapping.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f2cbe72db83aaafcc8617419f1985460", "path": "Sources/AppAuthCore/OIDGrantTypes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986748d19fa7e1ba79ca4cbb97a1ea7183", "path": "Sources/AppAuthCore/OIDGrantTypes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989bf66abe95e51f3ef1121864bb2639e6", "path": "Sources/AppAuthCore/OIDIDToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9801d4064bafe946a67e683b5cdba8c89d", "path": "Sources/AppAuthCore/OIDIDToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4ed539a813087478147601a3c9b6016", "path": "Sources/AppAuthCore/OIDRegistrationRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98abf6ceb03b7ded0d550d06fdc20c4c6f", "path": "Sources/AppAuthCore/OIDRegistrationRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd9859707c5fe4624c6d6c40ee815e25", "path": "Sources/AppAuthCore/OIDRegistrationResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b4d2f93043c9598ae88d08b4fedadeaa", "path": "Sources/AppAuthCore/OIDRegistrationResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989c21d8c9f8cc321f7944d4a96b5ccb53", "path": "Sources/AppAuthCore/OIDResponseTypes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e506c1858d34d8cc8354aeeb6ced6d27", "path": "Sources/AppAuthCore/OIDResponseTypes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aecf2a4a01c7f3ed5fdb35904d876986", "path": "Sources/AppAuthCore/OIDScopes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98971ae85d7eb6e401a04221edd6356ff5", "path": "Sources/AppAuthCore/OIDScopes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6b5449f925a6daffc2f7a9345804fbb", "path": "Sources/AppAuthCore/OIDScopeUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e8a43ba74868b1b221640d8c17ec8e01", "path": "Sources/AppAuthCore/OIDScopeUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982fec77085fe3d04abb4de5d8af7c6145", "path": "Sources/AppAuthCore/OIDServiceConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989219be695cfaddbe2ceb6f213aae13cc", "path": "Sources/AppAuthCore/OIDServiceConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98069491dcdd2d38adec1064d9223b3337", "path": "Sources/AppAuthCore/OIDServiceDiscovery.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9882a6edc3e3ae4e95d95bad47a73182d9", "path": "Sources/AppAuthCore/OIDServiceDiscovery.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aa4e1b6a90763835f0e5bdb8b419215f", "path": "Sources/AppAuthCore/OIDTokenRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cf4c35638b86a4fae41800d042fba539", "path": "Sources/AppAuthCore/OIDTokenRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b562de250d46c0dbb1254affb91116b7", "path": "Sources/AppAuthCore/OIDTokenResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9868076bea7d98a259a00ced351f611619", "path": "Sources/AppAuthCore/OIDTokenResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983015d6c4d1e801ab352823de4fb82be7", "path": "Sources/AppAuthCore/OIDTokenUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ddb005eb03bb2d5580822df7fe9843f1", "path": "Sources/AppAuthCore/OIDTokenUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870b4cc4f775b77dfd57bb6cb338c2461", "path": "Sources/AppAuthCore/OIDURLQueryComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980b715abc22bb0447d421fa0ec4797664", "path": "Sources/AppAuthCore/OIDURLQueryComponent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e702d4a29e99f3abcbf53e5ac4debf6", "path": "Sources/AppAuthCore/OIDURLSessionProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d602127e2832b17a4a8affd1ee977b49", "path": "Sources/AppAuthCore/OIDURLSessionProvider.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98208baf939efa64d32dd12464284265ec", "path": "Sources/AppAuthCore/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983d52a0ee92cdf16c731cc4eb4ed89621", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c794a9df04e5200b0ac83262fb4dba9e", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a8113ade6524ac73e2d2c5d160a3c9c", "path": "Sources/AppAuth.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833329f4f8ae55a5ffa6e9630c640b90b", "path": "Sources/AppAuth/iOS/OIDAuthorizationService+IOS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e3b5bbcc5437e00be17f1e64c2bd42f3", "path": "Sources/AppAuth/iOS/OIDAuthorizationService+IOS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9891e23a996a422af259b8433b80e5ef8d", "path": "Sources/AppAuth/iOS/OIDAuthState+IOS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985e4ac665bac2469ea793b6752f44f3ce", "path": "Sources/AppAuth/iOS/OIDAuthState+IOS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a929d4180d6e422cd373b06a2ce90dbf", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentCatalyst.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98db6cf973a5aeb3a164a0baa6020db185", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentCatalyst.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986beeb23137d6c80eeeba0dfa5dee8933", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f268ee34ca7fcfd394315593d71195f8", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985a73207a9bf21d042df6e39047f277b3", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOSCustomBrowser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98888cc9f93de5d015a31481c41e170d65", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOSCustomBrowser.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9862371a6156796cf4d6ec60a22349e9eb", "name": "ExternalUserAgent", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d617fa659db5da23f3b5dc51f01623f9", "path": "AppAuth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c6b26c92a75ce42bb67bff57e3a1e78", "path": "AppAuth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f05fb80bf373063d8abb710e0630bf77", "path": "AppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9817c7f2d0d4301601b484455233bce850", "path": "AppAuth-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f315ba213cd1f9357e659ef3110857ca", "path": "AppAuth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987f97e8c338074483dc711259f2296d39", "path": "AppAuth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dfc7c241c9258d65409ab0f9b5ad6341", "path": "AppAuth.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987b227a7ea1923035856ae910a742cbc9", "path": "ResourceBundle-AppAuthCore_Privacy-AppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9821c6c9b82e9cdd0ece05d4491b45f95c", "name": "Support Files", "path": "../Target Support Files/AppAuth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa9554c2e32414f848846896a2eb572b", "name": "AppAuth", "path": "AppAuth", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c2e696a844e3b76d55c99c2ee88e90ab", "path": "AppCheckCore/Sources/Public/AppCheckCore/AppCheckCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a2740ccf5ea14d3cf8e9c8048d6ab972", "path": "AppCheckCore/Sources/AppAttestProvider/DCAppAttestService+GACAppAttestService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983802d098da6072b1d3f718edd5f44705", "path": "AppCheckCore/Sources/AppAttestProvider/DCAppAttestService+GACAppAttestService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985952a0d219d2b8f834283080061b1daa", "path": "AppCheckCore/Sources/DeviceCheckProvider/DCDevice+GACDeviceCheckTokenGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98299b5de0e004139d0852a7effa0984fe", "path": "AppCheckCore/Sources/DeviceCheckProvider/DCDevice+GACDeviceCheckTokenGenerator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989967fab42c0073ea7095f486a4e48cfd", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e4da978e834b0a6d3f83c0d58c920908", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f435da06a55fb938fe63ba6e58c70f5a", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestArtifactStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c6c4bda2ec0f168dbcee531b4d5a6d7a", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestArtifactStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e337a46f2be9b8f7ae0f689e8b262558", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAttestationResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982cfcc1f211e279899887ef0e279ee227", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAttestationResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f2da2fbe9a455db5f4aab32cf15ab3c", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestKeyIDStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac6b977767f90c60f5c62f959d76620c", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestKeyIDStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f787b37c774e43061be69b2fe8ef665e", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppAttestProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9851b9a244c4c9f41dde58c408afdf69c7", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9822e1dc1183f58197086f4b29c5ad9434", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestProviderState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a9683e23352bc14dc3762d7ae927036e", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestProviderState.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98599af5271519a362c42e17af99352882", "path": "AppCheckCore/Sources/AppAttestProvider/Errors/GACAppAttestRejectionError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980972f8dcacdc37646483d829ebf26f2c", "path": "AppCheckCore/Sources/AppAttestProvider/Errors/GACAppAttestRejectionError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986bd71c7abf4cac01ac53f2b3038ecde4", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9836b7f83ddc890ec2d0becfbf17bf55d1", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestStoredArtifact.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f9d9c0500a0f1eafae2056dc4830ae2", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestStoredArtifact.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9853088f95cb56e5add7f10b53ea45c119", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheck.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832d813a8c6e83c56198de1a19cacfeb9", "path": "AppCheckCore/Sources/Core/GACAppCheck.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ad43792a78bebd533c55138a3eb9383", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9824aed41e3d8a8211e7b2345fd9c05a7a", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce6650f13e4f5fea99ff9f7e27dee8c3", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckAvailability.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988098d37a18760e5810e6251141fa03a9", "path": "AppCheckCore/Sources/Core/Backoff/GACAppCheckBackoffWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9854666f7692af0b9c175d2e777226f339", "path": "AppCheckCore/Sources/Core/Backoff/GACAppCheckBackoffWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98901918dc12788c9a18148f3da3f661a1", "path": "AppCheckCore/Sources/Core/Utils/GACAppCheckCryptoUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b640bdcd835ca499d2f3cb1b11c4136c", "path": "AppCheckCore/Sources/Core/Utils/GACAppCheckCryptoUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c06cacd4b2c020084f7d6d444caad16f", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckDebugProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a46a855b366f900a17cdff445c53ccab", "path": "AppCheckCore/Sources/DebugProvider/GACAppCheckDebugProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98394e3a1647b53c05319f8f884f6cd63d", "path": "AppCheckCore/Sources/DebugProvider/API/GACAppCheckDebugProviderAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804d11c4c071d0852f44ef68f87860ba3", "path": "AppCheckCore/Sources/DebugProvider/API/GACAppCheckDebugProviderAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98553fa1585b79187e8f9e2637f9c65854", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckErrors.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988b7d873e55564a072f3866e77678adec", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckErrors.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988046db50053a8d9d5c11d690c9b22f80", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckErrorUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987e54a9131eb868c49da71917ad2449bc", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckErrorUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e83f33811274f46ae91574bf590bbbf6", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckHTTPError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983e188624cd92a3c745a492fe6f7a3770", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckHTTPError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9864861c164e3e65d70fcfb2566a9ee1d8", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820a4974140e42b3d4b7d4a4e101fbe3b", "path": "AppCheckCore/Sources/Core/GACAppCheckLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989eb938ffbf30bad66de44807c914bfed", "path": "AppCheckCore/Sources/Core/GACAppCheckLogger+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ae193b6f2310fa23538feed1e09607f", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce34cd154f7a3a0021fde39be25f474c", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckSettings.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9877821fc8c0e374933f1dacd42c7bcc99", "path": "AppCheckCore/Sources/Core/GACAppCheckSettings.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9879423a73f65a274f6ffc11d10ffb5b2e", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984b4555b1137007434667b06b7687a537", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985135b86391ce9d448f530db849a5e22f", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988dc07028627d0f4669dfedcbcce184fb", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985dadaa4e640bbe9ea8c4d072d7560764", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken+GACAppCheckToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aae8f86f93284b2c4efe2c87d10c4267", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken+GACAppCheckToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5649ff2dedb3e94e3ecb460c622e111", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTimer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9880db3026f2cf55fee66fee72243eb6b1", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTimer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8d4d1e489209191f7c6feb2a46166e5", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982afdbaf54739f4dcbc71f8b7b93c958d", "path": "AppCheckCore/Sources/Core/GACAppCheckToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980fc37b5ac045be86db281fef07f443c1", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckToken+APIResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9871e9e4fd7fac37e0f507756ee67932ea", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckToken+APIResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98505d64d905e928c636df4ffd7be45961", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckTokenDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985806c99bbc86e0cd3edff2907769c633", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefresher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981ef9f324392680279a5e8e3833ecff51", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefresher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f1d86d815f850a564225191c985ae0cc", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefreshResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9825b7dcb2c0f71bf272895c21ec0cf66d", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefreshResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98916e49dd4b48bbfd681344c142b3b42b", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckTokenResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982f800cda99bb1d17e28a1a89e5e12091", "path": "AppCheckCore/Sources/Core/GACAppCheckTokenResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d01d5f0834e12c5c1048e9416e3aa365", "path": "AppCheckCore/Sources/DeviceCheckProvider/API/GACDeviceCheckAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe42474971bfc9fc5cacf2537e9695f6", "path": "AppCheckCore/Sources/DeviceCheckProvider/API/GACDeviceCheckAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983408354f3c1c20570c89a3dd2fe8b8c5", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACDeviceCheckProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ee023309a36225728856014b5b01b70b", "path": "AppCheckCore/Sources/DeviceCheckProvider/GACDeviceCheckProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bcb04a769a2d91fb17e048bb679a16d3", "path": "AppCheckCore/Sources/DeviceCheckProvider/GACDeviceCheckTokenGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b5f89aa8fa6efef8766aafa96a6ada6f", "path": "AppCheckCore/Sources/Core/APIService/GACURLSessionDataResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f7250a01fffcaf102af521b567f2761", "path": "AppCheckCore/Sources/Core/APIService/GACURLSessionDataResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980e5753250fb3d421a6a6741e97912c88", "path": "AppCheckCore/Sources/Core/APIService/NSURLSession+GACPromises.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983654709b62eea887983f48281bc39f47", "path": "AppCheckCore/Sources/Core/APIService/NSURLSession+GACPromises.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9816bd792b51f770d23a2ee13a56593a61", "path": "AppCheckCore.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3a2692b816542c3a6ab0ad293d48859", "path": "A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9813ee75c28da1cb89a8468dbd8f9168d0", "path": "AppCheckCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821b9d557d5636b3bd8387f9873feb06b", "path": "AppCheckCore-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984b912ea3eb8b3d2a2dbfdd3bdca72298", "path": "AppCheckCore.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989f1eedc46f240487faf150eebcdc6688", "path": "AppCheckCore.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98628dea1eda09e6ebab869770da41c457", "name": "Support Files", "path": "../Target Support Files/AppCheckCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981ac103e33c71ae23c1a69cb16e013801", "name": "AppCheckCore", "path": "AppCheckCore", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b266c4afda84c44d2ea06cf05624e1f5", "path": "CoreOnly/Sources/Firebase.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9877617628d1c982267cd9223970966e59", "name": "CoreOnly", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98635ea2bd0f341ebd91fb43e944a4fb06", "path": "Firebase.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985035cc10ec4d2b2d4b8c2a967c8eef38", "path": "Firebase.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983d15f17877e877629ef4e7b790e971e7", "name": "Support Files", "path": "../Target Support Files/Firebase", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbabaf1c386a56fddc8e47132d9d0896", "name": "Firebase", "path": "Firebase", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986cd2166349a965a8fd461a551e1da5bb", "path": "FirebaseAppCheck/Interop/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cb79833931d9412e3ab53ec48a2c9db4", "path": "FirebaseAppCheck/Interop/Public/FirebaseAppCheckInterop/FIRAppCheckInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9843afa9d5d0bf69685a16db84a33e883d", "path": "FirebaseAppCheck/Interop/Public/FirebaseAppCheckInterop/FIRAppCheckTokenResultInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf32432d80ea5140880b74d9681907fe", "path": "FirebaseAppCheck/Interop/Public/FirebaseAppCheckInterop/FirebaseAppCheckInterop.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c41b6d61b4ff0fa1587bf655032c8b43", "path": "FirebaseAppCheckInterop.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984e89cbfb838c1988d363357e39a384d6", "path": "FirebaseAppCheckInterop-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f60651731af2a27e060566ace6f0d0e1", "path": "FirebaseAppCheckInterop-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980168dd0b4182cad8ad950b89936a1d89", "path": "FirebaseAppCheckInterop-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b70e49a5cdde44495b5eb9692d73f9e5", "path": "FirebaseAppCheckInterop-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a74db41bcbd8e0e6b21b899aa59a4e61", "path": "FirebaseAppCheckInterop.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98cf18c14a7bb9e58ae4eb4bea1ac1afe0", "path": "FirebaseAppCheckInterop.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a12ddabbd4fa55697ce41f48f7d7c224", "name": "Support Files", "path": "../Target Support Files/FirebaseAppCheckInterop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db848babd906f23982bc31992a7951e4", "name": "FirebaseAppCheckInterop", "path": "FirebaseAppCheckInterop", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986d4dab0f3492508e795e5acc7d7270da", "path": "FirebaseAuth/Sources/Swift/ActionCode/ActionCodeInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e87eef9df69b466a648307b99383cb05", "path": "FirebaseAuth/Sources/Swift/ActionCode/ActionCodeOperation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ebd7f3f4129da8f96de0386d031408b7", "path": "FirebaseAuth/Sources/Swift/ActionCode/ActionCodeSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988839fff162b4d0022a7faa1876573fde", "path": "FirebaseAuth/Sources/Swift/ActionCode/ActionCodeURL.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ef9036508897c6d5e5c87092a49d48d6", "path": "FirebaseAuth/Sources/Swift/User/AdditionalUserInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d28c4f1ac54e52c98f01d481e7277d03", "path": "FirebaseAuth/Sources/Swift/Auth/Auth.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98009fd7053e58231d3be4af7d5fb3d7a6", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAPNSToken.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fed2239c3369308e2783bbed9773562a", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAPNSTokenManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bb846d97839c7b5f03a243ba2cad291e", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAPNSTokenType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98972007a1d602ca7208cc8c9079b34c60", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAppCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983c34e48674b81c06b0581d70246c32db", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAppCredentialManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983597eb7aabf399852330c83519f05676", "path": "FirebaseAuth/Sources/Swift/Backend/AuthBackend.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984eca9517035242ffe64e6c873bb21489", "path": "FirebaseAuth/Sources/Swift/Backend/AuthBackendRPCIssuer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9857986c874885182a1006d5da9611f471", "path": "FirebaseAuth/Sources/Swift/Auth/AuthComponent.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d9ac1d6134d5d8eef381c9f6676ad3ec", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthCondition.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985256e6d8b1a3c7560c95c15ab50ae485", "path": "FirebaseAuth/Sources/Swift/AuthProvider/AuthCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989f5dd0ef6bbc89cce6cdf1e065dbc426", "path": "FirebaseAuth/Sources/Swift/Auth/AuthDataResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c43aa92408c08267e552babf218e4126", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthDefaultUIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9869fb38c0f3b0b6fb1359491fe257aa3e", "path": "FirebaseAuth/Sources/Swift/Auth/AuthDispatcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980ec16f2900064965a50b4a19a163c57b", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthErrors.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98647abe64015765d7bd1690800b0dbd1e", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthErrorUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986d701e68c7fe38bbb0bac0a3ad89ff35", "path": "FirebaseAuth/Sources/Swift/Auth/AuthGlobalWorkQueue.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9835255c35bc15735d4a18354246d119f5", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthInternalErrors.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d23a3a68c6507f5c0500e14dbdc9d4ae", "path": "FirebaseAuth/Sources/Swift/Storage/AuthKeychainServices.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98884f26918c1d244bc67d1e3697c64072", "path": "FirebaseAuth/Sources/Swift/Storage/AuthKeychainStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9861a42e711f5c8dc794a6cb784cd24795", "path": "FirebaseAuth/Sources/Swift/Storage/AuthKeychainStorageReal.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98602a4acb2230629108b3e79814d92882", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthLog.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cd767296acbfd42a357d7ae6ea724e34", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/AuthMFAResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b96843e95034f2d778db39d4b54a52bd", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthNotificationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dbc6ae7388ed9172d0f151bcd99dfdb8", "path": "FirebaseAuth/Sources/Swift/Auth/AuthOperationType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984bc565874eb22729a1e1824ea9391048", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/AuthProto.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c2d55fb05ae7101b8923df7ab6a2007b", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoFinalizeMFAPhoneRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986156e3900f4a3b0491c2e6e1daa753b5", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoFinalizeMFAPhoneResponseInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dcc011a8f06f0c050e775f8f288e0a45", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoFinalizeMFATOTPEnrollmentRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988e19b089617bd7f7d8461eaf080d3f17", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoFinalizeMFATOTPEnrollmentResponseInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982995204e9d3e795a117e7a0c3f6f4745", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoFinalizeMFATOTPSignInRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984b13c8013d69ebd76693d97ac3935f74", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/AuthProtoMFAEnrollment.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9843d58e2711a20d54ddd43b1b04fcfb90", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoStartMFAPhoneRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b08eadd55aa7195fa19896b28f24a961", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoStartMFAPhoneResponseInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f61f2e1914f81c3e78b03c4d78b5aabe", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoStartMFATOTPEnrollmentRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989b00033635de0647d882975eabfebd1a", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoStartMFATOTPEnrollmentResponseInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9816bdad88376d7213e92c5cda58f8b138", "path": "FirebaseAuth/Sources/Swift/AuthProvider/AuthProviderID.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98799a1f1943bde41e9414910fbc7af948", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthRecaptchaVerifier.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986c88c9722604d63fa875917448da67b1", "path": "FirebaseAuth/Sources/Swift/Backend/AuthRequestConfiguration.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984124dc16dcaaa97c0464241ea93c3a72", "path": "FirebaseAuth/Sources/Swift/Backend/AuthRPCRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980ef55fc35829db8345b830f98700316d", "path": "FirebaseAuth/Sources/Swift/Backend/AuthRPCResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98002591e031c2dfb9791e130e48125fe1", "path": "FirebaseAuth/Sources/Swift/Auth/AuthSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9848b2edae422aaed146d363346af01965", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthStoredUserManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dabfa921acea7d978d7a8ca9c516ba59", "path": "FirebaseAuth/Sources/Swift/Auth/AuthTokenResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bce8510c0cc661101d34f34853e1a736", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthUIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98163e10359d7b419012075cc06e5aef34", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthURLPresenter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98df4bd2e8a7a86b20ec03e8afb91fa229", "path": "FirebaseAuth/Sources/Swift/Storage/AuthUserDefaults.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c0736a7e18031c82adf373386ed86021", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthWebUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9817e6f87767be33d2bccd9399a44cf2c0", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthWebView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cf6465cccc63fceb700cc47b144a5f04", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthWebViewController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ede65471dd6d381c69a2c73a9e3d1150", "path": "FirebaseAuth/Sources/Swift/Base64URLEncodedStringExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98765e38bf0e1fa9c4f548f902f804a63e", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/CreateAuthURIRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98418f61bea205f901ff66bf67493e52ac", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/CreateAuthURIResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cd5e6da912d86a0a029813be41a5b9a4", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/DeleteAccountRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983ee856e5484a333e4ae9bb8a2af77afe", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/DeleteAccountResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985cc36c763359b9643d73ad2b8cdce8de", "path": "FirebaseAuth/Sources/Swift/AuthProvider/EmailAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b700d6e24965ccda08e32d8f444b813d", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/EmailLinkSignInRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986acb13b43cec91d34d8beecc166e37e8", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/EmailLinkSignInResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98da182dcb8956831eb303570e9869c33c", "path": "FirebaseAuth/Sources/Swift/AuthProvider/FacebookAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f7c9a1ba1ad229717f9dcd9c6f24249a", "path": "FirebaseAuth/Sources/Swift/AuthProvider/FederatedAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f065ef8a5cabbc68698393d7f6ba7d89", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/FinalizeMFAEnrollmentRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9832ec1fce81296032e4646b973f0f59c0", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/FinalizeMFAEnrollmentResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b9b0e99ed15b9398e8d613c75691ae86", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/FinalizeMFASignInRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985f2101e378c41bd1553383c6c42af150", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/FinalizeMFASignInResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985415fafcd28e964d5df6b7af38b24fff", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRAuth.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b48637b1b2a4a5195c29e8687fafa4b", "path": "FirebaseAuth/Sources/ObjC/FIRAuth.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984fd593d5cf3c523fc6c4ddcf8b7e47ca", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRAuthErrors.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981b0e9c8d8a8494ef051c0ecf2a0f5def", "path": "FirebaseAuth/Sources/ObjC/FIRAuthErrorUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9888444cf087ae6d5eee8ce18d84668408", "path": "FirebaseAuth/Sources/ObjC/FIRAuthProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980cf12e5eeb23c45776462fbabdcd977a", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FirebaseAuth.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ffcfe0d83d0d54efe5ebd506405906dc", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIREmailAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989cad0ce85ff44437bd1a2eece823d18e", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRFacebookAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983be7d8b5c87ebf512c1eae784a53d25b", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRFederatedAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893b6055dd554ce74857a3dc78c2ee3e4", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRGameCenterAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981787597ffd7caf25cead629d14e9e629", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRGitHubAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98915b334d7bd9d3df56eb2b8ae8bdc784", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRGoogleAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982cbe8c93e7c01e7ef2d322873d1ce133", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRMultiFactor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98502070a0eb0d9c5f8456b334ab408581", "path": "FirebaseAuth/Sources/ObjC/FIRMultiFactorConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b55595ac30a4162b4f6b0c7dbf2b45a", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRPhoneAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc1305f6d71fc771777deebe9b96ff29", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRTwitterAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a6801d154fa1ad0ecc135a6f467dd3a", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRUser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98df2bbc6aedaed50b6aa25b67a8d37347", "path": "FirebaseAuth/Sources/Swift/AuthProvider/GameCenterAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ee63098ff43e54ecffc4551cf3f6f598", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetAccountInfoRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9819c2add2cd21c25f478c46d907d62614", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetAccountInfoResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986b2b340276aae854dbb6039e94cd4f8d", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetOOBConfirmationCodeRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98daedd0d3eebd5e4fcf5dfe263c872f6f", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetOOBConfirmationCodeResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b6ec5a67888953a8d3d8206646e29c50", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetProjectConfigRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9860f939d075dd49c09ca7a0d9e06c929e", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetProjectConfigResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e78484e68bfbca65a20a6dd65bd53a22", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetRecaptchaConfigRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98af7b59e4abed7caa5b1e1bcd7ba4a44b", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetRecaptchaConfigResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986a86392d5fa59650e885e51c33c1b8f5", "path": "FirebaseAuth/Sources/Swift/AuthProvider/GitHubAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9826e443d69fac04e7f10929f52dd5117d", "path": "FirebaseAuth/Sources/Swift/AuthProvider/GoogleAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986a5437758e3c71f56970d767a2f8c856", "path": "FirebaseAuth/Sources/Swift/Backend/IdentityToolkitRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980108541d8f4638f766c7372bb8913de8", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eed2ce58f4edce636a821b40d299fdc9", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorAssertion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986511fad1f429e3d4dcaaa67d16700c47", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9830e778def0e2909612983b88ca0cdb08", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorResolver.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a7856872328a73685f72d501854c4b28", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980b00df8130f9b0180d27d1a2c227e6ed", "path": "FirebaseAuth/Sources/Swift/AuthProvider/OAuthCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9887f0945094226a0cd7981e5c069d7390", "path": "FirebaseAuth/Sources/Swift/AuthProvider/OAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981e378bf232267d1d73e4d189deb0d485", "path": "FirebaseAuth/Sources/Swift/AuthProvider/PhoneAuthCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981a65678256f8be643129b70d7d9be279", "path": "FirebaseAuth/Sources/Swift/AuthProvider/PhoneAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9840afdd8235cd1b25ddd57d1dc2cd6129", "path": "FirebaseAuth/Sources/Swift/MultiFactor/Phone/PhoneMultiFactorAssertion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e4fa9ea994b6a1ddc073bd57309332ca", "path": "FirebaseAuth/Sources/Swift/MultiFactor/Phone/PhoneMultiFactorGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98de3d1e38d5a2572e8cd780e8af224123", "path": "FirebaseAuth/Sources/Swift/MultiFactor/Phone/PhoneMultiFactorInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98736eb0e2489689ff8d168f9a1807c518", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/ResetPasswordRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98040dd785f89af4176a1c5c413c71f1d0", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/ResetPasswordResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c5135b70ac4ca917eae968e182ea74b7", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/RevokeTokenRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989d549b33f294178ce54e1b1eb3ace3ae", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/RevokeTokenResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98be8bba7018300b4d9646738109b78525", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SecureTokenRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dc8c9022c2fe6affb80ee256d3c509e0", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SecureTokenResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988e4b92a2e6266e2244472625c8dbad18", "path": "FirebaseAuth/Sources/Swift/SystemService/SecureTokenService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9866ae0ce3a2fbf60b4273f6801868a4a6", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SendVerificationTokenRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9848cd3f3783545f9224571cb3c873fcb9", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SendVerificationTokenResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989e9248bf29f8592bf6960c65805ac39a", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SetAccountInfoRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98203dce48f2ec5c0b56b8708ada3e42f5", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SetAccountInfoResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985a7454a5e7b4c86b9f66a7af86e7f34d", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SignInWithGameCenterRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980da9dfc65e8ffb2fc863ee8486b216aa", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SignInWithGameCenterResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9803a0ab2ee6f3fdcc1fd12edefd9750da", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SignUpNewUserRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98463577676d23347d359c929d4cf32838", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SignUpNewUserResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fd333fb18c6792816dfe0bde05b0fa45", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/StartMFAEnrollmentRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b0b4c9190c33360ba9a85bf15720454f", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/StartMFAEnrollmentResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985277d18331b537a00960c8bdcecde2c6", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/StartMFASignInRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988824bdada187f64bbe7a7ca63b7d0eb6", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/StartMFASignInResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9853f6d94baa52f525123a44282120f803", "path": "FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPMultFactorAssertion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9870436079debb5c5020f73364fd2cd6b7", "path": "FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPMultiFactorGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ff03c0e9190c5b186d8b2c8b5c8dee60", "path": "FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPMultiFactorInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e5e4fa7a557521164cf1787d78157e00", "path": "FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPSecret.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9805f24f35ad02686eb4610a2155e67e53", "path": "FirebaseAuth/Sources/Swift/AuthProvider/TwitterAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986175ef55037162b81939e9b8c2669d3e", "path": "FirebaseAuth/Sources/Swift/User/User.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98602184a2ef8d69404475520e14f0df6a", "path": "FirebaseAuth/Sources/Swift/User/UserInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98905fddffc4ad541f70667338ed807e32", "path": "FirebaseAuth/Sources/Swift/User/UserInfoImpl.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9817233cd147cb0d6f84c08d4a38968105", "path": "FirebaseAuth/Sources/Swift/User/UserMetadata.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98405e780dcaaa1f9cf3d8c3b7c072376d", "path": "FirebaseAuth/Sources/Swift/User/UserProfileChangeRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d512ce5daa4ed34eb60a312bf3f76786", "path": "FirebaseAuth/Sources/Swift/User/UserProfileUpdate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9845fd093b03bb92a026fa5975d894099b", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyAssertionRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9800e9fc7c9d29ae4b977f6789fb217d07", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyAssertionResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9866816880a17b13b9e4261ec5a75ed400", "path": "FirebaseAuth/Sources/Swift/Backend/VerifyClientRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982038441e69bf3d902b0d2936c385be68", "path": "FirebaseAuth/Sources/Swift/Backend/VerifyClientResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985eb7bb7dc96a4d147f0be124926f0142", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyCustomTokenRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b0deaf65e46493d1c2fc5fd37049c787", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyCustomTokenResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9828f86bb200408565e7e6b104cda7a8b9", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPasswordRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98769a31e8d7d99f4ab4c8628ce7344ac9", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPasswordResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ee9e1c6ee1d5c3d3d3941b59ecfd971d", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPhoneNumberRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9844386959eaafce4bda5c128b197c32c3", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPhoneNumberResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980f71aa4ff31acddb12dff0b2aa4dada8", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Unenroll/WithdrawMFARequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980cbd11063b62819bafb892478d07d120", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Unenroll/WithdrawMFAResponse.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98b98cf43327e450dc5c9f48f15fd98c13", "path": "FirebaseAuth/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b6d8444640e00435ec0fc04daedcd8ca", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98bd58e4302883d505a7b42cf5b7013374", "path": "FirebaseAuth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cd63cb3f9e5e39cf0d9c496a6537802d", "path": "FirebaseAuth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b7a3e8e1243e58f8198625c3b87eb1c3", "path": "FirebaseAuth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988ab992897b9dc024db98e362f6644401", "path": "FirebaseAuth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98eab6c84da1c8fd736d05f733940b9e6f", "path": "FirebaseAuth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9876c830f004039b22da012a312b9e4978", "path": "FirebaseAuth.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c8641d262d1398430e84a879acf66bfd", "path": "ResourceBundle-FirebaseAuth_Privacy-FirebaseAuth-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98027bdb461467d04ca12f987a3500a538", "name": "Support Files", "path": "../Target Support Files/FirebaseAuth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a76792a996f2494665688f9e659ef62c", "name": "FirebaseAuth", "path": "FirebaseAuth", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982161aae8faeacb34aae0dd4104d649d6", "path": "FirebaseAuth/Interop/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98846d9393db9c9ac16881ef47ddf35573", "path": "FirebaseAuth/Interop/Public/FirebaseAuthInterop/FIRAuthInterop.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98585f3f16ceea602b2ce99eef52fdb4d6", "path": "FirebaseAuthInterop.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985fd9100ef1ddd633b2a5e39f1c7ddefc", "path": "FirebaseAuthInterop-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9832fad7d394e9d7da4b58f04cd0432c6d", "path": "FirebaseAuthInterop-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98369a0ca002bbf5c73806a81115d515be", "path": "FirebaseAuthInterop-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0b70c71be872da3353fef1c59e79741", "path": "FirebaseAuthInterop-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980041509e584c4662f09e7ad91129e381", "path": "FirebaseAuthInterop.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d6dae816f7b09937e6c03a18f425774b", "path": "FirebaseAuthInterop.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf7ca9ed082f41e73b80723892bfd517", "name": "Support Files", "path": "../Target Support Files/FirebaseAuthInterop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853ad8755f1d3d0045a786efb73dc52cd", "name": "FirebaseAuthInterop", "path": "FirebaseAuthInterop", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98371a757a792d97226b1e11d24f15e6a5", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989fef3281f2f5fa6e9a1b0e8dcaee237a", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eaba791926045b31827f6d7ee5fad796", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRApp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98884d6b37a7391d3e33799dab27c93f0c", "path": "FirebaseCore/Sources/FIRApp.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980b0676f60f21322639e5f09885e83fc3", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e194e289b81d1732ac4be81e815361b6", "path": "FirebaseCore/Sources/FIRBundleUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e3703997aa6d0572d9478e1edfa9e866", "path": "FirebaseCore/Sources/FIRBundleUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9811a671f668cc3eef1546223b0a8e91fe", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985603157ff20296b93f47030cd0c38055", "path": "FirebaseCore/Sources/FIRComponent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9885e598943a1d3a0cf89b7fbaf653a665", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c0ab0ccd30d6c560916f59611565f9b2", "path": "FirebaseCore/Sources/FIRComponentContainer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982b35f5ad791f4d046e7b7472c1191473", "path": "FirebaseCore/Sources/FIRComponentContainerInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be80e8e0ddbcae10a79304535af6ebbb", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98956e5d00f408f13895cee5ae79cc9fc0", "path": "FirebaseCore/Sources/FIRComponentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9857f654b327f6c9b521b607d0d82c77c4", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e725dc70f3115126d4dc46b421fcade5", "path": "FirebaseCore/Sources/FIRConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895c8b1dad835ebe84739a5a1d05b7f99", "path": "FirebaseCore/Sources/FIRConfigurationInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982db55048cce73705ee868c5360fd31c4", "path": "FirebaseCore/Sources/Public/FirebaseCore/FirebaseCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d40834350096a5dd473fbd2793ed9595", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e942bb7108a9376577837086dcd0069", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9847f19e4424c9ef968d6d6b33ca965dd7", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5c56134cdedc28b9dca43c28611661d", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98888a6965fe80f3cf0e0312a72841dcee", "path": "FirebaseCore/Sources/FIRHeartbeatLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9811d2597acdb5546be311377dcb10a83c", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3c895d33195686cf7607fa0a004b40e", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98818555ba34e81aada26bae6141e88360", "path": "FirebaseCore/Sources/FIRLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9835fe3cde8635f8dd7e5517b3ce509588", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRLoggerLevel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ed5a659ff0893355d05ea0c0335fe93", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIROptions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ae03b736cf780a0769d264514bf3f30b", "path": "FirebaseCore/Sources/FIROptions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a550ad345f2f3c84add9f9192d21b19a", "path": "FirebaseCore/Sources/FIROptionsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e13c6f06fb0ee8488acb875080775fae", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRTimestamp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98affff77b59640c787ad989e2c2006f17", "path": "FirebaseCore/Sources/FIRTimestamp.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986fb6f2f6ad7c37ed10c017de234bbcbb", "path": "FirebaseCore/Sources/FIRTimestampInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984cbd0e2d5c742fa2917b6f43e5174df2", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRVersion.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3b4c74929390a4977401362ece2d468", "path": "FirebaseCore/Sources/FIRVersion.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d9c31146b78fe767b989b1d676c276bd", "path": "FirebaseCore/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981c4c482e31d1137b822c893b2ffdf537", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984eb74f14d7bad55d6c199c167965f004", "path": "FirebaseCore.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986d681f708a8508ac3a36d639d2f6751f", "path": "FirebaseCore-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ae947481cf88413a2905d0350e2a7676", "path": "FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882a4224cd2734906e77743237e0745f7", "path": "FirebaseCore-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983c3e5578711d093a72fe4c7cfad8da03", "path": "FirebaseCore.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ec1824b8d0b7f574e38897e61b0d0413", "path": "FirebaseCore.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ac9321e919c887b5e8d312d4e6517f83", "path": "ResourceBundle-FirebaseCore_Privacy-FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98eb22c82d36e9435b009efc156c26b6d2", "name": "Support Files", "path": "../Target Support Files/FirebaseCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b1a4cd4195db1d537e2fe91099b0e56", "name": "FirebaseCore", "path": "FirebaseCore", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98da8474bf0d14482e17922f533786d224", "path": "FirebaseCore/Extension/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e1a61816f7e222f0f680ce3f2f93371", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d88997b2ed1f1a0b258f358bb986e15", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ca743436507cdb3610386c0539daf7fc", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b02bca023189cf1a436b9cd3afcd1b32", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98484459a5638c38323361506a7fd31a4b", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858a3df5f71c786291bbcf130ae5507ca", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b29bef3f09e92f54096cbe6b907307c2", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98118d06765900992de9a2b971146dda17", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e984ab1a9e8106f24f4937cd8a45542c6aa", "path": "FirebaseCore/Extension/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a1f4f95fedd2a08a0085bfdbd711a0e4", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d14fbefa06e03bbfffeea1a1c24d7e16", "path": "FirebaseCoreExtension.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981901e468d5386d79b99342b3ef23e82b", "path": "FirebaseCoreExtension-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984bade627330e37aa4b9c591f932ce9f2", "path": "FirebaseCoreExtension-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba0a99c6194a3a2a05af34be586025dd", "path": "FirebaseCoreExtension-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983f0cdfee7f4b78807dbc1b10da128530", "path": "FirebaseCoreExtension-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98be046eac8ac584e3f4ab7dbe6f678205", "path": "FirebaseCoreExtension.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98331015dfab415315798cba9c4d6f85bc", "path": "FirebaseCoreExtension.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ec8b6b0ba7b7f0ea27d83bf1452ad0b4", "path": "ResourceBundle-FirebaseCoreExtension_Privacy-FirebaseCoreExtension-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981974c7cdbc97400397ed8a1b03ddaebf", "name": "Support Files", "path": "../Target Support Files/FirebaseCoreExtension", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c4758014eedff59206e34186ff94dfa", "name": "FirebaseCoreExtension", "path": "FirebaseCoreExtension", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982e26fc23672a1d4545889c2d315dab61", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987d46695b03a1b0fa7be043ad6c8c3754", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c41477b602031daa984aeddc100db068", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Heartbeat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98abab9b169ee393d37bab23fb2ee932dd", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983bf6c989e10c98bfd5ee7b0dac618acc", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatLoggingTestUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ac962b5d266521efc547dbb2fdfda4d0", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsBundle.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989365e4c6334998c59234bf87e8eb7ceb", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a90e20fccd5391732b268d48520b4310", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9842b66097e329aeefde4e0e31fccc5544", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/RingBuffer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9864984a842d9f7223b03368c23813d428", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Storage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f4742968c41d7dbb6bbe8ee1f14ab15b", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/StorageFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988cb53f6ab9ad55fc93226724cf6340cf", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/WeakContainer.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9831c12cf7ffeb513c2302d7ece877a4da", "path": "FirebaseCore/Internal/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a38694dba3e16f74a770f7c180eb5654", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986dd55214e497fbb6b94ee9f6fd3d70b1", "path": "FirebaseCoreInternal.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9824fc624c705d20579a22825ceb10b9df", "path": "FirebaseCoreInternal-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b025e5d84d95a29f791671e0ea9e1e06", "path": "FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98816b063444da05fe1e78dde593b1f56f", "path": "FirebaseCoreInternal-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840fe700dfb334077afd9e5df782d476b", "path": "FirebaseCoreInternal-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981ba31bfbc82cc0697974d45fcff163aa", "path": "FirebaseCoreInternal.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98be4a26a834ccd6c4970347a96c3caf15", "path": "FirebaseCoreInternal.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9896fe32a6d0d5199e6efb2c39a9680b03", "path": "ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ca4525103438e5f1574122c357dd1049", "name": "Support Files", "path": "../Target Support Files/FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802613e40a5b54b49f54ed5d3105b8406", "name": "FirebaseCoreInternal", "path": "FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ead41c0ce5268e9faec8a5464882f55", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e68cf795f43c5837ebb918580b24fc1d", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9850a1d89c9c003dc97f2398197a03b9df", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982b620c591052b57e153a049a85e54ef2", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98def723b30ff28f27e3f0fd93948c49ef", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9885e9ca92f571b0cf851e1c3f4e78052e", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cae6d319170eb53be480feb636aeef92", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5aa907248b60546c33dc249199537f2", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FirebaseInstallations.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd4ca0ba8e1876edadb7b17accbf8f58", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982e9738340156c51971216cf328d28631", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98776cc992018e32a0973f2b89db1737bf", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallations.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98674581cf084aec471560d58746b6ef5b", "path": "FirebaseInstallations/Source/Library/FIRInstallations.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986c97a78ccab8b6d348fedee23ee1d713", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d42d48d0587485e4f76a450e46864f12", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893b9b246a667c2c68f08b10a24df29a7", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallationsAuthTokenResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f81be2cbdff85662fedddfb2d2855992", "path": "FirebaseInstallations/Source/Library/FIRInstallationsAuthTokenResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982085398a9db9bdbed2304efa7bee0de2", "path": "FirebaseInstallations/Source/Library/FIRInstallationsAuthTokenResultInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883399022cdd0a44d4acdbcd11ae02016", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsBackoffController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ed379ba00ca27bfe07803cfa1c8c649", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsBackoffController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cee03146c0517e8c31c683de79c39346", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallationsErrors.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ceb670d900a030d7e1846a3adb50b82", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsErrorUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a1918213261f97c980de855c9aaf5fbe", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsErrorUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ea3e5d22c3d6687fdfdb507f67bc90e", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsHTTPError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b44ef4fb4b621092a72131838d5a9ec0", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsHTTPError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9854865a943e1fd3458d925e44ff5588a9", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsIDController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9800f70ed1467a1c85e7e27d3618a9c312", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsIDController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858f0b1dac60bcd20aa7e2f990f828793", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ce36b83b05ce68fd75de41594ffecc68", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985146e282bdc00f1cbf790d1ddc1f437f", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDTokenStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f9b62552623e523fd677049841e88196", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDTokenStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1067e3558f5c345972633f010740537", "path": "FirebaseInstallations/Source/Library/FIRInstallationsItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985548f8ca31ccdf40e3252db77da35010", "path": "FirebaseInstallations/Source/Library/FIRInstallationsItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872ec9f44731c5bb4164b6a3e41b76253", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsItem+RegisterInstallationAPI.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983787306144fba9681956847921420859", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsItem+RegisterInstallationAPI.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e0b1aaf5eb4b39b3648a35669602a8fa", "path": "FirebaseInstallations/Source/Library/FIRInstallationsLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98455e42898fea3177d522dfd02ba7b07a", "path": "FirebaseInstallations/Source/Library/FIRInstallationsLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9869bb3dc65e0014294b3bb00cd8bfc39a", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsSingleOperationPromiseCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987fba70b156bbc0c0b4f63467bae677f6", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsSingleOperationPromiseCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98895b6e986cefb0ea061913753b2d8f60", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsStatus.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f5249667805b2e1de12671b7c35536cf", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d3f0e10d8b3eb7e9ba3dedca8e356270", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a1235d26302bbc3ead4ae169811eb3b", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredAuthToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9869a48ef5833857af0a808d4288dc7f85", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredAuthToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98658b4255c533d9f0000647707c600e24", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884ee9f5249ef306ef16f2516fad9d3de", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d72c4e4aa19a9f2c6abf97c2de18c601", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d60e5b49cc13899e28183430e89758c", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a6a670a47e78df3a3e1d994d9d2aff5b", "path": "FirebaseInstallations/Source/Library/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98eabc06e224334258be5da6fdb869c5cd", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ab44fbe51c653749678cace9d7013bf1", "path": "FirebaseInstallations.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989d31058df2886b1d4d6364b641f1bce0", "path": "FirebaseInstallations-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98797d23bfa1f16d86517802a6752a6214", "path": "FirebaseInstallations-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989902b73137acd1a6e4c49f54b86ed936", "path": "FirebaseInstallations-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9896ffa06b6790a09f3ca6fdf2b3bf6e28", "path": "FirebaseInstallations.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986d9ce4e9a9576ee7513d0ff5ffefe33d", "path": "FirebaseInstallations.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982ca8da7bfd51c07ade6be6c70d7d01e6", "path": "ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bbf4d95a1a64e3d75afe8decd10942f8", "name": "Support Files", "path": "../Target Support Files/FirebaseInstallations", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a835066a1b01f9ed4934413dde71213b", "name": "FirebaseInstallations", "path": "FirebaseInstallations", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9889ed4338c22d2a88ce75ba9d62334368", "path": "Interop/Analytics/Public/FIRAnalyticsInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9886373614aa6575471ed37bebee037d45", "path": "Interop/Analytics/Public/FIRAnalyticsInteropListener.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98440c0440196f71e26921d7883ba25a29", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98767fb25c3637912ece52edef90f3304f", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b04c0ce50de46388188868617ccea70", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f58c6e47def2b1461954c03cba92fc42", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d58460879761bc2233c9b7379c8e439c", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f55bbb99a7bf422d946db5c950b519c6", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6026f4e9c205122a3eb79cf58ddf78d", "path": "FirebaseMessaging/Sources/FirebaseMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987721bed3d64f7459d7809df9dcb93fc4", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FirebaseMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c03a7ae3258d2fb8fb48e8fd940ef02", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98333f54b64ae147356e64d5de814a50bb", "path": "Interop/Analytics/Public/FIRInteropEventNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9887597eeac149cc8d3acfa68a363a5f63", "path": "Interop/Analytics/Public/FIRInteropParameterNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985be5db38927f2299d71c7e9bf3771738", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e8b8128164fd6e9b57b3453a5169994", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986214d22950b7f08257e74152a6d93877", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981eef913e01b2b54ca9ee55a30fcd69d9", "path": "FirebaseMessaging/Sources/FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987fe47050956b91ae60ec6ff7abee1164", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessaging+ExtensionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985784e04decadf57e67bb7f291fec66d4", "path": "FirebaseMessaging/Sources/FIRMessaging+ExtensionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986285402f73fc394724ded332f200e6a7", "path": "FirebaseMessaging/Sources/FIRMessaging_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988975527d6f793139a175c1be5774058e", "path": "FirebaseMessaging/Sources/FIRMessagingAnalytics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98242e63295d00880d4e33cfbb99e700be", "path": "FirebaseMessaging/Sources/FIRMessagingAnalytics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a32bbd6045cf99a748f8da7b8bc3fb6", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAPNSInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aa694bf4a704f4605a4d41b6157786f5", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAPNSInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9810b9efcda9b7673f7d3ee6fced36a1f2", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthKeychain.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c9820b4618b9963037341f1cb689f5b9", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthKeychain.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e3c810c63f8d49fd44b186b5f38b346f", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9849754da8172c9bec6f1edf68e289c109", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988cc6c0e3d070a3aee783409596a078d9", "path": "FirebaseMessaging/Sources/Token/FIRMessagingBackupExcludedPlist.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987bd0df55ff8217005f65bdf6834a780d", "path": "FirebaseMessaging/Sources/Token/FIRMessagingBackupExcludedPlist.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859ca6f7c02d6d19991b6b9c19eff1038", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinPreferences.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982aaa1b78ecbd08129324ca8d97fbb9a4", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinPreferences.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ef4aadf753f85acbc744b800a3b216e", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983787e995d652e866c2d6d504d3c8b89f", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888cd9e5b19da20318041309509d0e9a1", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989d3f33bda4cf70ddbd4141c4936073b7", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897ad449977dbe92255789779a0ee2249", "path": "FirebaseMessaging/Sources/FIRMessagingCode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9898bbfddb917b602fdbc06a07eac7c97a", "path": "FirebaseMessaging/Sources/FIRMessagingConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a1d19c8fccf7d87306e4ca47ee7256d7", "path": "FirebaseMessaging/Sources/FIRMessagingConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0374a88699c33534e467f9c2eb566f5", "path": "FirebaseMessaging/Sources/FIRMessagingContextManagerService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f1ea6fc7011b06a1f3ae162fa791031", "path": "FirebaseMessaging/Sources/FIRMessagingContextManagerService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9ad5a68b26b0c7e515bd9f69224e3d6", "path": "FirebaseMessaging/Sources/FIRMessagingDefines.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4dcb9f8f841248aa59aa0a8079e0f38", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessagingExtensionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d28cc66f247265d3e034043d4f6833a5", "path": "FirebaseMessaging/Sources/FIRMessagingExtensionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983abc4f11966dfdb10d427805ba6ff3a1", "path": "FirebaseMessaging/Interop/FIRMessagingInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98983f84138b12be5533f3761ecc39479e", "path": "FirebaseMessaging/Sources/Token/FIRMessagingKeychain.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981c9922de9be686b1963ae032966d8995", "path": "FirebaseMessaging/Sources/Token/FIRMessagingKeychain.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809349eee3c494a395b5f50692fd3470d", "path": "FirebaseMessaging/Sources/FIRMessagingLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9855a64d0d9d6018e083ada79de83341f2", "path": "FirebaseMessaging/Sources/FIRMessagingLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984091ff7c3e8751e8ea2c28eac766d948", "path": "FirebaseMessaging/Sources/FIRMessagingPendingTopicsList.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9835b73d42d5f0c5b67124050e80ca03b2", "path": "FirebaseMessaging/Sources/FIRMessagingPendingTopicsList.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c5d03f1dd537d0ed06c6cfd587161a9", "path": "FirebaseMessaging/Sources/FIRMessagingPersistentSyncMessage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98014ad640dfc975a425a4e0ee25b5462c", "path": "FirebaseMessaging/Sources/FIRMessagingPersistentSyncMessage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a02a8988886a0074e34a63cbda565ea4", "path": "FirebaseMessaging/Sources/FIRMessagingPubSub.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9883cda5affec358987b8354ee269add69", "path": "FirebaseMessaging/Sources/FIRMessagingPubSub.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986dce04739016b9a4782630eebed7d1d5", "path": "FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815c7859c078041ef068e8de4a45264bc", "path": "FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809671df0d1c3ec1bea7805c8b8f9a5e3", "path": "FirebaseMessaging/Sources/FIRMessagingRmqManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9826b35af41ea6b195bcbc7d8ac04c15f6", "path": "FirebaseMessaging/Sources/FIRMessagingRmqManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981732204795dd262a13bdfaf229b9bec7", "path": "FirebaseMessaging/Sources/FIRMessagingSyncMessageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bac9bbdbafd130f5b54332d5148e9d4a", "path": "FirebaseMessaging/Sources/FIRMessagingSyncMessageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98811545c1453d9952441d3fdc273a054b", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenDeleteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9866b9306fc4f1a2b37d294bd5323b047b", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenDeleteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bcabc46f46ba4f1bccc0f422c58dc384", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenFetchOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982831a3341f02b1cf9010e59f0ae80e95", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenFetchOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c1feee33b411f6b0700a908367e5083", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f5eee2baf56b3e1a8982f01617f5f286", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ee9f374baf01a25b1f7aac02b5a2261", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983079343f569451ea7e5827ec6fadebd3", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b14976ba6939f7fa0259a10a148a169d", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b955ef92ab110a80210e1e6f5dffe8e", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dacdde460a24c2b221629e40d0030b41", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ebe1acf0aaff3b8a357e8537c06bc990", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aec5705867996ddca9d33f303f537cbd", "path": "FirebaseMessaging/Sources/FIRMessagingTopicOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9828bf6aa6882a12feb340e732609bd113", "path": "FirebaseMessaging/Sources/FIRMessagingTopicOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af288f7031202f8f98ebbb459fbbdcd2", "path": "FirebaseMessaging/Sources/FIRMessagingTopicsCommon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9853904d589b97eb8e32a5d4fc9ac4cd1a", "path": "FirebaseMessaging/Sources/FIRMessagingUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9805fb9917f291a9a5b79815266a588027", "path": "FirebaseMessaging/Sources/FIRMessagingUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e985ab6a09be32cc475a6cc0508239d56a1", "path": "FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986bba2eaa121ad9b00e7aec45c53ae8d1", "path": "FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9854aa01042ed40689e560988330937924", "path": "FirebaseMessaging/Sources/NSDictionary+FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fcde85646cb6ecc472e182dc9634dafc", "path": "FirebaseMessaging/Sources/NSDictionary+FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9827294934d035380966741317c37e17f3", "path": "FirebaseMessaging/Sources/NSError+FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983c6f8a7b2deba6baed7f67f96be0f450", "path": "FirebaseMessaging/Sources/NSError+FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98944b92eb7cd12c487690ea007cd950d7", "path": "FirebaseMessaging/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986a7418480c6b0af4ed4605fde97651d6", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988f48efd2d5b08189ea8360cc28c3b3d6", "path": "FirebaseMessaging.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981512fe0c9753801cb2b488c10b2901c8", "path": "FirebaseMessaging-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c2883abc7b4974c943c547e05afbddc9", "path": "FirebaseMessaging-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984315a3977835ca00888a26f818c11ccb", "path": "FirebaseMessaging-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983ffa9fdb5daaaf6bb2e743165e7387bf", "path": "FirebaseMessaging.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f226f7830e5561e1c14723251f24fa05", "path": "FirebaseMessaging.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ee08ca872dac63f1167ddcd06636132f", "path": "ResourceBundle-FirebaseMessaging_Privacy-FirebaseMessaging-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9806d09008822f20f0a2e565464b77c6c2", "name": "Support Files", "path": "../Target Support Files/FirebaseMessaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e77d48a59cf08cc2caed30ececb1dbd3", "name": "FirebaseMessaging", "path": "FirebaseMessaging", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987f35e71e3c4b211d6426a212bf98b498", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/CartesianPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989ba1b405c971deaa6ba6deabb9f717b0", "path": "Sources/GoogleMapsUtils/GeometryUtils/CLLocationCoordinate2D+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986da1781771d42ca3be00d4f81ef1a237", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98358294d5c2ca295e127a724ef81dd6cb", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f086b9ac6461402fc6e4c4c842aec649", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPath+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9887ee3188556029fb4a84a4da6cb6b922", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolygon+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9812bca6c8017b08e4ee169425b844a0d9", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolyline+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd8d0be61c2d9c96f593feadd4862a6c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981cde85750c4d6063d1fd382e93ea0669", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981e3e66cfc8dee690b1a14e9380229187", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986070608810b12b6d650eff615390db90", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb877c2fc4a55d3b0b5e15be69a32f7b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bbdf5666eb1c8457c00ef2f5603f4de3", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd53f58fe833e03117e66abfe6f155e8", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985fe62f8d0f3386042db2238c70bb3ee4", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a3739e25b43fb5e6b05d9998fae8685c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982772359c788631adc705ff8b4873e395", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984a4241969945f7698d6302d3e68b901f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6cce4400786ef08c871ef8da91f71d6", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b62d1e61b5365b33e92736a5511422f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bca7b133dde2f3351d888966c8edc86d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849918b1c110b6dda6b59354fc8ce9c95", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981a143a197728bd86d1e9133ac4bc5c4a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98945e24210c140b00c2a6708bdeeff463", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a738ae4b21f51dfc21b65818aa8ecbb9", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833bf0684aa8d4c8b8c5d4c3d3948578e", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809bcf9a04b0b45484a56040d3ebfca12", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986ce9e0219d035d2d3c95253ec911a862", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b6544b4a4918682f7051cd5eae43335", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989bf450442aec46a0ad07cf61bcb5a491", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca31518b8f93255bea92890f13c52663", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e0e7286e88b258590ae960362be59ed", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a7b51dbcd006eb669f50c41248e16e1", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e024b844ae1c376f920487221d4576e7", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f5968c6ef97aad7b9429bb793dd8f4f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fc1147f84ecada15974be817ec2f9e17", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98702010e11e1da29c0f6fdccff68e1e03", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987ced11fa1f4d33642f6412879e74075f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eb911c429d6d63b57ce059c3f76dde56", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98020dd2ba269f2fb1d85f5a6eb5d2ca9d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8a50a97b1618d8037d3cf0b8b84f66e", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989396ec18883225d34311275ae559bb00", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e4d0bfb502a61d8c6fe0c6c905275625", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809add8118e4cb9d8bedca3fbf8a0a8f9", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cdb3e8f408893d12ee8d077cb2f85e52", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987008866a0148b840349d820d22f4da21", "path": "Sources/GoogleMapsUtilsObjC/include/GMUMarkerClustering.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d6da57d7d50692dc89dc84e335efb63", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98778a80fbc6ff5adf00db436cd49b1eba", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e5f3cf073a6a77b3ecea47595e7cd7c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985a9626f5e120d9f9657f8c1a932bdeec", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98abfbbd13d6dadaa0c3548cb41ad7f2e0", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cde22280441bdbbb60c83e2a47c30d61", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986777150dfa842707a19801e6e16ee308", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982d6823201cbd1f12a5dfe742c8390b4a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c8e852e5c11d6af71f8f1283da2e733f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989062711a5aa1ee4ab19bc6756b792a20", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9845fd36cf63c5ee177e3760eecfc9f344", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fa69fff4866dd12a25015d791d9a3154", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98152fbb85a7874d4cbc4e2ca290e99c1c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984b7a759e5273b8bd99fa2e81ab310036", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9871309ae0d50d9f0f37b87387c9abebdc", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e2989ee0faf6c3c1f5cfe487fb46a27a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988905764231a0a7c5b56dac7d9ccee8c2", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c972e8b9ff297fcb1ee929359cc73df", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985444b10e6c5d98f6766ce4410862fdd7", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c24717b56b3f1819b7d57327810d3d28", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f6f03595da6b42526601328363e5ea4", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c6a6214264ecc73f3f629564bcd8dcf5", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ffea25397291ed0019b54774ebc08b36", "path": "Sources/GoogleMapsUtilsObjC/include/GoogleMapsUtils-Bridging-Header.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c6aed383f4277767fd9b85457db1179", "path": "Sources/GoogleMapsUtilsObjC/include/GQTBounds.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98416a394491ed5f316c6c12a4e8cba5fa", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9876f108b4a5205666c99e7c1df60120c4", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98560896131cc30e7e1a540ca8bd13f358", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f34d214c89d67cbd3ae630572bdc70f", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c0de3d2cc114fab516a17eb370d8ee69", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d4675f02284dfc674f72c019a802ad24", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9847cb16770484a927983533b1333f7e97", "path": "Sources/GoogleMapsUtils/Heatmap/HeatmapInterpolationPoints.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bb75c1475337a63370cf5b4edd607371", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/LatLngRadians.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985a2505c271e69405cae0b973c34bae27", "path": "Sources/GoogleMapsUtils/GeometryUtils/MapPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989096b9e8f287a19bf454aad0ae4949f1", "path": "Sources/GoogleMapsUtils/GeometryUtils/Math.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988b9db1a388cf6cde18ffa582cd47ed90", "path": "Sources/GoogleMapsUtils/Helper/MockMapView.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9828fff80f8b967a38b8faa7484f3baa5e", "path": "Google-Maps-iOS-Utils.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982ae79a9259be4171faa3fd228b189a29", "path": "Google-Maps-iOS-Utils-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9804ab67e01acd1c118c3f3bffeb84fef8", "path": "Google-Maps-iOS-Utils-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cfff05702ee83b5713f6cea54e2fb14d", "path": "Google-Maps-iOS-Utils-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989250e630df805de8c420bf44daecf5c5", "path": "Google-Maps-iOS-Utils-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98acc2d811af053b6efc6f7337af55bd3c", "path": "Google-Maps-iOS-Utils.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d1184a8e1d9ad9996c6aa16e5d324161", "path": "Google-Maps-iOS-Utils.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981f3abe2a6b61ca5dfd89d7d26c7b057f", "name": "Support Files", "path": "../Target Support Files/Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cf331d06d97fa5c498ece0feee28b4a", "name": "Google-Maps-iOS-Utils", "path": "Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c423bd37f90ede58890c505719a6596e", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b4fd7469791067c8e90b1b2309171419", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984267916e65870e5146c1ffcae5b68758", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc583ffe2876e073f56e9bd930eb7db3", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98638903fe7b0f8cb01fe8a458e683402d", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f01673d3aff8a9290669e02103fba06", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982769e20a46a3fafa7fcd2388492bcdc1", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987079ac13e7e2cfb2298746a8ee4e42d6", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b51bd39bf1dece009a1fe079bbe35c90", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986c2a41b01d23e575226f985d3bb6c6fd", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984cee145e6feb3813800ea74bfa404ad9", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTCompressionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9830c4e8124e0a301f84c272dc804fa34f", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTCompressionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da39f3eb138e476d061383c5713f7628", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTNanopbHelpers.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9839f53d46623f63a45421ba1a8bf09fc6", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTNanopbHelpers.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f88f49bf555381c17741a36a4009128", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98607e93719ca22466e9087649e9b0c3bc", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855f4f0da21182281dcce4d41b426d9bc", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploadOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98784cd2b27034ad81a7de2b72690b9c89", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploadOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980fa85c71bc5fa1f6f65e6f2f0bcf9d3c", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTURLSessionDataResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982383b3ef041b8263a76841235cf9ff12", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTURLSessionDataResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9899e88f73fc25e2917ef0b375135a593e", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORAssert.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983c6a8e90ecc0a12bbf315ec7d68b771b", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORAssert.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98280fd96bc1e7ae5772b019ab1506dfcb", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORClock.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989a62466936dbf8592a03baafd8d2e4df", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORClock.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809deab787524072598db591f1eda2f77", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORConsoleLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac573ee5a328ecb4a0aa03277ddb231d", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORConsoleLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d03a56c3eae2244d358f0b08cb40bdc1", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORDirectorySizeTracker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ef269351fe458eb1e71e668a1d4299f", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORDirectorySizeTracker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98134ca0be3fa2c22ca53a86b999cde677", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREndpoints.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f8f2a3e3781938e3c52be318d1415732", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREndpoints.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981e88933cad78e65c6a091200c460c509", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREndpoints_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d2037a726fc6f988a09f7c23ef43598e", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREvent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9822339448dc56cc2bf6690f387f09097e", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREvent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982fa06e6503917a3f33ffd6d902f88965", "path": "GoogleDataTransport/GDTCCTLibrary/Public/GDTCOREvent+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985fffe69cf4cd73f643c987d9b66a7923", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e28358358d12bd057fce55869d910212", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCOREvent+GDTMetricsSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9880fde77d5a108548613b8082b72a4644", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTMetricsSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e9632a60e75e1f87b6d3e4d613fa0878", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREvent_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e3840084ea55e44c79cb112f18178115", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventDataObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987179328ba4813f4d0668ecee4e44853b", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCOREventDropReason.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa6f63b8e522f9cf4b347cb63d656984", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e3dbf6ce25371c47d2273102e64ee3a2", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9824cec58eaccec6f6fcceb8e37c25aa0b", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805bdce36db301436786122e2a918311d", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage+Promises.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981e9c778c8c4a4bbd5d23ad1673cd566a", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage+Promises.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ac980077b8f6c6759a26d10e46202e2", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORLifecycle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9870ac761c7242b796c9b995a5808ce85e", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLifecycle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3d968d1986831827f92c955577df47c", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORLogSourceMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d05e397e4606f6d4f875f7884e427e1c", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLogSourceMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98938284e6500f0b98aec75a8980f06744", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b6b350c17820bd122d26c53f893f6b6e", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834bd45cb684d0cc87707b77778634c86", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCORMetrics+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982268dfcd052c16f4bb6956221999a7ae", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCORMetrics+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd084baa53855fe2f15e8875d62ef814", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e4f12b6d3884e99a34e35f92f5252a29", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6a73f78d67f891be6c167e14a1d7837", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORMetricsControllerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9867afe3e0a87cb244f1fa9346728a494d", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ee93b27e54ece1db435de480f8bf0544", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98796bb4fabd6d0aa972f6718dabde846c", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORPlatform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9870ec494ca7d0597bbbcdc1d3c74ef4d3", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORPlatform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805c4ca5c7946d6892a39b1b7c47f8bfe", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORProductData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981534578635f0c05f6605e83e30ccfc5b", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORProductData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb2b90eb08c1e31edf081e3d60ed49fd", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORReachability.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9827264a5e944cc0917a8b5a2021ecc1a7", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORReachability.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98978b0a52cb71f069f2e959acb5851d63", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORReachability_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982be9b0d9d6836ff06a556881e9e7c034", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORRegistrar.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f90199ccee0ac4e735c085b13dc4e017", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORRegistrar.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988305c436a48c62d672f879d46b95377b", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORRegistrar_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f5e040059aa5e59f346beb4ed86140e6", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageEventSelector.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9859ac9315189fac63bcf73a8979adb872", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageEventSelector.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8add499f373d073198ca3446eda3ac4", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORStorageMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9836da438923495fa15acfd3b03caa0a73", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ecd7442f120d34696112740b5b4fd2bb", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9853f6f7087dbd1168d5ce5f60359ba0d0", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageSizeBytes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad56250fe32b2ac177af855dd0672dda", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTargets.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988798adb7dd92e357c157a92bef196b4f", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9869a127460d85cd115178c8f11fa589cd", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9878c7a87ec30ee1d99e9bdb5630d0f774", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98533231c8c8109d489131db53a3e0192a", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTransport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98798d33e02f946a6cdc486a7067289280", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856c983d11a2d7cb736130a18f11dd4be", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransport_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d936c7329b0628f250a812274713945f", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadBatch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985701da4616662f26991386eac0c75127", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadBatch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0547690736e262ef0c1c260434a957d", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadCoordinator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c8211ac9ced0c85a37f904e4b54b10c1", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadCoordinator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9853326c3509dc71e8b0ac4c2c09bb7f01", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98684fc6e50587b8f4f8e949b13ce6c9b2", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GoogleDataTransport.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e989a7678f759b67296ddf6d8ae2f71a247", "path": "GoogleDataTransport/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9868cd1522d41dd7cb24528630928545a3", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d338c10b9ee97bc7f75102290045a88a", "path": "GoogleDataTransport.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984e2b2c19ed1f449b6f1e38f7ad83dca0", "path": "GoogleDataTransport-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fbaf58a8eff6429b13f00254cc2ac3d1", "path": "GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988995996f9d46d6644cbd611de05525fb", "path": "GoogleDataTransport-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a0d12d7b900bccf0f51d7ceec3d3b544", "path": "GoogleDataTransport.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98de006a120b251c91d06ffdfeb0614de6", "path": "GoogleDataTransport.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988a856ba8bff411d8f04b75b9fb6a61e3", "path": "ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c818fd7804acf046aca0d3a5004930b9", "name": "Support Files", "path": "../Target Support Files/GoogleDataTransport", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889abd9286f66bc619d492ff79789fa8c", "name": "GoogleDataTransport", "path": "GoogleDataTransport", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a16fe1004c3c30a9f07e1d5b6bbe465", "path": "Base/Sources/GMSEmpty.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98c0d19090e936cb307310070de17ab0cc", "path": "Base/Frameworks/GoogleMapsBase.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9879c3f8353c1f77fcd5510d2706892ec8", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860aee100ab28794837ff506f6852522f", "name": "Base", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b49179fb5ecec9e45c4cacee2d1c5016", "path": "Maps/Sources/GMSEmpty.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e984f0cbec36ebb37d5a2f5c370b569e56a", "path": "Maps/Frameworks/GoogleMaps.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98bc4c09ea90aa63ff35031df21f99a8b0", "path": "Maps/Frameworks/GoogleMapsCore.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986ee93a69acc5f4f81d033ad1a2f4b989", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "wrapper.plug-in", "guid": "bfdfe7dc352907fc980b868725387e986c92de27be4bb068093af35dea816164", "path": "Maps/Resources/GoogleMapsResources/GoogleMaps.bundle", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9838fee2437d7f51fcecb532f6daf42ccf", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9881396f52719fc10d2391fb8b0a0b0", "name": "Maps", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98b007dccbb1c1330f4bce31d4cf24c769", "path": "GoogleMaps-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984d0b6eaec60ee2adbcddfcb035e8543e", "path": "GoogleMaps.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98320913558773e393951ec22b60692401", "path": "GoogleMaps.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986d80eebcdfce24e9de5c0141862d63b1", "path": "ResourceBundle-GoogleMapsResources-GoogleMaps-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d65a32b986d9e1b56ea87c3b8950bb69", "name": "Support Files", "path": "../Target Support Files/GoogleMaps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98983fbfec6563df5502221ba7c1e48c82", "name": "GoogleMaps", "path": "GoogleMaps", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c1c365d200bf919c30ee6a1e3a1c68bd", "path": "GoogleSignIn/Sources/GIDAppCheck/UI/GIDActivityIndicatorViewController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f24eda75d5cf8b2a9108207ec761036", "path": "GoogleSignIn/Sources/GIDAppCheck/UI/GIDActivityIndicatorViewController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e08443e265a4384ee7159de0506f7b18", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/GIDAppCheck.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985c599ee0cc13aa6ffc2a6803428548d6", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/GIDAppCheck.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee2c26e4ff559e6d0b844b134a2a46b3", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDAppCheckError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cced89c82fedbacd8508579fddd4fde9", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/Fake/GIDAppCheckProviderFake.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc1d574974b4de542c4e6c9c4eb33190", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/Fake/GIDAppCheckProviderFake.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe8f3cee9585814513f2f9d7384881a8", "path": "GoogleSignIn/Sources/GIDAuthentication.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d1c2dec4a720ba820ed3fa3bd413af74", "path": "GoogleSignIn/Sources/GIDAuthentication.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a3c4f4a616e1fbe47a0fa776089ce40d", "path": "GoogleSignIn/Sources/GIDAuthStateMigration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98738b061f3cc48c44ce95a391af8e43b0", "path": "GoogleSignIn/Sources/GIDAuthStateMigration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d91e4f988a27d39b81c1d094761dc69", "path": "GoogleSignIn/Sources/GIDCallbackQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c92385f8918ead20ae01952707c57dbc", "path": "GoogleSignIn/Sources/GIDCallbackQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819f8d46ffd52fbf3125d945630c893c7", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987796cc384f9805f01401f20fa3e2094b", "path": "GoogleSignIn/Sources/GIDConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b48ea192efd526f8331be5d166f053a", "path": "GoogleSignIn/Sources/GIDEMMErrorHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98059beb9b1b3e45c7c8a09fe3117a5d7d", "path": "GoogleSignIn/Sources/GIDEMMErrorHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98869339e2e1052c1d5f0e1e84776b29b7", "path": "GoogleSignIn/Sources/GIDEMMSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98da4ddbf15aecf4f87d3c14bb4ec612b2", "path": "GoogleSignIn/Sources/GIDEMMSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989fb64f87f090d0ffdec279ebb7492033", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDGoogleUser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982d9f2ec13d22d48297ad28d49a29eca9", "path": "GoogleSignIn/Sources/GIDGoogleUser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c66917c18ae579dfc2542f08d7b05550", "path": "GoogleSignIn/Sources/GIDGoogleUser_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e6b0041e0ad77b64e79f44708425247", "path": "GoogleSignIn/Sources/GIDMDMPasscodeCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b6dacbec876c2b26ebbbfeb8e7311d52", "path": "GoogleSignIn/Sources/GIDMDMPasscodeCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ffe54d7bf84a20bd698c8a4202a5cd47", "path": "GoogleSignIn/Sources/GIDMDMPasscodeState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ebef8438ae82d3495f81e0586a6b3ae5", "path": "GoogleSignIn/Sources/GIDMDMPasscodeState.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988da05b1b9177013bac72be1f47080638", "path": "GoogleSignIn/Sources/GIDMDMPasscodeState_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9861f55af26aa6b360f95f7c2d612996dd", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDProfileData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98140ec64b0d739e0c3b22bdc92c05f687", "path": "GoogleSignIn/Sources/GIDProfileData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989190abc27b32a9e4c66173b014670d3c", "path": "GoogleSignIn/Sources/GIDProfileData_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9854760c29fd31f9df6302076f7050fab8", "path": "GoogleSignIn/Sources/GIDScopes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b6be3133aac137b011b1ad78e6fccc07", "path": "GoogleSignIn/Sources/GIDScopes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4bf4d24f0e7978dea4600af87156004", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDSignIn.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b21fdf5ca403ea7fd38c690ba567c646", "path": "GoogleSignIn/Sources/GIDSignIn.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9857946820cc4fb9c39a7a0834c46b8b57", "path": "GoogleSignIn/Sources/GIDSignIn_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98daacf90710d66d155d9b8b693c076c73", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDSignInButton.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9899e3b3fabb3ce420f9a140c0ac58efad", "path": "GoogleSignIn/Sources/GIDSignInButton.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9841f7c47604a3293cc4d4d45546aaf205", "path": "GoogleSignIn/Sources/GIDSignInCallbackSchemes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ab84f455914b4788a6b9e7c768086478", "path": "GoogleSignIn/Sources/GIDSignInCallbackSchemes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98316d32137f42a2ba106661dad5b4830e", "path": "GoogleSignIn/Sources/GIDSignInInternalOptions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f50813d37472ab048ffe818c15442f42", "path": "GoogleSignIn/Sources/GIDSignInInternalOptions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981667ff4abc26fc6bdaa3561ee043791d", "path": "GoogleSignIn/Sources/GIDSignInPreferences.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9887810b8322fc41bee130726f64952c6d", "path": "GoogleSignIn/Sources/GIDSignInPreferences.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9876b19da0b4ff7416f732e3c4da3acc71", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDSignInResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884286a8f1d0cf26c869f6ff5f99c05cc", "path": "GoogleSignIn/Sources/GIDSignInResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98008e1784797f7256fea5890116281b28", "path": "GoogleSignIn/Sources/GIDSignInResult_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aa3c1dc2d395ff91d82b33e6b05c71b3", "path": "GoogleSignIn/Sources/GIDSignInStrings.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983bdc1805156b55975aeca1222dddf100", "path": "GoogleSignIn/Sources/GIDSignInStrings.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d4adf4440eba14ecef39d9ee314e8629", "path": "GoogleSignIn/Sources/GIDTimedLoader/GIDTimedLoader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986e409b424813cd8bb6a8df53782db743", "path": "GoogleSignIn/Sources/GIDTimedLoader/GIDTimedLoader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a8edaff1ebeb677f32b68a531c092970", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4d1abc6181bec7d57eaeecbc8f86fc5", "path": "GoogleSignIn/Sources/GIDToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98270c1412825ee04043305479b149fd0a", "path": "GoogleSignIn/Sources/GIDToken_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98654663b520bf9ce286ccce8ea6fe8e8d", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GoogleSignIn.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989319b5657bfb81717b37cc5f32beb3d1", "path": "GoogleSignIn/Sources/NSBundle+GID3PAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e3a9886020494151ae24b9932a081eb6", "path": "GoogleSignIn/Sources/NSBundle+GID3PAdditions.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98b1130a2968316b44abe726de32c25c5e", "path": "GoogleSignIn/Sources/Strings/ar.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9880ea21031d9e647baa646790c3e4751e", "path": "GoogleSignIn/Sources/Strings/ca.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9836802cbc9a0375cb2901138ce4d3915f", "path": "GoogleSignIn/Sources/Strings/cs.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98b0664e3cd24b90517865b41439d9609d", "path": "GoogleSignIn/Sources/Strings/da.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98277a7bdac89aa320a82b6c0a1065aef6", "path": "GoogleSignIn/Sources/Strings/de.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9832b988ffdbedf60ee8a5b2852d04f3e5", "path": "GoogleSignIn/Sources/Strings/el.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98612009733384cf3aaf286eb04f937442", "path": "GoogleSignIn/Sources/Strings/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e981070da4be2b0dc90bf584f2293933383", "path": "GoogleSignIn/Sources/Strings/en_GB.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e981fd050bd5b30437f439a2cca61e4db8a", "path": "GoogleSignIn/Sources/Strings/es.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98fe897023fd55d07b9655ca362a349d7a", "path": "GoogleSignIn/Sources/Strings/es_MX.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e986599d9e1376a85193786b1fdc2dc1651", "path": "GoogleSignIn/Sources/Strings/fi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98ee60d1f76fb8785798eda74333f0b290", "path": "GoogleSignIn/Sources/Strings/fr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a81e0f510ab65e0896e18fde1d6622e3", "path": "GoogleSignIn/Sources/Strings/fr_CA.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e98ad61d502fd59e4153115f21d242a478a", "path": "GoogleSignIn/Sources/Resources/google.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e98896e54d37f98135888b2c2f61ee04eb1", "path": "GoogleSignIn/Sources/Resources/<EMAIL>", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e985059a6daec671fb40d7858e195a27e19", "path": "GoogleSignIn/Sources/Resources/<EMAIL>", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a774c0df822b772d626044e8dfbe2566", "path": "GoogleSignIn/Sources/Strings/he.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9870783699e3584b9d58d4f45c74073fa6", "path": "GoogleSignIn/Sources/Strings/hi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98ff3ada1ecc3320d8c46ff7af6e86a98c", "path": "GoogleSignIn/Sources/Strings/hr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e983ff37f368dabc6acce6aa03d2cf85037", "path": "GoogleSignIn/Sources/Strings/hu.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98c515d86fe661e067b992e046a92fb84d", "path": "GoogleSignIn/Sources/Strings/id.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98230f1028e4e652aeeb361e0c525fae5e", "path": "GoogleSignIn/Sources/Strings/it.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d4dc8b84c880c413400e39e3bc7c03a3", "path": "GoogleSignIn/Sources/Strings/ja.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e984bec5e44d78393689e1e55ca04a0cac4", "path": "GoogleSignIn/Sources/Strings/ko.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9813d84f58f6e9b08dfe60b98a8391fa35", "path": "GoogleSignIn/Sources/Strings/ms.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e987186bd7c914b310b90b0158ea5b663af", "path": "GoogleSignIn/Sources/Strings/nb.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e983a76fbf98850b6484b40ee390b93bd0d", "path": "GoogleSignIn/Sources/Strings/nl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98ed8c5509548de098be0aeec345452932", "path": "GoogleSignIn/Sources/Strings/pl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a5cfe381ce2eef5619f222888e854a76", "path": "GoogleSignIn/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e981b9598d13cb71f2fe7f76ee42e4e9866", "path": "GoogleSignIn/Sources/Strings/pt.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e989688dee9623cf77731c7c84d761cda77", "path": "GoogleSignIn/Sources/Strings/pt_BR.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e984e917d4b37d21bbf4ac05b016fbada57", "path": "GoogleSignIn/Sources/Strings/pt_PT.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98f7b2cb3a3d3f30fafa60cf832824c354", "path": "GoogleSignIn/Sources/Strings/ro.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "file", "guid": "bfdfe7dc352907fc980b868725387e986de5c300e0a4b04924a295d0affb72d6", "path": "GoogleSignIn/Sources/Resources/Roboto-Bold.ttf", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98805614e00c53f0ac59e1a9bb3dd4343d", "path": "GoogleSignIn/Sources/Strings/ru.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9816c61b1c620d626a8f4eb040fd4bac83", "path": "GoogleSignIn/Sources/Strings/sk.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9806193295e105de12c85ae0d49ac0c445", "path": "GoogleSignIn/Sources/Strings/sv.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e983d2bb87ed9b835c98091f17a556f6c3b", "path": "GoogleSignIn/Sources/Strings/th.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9881c3523f6db680ba25ea2c9661e2e3a0", "path": "GoogleSignIn/Sources/Strings/tr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98847f821f4e72b157f23b09408d1466e2", "path": "GoogleSignIn/Sources/Strings/uk.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98745cfbeac3bdf917dd271ecc1a27c978", "path": "GoogleSignIn/Sources/Strings/vi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e982c0cb8d008da03b8d15da3b8e77ad02d", "path": "GoogleSignIn/Sources/Strings/zh_CN.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98b47001ebe6fd35c2eb7bd51d0d4c5b20", "path": "GoogleSignIn/Sources/Strings/zh_TW.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a62dc5e63ddea06140e523ddd7345c0b", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980247c86cb2514b4db5b0acd4abe6755e", "path": "GoogleSignIn.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989923aa0865ab7de5f599085010f2a320", "path": "GoogleSignIn-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bb1a93fa36417ad4374914a0398eb0f9", "path": "GoogleSignIn-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e48637f3ccdc102acd2c7386bafa7d7", "path": "GoogleSignIn-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a2ade4ceda366bb9c5ad27e509beef80", "path": "GoogleSignIn.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9859c3b18b4a5885185381bdef62fc52cc", "path": "GoogleSignIn.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9872510d257c67d429ea1e38a9bce405c7", "path": "ResourceBundle-GoogleSignIn-GoogleSignIn-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98636071bb92f3fe8b6ba4bebcd3273e51", "name": "Support Files", "path": "../Target Support Files/GoogleSignIn", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883825d5c2335588048d984e5ad8a047d", "name": "GoogleSignIn", "path": "GoogleSignIn", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e19605d643d1d2a03b7414aeeb424e0d", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULAppDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982074b2166667d192f0fc1e8bd66da50c", "path": "GoogleUtilities/AppDelegateSwizzler/GULAppDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a2b2c71ca617955b2d9017cc27423b37", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULAppDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed1bcffec308bbe782711fceae31c475", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULApplication.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984f0a057d7b7c405ae60d0afd92ff309e", "path": "GoogleUtilities/Common/GULLoggerCodes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bcf636d7209eb8aecef961c5e965a177", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULSceneDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fa3a89b9937327c2a83136e0a185f480", "path": "GoogleUtilities/AppDelegateSwizzler/GULSceneDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb58fd8e476026f7e68552c853dd901f", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULSceneDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980cd5d6a932ec2c3d50302c5f2b942424", "name": "AppDelegateSwizzler", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98df885793686fc1075301decb2b396b1a", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULAppEnvironmentUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98583c82365a0a48c9cae385a0a11050c8", "path": "GoogleUtilities/Environment/GULAppEnvironmentUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f48a175c1ee811cbeaf0ca0e42276912", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9887af7868c042a365c229c972b8844713", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c563db6b1d2cb2caae13be72fbc50e1d", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9817cef0bc42178adc6e8011b5d12bd422", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982513dccffdffd006eac607f5b0826608", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULNetworkInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98211c3fd3a34fb6cbfdde8deda599db44", "path": "GoogleUtilities/Environment/NetworkInfo/GULNetworkInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0323f2ab40209fe14672b13f8c8e9c9", "path": "third_party/IsAppEncrypted/Public/IsAppEncrypted.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d5df668a9c3daa0a13f08377cf53d95f", "path": "third_party/IsAppEncrypted/IsAppEncrypted.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98efdfb7c5fe331dfd7ebd9dde92956be0", "name": "Environment", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988eb7750d3b02f3a227e3b293e6f3f589", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9800819c8faaa9d789cff471d3494b87ad", "path": "GoogleUtilities/Logger/GULLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ef9baea0f560c4fc61b658d3f8d02e5", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLoggerLevel.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a412c5578fd7db148604643c5c7af8b5", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c5092118f4bef7838d466583334ee244", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULMutableDictionary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985365d183a43975c08587f5a372af8462", "path": "GoogleUtilities/Network/GULMutableDictionary.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea0dfa6e9dbd7228563290440d6c48f6", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetwork.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af039e21294b36f99587e1232a1bcfd5", "path": "GoogleUtilities/Network/GULNetwork.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c029d754ba2c18ec8a93b096e8bb0e4c", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9825f5f481790d13255815592fcace18c6", "path": "GoogleUtilities/Network/GULNetworkConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf19a29c94a71aea41cc8c1d3d4a720b", "path": "GoogleUtilities/Network/GULNetworkInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9832eecbe7c503961daca0c03f7a51bc53", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkLoggerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983763c4d2768e47fdc8a18d75f2f10e66", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkMessageCode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a2a0e77afc34e248ae3ff8b708f44ba", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkURLSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988c64591d5d7750dd174ee6658a3e680a", "path": "GoogleUtilities/Network/GULNetworkURLSession.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985b5de395f4f2375a31966ceafcf5d67e", "name": "Network", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848e34c4fd9bbcce878e41eaad195db07", "path": "GoogleUtilities/NSData+zlib/Public/GoogleUtilities/GULNSData+zlib.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c8ebfef32e154a3c2c8983724ac9f00c", "path": "GoogleUtilities/NSData+zlib/GULNSData+zlib.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fbce468f3e5bbff4754ff64272e53d23", "name": "NSData+zlib", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9809c822d0b323619473fc13b98cc3e1b5", "path": "GoogleUtilities/Privacy/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987bfd5a0017c78e239e11de4535c4c4d7", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e360cd35b317a0e3eb9d64b6eb0eed9", "name": "Privacy", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837754b17a3f149c7bf5de6e77ff56b40", "path": "GoogleUtilities/Reachability/Public/GoogleUtilities/GULReachabilityChecker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981a00eeb05dd72445f824c2f5ca732d1c", "path": "GoogleUtilities/Reachability/GULReachabilityChecker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e5c223c5bcb1bb57d73b3941bd50149", "path": "GoogleUtilities/Reachability/GULReachabilityChecker+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e992c24651a1dec2f4692b1fced7a9f1", "path": "GoogleUtilities/Reachability/GULReachabilityMessageCode.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983e12a5fc5420e0919147929648317b9a", "name": "Reachability", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b5de38ef5b85fecb740e216b5c01528a", "path": "GoogleUtilities.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9858f75aadaa8e62a709333d81fd43ced6", "path": "GoogleUtilities-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98872d9a5c41b984a11d339d6d4c4b0323", "path": "GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98985a6b8243172593c9569b494428b6b5", "path": "GoogleUtilities-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986d53086f9bc9e27c4384487d48ecaa65", "path": "GoogleUtilities.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98eaec5e5c8dd01159403a6cd3b0a1dd0d", "path": "GoogleUtilities.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cc273175a176abad6c0ff8c1e047edba", "path": "ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98222ba74ff551748e1acc5fd7f27d3ee2", "name": "Support Files", "path": "../Target Support Files/GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a699d1401023d3754071c4cc285fbdb5", "path": "GoogleUtilities/UserDefaults/Public/GoogleUtilities/GULUserDefaults.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98de3a1dfbb72c3a862d774fd63e86b35f", "path": "GoogleUtilities/UserDefaults/GULUserDefaults.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d0d6550f6b18b405c14d6c7d4a424b2b", "name": "UserDefaults", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98264db9e0cc740f06ad0c29ddc362b00c", "name": "GoogleUtilities", "path": "GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98126cdb708ac37a6919d8b26b1cf789fa", "path": "GTMAppAuth/Sources/AuthSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984feab9bc28ea663a9c58784b6fffc045", "path": "GTMAppAuth/Sources/AuthSessionDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98890156118a52dda7897922e4ba0da0fe", "path": "GTMAppAuth/Sources/AuthSessionStore.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988fd92141f5147867ff8d116824cc6c94", "path": "GTMAppAuth/Sources/KeychainStore/GTMOAuth2Compatibility.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d593047c7211167fd8f5ddbd87b14bae", "path": "GTMAppAuth/Sources/KeychainStore/KeychainAttribute.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9820e0ab62c286441665ffba7f5c2c3e93", "path": "GTMAppAuth/Sources/KeychainStore/KeychainHelper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984d4a704afe5e8334ebb57660011003d6", "path": "GTMAppAuth/Sources/KeychainStore/KeychainStore.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d9aa466749c57908785d5b1cdf67e056", "path": "GTMAppAuth/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cd8fcddf57994267ff8d5fb7713384a0", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9886781ad07b6bca8c8bc997770057e93b", "path": "GTMAppAuth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d341d09ba9ebf12f05ca8cc9b1172760", "path": "GTMAppAuth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9853a3df72c524e944f446b8b847ffc108", "path": "GTMAppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980834c28f1da69993dc50edf3da6d0a64", "path": "GTMAppAuth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98231032fa0ef4c15bcf8809e931fabc95", "path": "GTMAppAuth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d749ae290732ca7c3309e55abab804b4", "path": "GTMAppAuth.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98dcf1e665ef3107d70c2b4a312d92e113", "path": "ResourceBundle-GTMAppAuth_Privacy-GTMAppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98de1098ba4089f781f2734b1725b107a0", "name": "Support Files", "path": "../Target Support Files/GTMAppAuth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb4b221683e38e6a377c0dc85fffa4f0", "name": "GTMAppAuth", "path": "GTMAppAuth", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c03f91efee9d6e878af6caefbf16169", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985667843c516b465ba1a043af68138796", "path": "Sources/Core/GTMSessionFetcher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9815f80edab65164cf48866b3a80636acf", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcherLogging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a788f607e31baf2eca919be3264c1518", "path": "Sources/Core/GTMSessionFetcherLogging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b863944304edcc56a6b738e336b3f6f2", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcherService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4ce15e9ea05708a4343b881f0a88180", "path": "Sources/Core/GTMSessionFetcherService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a89421a577365e5de4acec2029f2ca7b", "path": "Sources/Core/GTMSessionFetcherService+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98725428406de73808e7c9bdc7f13c2113", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionUploadFetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d217e6f64660eef7d45f5c6764be1d29", "path": "Sources/Core/GTMSessionUploadFetcher.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e986808b9fd29cdb174cab8550797bd5962", "path": "Sources/Core/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9836aabaf120d9d65598c8b1836c23394d", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e2aa4686d778fc09c3d6fcb2ce7d75ef", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9820df5350723d054e6294bf53401a9fcc", "path": "Sources/Full/Public/GTMSessionFetcher/GTMGatherInputStream.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f350c2d64d0af6d98ce8b5b9bc110458", "path": "Sources/Full/GTMGatherInputStream.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987c3afa80fc1ed90f972100cc470f88a4", "path": "Sources/Full/Public/GTMSessionFetcher/GTMMIMEDocument.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988fde32525d35ebf13acb5b16c5883525", "path": "Sources/Full/GTMMIMEDocument.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983629f6c7be76f1d4a49fec214284ee44", "path": "Sources/Full/Public/GTMSessionFetcher/GTMReadMonitorInputStream.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9816f51552bdd925077281706e9673bdac", "path": "Sources/Full/GTMReadMonitorInputStream.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e988a6d41b1eaf0753fc45d3654ac28a677", "path": "Sources/Full/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d3ac46f48d3ee1bd0f3730db331b5bc1", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f6792c3ef4d0990ad50f3091c638348", "name": "Full", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ca11913943fbfa9713f0946fa574438c", "path": "GTMSessionFetcher.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9876c505661d3eeb868fe92df5256f22f3", "path": "GTMSessionFetcher-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98660393dae3dd9b107f4ec5918f904578", "path": "GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dc7529eee8281d4392c8db8d268d4ccc", "path": "GTMSessionFetcher-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989d57d005c8531c1121c1cc62729f1b30", "path": "GTMSessionFetcher.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982b1c913001e713820ed6f947c73fd926", "path": "GTMSessionFetcher.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a1c7e30e97f5dab4cde36bfcc5e72ba7", "path": "ResourceBundle-GTMSessionFetcher_Core_Privacy-GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9891ecc3598e8f1135539ce49a4f732e11", "path": "ResourceBundle-GTMSessionFetcher_Full_Privacy-GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fd814d40f224841e4d2faac2a659d098", "name": "Support Files", "path": "../Target Support Files/GTMSessionFetcher", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c76e619509ae3bb6700e0e5a343651a", "name": "GTMSessionFetcher", "path": "GTMSessionFetcher", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f2feb9cfadf6014ee49cf1abcb3ea39", "path": "pb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986454072127b7d078f9d0b6c3eedf0b40", "path": "pb_common.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987da7e9004afacdd6aacdd3571a8c9be0", "path": "pb_common.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98923e1c57384375d790f302d1ad6db463", "path": "pb_decode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b428f446035f63ded6d5285ca6adca9f", "path": "pb_decode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98923b174c342db41833e985e1115e7c05", "path": "pb_encode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988cd497fced8d78751e265e3982c074da", "path": "pb_encode.h", "sourceTree": "<group>", "type": "file"}, {"guid": "bfdfe7dc352907fc980b868725387e9827fd9e2bdeb74e14d0b621d7914e4463", "name": "decode", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e9832a5455d64f14ac1637598e0ba552216", "name": "encode", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98bd903235d9ce90e4f965c95a76ff6ab1", "path": "spm_resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ea00b6effc0d43847355d7f02a4d32e6", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98652f1e188b305b668c118fa4a4ad458f", "path": "nanopb.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985895844867c317370996c1033d22fecf", "path": "nanopb-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a056f468f98890924510b4623c3430b2", "path": "nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a2bc410ea5b895167c292aeab3b2179", "path": "nanopb-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d432df5db1dc8bd3bf001bea456b40f", "path": "nanopb-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fdf9558b5a811afc91207bd256724feb", "path": "nanopb.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989759eab3dd8ed75505a686adeb75c1a2", "path": "nanopb.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98df312fbd02c63999d6d1479ed857dc93", "path": "ResourceBundle-nanopb_Privacy-nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98388dae49f3d2ad9154b4cfa3062ee5e5", "name": "Support Files", "path": "../Target Support Files/nanopb", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a62d05a11c6962cad3961a9ac2f3543a", "name": "nanopb", "path": "nanopb", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9852e0addb21e2d7aa0f5d7625212e3b94", "path": "Sources/FBLPromises/include/FBLPromise.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98570f9cb2f8340fba16c899a77afc8d3f", "path": "Sources/FBLPromises/FBLPromise.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828a8212b505f2e2797bcec9fa56cf484", "path": "Sources/FBLPromises/include/FBLPromise+All.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98de338375113d2bf7b25f11fe6d1ca2d9", "path": "Sources/FBLPromises/FBLPromise+All.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dea6fc1e110faac879c47e698c679754", "path": "Sources/FBLPromises/include/FBLPromise+Always.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98415627a93ef7363afd54bfcf4a8d4887", "path": "Sources/FBLPromises/FBLPromise+Always.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98802ac3812b8538d1f36c01aa4e8f98f7", "path": "Sources/FBLPromises/include/FBLPromise+Any.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989edfd1a543ea8855bea1404d9a618cd4", "path": "Sources/FBLPromises/FBLPromise+Any.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd5d06e0983378ab33a3db4b7aed958f", "path": "Sources/FBLPromises/include/FBLPromise+Async.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98121364798d0c381e1398725b1412d740", "path": "Sources/FBLPromises/FBLPromise+Async.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d673b3ce893a7455e185259d0a6d2d5d", "path": "Sources/FBLPromises/include/FBLPromise+Await.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9801ce7d2f862c93032c7840a8e4a8f512", "path": "Sources/FBLPromises/FBLPromise+Await.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1d1bec8986909546570b497ec65ceee", "path": "Sources/FBLPromises/include/FBLPromise+Catch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98507e0d62cfa23cf54b210d1c01bf4cdd", "path": "Sources/FBLPromises/FBLPromise+Catch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bff3d2cbcc8d3f18eeb3da0b728f899f", "path": "Sources/FBLPromises/include/FBLPromise+Delay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988624cc1028e81db024c8c5e158f3d586", "path": "Sources/FBLPromises/FBLPromise+Delay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eb565de2d9f8fad04e5690a2c949af6c", "path": "Sources/FBLPromises/include/FBLPromise+Do.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b471b09003e26800efa9743cedd0635c", "path": "Sources/FBLPromises/FBLPromise+Do.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c7e2e45e757cd1a8e5f221697d5adb83", "path": "Sources/FBLPromises/include/FBLPromise+Race.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a1e59af7f23bcc3441d3f40dfb9117b1", "path": "Sources/FBLPromises/FBLPromise+Race.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98118cbfdd1581f5242842541d994ddbcc", "path": "Sources/FBLPromises/include/FBLPromise+Recover.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9862d59d981dabe7b766589ee9c3834fa1", "path": "Sources/FBLPromises/FBLPromise+Recover.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98639eddc22b09d3ef7e57a03c565e316c", "path": "Sources/FBLPromises/include/FBLPromise+Reduce.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809e8a654b35b0507c2272148ead6c220", "path": "Sources/FBLPromises/FBLPromise+Reduce.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985425b6bbc8a649fc60ed5e69fbd66b13", "path": "Sources/FBLPromises/include/FBLPromise+Retry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982979bb64438c8da47fc3886bc1d38336", "path": "Sources/FBLPromises/FBLPromise+Retry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1fbf3209e712c269d3e1b0efe7407e0", "path": "Sources/FBLPromises/include/FBLPromise+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac0407a1b5bb3254a510a6ba72c43150", "path": "Sources/FBLPromises/FBLPromise+Testing.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984da42d5541631fc7325660fc33f23c41", "path": "Sources/FBLPromises/include/FBLPromise+Then.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e1e83e6423aa676a19167aabfaa64c41", "path": "Sources/FBLPromises/FBLPromise+Then.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980cef1443d232bb70e4477410f6dab1f8", "path": "Sources/FBLPromises/include/FBLPromise+Timeout.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9813c6525f80cfe045210e65b4462e09d4", "path": "Sources/FBLPromises/FBLPromise+Timeout.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bc961074f8a879732b94055041e797d2", "path": "Sources/FBLPromises/include/FBLPromise+Validate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b79e1e65aee2846b11bdeb3e4e3767ce", "path": "Sources/FBLPromises/FBLPromise+Validate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840db25f8922f45dc36ab08488e96b9a4", "path": "Sources/FBLPromises/include/FBLPromise+Wrap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e13a6403d0bdbe43f89c19417fe7cd53", "path": "Sources/FBLPromises/FBLPromise+Wrap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98168adcd9081f99d1cdfe6c7c207d4714", "path": "Sources/FBLPromises/include/FBLPromiseError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e3393aaa12efa7e258e5a9185db549dc", "path": "Sources/FBLPromises/FBLPromiseError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9843b52d5f188266d44edef58cfc4d45c5", "path": "Sources/FBLPromises/include/FBLPromisePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c3c31c4bbbcd9cd3360a45908dec70a", "path": "Sources/FBLPromises/include/FBLPromises.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98b911a62365cc34f3e95a295807b9e61e", "path": "Sources/FBLPromises/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f6b09c182a0d7268334b8c9b4926487d", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983dcc52723cd5c8f584a4451378588d63", "path": "PromisesObjC.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fba1a55832ed94cd91e6e338d14ce433", "path": "PromisesObjC-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987519c2125605dc29bbca42dc335a905f", "path": "PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a5ac93693fa4f1c56fe0bf37c67b92c", "path": "PromisesObjC-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982aaec622388e1762c97b2fd2b7266d0e", "path": "PromisesObjC.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9838f88a713611b26d5a146d669edf0e1a", "path": "PromisesObjC.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981a194051fddb61ad749a86ee441e537c", "path": "ResourceBundle-FBLPromises_Privacy-PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9844bbf3cbbdc72bc20c0f252ed5a7770d", "name": "Support Files", "path": "../Target Support Files/PromisesObjC", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6eff6e0c162ef5aeb2d450969217ad4", "name": "PromisesObjC", "path": "PromisesObjC", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb0a618f8193a842172e8989da3b78c8", "path": "RecaptchaEnterprise/RecaptchaInterop/placeholder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987701c3f98760742915e87fe08efc6cd0", "path": "RecaptchaEnterprise/RecaptchaInterop/Public/RecaptchaInterop/RCAActionProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983637021a823605289e48fb979d68dca7", "path": "RecaptchaEnterprise/RecaptchaInterop/Public/RecaptchaInterop/RCARecaptchaClientProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98595e79e7d1b2baadd2e667ba41cb1672", "path": "RecaptchaEnterprise/RecaptchaInterop/Public/RecaptchaInterop/RCARecaptchaProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d22b954d85ec74168137442a8c657ad5", "path": "RecaptchaEnterprise/RecaptchaInterop/Public/RecaptchaInterop/RecaptchaInterop.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e987d6ca4cdbf8c2c63a5f37d0e7553bf0d", "path": "RecaptchaInterop.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981a81ba350e6564b5301f6e48f4b37860", "path": "RecaptchaInterop-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9885c131c321e4b002c3d08983ff242f12", "path": "RecaptchaInterop-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98358550e784f6d28549d5cf083241790e", "path": "RecaptchaInterop-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eff9b33f8f79bd59210f5af794e0ca5b", "path": "RecaptchaInterop-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980d9146f0e94f12a6701b2745ac7a3777", "path": "RecaptchaInterop.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9877547763c00037a2af292e03077a38e8", "path": "RecaptchaInterop.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9877b959fa4726cae739eb5991546ec62f", "name": "Support Files", "path": "../Target Support Files/RecaptchaInterop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed29c602583b1a5676e576d6d4b3e775", "name": "RecaptchaInterop", "path": "RecaptchaInterop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f239f78aff244b2162e03f333f4d6abd", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98e00043a50800d767fe547b86b3ce93ce", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/Desktop/FLUTTER/db-eats/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/FLUTTER/db-eats/ios/Pods", "targets": ["TARGET@v11_hash=806e60f12bc125c17ad41d605a4c6fb4", "TARGET@v11_hash=c6e0248f2b0716ce6196088c918833f2", "TARGET@v11_hash=ba779c5cad6e4463865793c3ebbaf116", "TARGET@v11_hash=b6e26f73ad8667d8ad01527ecc9489fd", "TARGET@v11_hash=dd13ec7b11012c6066c3334b0a179c69", "TARGET@v11_hash=0c0ebc402bfc3756b2b5051c1c3decf1", "TARGET@v11_hash=a37b577fb3ea8bcebf61ebf2d8aabd35", "TARGET@v11_hash=12c0bd382f671886823b511e12bc82b2", "TARGET@v11_hash=79be1143339d7d44bf6f5c480d197d0a", "TARGET@v11_hash=9d86a1b0c462fe847895b639e32afe43", "TARGET@v11_hash=4d44e0cbff8c35d1c7233fae0b03c318", "TARGET@v11_hash=688c1c0cf44943a0522bf69cddee1f7d", "TARGET@v11_hash=33cac05f5b294aba07c69c1b41efb19c", "TARGET@v11_hash=cbe1eef23528be4b29ff025e6de0a5a6", "TARGET@v11_hash=64bd42359ab7f9e29b78b4d626ea63cf", "TARGET@v11_hash=11603706e2ac75ad33f73ddf34001848", "TARGET@v11_hash=77befe36850bf44713de3afcc77b0928", "TARGET@v11_hash=a99eba7c10ab9d0b02b92bc8e8fe4bf7", "TARGET@v11_hash=0cc2eec682fff3a2c837323dfc9870ba", "TARGET@v11_hash=49ca0df86486fe7c673b05e169eebdb2", "TARGET@v11_hash=38e35d678716dc5cd78d688479f49f1c", "TARGET@v11_hash=d4b3af2e8d889f35b045a0ef253360d9", "TARGET@v11_hash=2c9c1272cf76d3a05ea79b49e0870a95", "TARGET@v11_hash=52e062da991253b9ba74f15bc4643662", "TARGET@v11_hash=c479446950316b974e05fa4130bd682d", "TARGET@v11_hash=13bcc7480b44399ee2af62f267f3bc4f", "TARGET@v11_hash=033ac40e23ab9d3994beb8727f29442a", "TARGET@v11_hash=f63774868a7cce3e802dee9b7045fbb2", "TARGET@v11_hash=68cae022d674e2cfb2e946996ed4cec9", "TARGET@v11_hash=fc9d09d23047d84d0fa975d391fe0306", "TARGET@v11_hash=1d84ecfba3d72cde6480e2c843d5fbd6", "TARGET@v11_hash=6343966b8d0366cf7edb3460b7b163bc", "TARGET@v11_hash=b1162c746f2a3899c9c2b3bcafc13f25", "TARGET@v11_hash=7f93db6ab7a87d4bcc46cd8447a258ff", "TARGET@v11_hash=009c062d202ddc040a43c10843b0d9fc", "TARGET@v11_hash=7ad2743949a481965b62a63fb2efa933", "TARGET@v11_hash=fb83bde40a5d746c565d0458765dde67", "TARGET@v11_hash=6266065658fcca93dae08190e83d7e66", "TARGET@v11_hash=96d6cd26d97379e85fa639bc4df8ec8b", "TARGET@v11_hash=e22a41a48df92b3916f0763574786af5", "TARGET@v11_hash=49106d128eea52fc39c0a5dd490d61f2", "TARGET@v11_hash=5becb51a985f6734f400ab07e765b8af", "TARGET@v11_hash=81deeb4c0d1ead58eeb89b970390dcef", "TARGET@v11_hash=2beefcaa643de00e59080810b5328670", "TARGET@v11_hash=b32c93e08d0978a503980fa370bf111c", "TARGET@v11_hash=5d2b3e630a431647024afcdfaf134cba", "TARGET@v11_hash=cf8110499c48eee4f178e848fcb1513e", "TARGET@v11_hash=1aea0c8bf4844f07d9b927e189bbfac7", "TARGET@v11_hash=cb331236dcf2d9efc91acdd0388f2ba3", "TARGET@v11_hash=636c48546e8792c3b26a422b262515b2", "TARGET@v11_hash=fc64ba8ffcf97d3056096d0d59ea637c", "TARGET@v11_hash=279c191c511a310955d1eda379e999e3", "TARGET@v11_hash=7f10fe31d055549fe99583a750704bf7", "TARGET@v11_hash=b6638fd658b92e870ebf98e5b80f0fc3", "TARGET@v11_hash=f684b61594dfadd9212bc9134795623c", "TARGET@v11_hash=b8d2fab615fad4784433da2ee9cb74eb", "TARGET@v11_hash=4755c9cda4a969ef780e90d5536da486", "TARGET@v11_hash=801ae566b78b309d175dcaab7d4af980", "TARGET@v11_hash=322b0f3f83d2073bd5e1104ef5d38f1b", "TARGET@v11_hash=af4986c164fbfa713f3402b5ec9a87cb", "TARGET@v11_hash=dfc09f985c3b1c788885628f430296c8", "TARGET@v11_hash=6015a701b38f6d0994d9bf68bc29fdb9", "TARGET@v11_hash=06dd59ab4cffefedf4d1d805dcd50de7", "TARGET@v11_hash=131e64c24ccb0e0b0247f68855b21bb7"]}