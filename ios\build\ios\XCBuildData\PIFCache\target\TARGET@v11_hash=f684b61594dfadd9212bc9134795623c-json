{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982aaec622388e1762c97b2fd2b7266d0e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987bb414cb776820072fe070f56f48a2c9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838f88a713611b26d5a146d669edf0e1a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98462331d247df18c749fa90f0ab12a33a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838f88a713611b26d5a146d669edf0e1a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fe3fdde9909508e58144aa7590848744", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9852e0addb21e2d7aa0f5d7625212e3b94", "guid": "bfdfe7dc352907fc980b868725387e9827a81d45eb5c3ed5cead78b7896c3078", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828a8212b505f2e2797bcec9fa56cf484", "guid": "bfdfe7dc352907fc980b868725387e98e10fbcd9e5cc29e758ff721157b5f37b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dea6fc1e110faac879c47e698c679754", "guid": "bfdfe7dc352907fc980b868725387e98c99f33a53a2060d214b7d80f53641dcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98802ac3812b8538d1f36c01aa4e8f98f7", "guid": "bfdfe7dc352907fc980b868725387e98d2881bf146839efca132dbd11052d35a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd5d06e0983378ab33a3db4b7aed958f", "guid": "bfdfe7dc352907fc980b868725387e9891b49363b9b6bcc69b26d646d89fffc4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d673b3ce893a7455e185259d0a6d2d5d", "guid": "bfdfe7dc352907fc980b868725387e989c2347716a21e1e8541ce52ba8301667", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1d1bec8986909546570b497ec65ceee", "guid": "bfdfe7dc352907fc980b868725387e98d1fc7bb4f70c1160bd987e59ed76f93e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bff3d2cbcc8d3f18eeb3da0b728f899f", "guid": "bfdfe7dc352907fc980b868725387e988be634610a6758d4037501f776b63826", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb565de2d9f8fad04e5690a2c949af6c", "guid": "bfdfe7dc352907fc980b868725387e989e6985658fa28f777d3221fee52cb5a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7e2e45e757cd1a8e5f221697d5adb83", "guid": "bfdfe7dc352907fc980b868725387e98330c3ac19d36fdaff44d5f1f6536158c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98118cbfdd1581f5242842541d994ddbcc", "guid": "bfdfe7dc352907fc980b868725387e98fb9eb069a316d5248d06cb0963db86d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98639eddc22b09d3ef7e57a03c565e316c", "guid": "bfdfe7dc352907fc980b868725387e980fee9d253523be83fa60fcc35bf13e0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985425b6bbc8a649fc60ed5e69fbd66b13", "guid": "bfdfe7dc352907fc980b868725387e98965a0d188676e93efe9974ddd4e66819", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1fbf3209e712c269d3e1b0efe7407e0", "guid": "bfdfe7dc352907fc980b868725387e9851c92fc138bd1058b611f96cbaf222a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984da42d5541631fc7325660fc33f23c41", "guid": "bfdfe7dc352907fc980b868725387e983ae2aa0b026812f88d5f9e88664c1439", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cef1443d232bb70e4477410f6dab1f8", "guid": "bfdfe7dc352907fc980b868725387e983239601a9efd6a7264a2d326916d9400", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc961074f8a879732b94055041e797d2", "guid": "bfdfe7dc352907fc980b868725387e984e5db97c5662981719c583d0f32491a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840db25f8922f45dc36ab08488e96b9a4", "guid": "bfdfe7dc352907fc980b868725387e9856796c2d710b51506c3aff2142a2b9f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98168adcd9081f99d1cdfe6c7c207d4714", "guid": "bfdfe7dc352907fc980b868725387e98dd0439b5b1685247c003d7e07e9f187e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843b52d5f188266d44edef58cfc4d45c5", "guid": "bfdfe7dc352907fc980b868725387e9881dc6346897ef6c640466c51344dd5de", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c3c31c4bbbcd9cd3360a45908dec70a", "guid": "bfdfe7dc352907fc980b868725387e98a02e85bc93ec74a1aa4f422a60e30721", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a5ac93693fa4f1c56fe0bf37c67b92c", "guid": "bfdfe7dc352907fc980b868725387e987320041b255c7e19d55beedb4624677e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981d1f8ec70f7f22ed22f79353bd3b98db", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98570f9cb2f8340fba16c899a77afc8d3f", "guid": "bfdfe7dc352907fc980b868725387e989c7919fe41f064ea3a437c7a7af34479"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de338375113d2bf7b25f11fe6d1ca2d9", "guid": "bfdfe7dc352907fc980b868725387e98422f0dc3b69835b2f5665cae1bcfb89c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98415627a93ef7363afd54bfcf4a8d4887", "guid": "bfdfe7dc352907fc980b868725387e9886795ffc23f620dd4eac9249c54aad2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989edfd1a543ea8855bea1404d9a618cd4", "guid": "bfdfe7dc352907fc980b868725387e98025d9bd65db9316eb55cab7ab579a348"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98121364798d0c381e1398725b1412d740", "guid": "bfdfe7dc352907fc980b868725387e982dc3212b2959f3000ecc9bdb4d8e2937"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801ce7d2f862c93032c7840a8e4a8f512", "guid": "bfdfe7dc352907fc980b868725387e98b4f24ad9eef2c81753b6be3159d1c21d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98507e0d62cfa23cf54b210d1c01bf4cdd", "guid": "bfdfe7dc352907fc980b868725387e9826ec60e08f509b28f52092e7b112a19e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988624cc1028e81db024c8c5e158f3d586", "guid": "bfdfe7dc352907fc980b868725387e98af8e671a64acba532c3842848f6a126a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b471b09003e26800efa9743cedd0635c", "guid": "bfdfe7dc352907fc980b868725387e986bfc580a04a9cdb1407ac05714ebe4a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1e59af7f23bcc3441d3f40dfb9117b1", "guid": "bfdfe7dc352907fc980b868725387e98e65b68a07bbcdc7f549483a791517dc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862d59d981dabe7b766589ee9c3834fa1", "guid": "bfdfe7dc352907fc980b868725387e98465995e0e51cc17580390d1d0af45ef8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809e8a654b35b0507c2272148ead6c220", "guid": "bfdfe7dc352907fc980b868725387e98584aaedad82e40fa3b4b86809a29f6d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982979bb64438c8da47fc3886bc1d38336", "guid": "bfdfe7dc352907fc980b868725387e9844684d6ee2b8631fdec48f10339ea4e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac0407a1b5bb3254a510a6ba72c43150", "guid": "bfdfe7dc352907fc980b868725387e98e79a543389db87f45a7396e8501b65a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1e83e6423aa676a19167aabfaa64c41", "guid": "bfdfe7dc352907fc980b868725387e98bc37afaa6f1ed28ec2c70455dbcdd368"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813c6525f80cfe045210e65b4462e09d4", "guid": "bfdfe7dc352907fc980b868725387e98cfa39f08bc6e10490b46c4675494adac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b79e1e65aee2846b11bdeb3e4e3767ce", "guid": "bfdfe7dc352907fc980b868725387e980a6365c7423c4b064fd42bfb5232bb40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13a6403d0bdbe43f89c19417fe7cd53", "guid": "bfdfe7dc352907fc980b868725387e988a8ce6ca52bbf7ae6356aef17127f6a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3393aaa12efa7e258e5a9185db549dc", "guid": "bfdfe7dc352907fc980b868725387e98a717c2eb8defcc69b01993dd9d53aa53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fba1a55832ed94cd91e6e338d14ce433", "guid": "bfdfe7dc352907fc980b868725387e982bb9a6561f95714db8d7e1561240e2cf"}], "guid": "bfdfe7dc352907fc980b868725387e98559491256d711f262e3939b1e2044926", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e984a3fd3275696484d4629dcd1cf45a3df"}], "guid": "bfdfe7dc352907fc980b868725387e989468ce09c490110f926266cb37945abb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989db0f47c1e80ebe4fb70f2ab6baf31a5", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9874e7187cc9480acbb4d0d21cc97a17ab", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}