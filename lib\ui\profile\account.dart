import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/storage/localstorage.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/ui/home.dart';
import 'package:db_eats/ui/monthlysaverSubscription/monthlySubscription.dart';
import 'package:db_eats/ui/profile/address.dart';
import 'package:db_eats/ui/profile/dabbawallet.dart';
import 'package:db_eats/ui/profile/faq/manage_faq.dart';
import 'package:db_eats/ui/profile/favorites.dart';
import 'package:db_eats/ui/profile/profile.dart';
import 'package:db_eats/ui/profile/settings.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AccountPage extends StatefulWidget {
  const AccountPage({super.key});

  @override
  State<AccountPage> createState() => _AccountPageState();
}

class _AccountPageState extends State<AccountPage> {
  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    void openCart() {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const CartPage()),
      );
    }

    const Color backgroundColor = Color(0xFFf6f3ec);

    return BlocListener<AccountBloc, AccountState>(
      listener: (context, state) {
        if (state is GetCartCountSuccess) {
          setState(() {});
        }
      },
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) async {
          if (state is LogoutLoading) {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (_) => const Center(child: CircularProgressIndicator()),
            );
          } else if (state is LogoutSuccess) {
            Navigator.of(context).pop(); // close loading
            await LocalStorage.clearTokens();
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (_) => const Home()),
              (route) => false,
            );
          } else if (state is LogoutFailed) {
            Navigator.of(context).pop(); // close loading
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          }
        },
        child: Scaffold(
          backgroundColor: backgroundColor,
          appBar: AppBar(
            backgroundColor: backgroundColor,
            elevation: 0,
            automaticallyImplyLeading: false,
            centerTitle: false,
            title: Text(
              'Account',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w700,
                fontSize: twenty,
                color: Colors.black,
              ),
            ),
          ),
          body: ListView(
            children: [
              SizedBox(height: sixteen / 4),
              _buildOptionRow(
                context,
                'Account Info',
                'assets/icons/account.png',
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const AccountInfoPage()),
                ),
              ),
              _buildOptionRow(
                context,
                'Favorites',
                'assets/icons/favorites.png',
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const FavoritesPage()),
                ),
              ),
              _buildOptionRow(
                context,
                'Saved Addresses',
                'assets/icons/location.png',
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const SavedAddressPage()),
                ),
              ),
              _buildOptionRow(
                context,
                "Monthly Saver's Plan",
                'assets/icons/percent.png',
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => MonthlySubscriptionPage()),
                ),
              ),
              _buildOptionRow(
                context,
                'Settings',
                'assets/icons/settings.png',
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const SettingsPage()),
                ),
              ),
              _buildOptionRow(
                context,
                'Eatro-Wallet',
                'assets/icons/payment.png',
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const DabbaWalletPage()),
                ),
              ),
              _buildOptionRow(
                context,
                'Help Center',
                'assets/icons/help.png',
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const ManageFAQScreen()),
                ),
              ),
              _buildOptionRow(
                context,
                'Logout',
                'assets/icons/logout.png',
                () => _handleLogout(context),
              ),
            ],
          ),
          floatingActionButton: CartFloatingActionButton(
            itemCount: Initializer.cartCount ?? 0,
            onPressed: openCart,
          ),
        ),
      ),
    );
  }

  Future<void> _handleLogout(BuildContext context) async {
    final notificationId = await LocalStorage.getPlayerId();
    context.read<HomeBloc>().add(
          LogoutEvent(data: {"notification_id": notificationId}),
        );
  }

  Widget _buildOptionRow(
    BuildContext context,
    String title,
    String iconPath,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: sixteen, vertical: ten * 1.3),
        child: Row(
          children: [
            Image.asset(iconPath, width: eighteen, height: eighteen),
            SizedBox(width: sixteen),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  fontSize: forteen,
                ),
              ),
            ),
            Image.asset(
              'assets/icons/right.png',
              width: ten * 1.1,
              height: ten * 1.1,
            ),
          ],
        ),
      ),
    );
  }
}
