{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d74ba8931f5d89d9924043ff9b70e42b", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseInstallations", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseInstallations", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "FirebaseInstallations_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98b76b56dbfc1afa4bf1041653b498b4d9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba6b38401a0b68d7b07af48e31c997d0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseInstallations", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseInstallations", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "FirebaseInstallations_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98fe0b2a923b4b36b5cec645580f8b7fec", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba6b38401a0b68d7b07af48e31c997d0", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseInstallations", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseInstallations", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "FirebaseInstallations_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9865e147ab1eb3cb36d5bda04aba337f7e", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98d7c48875d6a699b1378eb90301d20258", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e986e5b0505ec2fab2566688f4d5bb6a8ce", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9868dea8a40d43330f6d1885fa169e83f3", "guid": "bfdfe7dc352907fc980b868725387e98defc005da3acd579167cc1cf92a5c9ac"}], "guid": "bfdfe7dc352907fc980b868725387e98e5d623a223356d61279506ab49d65a3c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981703d6bed554c9878c28cb40b989a332", "name": "FirebaseInstallations_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}