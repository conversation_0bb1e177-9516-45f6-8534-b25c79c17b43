import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/chef/view_chef2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/data/models/guesthome/recommendedchefsmodel.dart';
import 'package:shimmer/shimmer.dart';
import 'package:geolocator/geolocator.dart';

class RecommendedChefsPage extends StatefulWidget {
  const RecommendedChefsPage({Key? key}) : super(key: key);

  @override
  State<RecommendedChefsPage> createState() => _RecommendedChefsPageState();
}

class _RecommendedChefsPageState extends State<RecommendedChefsPage> {
  final ScrollController _scrollController = ScrollController();
  double? _currentLatitude;
  double? _currentLongitude;
  bool _isFetchingLocation = true;

  // Color scheme
  final Color _backgroundColor = const Color(0xFFF6F3EC);
  final Color _cardColor = Colors.white;
  final Color _primaryTextColor = const Color(0xFF1F2122);
  final Color _secondaryTextColor = const Color(0xFF414346);
  final Color _accentColor = const Color(0xFF2E7D32);

  @override
  void initState() {
    super.initState();
    _getLocationAndLoadChefs();
  }

  Future<void> _getLocationAndLoadChefs() async {
    try {
      // // Check location permissions using Initializer
      // bool isPermissionGranted =
      //     await Initializer.checkLocationPermission(context);
      // if (!isPermissionGranted) {
      //   setState(() {
      //     _isFetchingLocation = false;
      //   });
      //   return;
      // }

      // Get latitude and longitude from Initializer
      _currentLatitude = Initializer().getLatitude;
      _currentLongitude = Initializer().getLongitude;

      // Check if coordinates are available
      if (_currentLatitude != null && _currentLongitude != null) {
        _loadRecommendedChefs(_currentLatitude!, _currentLongitude!);
      } else {
        // Fallback: If coordinates are not available, try to get current position
        final position = await Geolocator.getCurrentPosition();
        _currentLatitude = position.latitude;
        _currentLongitude = position.longitude;
        // Optionally update Initializer with new coordinates
        Initializer().setCoordinates(_currentLatitude!, _currentLongitude!);
        _loadRecommendedChefs(_currentLatitude!, _currentLongitude!);
      }
    } catch (e) {
      print('Error fetching location: $e');
    } finally {
      setState(() {
        _isFetchingLocation = false;
      });
    }
  }

  void _loadRecommendedChefs(double latitude, double longitude) {
    context.read<HomeBloc>().add(
          GetRecommendedChefsEvent(
            latitude: latitude,
            longitude: longitude,
          ),
        );
  }

  Future<void> _navigateBack() async {
    final savedFilters = await Initializer.getAppliedFilters();

    // Create request data with coordinates regardless of filter status
    final requestData = <String, dynamic>{
      'latitude': Initializer.latitude,
      'longitude': Initializer.longitude,
    };

    // Add filter data if it exists
    if (savedFilters != null) {
      requestData.addAll(savedFilters);
    }

    // Call the event with either just coordinates or coordinates + filters
    context.read<HomeBloc>().add(GetHomeDataEvent(data: requestData));
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final baseTextSize = getResponsiveSize(context);

    return WillPopScope(
      onWillPop: () async {
        _navigateBack();
        return false;
      },
      child: Scaffold(
        backgroundColor: _backgroundColor,
        appBar: AppBar(
          backgroundColor: _backgroundColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: _primaryTextColor,
              size: isLandscape ? size.height * 0.035 : baseTextSize * 1.5,
            ),
            onPressed: _navigateBack,
          ),
          title: Text(
            'Recommended Chefs',
            style: TextStyle(
              color: _primaryTextColor,
              fontSize: isLandscape ? size.height * 0.035 : baseTextSize * 1.4,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
            ),
          ),
        ),
        body: _isFetchingLocation
            ? _buildShimmerLoading()
            : BlocBuilder<HomeBloc, HomeState>(
                builder: (context, state) {
                  if (state is RecommendedChefsLoading) {
                    return _buildShimmerLoading();
                  }

                  if (state is RecommendedChefsSuccess) {
                    final chefs = state.data.data?.recommendedChefs ?? [];

                    if (chefs.isEmpty) {
                      return Center(
                        child: Text(
                          'No chefs available in your area',
                          style: TextStyle(
                            color: _secondaryTextColor,
                            fontSize: 16,
                            fontFamily: 'Inter',
                          ),
                        ),
                      );
                    }

                    return GridView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount:
                            MediaQuery.of(context).size.width > 600 ? 3 : 2,
                        childAspectRatio: 0.85,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                      ),
                      itemCount: chefs.length,
                      itemBuilder: (context, index) {
                        return _buildChefCard(chefs[index]);
                      },
                    );
                  }

                  if (state is RecommendedChefsFailed) {
                    return Center(
                      child: Text(
                        'Failed to load chefs. Please try again.',
                        style: TextStyle(
                          color: _secondaryTextColor,
                          fontSize: 16,
                          fontFamily: 'Inter',
                        ),
                      ),
                    );
                  }

                  return _buildShimmerLoading();
                },
              ),
      ),
    );
  }

  String _formatDistance(double? meters) {
    if (meters == null) return '0.0 km';
    final kilometers = meters / 1000;
    return '${kilometers.toStringAsFixed(1)} km';
  }

  Widget _buildChefCard(RecommendedChefs chef) {
    final size = MediaQuery.of(context).size;
    final cardWidth = (size.width - 48) / (size.width > 600 ? 3 : 2);
    final imageHeight = cardWidth * 0.6;
    final profileSize = cardWidth * 0.25;

    final operationDays = chef.chef?.operationDays
            ?.map((d) => d.day?.name?.substring(0, 1))
            .join(', ') ??
        '';

    return GestureDetector(
      onTap: () {
        final currentChefId = chef.chefId ?? 0;
        context.read<HomeBloc>().add(ViewChefDetailsEvent(
              data: {
                'chef_id': currentChefId.toString(),
                'latitude': Initializer.latitude.toString(),
                'longitude': Initializer.longitude.toString(),
              },
            ));

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ViewChef2(
              id: currentChefId,
              title:
                  '${chef.chef?.firstName ?? ''} ${chef.chef?.lastName ?? ''}',
              distance: _formatDistance(chef.distance),
              fromPage: 'RecommendedChefs',
            ),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    // Cover Photo
                    Container(
                      height: imageHeight,
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(12)),
                        image: DecorationImage(
                          image: NetworkImage(
                              ServerHelper.imageUrl + (chef.coverPhoto ?? '')),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    // Profile Photo
                    Positioned(
                      left: 16,
                      bottom: -profileSize / 2,
                      child: Container(
                        width: profileSize,
                        height: profileSize,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(profileSize / 2),
                          child: Image.network(
                            ServerHelper.imageUrl + (chef.profilePhoto ?? ''),
                            fit: BoxFit.cover,
                            errorBuilder: (_, __, ___) => Icon(
                              Icons.person,
                              size: profileSize * 0.6,
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Distance chip
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: cardWidth * 0.06,
                          vertical: cardWidth * 0.02,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.location_on_outlined,
                                size: cardWidth * 0.06,
                                color: Color(0xFF1F2122)),
                            SizedBox(width: 4),
                            Text(
                              _formatDistance(chef.distance),
                              style: TextStyle(
                                fontSize: cardWidth * 0.05,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: profileSize / 2 + 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${chef.chef?.firstName ?? ''} ${chef.chef?.lastName ?? ''}',
                        style: TextStyle(
                          fontSize: cardWidth * 0.08,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Inter',
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4),
                      Text(
                        chef.searchTags?.join(', ') ?? '',
                        style: TextStyle(
                          fontSize: cardWidth * 0.06,
                          color: Color(0xFF414346),
                          fontFamily: 'Inter',
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4),
                      Row(
                        children: [
                          Image.asset(
                            'assets/icons/calender_2.png',
                            width: cardWidth * 0.05,
                            height: cardWidth * 0.05,
                            color: Color(0xFF414346),
                          ),
                          SizedBox(width: 4),
                          Text(
                            operationDays,
                            style: TextStyle(
                              fontSize: cardWidth * 0.06,
                              color: Color(0xFF414346),
                              fontFamily: 'Inter',
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Positioned(
              right: 8,
              bottom: 8,
              child: Container(
                padding: EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Color(0xFFE1E3E6),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.arrow_forward,
                  size: cardWidth * 0.06,
                  color: Color(0xFF1F2122),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerLoading() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: GridView.builder(
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: MediaQuery.of(context).size.width > 600 ? 3 : 2,
            childAspectRatio: 0.85,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: 6,
          itemBuilder: (_, __) => _buildShimmerCard(),
        ),
      ),
    );
  }

  Widget _buildShimmerCard() {
    return Card(
      elevation: 1,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image placeholder
          Container(
            height: 120,
            width: double.infinity,
            color: Colors.white,
          ),
          // Content placeholders
          Padding(
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Name placeholder
                Container(
                  width: double.infinity,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(height: 8),
                // Tags placeholder
                Container(
                  width: double.infinity,
                  height: 10,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  width: 100,
                  height: 10,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
                const SizedBox(height: 8),
                // Rating placeholder
                Row(
                  children: [
                    Container(
                      width: 14,
                      height: 14,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Container(
                      width: 60,
                      height: 10,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(5),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const Spacer(),
          // View menu button placeholder
          Container(
            width: double.infinity,
            height: 36,
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 60,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  double getResponsiveSize(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return size.width * 0.04;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
