class ListIssuesModel {
  bool? status;
  String? message;
  int? statusCode;
  IssuesData? data;

  ListIssuesModel({this.status, this.message, this.statusCode, this.data});

  ListIssuesModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? IssuesData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class IssuesData {
  List<ListedIssueData>? issues;
  PaginationData? pagination;

  IssuesData({this.issues, this.pagination});

  IssuesData.fromJson(Map<String, dynamic> json) {
    if (json['issues'] != null) {
      issues = <ListedIssueData>[];
      json['issues'].forEach((v) {
        issues!.add(ListedIssueData.fromJson(v));
      });
    }
    pagination = json['pagination'] != null
        ? PaginationData.fromJson(json['pagination'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (issues != null) {
      data['issues'] = issues!.map((v) => v.toJson()).toList();
    }
    if (pagination != null) {
      data['pagination'] = pagination!.toJson();
    }
    return data;
  }
}

class PaginationData {
  int? totalLength;
  int? page;
  int? limit;

  PaginationData({this.totalLength, this.page, this.limit});

  PaginationData.fromJson(Map<String, dynamic> json) {
    totalLength = json['totalLength'];
    page = json['page'];
    limit = json['limit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['totalLength'] = totalLength;
    data['page'] = page;
    data['limit'] = limit;
    return data;
  }
}

class ListedIssueData {
  int? id;
  String? ticketNumber;
  int? categoryid;
  String? categoryName;
  String? description;
  String? orderNumber;
  String? createdAt;
  String? status;

  ListedIssueData(
      {this.id,
      this.ticketNumber,
      this.categoryid,
      this.categoryName,
      this.description,
      this.orderNumber,
      this.createdAt,
      this.status});

  ListedIssueData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    ticketNumber = json['ticket_number'];
    categoryid = json['categoryid'];
    categoryName = json['category_name'];
    description = json['description'];
    orderNumber = json['order_number'];
    createdAt = json['created_at'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['ticket_number'] = this.ticketNumber;
    data['categoryid'] = this.categoryid;
    data['category_name'] = this.categoryName;
    data['description'] = this.description;
    data['order_number'] = this.orderNumber;
    data['created_at'] = this.createdAt;
    data['status'] = this.status;
    return data;
  }
}
