class SpiceLevelListModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  SpiceLevelListModel({this.status, this.message, this.statusCode, this.data});

  SpiceLevelListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<SpiceLevels>? spiceLevels;

  Data({this.spiceLevels});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['spice_levels'] != null) {
      spiceLevels = <SpiceLevels>[];
      json['spice_levels'].forEach((v) {
        spiceLevels!.add(new SpiceLevels.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.spiceLevels != null) {
      data['spice_levels'] = this.spiceLevels!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SpiceLevels {
  int? id;
  String? name;

  SpiceLevels({this.id, this.name});

  SpiceLevels.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}
