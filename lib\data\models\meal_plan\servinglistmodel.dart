class ServingsListModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  ServingsListModel({this.status, this.message, this.statusCode, this.data});

  ServingsListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<ServingSizes>? servingSizes;

  Data({this.servingSizes});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['serving_sizes'] != null) {
      servingSizes = <ServingSizes>[];
      json['serving_sizes'].forEach((v) {
        servingSizes!.add(new ServingSizes.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.servingSizes != null) {
      data['serving_sizes'] =
          this.servingSizes!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ServingSizes {
  int? id;
  String? title;
  String? description;
  int? serves;

  ServingSizes({this.id, this.title, this.description, this.serves});

  ServingSizes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
    serves = json['serves'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['description'] = this.description;
    data['serves'] = this.serves;
    return data;
  }
}
