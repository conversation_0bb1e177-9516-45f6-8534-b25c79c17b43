import 'dart:developer';

import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/new_meal_plan/mealplanprogresslatest.dart';
import 'package:db_eats/data/models/new_meal_plan/newfilteredchefsmodel.dart';
import 'package:db_eats/data/models/new_meal_plan/newfiltereddishesmodel.dart';
import 'package:db_eats/data/models/verifyrefreshtokenmodel.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/storage/localstorage.dart';

class NewmealplanBloc extends Bloc<NewMealPlanEvent, NewMealPlanState> {
  NewmealplanBloc() : super(ListNewMealPlanSummaryLoading()) {
    on<RefreshTokenEvent>(_refreshToken);
    on<ListNewMealPlanSummaryEvent>(_listNewMealPlanSummary);
    on<NewStep1MEalPlanEvent>(_newStep1MealPlan);
    on<NewStep2MEalPlanEvent>(_newStep2MealPlan);
    on<NewStep3MEalPlanEvent>(_newStep3MealPlan);
    on<NewStep4MEalPlanEvent>(_newStep4MealPlan);
    on<NewCheckoutMealPlanEvent>(_newCheckoutMealPlan);
    on<NewFilterChefsEvent>(_newFilterChefs);
    on<NewFilterDishesEvent>(_newFilterDishes);
    on<NewEditChefEvent>(_newEditChef);
  }

  Future<void> _refreshToken(
      RefreshTokenEvent event, Emitter<NewMealPlanState> emit) async {
    emit(RefreshTokenLoading());
    try {
      Initializer.verifyRefreshTokenModel = VerifyRefreshTokenModel.fromJson(
          await ServerHelper.getrefresh(
              '/v1/customer/auth/verify-refresh-token'));
      if (Initializer.verifyRefreshTokenModel.status == true) {
        await LocalStorage.setAccessToken(
            Initializer.verifyRefreshTokenModel.data?.accessToken ?? "");
        emit(RefreshTokenSuccess());
        if (event.nextEvent != null) {
          add(event.nextEvent!);
        }
      } else if (Initializer.verifyRefreshTokenModel.statusCode == 401) {
        emit(RefreshTokenFailed());
        // await LocalStorage.setAccessToken('');
        // await LocalStorage.setRefreshToken('');
        // Navigator.of(context).pushAndRemoveUntil(
        //   MaterialPageRoute(builder: (context) => const Home()),
        //   (route) => false,
        // );
      }
    } catch (e) {
      emit(RefreshTokenFailed());
    }
  }

  Future<void> _listNewMealPlanSummary(
      ListNewMealPlanSummaryEvent event, Emitter<NewMealPlanState> emit) async {
    emit(ListNewMealPlanSummaryLoading());
    try {
      final eventData = event.data ?? {};
      final filteredData = <String, dynamic>{};
      eventData.forEach((key, value) {
        if (value != null) {
          filteredData[key] = value;
        }
      });

      final response = await ServerHelper.post1(
          '/v1/customer/meal_plan_latest/progress', filteredData);
      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }
      log('New Meal plan Progress Response: $response');
      Initializer.mealPlanProgressLatestModel =
          MealPlanProgressLatestModel.fromJson(response);
      if (Initializer.mealPlanProgressLatestModel.status == true) {
        emit(NewMealPlanSummaryLoaded(
            Initializer.mealPlanProgressLatestModel.data));
      } else {
        emit(NewMealPlanSummaryLoadFailed(
            response.message ?? 'Failed to load meal plan summary'));
      }
    } catch (e) {
      emit(NewMealPlanSummaryLoadFailed(e.toString()));
    }
  }

  Future<void> _newStep1MealPlan(
      NewStep1MEalPlanEvent event, Emitter<NewMealPlanState> emit) async {
    emit(NewMealPlanStep1StateLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/meal_plan_latest/step1', event.data);
      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }
      log('Step 1 Meal Plan Response: $response');
      if (response['status'] == true) {
        emit(NewMealPlanStep1StateSuccess(response));
      } else {
        emit(NewMealPlanStep1StateLoadFailed(response['message'] ?? ''));
      }
    } catch (e) {
      emit(NewMealPlanStep1StateLoadFailed(e.toString()));
    }
  }

  Future<void> _newStep2MealPlan(
      NewStep2MEalPlanEvent event, Emitter<NewMealPlanState> emit) async {
    emit(NewMealPlanStep2StateLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/meal_plan_latest/step2', event.data);
      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }
      log('Step 2 Meal Plan Response: $response');
      if (response['status'] == true) {
        emit(NewMealPlanStep2StateSuccess(response));
      } else {
        emit(NewMealPlanStep2StateLoadFailed(response['message'] ?? ''));
      }
    } catch (e) {
      emit(NewMealPlanStep2StateLoadFailed(e.toString()));
    }
  }

  Future<void> _newStep3MealPlan(
      NewStep3MEalPlanEvent event, Emitter<NewMealPlanState> emit) async {
    emit(NewMealPlanStep3StateLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/meal_plan_latest/step3', event.data);
      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }
      log('Step 3 Meal Plan Response: $response');
      if (response['status'] == true) {
        emit(NewMealPlanStep3StateSuccess(response));
      } else {
        emit(NewMealPlanStep3StateLoadFailed(response['message'] ?? ''));
      }
    } catch (e) {
      emit(NewMealPlanStep3StateLoadFailed(e.toString()));
    }
  }

  Future<void> _newStep4MealPlan(
      NewStep4MEalPlanEvent event, Emitter<NewMealPlanState> emit) async {
    emit(NewMealPlanStep4StateLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/meal_plan_latest/step4', event.data);
      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }
      log('Step 4 Meal Plan Response: $response');
      if (response['status'] == true) {
        emit(NewMealPlanStep4StateSuccess(response));
      } else {
        emit(NewMealPlanStep4StateLoadFailed(response['message'] ?? ''));
      }
    } catch (e) {
      emit(NewMealPlanStep4StateLoadFailed(e.toString()));
    }
  }

  Future<void> _newCheckoutMealPlan(
      NewCheckoutMealPlanEvent event, Emitter<NewMealPlanState> emit) async {
    emit(NewCheckoutMealPlanStateLoading());
    try {
      log('Checkout meal plan event data: ${event.data}');
      final response = await ServerHelper.post1(
          '/v1/customer/meal_plan_latest/step7', event.data);
      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }
      log('Checkout Meal Plan Response: $response');
      if (response['status'] == true) {
        final paymentUrl = response['data']?['payment']?['checkout_url'] ??
            response['payment']?['checkout_url'] ??
            '';
        final orderId = response['data']?['payment']?['id'] ??
            response['payment']?['id'] ??
            '';

        log('Extracted payment URL: $paymentUrl');
        log('Extracted order ID: $orderId');

        emit(NewCheckoutMealPlanStateSuccess(
          response['message'] ?? 'Order placed successfully',
          response['data']?['total'] ?? 0,
          paymentUrl,
          orderId,
        ));
      } else {
        emit(NewCheckoutMealPlanStateLoadFailed(response['message'] ?? ''));
      }
    } catch (e) {
      emit(NewCheckoutMealPlanStateLoadFailed(e.toString()));
    }
  }

  Future<void> _newFilterChefs(
      NewFilterChefsEvent event, Emitter<NewMealPlanState> emit) async {
    emit(NewFilterChefsStateLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/meal_plan_latest/filter-chefs', event.data);
      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }
      log('Filter Chefs Response: $response');
      Initializer.newFilteredChefModel =
          NewFilteredChefModel.fromJson(response);
      if (Initializer.newFilteredChefModel.status == true) {
        emit(NewFilterChefsStateSuccess(Initializer.newFilteredChefModel.data));
      } else {
        emit(NewFilterChefsStateLoadFailed(
            Initializer.newFilteredChefModel.message ?? ''));
      }
    } catch (e) {
      emit(NewFilterChefsStateLoadFailed(e.toString()));
    }
  }

  Future<void> _newFilterDishes(
      NewFilterDishesEvent event, Emitter<NewMealPlanState> emit) async {
    emit(NewFilterDishesStateLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/meal_plan_latest/filter-dishes', event.data);
      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }
      log('Filter Dishes Response: $response');
      Initializer.newFilteredDishesModel =
          NewFilteredDishesModel.fromJson(response);
      if (Initializer.newFilteredDishesModel.status == true) {
        emit(NewFilterDishesStateSuccess(
            Initializer.newFilteredDishesModel.data));
      } else {
        emit(NewFilterDishesStateLoadFailed(
            Initializer.newFilteredDishesModel.message ?? ''));
      }
    } catch (e) {
      emit(NewFilterDishesStateLoadFailed(e.toString()));
    }
  }

  Future<void> _newEditChef(
      NewEditChefEvent event, Emitter<NewMealPlanState> emit) async {
    emit(NewEditChefStateLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/meal_plan_latest/add-edit-days', event.data);
      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }
      log('Edit Chef Response: $response');
      if (response['status'] == true) {
        emit(NewEditChefStateSuccess(response));
      } else {
        emit(NewEditChefStateLoadFailed(response['message'] ?? ''));
      }
    } catch (e) {
      emit(NewEditChefStateLoadFailed(e.toString()));
    }
  }
}

// Events
abstract class NewMealPlanEvent {}

class RefreshTokenEvent extends NewMealPlanEvent {
  final String refreshToken;
  final NewMealPlanEvent? nextEvent;
  RefreshTokenEvent({required this.refreshToken, required this.nextEvent});
}

class ListNewMealPlanSummaryEvent extends NewMealPlanEvent {
  Map<String, dynamic>? data;
  ListNewMealPlanSummaryEvent({this.data});
}

class NewStep1MEalPlanEvent extends NewMealPlanEvent {
  Map<String, dynamic> data;
  NewStep1MEalPlanEvent(this.data);
}

class NewStep2MEalPlanEvent extends NewMealPlanEvent {
  Map<String, dynamic> data;
  NewStep2MEalPlanEvent(this.data);
}

class NewStep3MEalPlanEvent extends NewMealPlanEvent {
  Map<String, dynamic> data;
  NewStep3MEalPlanEvent(this.data);
}

class NewStep4MEalPlanEvent extends NewMealPlanEvent {
  Map<String, dynamic> data;
  NewStep4MEalPlanEvent(this.data);
}

class NewCheckoutMealPlanEvent extends NewMealPlanEvent {
  Map<String, dynamic> data;
  NewCheckoutMealPlanEvent(this.data);
}

class NewFilterChefsEvent extends NewMealPlanEvent {
  Map<String, dynamic> data;
  NewFilterChefsEvent(this.data);
}

class NewFilterDishesEvent extends NewMealPlanEvent {
  Map<String, dynamic> data;
  NewFilterDishesEvent(this.data);
}

class NewEditChefEvent extends NewMealPlanEvent {
  Map<String, dynamic> data;
  NewEditChefEvent(this.data);
}

// States
abstract class NewMealPlanState {}

// Refresh Token States

class RefreshTokenLoading extends NewMealPlanState {}

class RefreshTokenSuccess extends NewMealPlanState {}

class RefreshTokenFailed extends NewMealPlanState {}

// Meal Plan Summary States
class ListNewMealPlanSummaryLoading extends NewMealPlanState {}

class NewMealPlanSummaryLoaded extends NewMealPlanState {
  final dynamic response;

  NewMealPlanSummaryLoaded(this.response);
}

class NewMealPlanSummaryLoadFailed extends NewMealPlanState {
  final String error;

  NewMealPlanSummaryLoadFailed(this.error);
}

//Step 1 Meal Plan State
class NewMealPlanStep1StateLoading extends NewMealPlanState {}

class NewMealPlanStep1StateSuccess extends NewMealPlanState {
  final dynamic response;

  NewMealPlanStep1StateSuccess(this.response);
}

class NewMealPlanStep1StateLoadFailed extends NewMealPlanState {
  final String error;

  NewMealPlanStep1StateLoadFailed(this.error);
}

// Step 2 Meal Plan State
class NewMealPlanStep2StateLoading extends NewMealPlanState {}

class NewMealPlanStep2StateSuccess extends NewMealPlanState {
  final dynamic response;

  NewMealPlanStep2StateSuccess(this.response);
}

class NewMealPlanStep2StateLoadFailed extends NewMealPlanState {
  final String message;

  NewMealPlanStep2StateLoadFailed(this.message);
}

// Step 3 Meal Plan State
class NewMealPlanStep3StateLoading extends NewMealPlanState {}

class NewMealPlanStep3StateSuccess extends NewMealPlanState {
  final dynamic response;

  NewMealPlanStep3StateSuccess(this.response);
}

class NewMealPlanStep3StateLoadFailed extends NewMealPlanState {
  final String error;

  NewMealPlanStep3StateLoadFailed(this.error);
}

// Step 4 Meal Plan State
class NewMealPlanStep4StateLoading extends NewMealPlanState {}

class NewMealPlanStep4StateSuccess extends NewMealPlanState {
  final dynamic response;

  NewMealPlanStep4StateSuccess(this.response);
}

class NewMealPlanStep4StateLoadFailed extends NewMealPlanState {
  final String error;

  NewMealPlanStep4StateLoadFailed(this.error);
}

// Checkout Meal Plan State
class NewCheckoutMealPlanStateLoading extends NewMealPlanState {}

class NewCheckoutMealPlanStateSuccess extends NewMealPlanState {
  final String message;
  final num total;
  final String? paymentUrl;
  final String? orderId;

  NewCheckoutMealPlanStateSuccess(
      this.message, this.total, this.paymentUrl, this.orderId);
}

class NewCheckoutMealPlanStateLoadFailed extends NewMealPlanState {
  final String error;

  NewCheckoutMealPlanStateLoadFailed(this.error);
}

// New Filter Chefs State
class NewFilterChefsStateLoading extends NewMealPlanState {}

class NewFilterChefsStateSuccess extends NewMealPlanState {
  final dynamic response;

  NewFilterChefsStateSuccess(this.response);
}

class NewFilterChefsStateLoadFailed extends NewMealPlanState {
  final String error;

  NewFilterChefsStateLoadFailed(this.error);
}

// New Filter Dishes State
class NewFilterDishesStateLoading extends NewMealPlanState {}

class NewFilterDishesStateSuccess extends NewMealPlanState {
  final dynamic response;

  NewFilterDishesStateSuccess(this.response);
}

class NewFilterDishesStateLoadFailed extends NewMealPlanState {
  final String error;

  NewFilterDishesStateLoadFailed(this.error);
}

// New Edit Chef State
class NewEditChefStateLoading extends NewMealPlanState {}

class NewEditChefStateSuccess extends NewMealPlanState {
  final dynamic response;

  NewEditChefStateSuccess(this.response);
}

class NewEditChefStateLoadFailed extends NewMealPlanState {
  final String error;

  NewEditChefStateLoadFailed(this.error);
}
