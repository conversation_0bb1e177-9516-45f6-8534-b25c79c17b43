{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983c3e5578711d093a72fe4c7cfad8da03", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f2baec5848df0f1ccfae395236a06808", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ec1824b8d0b7f574e38897e61b0d0413", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98186b7e73fa360225d39ff141bcb7f396", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ec1824b8d0b7f574e38897e61b0d0413", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985df4e2175accb09242a490e01494742d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98371a757a792d97226b1e11d24f15e6a5", "guid": "bfdfe7dc352907fc980b868725387e98f71b2f99ac98c43b31332b8cbed87070"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaba791926045b31827f6d7ee5fad796", "guid": "bfdfe7dc352907fc980b868725387e9841074b5220e642d690de27fda93d3ede", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b0676f60f21322639e5f09885e83fc3", "guid": "bfdfe7dc352907fc980b868725387e98794838261b1f71b0337e4939ce71c8e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e194e289b81d1732ac4be81e815361b6", "guid": "bfdfe7dc352907fc980b868725387e983a2498c44efe57a705678651b91fb050"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811a671f668cc3eef1546223b0a8e91fe", "guid": "bfdfe7dc352907fc980b868725387e98c0b1636638b36f6b985acd81f64051b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885e598943a1d3a0cf89b7fbaf653a665", "guid": "bfdfe7dc352907fc980b868725387e98cbd579eefd7ed92e9e90c8d6b28a48c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b35f5ad791f4d046e7b7472c1191473", "guid": "bfdfe7dc352907fc980b868725387e980c08319b24c8ebbbff439022596924d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be80e8e0ddbcae10a79304535af6ebbb", "guid": "bfdfe7dc352907fc980b868725387e9886ea8c5eb7a35149dd61ab943b25576c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857f654b327f6c9b521b607d0d82c77c4", "guid": "bfdfe7dc352907fc980b868725387e98af0b90dae90bbf5de6d7abd5b12b893d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895c8b1dad835ebe84739a5a1d05b7f99", "guid": "bfdfe7dc352907fc980b868725387e987eb3d7d6e28eb04adfd6cb0d758a0747"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982db55048cce73705ee868c5360fd31c4", "guid": "bfdfe7dc352907fc980b868725387e987bb7da1e94ab4ebbaf2574783db35c9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882a4224cd2734906e77743237e0745f7", "guid": "bfdfe7dc352907fc980b868725387e989421f0f717ab973567b4ab815b8651f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d40834350096a5dd473fbd2793ed9595", "guid": "bfdfe7dc352907fc980b868725387e989ecb3080cb6243fbf65d21d3d8720015"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e942bb7108a9376577837086dcd0069", "guid": "bfdfe7dc352907fc980b868725387e9824de91d3e741d8ae5d388ad4a1b88556"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5c56134cdedc28b9dca43c28611661d", "guid": "bfdfe7dc352907fc980b868725387e98d1babf9f9ff034b3f6246aa3c4c5fc66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811d2597acdb5546be311377dcb10a83c", "guid": "bfdfe7dc352907fc980b868725387e989b302640aa6e165853d9fbe2884cd786"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3c895d33195686cf7607fa0a004b40e", "guid": "bfdfe7dc352907fc980b868725387e980b74f35e3b8079e2d5a30b11596f4b3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835fe3cde8635f8dd7e5517b3ce509588", "guid": "bfdfe7dc352907fc980b868725387e982d887e51d445d7b02ba7ce6cd8950113", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ed5a659ff0893355d05ea0c0335fe93", "guid": "bfdfe7dc352907fc980b868725387e9822db26ef607b3340c169275b1532f06f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a550ad345f2f3c84add9f9192d21b19a", "guid": "bfdfe7dc352907fc980b868725387e98d66bea49af9370fa769c8a0de542bba6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13c6f06fb0ee8488acb875080775fae", "guid": "bfdfe7dc352907fc980b868725387e9803dd0dab5644f2305c0fdeaf0085644f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fb6f2f6ad7c37ed10c017de234bbcbb", "guid": "bfdfe7dc352907fc980b868725387e98b4bdfff4a91744e17cf50e674494b215"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cbd0e2d5c742fa2917b6f43e5174df2", "guid": "bfdfe7dc352907fc980b868725387e98a19cb13147ffde327a8c199eefc9181e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a84725533f12b6794a62b62f310f608d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989fef3281f2f5fa6e9a1b0e8dcaee237a", "guid": "bfdfe7dc352907fc980b868725387e98214d7d3645116545f6135f009168a9e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98884d6b37a7391d3e33799dab27c93f0c", "guid": "bfdfe7dc352907fc980b868725387e98b62fc5c144ecf6f363787f16b4f1d6ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3703997aa6d0572d9478e1edfa9e866", "guid": "bfdfe7dc352907fc980b868725387e9893ddbab32dcb50f25fcc5f25a2b598f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985603157ff20296b93f47030cd0c38055", "guid": "bfdfe7dc352907fc980b868725387e98d98f9e300f83b8d2a0d20d54c5997450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0ab0ccd30d6c560916f59611565f9b2", "guid": "bfdfe7dc352907fc980b868725387e98bef9bf25c7b752811654f2ebe18e84c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956e5d00f408f13895cee5ae79cc9fc0", "guid": "bfdfe7dc352907fc980b868725387e9804e42e103d49806328ab81bf5fc6ba7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e725dc70f3115126d4dc46b421fcade5", "guid": "bfdfe7dc352907fc980b868725387e98a46d4975faf9014f2892821eb9097107"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d681f708a8508ac3a36d639d2f6751f", "guid": "bfdfe7dc352907fc980b868725387e98b766d3fe55c2bb8d52811668e5e4d729"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847f19e4424c9ef968d6d6b33ca965dd7", "guid": "bfdfe7dc352907fc980b868725387e983b54dcc2001c4c152f2fc8cc927d31d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98888a6965fe80f3cf0e0312a72841dcee", "guid": "bfdfe7dc352907fc980b868725387e9838c5a1cfa2cf6e6126d697abb1628dcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98818555ba34e81aada26bae6141e88360", "guid": "bfdfe7dc352907fc980b868725387e9826ab7febeddd97260c803c135e8889c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae03b736cf780a0769d264514bf3f30b", "guid": "bfdfe7dc352907fc980b868725387e9814abfde854d18c02acb6d0dbf84b03e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98affff77b59640c787ad989e2c2006f17", "guid": "bfdfe7dc352907fc980b868725387e985c8a69b7a5c80747ae93f3015bedf548"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3b4c74929390a4977401362ece2d468", "guid": "bfdfe7dc352907fc980b868725387e98b474a8f2e60bb5a8e181e0698651e82b"}], "guid": "bfdfe7dc352907fc980b868725387e985c8c591a1d9919af15c3851b4a53d661", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9834a9ecd554fd7ebc9fd0a500d03e0250"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b194461b3d65889636e9bae473ca683", "guid": "bfdfe7dc352907fc980b868725387e98ee3902d883acabd12b463ecaed21b15a"}], "guid": "bfdfe7dc352907fc980b868725387e985dff6808c6190a5791562d4ab8f6a424", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e5bd12c62fa0ef2bb7e32508a281df68", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98898e8ccdb55dbaae1009277b61b9e4d6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}