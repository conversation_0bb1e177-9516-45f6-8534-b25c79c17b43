// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';

class VehicleFilterBar extends StatefulWidget {
  final Function(Map<String, String>) onFilterChange;

  const VehicleFilterBar({
    super.key,
    required this.onFilterChange,
  });

  @override
  _VehicleFilterBarState createState() => _VehicleFilterBarState();
}

class _VehicleFilterBarState extends State<VehicleFilterBar> {
  final Map<String, TextEditingController> _controllers = {
    'regnostate': TextEditingController(),
    'regnorto': TextEditingController(),
    'regnovno': TextEditingController(),
    'model': TextEditingController(),
    'brand': TextEditingController(),
    'color': TextEditingController(),
  };

  final Map<String, String> _activeFilters = {};
  bool _showFilters = false;

  @override
  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _applyFilter(String field, String value) {
    setState(() {
      if (value.isNotEmpty) {
        _activeFilters[field] = value;
      } else {
        _activeFilters.remove(field);
      }
    });
    widget.onFilterChange(_activeFilters);
  }

  void _removeFilter(String field) {
    setState(() {
      _activeFilters.remove(field);
      _controllers[field]?.clear();
    });
    widget.onFilterChange(_activeFilters);
  }

  Widget _buildActiveFiltersDisplay() {
    if (_activeFilters.isEmpty) return const SizedBox.shrink();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: _activeFilters.entries.map((entry) {
          String displayName = _getDisplayName(entry.key);
          return Chip(
            label: Text(
              '$displayName: ${entry.value}',
              style: const TextStyle(fontSize: 12),
            ),
            deleteIcon: const Icon(Icons.close, size: 16),
            onDeleted: () => _removeFilter(entry.key),
            backgroundColor: const Color(0xFFCD853F).withOpacity(0.1),
            labelStyle: const TextStyle(color: Color(0xFFCD853F)),
          );
        }).toList(),
      ),
    );
  }

  String _getDisplayName(String field) {
    switch (field) {
      case 'regnostate':
        return 'State';
      case 'regnorto':
        return 'RTO';
      case 'regnovno':
        return 'Number';
      case 'model':
        return 'Model';
      case 'brand':
        return 'Brand';
      case 'color':
        return 'Color';
      default:
        return field;
    }
  }

  Widget _buildFilterField(String field, String hint) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: TextField(
        controller: _controllers[field],
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: TextStyle(
            color: Colors.grey[800],
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          filled: true,
          fillColor: Colors.grey[50],
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide.none,
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        onChanged: (value) => _applyFilter(field, value),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'Vehicle Search Filters',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(
                  _showFilters ? Icons.filter_list_off : Icons.filter_list,
                  color: const Color(0xFFCD853F),
                ),
                onPressed: () => setState(() => _showFilters = !_showFilters),
              ),
            ],
          ),
          if (_showFilters) ...[
            const SizedBox(height: 12),
            _buildFilterField('regnostate', 'State (e.g., KL)'),
            _buildFilterField('regnorto', 'RTO Number (e.g., 52)'),
            _buildFilterField('regnovno', 'Registration Number'),
            _buildFilterField('model', 'Vehicle Model'),
            _buildFilterField('brand', 'Vehicle Brand'),
            _buildFilterField('color', 'Vehicle Color'),
            const SizedBox(height: 8),
            _buildActiveFiltersDisplay(),
          ],
        ],
      ),
    );
  }
}
