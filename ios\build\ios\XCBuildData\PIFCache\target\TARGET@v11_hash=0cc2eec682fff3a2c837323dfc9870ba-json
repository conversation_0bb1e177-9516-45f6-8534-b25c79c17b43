{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9896ffa06b6790a09f3ca6fdf2b3bf6e28", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9882ea8824e2129f4705d7bec998df6711", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d9ce4e9a9576ee7513d0ff5ffefe33d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f3543d0b5867fa17372ae237495881a4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d9ce4e9a9576ee7513d0ff5ffefe33d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9836254bf2b549f6c85a025ffe8ef1dafa", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984ead41c0ce5268e9faec8a5464882f55", "guid": "bfdfe7dc352907fc980b868725387e9858db28665e0e3a96db2285e7a4627591"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e68cf795f43c5837ebb918580b24fc1d", "guid": "bfdfe7dc352907fc980b868725387e985b2d209972a1a29e0661670fe62122d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a1d89c9c003dc97f2398197a03b9df", "guid": "bfdfe7dc352907fc980b868725387e982163cd8f58363a6c7091bb56c380e903"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b620c591052b57e153a049a85e54ef2", "guid": "bfdfe7dc352907fc980b868725387e98714abe46ac2589a2a868c2ac562735e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98def723b30ff28f27e3f0fd93948c49ef", "guid": "bfdfe7dc352907fc980b868725387e9826d599229d284aa9ce09d5d51e95745d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cae6d319170eb53be480feb636aeef92", "guid": "bfdfe7dc352907fc980b868725387e986f9f80a8f28cf98c5f8ddbf2843ee312"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5aa907248b60546c33dc249199537f2", "guid": "bfdfe7dc352907fc980b868725387e98d43a72b36e25ae7dc12eb3ba67a905f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989902b73137acd1a6e4c49f54b86ed936", "guid": "bfdfe7dc352907fc980b868725387e98779e57e361b285f323582e6e5b59f328", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd4ca0ba8e1876edadb7b17accbf8f58", "guid": "bfdfe7dc352907fc980b868725387e98831cfc7112fe949da1f7b9c432e58c2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e9738340156c51971216cf328d28631", "guid": "bfdfe7dc352907fc980b868725387e98d7b1ac1830af6179075ee452a5462b64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98776cc992018e32a0973f2b89db1737bf", "guid": "bfdfe7dc352907fc980b868725387e984e77b858d48577e54a0d72023d858012", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c97a78ccab8b6d348fedee23ee1d713", "guid": "bfdfe7dc352907fc980b868725387e988e9b4b7f7d1b0a64a04630066a712c7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b9b246a667c2c68f08b10a24df29a7", "guid": "bfdfe7dc352907fc980b868725387e98b7fc2122621c438e76967c3e5e656509", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982085398a9db9bdbed2304efa7bee0de2", "guid": "bfdfe7dc352907fc980b868725387e98c4d4a9f99b10d40b56f7f6a9d0505f5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883399022cdd0a44d4acdbcd11ae02016", "guid": "bfdfe7dc352907fc980b868725387e982fd8d43e54b8c0ec1de2f623494cc0de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee03146c0517e8c31c683de79c39346", "guid": "bfdfe7dc352907fc980b868725387e9871dc692e0946791fdd7279657ce7cb6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ceb670d900a030d7e1846a3adb50b82", "guid": "bfdfe7dc352907fc980b868725387e98af73dab15706ef077b4a06472e5fb283"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ea3e5d22c3d6687fdfdb507f67bc90e", "guid": "bfdfe7dc352907fc980b868725387e98007c1ff4e7bbf5fb62500796e9042991"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854865a943e1fd3458d925e44ff5588a9", "guid": "bfdfe7dc352907fc980b868725387e987893e70f8b50f3d0b97ce27d3db61eca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858f0b1dac60bcd20aa7e2f990f828793", "guid": "bfdfe7dc352907fc980b868725387e980151e7f150ed9320c3c57bed9aef267c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985146e282bdc00f1cbf790d1ddc1f437f", "guid": "bfdfe7dc352907fc980b868725387e98f08a25fc1d54a990c5209d0cf8d9e4c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1067e3558f5c345972633f010740537", "guid": "bfdfe7dc352907fc980b868725387e98fbf4253fbb63d1bc5fcc027e19f89dae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872ec9f44731c5bb4164b6a3e41b76253", "guid": "bfdfe7dc352907fc980b868725387e98d0b10767c65ae452cf8af24872255e84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0b1aaf5eb4b39b3648a35669602a8fa", "guid": "bfdfe7dc352907fc980b868725387e9821ef1d5ae61beadf76fa99a30064daf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869bb3dc65e0014294b3bb00cd8bfc39a", "guid": "bfdfe7dc352907fc980b868725387e98c7c9d01070906905968aa05fac4bd810"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98895b6e986cefb0ea061913753b2d8f60", "guid": "bfdfe7dc352907fc980b868725387e98e58e3fe8d957c8efe1c443a6189ba080"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5249667805b2e1de12671b7c35536cf", "guid": "bfdfe7dc352907fc980b868725387e9882ca970cd0edfbf780326a5c3b86cc69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1235d26302bbc3ead4ae169811eb3b", "guid": "bfdfe7dc352907fc980b868725387e98b72c84b2650fced3160f274acae330f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98658b4255c533d9f0000647707c600e24", "guid": "bfdfe7dc352907fc980b868725387e9830b4cb8cb86ecd21937334bb8707b680"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d72c4e4aa19a9f2c6abf97c2de18c601", "guid": "bfdfe7dc352907fc980b868725387e9832c3d06b9cf74fee2191b279f38acabc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d60e5b49cc13899e28183430e89758c", "guid": "bfdfe7dc352907fc980b868725387e98ccc0c2433dcfcff4e9cdad2ef652e531"}], "guid": "bfdfe7dc352907fc980b868725387e9823451a5af54982a5423c093ec85154ce", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9885e9ca92f571b0cf851e1c3f4e78052e", "guid": "bfdfe7dc352907fc980b868725387e98ee242356515ef76485dadf31943a58d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d31058df2886b1d4d6364b641f1bce0", "guid": "bfdfe7dc352907fc980b868725387e984cc7870919695a02a0011653ce5322db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98674581cf084aec471560d58746b6ef5b", "guid": "bfdfe7dc352907fc980b868725387e98de7faa6adc673b880fe1d1e8f224e4c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d42d48d0587485e4f76a450e46864f12", "guid": "bfdfe7dc352907fc980b868725387e9861258e1fcaa5a7cf0cbab9edec5095f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f81be2cbdff85662fedddfb2d2855992", "guid": "bfdfe7dc352907fc980b868725387e981097864accb4d9a2174cc60211f75798"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ed379ba00ca27bfe07803cfa1c8c649", "guid": "bfdfe7dc352907fc980b868725387e98ff8b1ceb773a7cd8cd3f4dd77ba34b6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1918213261f97c980de855c9aaf5fbe", "guid": "bfdfe7dc352907fc980b868725387e98a02a0bfcf79b9cef1ad336c22252f982"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b44ef4fb4b621092a72131838d5a9ec0", "guid": "bfdfe7dc352907fc980b868725387e98737949fc03dea582fe7217bbcf706889"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800f70ed1467a1c85e7e27d3618a9c312", "guid": "bfdfe7dc352907fc980b868725387e9826ec18fe84a99fcbae9ff6c27fe54dfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce36b83b05ce68fd75de41594ffecc68", "guid": "bfdfe7dc352907fc980b868725387e988b08db4d9ca1918d5d9341214cf9f314"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9b62552623e523fd677049841e88196", "guid": "bfdfe7dc352907fc980b868725387e98f5d3fdbcad2d590fddd5a5ce4bea84eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985548f8ca31ccdf40e3252db77da35010", "guid": "bfdfe7dc352907fc980b868725387e982845d948be4d34fee3baa3c532ab70bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983787306144fba9681956847921420859", "guid": "bfdfe7dc352907fc980b868725387e98a3e8bda34389ee86b7cee7d4681903b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98455e42898fea3177d522dfd02ba7b07a", "guid": "bfdfe7dc352907fc980b868725387e98be581fe8e5a248efffab52e5d6e9cc24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fba70b156bbc0c0b4f63467bae677f6", "guid": "bfdfe7dc352907fc980b868725387e9858218767936c0971717c327d517b18b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3f0e10d8b3eb7e9ba3dedca8e356270", "guid": "bfdfe7dc352907fc980b868725387e98556b7ef68e1e2aadfa0ea38c2976bee4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869a48ef5833857af0a808d4288dc7f85", "guid": "bfdfe7dc352907fc980b868725387e982fe6e2ee59e523d300890a446b6c35d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884ee9f5249ef306ef16f2516fad9d3de", "guid": "bfdfe7dc352907fc980b868725387e98a20c42a63c75fa9400b93da7134f34e9"}], "guid": "bfdfe7dc352907fc980b868725387e98db29fb96389b5c0dd2e4151f9229c1bb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e981ae2cb821fe87c52884f889123dcafd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e980df5ec87ad0a56c1350a10d5ffec6b4e"}], "guid": "bfdfe7dc352907fc980b868725387e98135991b8771d4764e026b36da5b80615", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fe6ff61d302fe0026e9d1acaa4fbc6a3", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e985f906de422fdc7a311d3cf49a840ea9f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}