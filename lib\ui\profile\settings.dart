import 'package:db_eats/bloc/main_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool pushNotifications = true;
  bool emailOffers = false;
  bool smsOffers = true;
  bool language = true;

  bool accountEmailOffers = true;
  bool accountSmsOffers = false;

  String? selectedLanguage = 'English (Default)';
  final List<String> languages = [
    'English (Default)',
    'Hindi',
    'French',
    'Spanish'
  ];
  
  // Track which setting is currently being changed
  String? _changingSettingKey;
  @override
  void initState() {
    super.initState();
    context.read<MainBloc>().add(GetSettingsInfoEvent());
    _valuchabnge();
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  Widget _buildShimmerEffect() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: twenty),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: ListView(
          children: [
            Container(
              width: double.infinity,
              height: twentyFour,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            SizedBox(height: twenty),
            Container(
              padding: EdgeInsets.all(sixteen),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(twelve),
              ),
              child: Column(
                children: List.generate(
                  4,
                  (index) => Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            width: twentyFour,
                            height: twentyFour,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: twelve),
                          Container(
                            width: screenWidth * 0.4,
                            height: sixteen,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: sixteen),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFf6f3ec),
      appBar: AppBar(
        backgroundColor: const Color(0xFFf6f3ec),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: BlocConsumer<MainBloc, MainState>(
        listener: (context, state) {
          if (state is GetSettingsInfoSuccess) {
            final settingsData = state.data;

      // Store it somewhere (e.g., local variable, Provider, singleton, etc.)
      // For example, if you're using a local variable in a StatefulWidget:
      setState(() {
        pushNotifications =  settingsData.pushNotification;
        emailOffers =  settingsData.emailOffers;
        smsOffers =  settingsData.smsOffers;
      });
    } else if (state is SettingsChangeLoadingState) {
      // Keep the _changingSettingKey as is during loading
    } else if (state is SettingsChangeSuccess || state is SettingsChangeFailed) {
      // Clear the changing setting key when operation completes
      setState(() {
        _changingSettingKey = null;
      });
    }
  },
        builder: (context, state) {
          if (state is GetSettingsInfoLoadingState) {
            return _buildShimmerEffect();
          }
          
          return Initializer.settingsInfoModel.data == null
              ? const Center(
                  child: CircularProgressIndicator(
                  color: Colors.black,
                ))
              : Initializer.settingsInfoModel.data!.pushNotification == null
                  ? const Center(
                      child: CircularProgressIndicator(
                      color: Colors.black,
                    ))
                  : Padding(
                      padding: EdgeInsets.symmetric(horizontal: twenty),
                      child: ListView(
                        children: [
                          Text(
                            'Settings',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w600,
                              fontSize: eighteen,
                            ),
                          ),
                          SizedBox(height: ten),
                          Container(
                            padding: EdgeInsets.only(
                                top: forteen,
                                left: sixteen / 2,
                                right: sixteen / 2,
                                bottom: forteen),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(twelve),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: twelve),
                                  child: Text(
                                    'Category',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w600,
                                      fontSize: forteen,
                                    ),
                                  ),
                                ),
                                SizedBox(height: ten * 1.5),
                                _buildToggleRow(
                                  'Push Notifications',
                                  pushNotifications,
                                  (val) {
                                    setState(() {
                                      pushNotifications = val;
                                      _changingSettingKey = 'push_notification';
                                    });
                                    final data = {
                                      'push_notification': val,
                                    };
                                    context.read<MainBloc>().add(
                                          SettingsChangeEvent(data: data),
                                        );
                                  },
                                  isLoading: _changingSettingKey == 'push_notification',
                                ),
                                SizedBox(height: ten),
                                _divider(),
                                SizedBox(height: twelve / 2),
                                _buildToggleRow('Email Offers', emailOffers,
                                    (val) {
                                  setState(() {
                                    emailOffers = val;
                                    _changingSettingKey = 'email_offers';
                                  });
                                  final data = {'email_offers': val};
                                  context.read<MainBloc>().add(
                                        SettingsChangeEvent(data: data),
                                      );
                                }, isLoading: _changingSettingKey == 'email_offers'),
                                SizedBox(height: sixteen / 2),
                                _divider(),
                                SizedBox(height: twelve / 2),
                                _buildToggleRow('SMS Offers', smsOffers, (val) {
                                  setState(() {
                                    smsOffers = val;
                                    _changingSettingKey = 'sms_offers';
                                  });
                                  final data = {'sms_offers': val};
                                  context.read<MainBloc>().add(
                                        SettingsChangeEvent(data: data),
                                      );
                                }, isLoading: _changingSettingKey == 'sms_offers'),
                                // const SizedBox(height: twenty),
                                SizedBox(height: sixteen / 4),
                                _divider(), SizedBox(height: ten),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: twelve),
                                  child: Text(
                                    'Language',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w600,
                                      fontSize: forteen,
                                    ),
                                  ),
                                ),
                                SizedBox(height: eighteen),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: twelve),
                                  child: CustomDropdown(
                                    value: selectedLanguage,
                                    items: languages,
                                    onChanged: (value) {
                                      setState(() {
                                        selectedLanguage = value;
                                      });
                                    },
                                  ),
                                ),

                                // Replace your _buildDropdown call with:
// CustomDropdown(
//   value: yourValue,
//   items: yourItems,
//   onChanged: yourOnChangedFunction,
// )
                                SizedBox(height: sixteen / 4),
                                // Padding(
                                //   padding: const EdgeInsets.symmetric(
                                //       horizontal: twelve),
                                //   child: const Text(
                                //     'Account Security',
                                //     style: TextStyle(
                                //       fontFamily: 'Inter',
                                //       fontWeight: FontWeight.w600,
                                //       fontSize: forteen,
                                //     ),
                                //   ),
                                // ),
                                // const SizedBox(height: eighteen),
                                // _buildToggleRow('Language', language,
                                //     (val) => setState(() => language = val)),
                                // const SizedBox(height: sixteen/4),
                                // _divider(), const SizedBox(height: ten),
                                // _buildToggleRow(
                                //     'Email Offers',
                                //     accountEmailOffers,
                                //     (val) => setState(
                                //         () => accountEmailOffers = val)),
                                // const SizedBox(height: sixteen/4),
                                // _divider(), const SizedBox(height: ten),
                                // _buildToggleRow(
                                //     'SMS Offers',
                                //     accountSmsOffers,
                                //     (val) =>
                                //         setState(() => accountSmsOffers = val)),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
        },
      ),
    );
  }

  Widget _buildToggleRow(
      String label, bool value, ValueChanged<bool> onChanged, {bool isLoading = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // Transform.scale(
        //   // scaleX: 1,
        //   // scaleY: 0.9,
        //   scale: 0.6, // Reduce size of the switch
        //   child: Switch(
        //     value: value,
        //     onChanged: onChanged,
        //     activeColor: Colors.white, // Thumb color
        //     inactiveThumbColor: Colors.white, // Thumb color when off
        //     activeTrackColor: Colors.black, // Background when ON
        //     inactiveTrackColor: const Color(0xFFAAADB1), // Background when OFF
        //     materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        //   ),
        // ),

        Padding(
          padding: const EdgeInsets.all(10.0),
          child: isLoading 
              ? Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    width: 35,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                )
              : CustomToggle(
                  value: value,
                  onChanged: onChanged,
                  width: 35,
                  height: 20,
                  activeColor: Colors.black,
                  inactiveColor: Color(0xFFAAADB1),
                  thumbColor: Colors.white,
                ),
        ), // SizedBox(
        //   width: ten,
        // ),
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: forteen,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Widget _divider() {
    return Divider(
      color: Color(0xFFE1E3E6),
      thickness: 1,
      height: twentyFour,
    );
  }

  void _valuchabnge() {
    if (Initializer.settingsInfoModel.data != null) {
      pushNotifications =
          Initializer.settingsInfoModel.data!.pushNotification ??
              pushNotifications;
      emailOffers =
          Initializer.settingsInfoModel.data!.emailOffers ?? emailOffers;
      smsOffers = Initializer.settingsInfoModel.data!.smsOffers ?? smsOffers;
    }
  }
}

class CustomToggle extends StatefulWidget {
  final bool value;
  final ValueChanged<bool>? onChanged;
  final double width;
  final double height;
  final Color activeColor;
  final Color inactiveColor;
  final Color thumbColor;
  final Duration animationDuration;
  final Widget? activeIcon;
  final Widget? inactiveIcon;

  const CustomToggle({
    super.key,
    required this.value,
    this.onChanged,
    this.width = 50.0,
    this.height = 25.0,
    this.activeColor = Colors.black,
    this.inactiveColor = const Color(0xFFAAADB1),
    this.thumbColor = Colors.white,
    this.animationDuration = const Duration(milliseconds: 200),
    this.activeIcon,
    this.inactiveIcon,
  });

  @override
  State<CustomToggle> createState() => _CustomToggleState();
}

class _CustomToggleState extends State<CustomToggle>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _colorAnimation = ColorTween(
      begin: widget.inactiveColor,
      end: widget.activeColor,
    ).animate(_animationController);

    if (widget.value) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(CustomToggle oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      if (widget.value) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTap() {
    if (widget.onChanged != null) {
      widget.onChanged!(!widget.value);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Calculate thumb size (smaller than track height)
    final thumbSize = widget.height * 0.65; // Reduced to 65% of track height
    final verticalPadding =
        (widget.height - thumbSize) / 2; // Vertical centering
    final horizontalPadding = widget.height * 0.15; // More horizontal padding
    final maxThumbPosition = widget.width - thumbSize - (horizontalPadding * 2);

    return GestureDetector(
      onTap: _handleTap,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Container(
            width: widget.width,
            height: widget.height,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(widget.height / 2),
              color: _colorAnimation.value,
            ),
            child: Stack(
              children: [
                AnimatedPositioned(
                  duration: widget.animationDuration,
                  curve: Curves.easeInOut,
                  left: widget.value
                      ? maxThumbPosition + horizontalPadding
                      : horizontalPadding,
                  top: verticalPadding,
                  child: Container(
                    width: thumbSize,
                    height: thumbSize,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: widget.thumbColor,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child:
                        widget.value ? widget.activeIcon : widget.inactiveIcon,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class CustomDropdown extends StatefulWidget {
  final String? value;
  final List<String> items;
  final void Function(String?) onChanged;

  const CustomDropdown({
    super.key,
    required this.value,
    required this.items,
    required this.onChanged,
  });

  @override
  State<CustomDropdown> createState() => _CustomDropdownState();
}

class _CustomDropdownState extends State<CustomDropdown> {
  bool isExpanded = false;

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main dropdown container
        GestureDetector(
          onTap: () {
            setState(() {
              isExpanded = !isExpanded;
            });
          },
          child: Container(
            height: ten * 3.6, // ten * 3.6
            padding: EdgeInsets.symmetric(horizontal: eighteen), // eighteen
            decoration: BoxDecoration(
              color: const Color(0xFFFFFFFF),
              border: Border.all(color: const Color(0xFFFFFFFF)),
              borderRadius: BorderRadius.circular(35),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4, // sixteen / 4
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.value ?? 'Select..',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: widget.value != null
                        ? forteen
                        : sixteen, // forteen : sixteen
                    color:
                        widget.value != null ? Colors.black : Color(0xFF66696D),
                  ),
                ),
                AnimatedRotation(
                  turns: isExpanded ? 0.5 : 0,
                  duration: Duration(milliseconds: 200),
                  child: Icon(
                    Icons.keyboard_arrow_down,
                    color: Color(0xFF1F2122),
                  ),
                ),
              ],
            ),
          ),
        ),

        // Expandable options container
        AnimatedContainer(
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          height:
              isExpanded ? (widget.items.length * ten * 5).clamp(0, 200) : 0,
          child: Container(
            margin: EdgeInsets.only(top: sixteen / 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: isExpanded
                  ? [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : [],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(twelve),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                itemCount: widget.items.length,
                itemBuilder: (context, index) {
                  final item = widget.items[index];
                  final isSelected = widget.value == item;

                  return GestureDetector(
                    onTap: () {
                      widget.onChanged(item);
                      setState(() {
                        isExpanded = false;
                      });
                    },
                    child: Container(
                      height: ten * 4.8,
                      padding: EdgeInsets.symmetric(horizontal: eighteen),
                      decoration: BoxDecoration(
                        color:
                            isSelected ? Color(0xFFF5F5F5) : Colors.transparent,
                        border: index < widget.items.length - 1
                            ? Border(
                                bottom: BorderSide(
                                  color: Color(0xFFE5E5E5),
                                  width: 0.5,
                                ),
                              )
                            : null,
                      ),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          item,
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight:
                                isSelected ? FontWeight.w500 : FontWeight.w400,
                            fontSize: forteen, // forteen
                            color: isSelected
                                ? Color(0xFF1F2122)
                                : Color(0xFF66696D),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),

        SizedBox(height: twenty), // twenty
      ],
    );
  }
}

// Usage:
// Replace your _buildDropdown call with:
// CustomDropdown(
//   value: yourValue,
//   items: yourItems,
//   onChanged: yourOnChangedFunction,
// )
