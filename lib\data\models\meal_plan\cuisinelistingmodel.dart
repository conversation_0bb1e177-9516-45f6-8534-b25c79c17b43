class CuisinesListModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  CuisinesListModel({this.status, this.message, this.statusCode, this.data});

  CuisinesListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<Cuisines>? cuisines;

  Data({this.cuisines});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['cuisines'] != null) {
      cuisines = <Cuisines>[];
      json['cuisines'].forEach((v) {
        cuisines!.add(new Cuisines.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.cuisines != null) {
      data['cuisines'] = this.cuisines!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Cuisines {
  int? id;
  String? name;
  List<SubCuisines>? subCuisines;

  Cuisines({this.id, this.name, this.subCuisines});

  Cuisines.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    if (json['sub_cuisines'] != null) {
      subCuisines = <SubCuisines>[];
      json['sub_cuisines'].forEach((v) {
        subCuisines!.add(new SubCuisines.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    if (this.subCuisines != null) {
      data['sub_cuisines'] = this.subCuisines!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SubCuisines {
  int? id;
  String? name;
  List<LocalCuisines>? localCuisines;

  SubCuisines({this.id, this.name, this.localCuisines});

  SubCuisines.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    if (json['local_cuisines'] != null) {
      localCuisines = <LocalCuisines>[];
      json['local_cuisines'].forEach((v) {
        localCuisines!.add(new LocalCuisines.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    if (this.localCuisines != null) {
      data['local_cuisines'] =
          this.localCuisines!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class LocalCuisines {
  int? id;
  String? name;

  LocalCuisines({this.id, this.name});

  LocalCuisines.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}
