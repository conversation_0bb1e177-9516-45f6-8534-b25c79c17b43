{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b720f56c8b5336e02bf362443834fa8c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9817975952ec1e7de7f9434d892b14a841", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9817975952ec1e7de7f9434d892b14a841", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9846f80ce2b720b0fd0bc9f762f52408c3", "guid": "bfdfe7dc352907fc980b868725387e985f0085899a9f5aec5a0202605a8918e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f953b336cfa1c4cc27b0dc71196b655e", "guid": "bfdfe7dc352907fc980b868725387e98611718009e578d911dfa24820f8938c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bee82f28dd38f09ec2f60dd7601c60c", "guid": "bfdfe7dc352907fc980b868725387e98a6682a128e26f73988588a0b9dc99c4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829a226bdd753c5ddb585b1961d2487f7", "guid": "bfdfe7dc352907fc980b868725387e98a3fcd6fa162c0d365d634616e789d6ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98136fd0f50bd3c04347aedd84774a083e", "guid": "bfdfe7dc352907fc980b868725387e983e6167a4e115668fb3799c465a0cfc40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c846b027293826843faeed1d9cba0f", "guid": "bfdfe7dc352907fc980b868725387e98322b1742f5524a6d0e37f0b0497cc76c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b17e58512161b636fe30629b0fc4fc9", "guid": "bfdfe7dc352907fc980b868725387e9843ada8db56df3fa463ceac3d83ee854b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f910db4175cd95b02dc919c096a3a4fd", "guid": "bfdfe7dc352907fc980b868725387e980ac4bf4b39ebe460e5e8c74c9450f291", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889e4928138e4356666371cb625fe018a", "guid": "bfdfe7dc352907fc980b868725387e98bbd8a28e44ee02016bb2b7154fb70861", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806b12e9cd63154a358c31fe187d84711", "guid": "bfdfe7dc352907fc980b868725387e9843fad2db3368a046c718d5256715c904", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff39e5de10c089cbec371ee59353fcae", "guid": "bfdfe7dc352907fc980b868725387e9828d12d05508c1581ba2436be33f4de22", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98277085eb8a2d918229073382c42a3467", "guid": "bfdfe7dc352907fc980b868725387e982a76ee02c6f40170d9e26dcda3f47392", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc496e5593487061cb28519b38e3f656", "guid": "bfdfe7dc352907fc980b868725387e982179a03359c5c1547754ab9df73dc6ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4ba3d860e7b12008dcf199e0331a150", "guid": "bfdfe7dc352907fc980b868725387e98d678334a5d238374f2ad9c04b29d4364", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd52aabef6704c650745b8602c8bc6f6", "guid": "bfdfe7dc352907fc980b868725387e98775d03fe3e3492d0099115e1bf707a2c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98013429f51e5abdf6bab97b52ca28e550", "guid": "bfdfe7dc352907fc980b868725387e98079c00739040ad2f95375b026b07ccfc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834c69bf6bd1f4abf9f77af1f38427f83", "guid": "bfdfe7dc352907fc980b868725387e98e3932ebd3a8b73ab380edcbb9178253b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5a360902ac5360ca4d4ee7727d9a4a7", "guid": "bfdfe7dc352907fc980b868725387e98a280d73544069688727cb901c0e4c23b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839b3182d3fa270a4c5a4ba98012244be", "guid": "bfdfe7dc352907fc980b868725387e98b0f7618034ed189c15db88fd3fea3d89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d800c5e98093470ffdf9f3f5be496e23", "guid": "bfdfe7dc352907fc980b868725387e98c601d2214f03bd2cd74b3097ecac9d80", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9849b421afaacc8be2fa9c9128251ce1ce", "guid": "bfdfe7dc352907fc980b868725387e98adadec3b201175c8d0e30e0e0875c721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f06747b5f07b42bbc40765c9b302b48", "guid": "bfdfe7dc352907fc980b868725387e9855bbf862dbacc561aea6d6c3b71beffd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f447121c6f4c39664f75a280496c0d20", "guid": "bfdfe7dc352907fc980b868725387e980933bd8b7809f3d220559f518c73495b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a002763721326a65daa4af0d18bbc83c", "guid": "bfdfe7dc352907fc980b868725387e984f3927b5025fbab50f810894b2944645"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a78403fe0e977dd6b371d5fbf0c8fea8", "guid": "bfdfe7dc352907fc980b868725387e98a7337d83be2cab6dc31cc46cebb38081"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98194226a54a2b356249c7ffb9bb46fe7a", "guid": "bfdfe7dc352907fc980b868725387e98c97a8ecbd5b2762aca0093db40716ef3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897b132a2f66778603c548ed3eaba41e0", "guid": "bfdfe7dc352907fc980b868725387e98ec11d162d18201d0db7fc2a6c6450791"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98362c66aad9ce20934f704d8f8b10594d", "guid": "bfdfe7dc352907fc980b868725387e98b89d056539b9db589f8f435fb0ee22cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aebff333d6324a70fc9df7ea75a1423f", "guid": "bfdfe7dc352907fc980b868725387e98e74e91e4c474192ff4f6c671250611a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803ace54465c31a1214b4604f5cee0248", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dd50d7a3e1b827f5738bc1edf32f01e", "guid": "bfdfe7dc352907fc980b868725387e98f3c9db90a8372e8e2e2d76a4d3f5b63f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f5cbed2d065b01a74353b99e2497a33", "guid": "bfdfe7dc352907fc980b868725387e98ac4a1bc695ff6aa06833c9cbb5c44312"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98451b65abbf5624499d0476837209d704", "guid": "bfdfe7dc352907fc980b868725387e984219fb5f96a080c36f13a083e2af6774"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa75f9d54063be921b1fae7f111af7ce", "guid": "bfdfe7dc352907fc980b868725387e98ad13bb31d800eda1d0c0c6976c915b1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855f2db8c333febd23a4d796a6bcfeeb1", "guid": "bfdfe7dc352907fc980b868725387e98fdd592debdee1d55b93b79d74af0e92b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f79b3d566d89fda6197ff3c6adc36348", "guid": "bfdfe7dc352907fc980b868725387e98cf4f6f22792b8db021e8e1c754326233"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}