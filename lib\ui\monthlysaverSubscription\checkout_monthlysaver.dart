import 'package:db_eats/bloc/saversplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:db_eats/ui/paymentgateway/paymentwebview.dart';
import 'package:db_eats/utils/dotteddivider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';

class CheckoutMonthlysaver extends StatefulWidget {
  final int subscriptionId;
  const CheckoutMonthlysaver({super.key, required this.subscriptionId});

  @override
  State<CheckoutMonthlysaver> createState() => _CheckoutMonthlysaverState();
}

class _CheckoutMonthlysaverState extends State<CheckoutMonthlysaver> {
  bool _useWalletCredits = false;

  @override
  void initState() {
    super.initState();
    _loadCheckoutSummary();
  }

  void _loadCheckoutSummary() {
    context.read<SaversplanBloc>().add(
          CheckoutSummarySaversPassEvent(data: {
            "id": widget.subscriptionId,
            "use_wallet_credits": _useWalletCredits == true,
          }),
        );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final screenWidth = size.width;

    final horizontalPadding = screenWidth * 0.04;
    final fontSize12 = screenWidth * 0.03;
    final fontSize14 = screenWidth * 0.035;
    final fontSize16 = screenWidth * 0.04;
    final fontSize20 = screenWidth * 0.05;

    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF6F3EC),
        elevation: 0,
        automaticallyImplyLeading: false,
        leading: IconButton(
          icon: Icon(Icons.arrow_back,
              color: Color(0xFF1F2122), size: fontSize20),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Checkout',
          style: TextStyle(
            color: Color(0xFF1F2122),
            fontSize: fontSize20,
            fontWeight: FontWeight.w600,
            fontFamily: 'Inter',
          ),
        ),
      ),
      body: BlocConsumer<SaversplanBloc, SaversPlanState>(
        listener: (context, state) {
          if (state is CheckoutSummarySaversPassFailed) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
          } else if (state is CheckoutSaversPassSuccess) {
            // Handle payment gateway integration
            final paymentUrl = state.paymentUrl ?? "";
            final total = state.total ?? 0.0;

            print('Payment URL: $paymentUrl');
            print('Total: $total');

            if (total > 0 && paymentUrl.isNotEmpty) {
              // Navigate to payment gateway
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PaymentWebView(
                    paymentUrl: paymentUrl,
                    onPaymentComplete: () {
                      _showPaymentSuccessDialog();
                    },
                    onPaymentCancelled: () {
                      _showPaymentFailedDialog();
                    },
                  ),
                ),
              );
            } else {
              // Payment covered by wallet credits or no payment required
              _showPaymentSuccessDialog();
            }
          } else if (state is CheckoutSaversPassFailed) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is CheckoutSummarySaversPassLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return _buildCheckoutContent(size, horizontalPadding, fontSize12,
              fontSize14, fontSize16, fontSize20);
        },
      ),
    );
  }

  Widget _buildWalletCreditsToggle(Size size, double fontSize12,
      double fontSize14, double horizontalPadding) {
    final summaryData = Initializer.saversPlanSummaryModel.data;
    final walletBalance = summaryData?.walletBalance ?? 0.0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Container(
              width: size.width * 0.06,
              height: size.height * 0.02125,
              decoration: BoxDecoration(
                color: const Color(0xFF1F2122),
                borderRadius: BorderRadius.circular(size.width * 0.01),
              ),
              alignment: Alignment.center,
              child: Text(
                'DB',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: fontSize12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            SizedBox(width: horizontalPadding * 0.5),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Dabba Wallet Credits',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: fontSize14,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF1F2122),
                  ),
                ),
                Text(
                  'Available: \$${walletBalance.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: fontSize12,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF1F2122),
                  ),
                ),
              ],
            ),
          ],
        ),
        Transform.scale(
          scale: size.width * 0.002,
          child: Switch(
            value: _useWalletCredits,
            onChanged: walletBalance > 0
                ? (value) {
                    setState(() {
                      _useWalletCredits = value;
                    });
                    _loadCheckoutSummary(); // Refresh checkout summary
                  }
                : null,
            activeColor: const Color(0xFF1F2122),
            inactiveTrackColor: const Color(0xFFE1E3E6),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderTotalSection(
      Size size, double fontSize12, double fontSize14, double fontSize16) {
    final summaryData = Initializer.saversPlanSummaryModel.data;
    final subTotal = summaryData?.subTotal ?? 0.0;
    final walletCredits = summaryData?.walletCredits ?? 0.0;
    final tax = summaryData?.tax ?? 0.0;
    final total = summaryData?.total ?? 0.0;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Subtotal',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: fontSize14,
                fontWeight: FontWeight.w400,
                color: Color(0xFF1F2122),
              ),
            ),
            Text(
              '\$${subTotal.toStringAsFixed(2)}',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: fontSize12,
                fontWeight: FontWeight.w400,
                color: Color(0xFF414346),
              ),
            ),
          ],
        ),
        SizedBox(height: size.height * 0.01),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Dabba Wallet Credits',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: fontSize14,
                fontWeight: FontWeight.w400,
                color: Color(0xFF1F2122),
              ),
            ),
            Text(
              '-\$${walletCredits.toStringAsFixed(2)}',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: fontSize12,
                fontWeight: FontWeight.w500,
                color: Color(0xFFD31510),
              ),
            ),
          ],
        ),
        SizedBox(height: size.height * 0.01),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Taxes & Fees',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: fontSize14,
                fontWeight: FontWeight.w400,
                color: Color(0xFF1F2122),
              ),
            ),
            Text(
              '\$${tax.toStringAsFixed(2)}',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: fontSize12,
                fontWeight: FontWeight.w500,
                color: Color(0xFF1F2122),
              ),
            ),
          ],
        ),
        SizedBox(height: size.height * 0.01),
        DottedDivider(),
        SizedBox(height: size.height * 0.01),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Total',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: fontSize16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1F2122),
              ),
            ),
            Text(
              '\$${total.toStringAsFixed(2)}',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: fontSize16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1F2122),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPayNowButton(Size size, double fontSize16) {
    final screenHeight = size.height * 0.60;
    final summaryData = Initializer.saversPlanSummaryModel.data;
    final total = summaryData?.total ?? 0.0;

    return SizedBox(
      width: double.infinity,
      height: screenHeight * 0.08,
      child: BlocBuilder<SaversplanBloc, SaversPlanState>(
        builder: (context, state) {
          final isLoading = state is CheckoutSaversPassLoading;

          return ElevatedButton(
            onPressed: isLoading
                ? null
                : () {
                    context.read<SaversplanBloc>().add(
                          CheckoutSaversPassEvent(data: {
                            "id": widget.subscriptionId,
                            "use_wallet_credits": _useWalletCredits,
                          }),
                        );
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  isLoading ? const Color(0xFF9E9E9E) : const Color(0xFF1F2122),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(26),
              ),
            ),
            child: isLoading
                ? SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    'Pay \$${total.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: MediaQuery.of(context).size.width * 0.04,
                      color: Colors.white,
                    ),
                  ),
          );
        },
      ),
    );
  }

  Widget _buildCheckoutContent(
      Size size,
      double horizontalPadding,
      double fontSize12,
      double fontSize14,
      double fontSize16,
      double fontSize20) {
    final screenHeight = size.height;
    final screenWidth = size.width;

    return SingleChildScrollView(
      padding: EdgeInsets.all(horizontalPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(horizontalPadding),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth * 0.03),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: screenWidth * 0.013,
                  offset: Offset(0, screenHeight * 0.001),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Subscription Summary',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: fontSize20,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1F2122),
                  ),
                ),
                SizedBox(height: screenHeight * 0.01),
                DottedDivider(),
                SizedBox(height: screenHeight * 0.01),
                _buildPlanDetails(size, fontSize14, fontSize16),
                SizedBox(height: screenHeight * 0.01),
                DottedDivider(),
                // SizedBox(height: screenHeight * 0.01),
                // _buildAddPromoCode(
                //     size, fontSize12, fontSize14, horizontalPadding),
                // SizedBox(height: screenHeight * 0.02),
                _buildWalletCreditsToggle(
                    size, fontSize12, fontSize14, horizontalPadding),
                DottedDivider(),
                SizedBox(height: screenHeight * 0.01),
                _buildOrderTotalSection(
                    size, fontSize12, fontSize14, fontSize16),
                SizedBox(height: screenHeight * 0.03),
                _buildPayNowButton(size, fontSize16),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanDetails(Size size, double fontSize14, double fontSize16) {
    final screenHeight = size.height;
    final summaryData = Initializer.saversPlanSummaryModel.data;
    final plan = summaryData?.plan;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              plan?.planName ?? 'Plan',
              style: TextStyle(
                fontFamily: 'Inter-bold',
                fontSize: fontSize16,
                color: Color(0xFF1F2122),
              ),
            ),
          ],
        ),
        SizedBox(height: screenHeight * 0.01),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Plan Price',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: fontSize16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF1F2122),
              ),
            ),
            Text(
              '\$${plan?.price?.toStringAsFixed(2) ?? '0.00'}',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: fontSize16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF1F2122),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showPaymentSuccessDialog() {
    final screenSize = MediaQuery.of(context).size;
    final isLargeScreen = screenSize.width > 600;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(screenSize.width * 0.04),
          ),
          contentPadding: EdgeInsets.all(screenSize.width * 0.04),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                alignment: Alignment.center,
                width: isLargeScreen ? 220 : screenSize.width * 0.55,
                child: Lottie.asset(
                  'assets/success.json',
                  repeat: true,
                  animate: true,
                ),
              ),
              Text(
                'Payment Successful!',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: isLargeScreen ? 20 : screenSize.width * 0.05,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1F2122),
                ),
              ),
              SizedBox(height: screenSize.height * 0.015),
              Text(
                'Your subscription has been activated successfully.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: isLargeScreen ? 14 : screenSize.width * 0.035,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF66696D),
                ),
              ),
              SizedBox(height: screenSize.height * 0.03),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(); // Close dialog
                    Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const MainNavigationScreen(),
                      ),
                      (route) => false,
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF1F2122),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(screenSize.width * 0.07),
                    ),
                    padding: EdgeInsets.symmetric(
                      vertical: isLargeScreen ? 14 : screenSize.height * 0.018,
                    ),
                  ),
                  child: Text(
                    'Continue',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                      fontSize: isLargeScreen ? 14 : screenSize.width * 0.035,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showPaymentFailedDialog() {
    final screenSize = MediaQuery.of(context).size;
    final isLargeScreen = screenSize.width > 600;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(screenSize.width * 0.04),
          ),
          contentPadding: EdgeInsets.all(screenSize.width * 0.04),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: isLargeScreen ? 220 : screenSize.width * 0.55,
                alignment: Alignment.center,
                child: Lottie.asset(
                  'assets/failed.json',
                  repeat: true,
                  animate: true,
                ),
              ),
              Text(
                'Payment Failed!',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: isLargeScreen ? 20 : screenSize.width * 0.05,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1F2122),
                ),
              ),
              SizedBox(height: screenSize.height * 0.015),
              Text(
                'Your payment could not be processed. Please try again or use a different payment method.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: isLargeScreen ? 14 : screenSize.width * 0.035,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF66696D),
                ),
              ),
              SizedBox(height: screenSize.height * 0.03),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.of(context).pop(); // Close dialog only
                      },
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(
                          color: Color(0xFF1F2122),
                          width: isLargeScreen ? 1 : screenSize.width * 0.002,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(screenSize.width * 0.07),
                        ),
                        padding: EdgeInsets.symmetric(
                          vertical:
                              isLargeScreen ? 14 : screenSize.height * 0.018,
                        ),
                      ),
                      child: Text(
                        'Try Again',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w500,
                          fontSize:
                              isLargeScreen ? 16 : screenSize.width * 0.04,
                          color: Color(0xFF1F2122),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
