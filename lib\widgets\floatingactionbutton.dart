import 'package:db_eats/bloc/account_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CartFloatingActionButton extends StatefulWidget {
  final VoidCallback onPressed;
  final int itemCount;

  const CartFloatingActionButton({
    super.key,
    required this.onPressed,
    this.itemCount = 0,
  });

  @override
  State<CartFloatingActionButton> createState() =>
      _CartFloatingActionButtonState();
}

class _CartFloatingActionButtonState extends State<CartFloatingActionButton> {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AccountBloc, AccountState>(
      listener: (context, state) {
        if (state is GetCartCountSuccess) {
          setState(() {});
        }
      },
      builder: (context, state) {
        // Responsive scaling factor based on screen width
        final screenWidth = MediaQuery.of(context).size.width;
        final scale = screenWidth / 375.0; // 375 is a common base width

        return Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(16 * scale),
            onTap: widget.onPressed,
            child: Padding(
              padding: EdgeInsets.only(left: 70 * scale, right: 36 * scale),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 16 * scale,
                  vertical: 1 * scale,
                ),
                decoration: BoxDecoration(
                  color: Color(0xFF1F2122),
                  borderRadius: BorderRadius.circular(50 * scale),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8 * scale,
                      offset: Offset(0, 4 * scale),
                      spreadRadius: 1 * scale,
                    ),
                  ],
                ),
                child: Row(
                  // mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Cart Icon
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Icon(
                        Icons.shopping_cart_outlined,
                        color: Colors.white,
                        size: 24 * scale,
                      ),
                    ),

                    // Item count badge (if any)
                    // if (widget.itemCount > 0)
                    Container(
                      height: 45 * scale,
                      decoration: BoxDecoration(
                        color: const Color.fromARGB(0, 255, 189, 22),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 4 * scale,
                            offset: Offset(0, 2 * scale),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              'Cart',
                              style: TextStyle(
                                fontFamily: 'Inter-medium',
                                color: Color(0xFFFFFFFF),
                                fontSize: 12 * scale,
                              ),
                            ),
                            Row(
                              children: [
                                Text(
                                  'Added',
                                  style: TextStyle(
                                    color: Color(0xFFAAADB1),
                                    fontSize: 12 * scale,
                                    fontFamily: 'Inter-medium',
                                  ),
                                ),
                                SizedBox(width: 4 * scale),
                                Text(
                                  widget.itemCount > 99
                                      ? '99+'
                                      : widget.itemCount.toString(),
                                  style: TextStyle(
                                    color: Color(0xFFAAADB1),
                                    fontSize: 12 * scale,
                                    fontFamily: 'Inter-medium',
                                  ),
                                ),
                                SizedBox(width: 4 * scale),
                                Text(
                                  'Items',
                                  style: TextStyle(
                                    color: Color(0xFFAAADB1),
                                    fontSize: 12 * scale,
                                    fontFamily: 'Inter-medium',
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    // Arrow icon replaced with right_arrow.png asset
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: Image.asset(
                        'assets/icons/right_arrow.png',
                        width: 18 * scale,
                        height: 18 * scale,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
