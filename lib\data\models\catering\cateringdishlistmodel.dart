class CateringDishListModel {
  bool? status;
  String? message;
  int? statusCode;
  CateringDishListData? data;

  CateringDishListModel({
    this.status,
    this.message,
    this.statusCode,
    this.data,
  });

  CateringDishListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null
        ? CateringDishListData.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['status_code'] = statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class CateringDishListData {
  List<FeaturedList>? featuredList;
  List<CategoryBasedList>? categoryBasedList;

  CateringDishListData({this.featuredList, this.categoryBasedList});

  CateringDishListData.fromJson(Map<String, dynamic> json) {
    if (json['featured_list'] != null) {
      featuredList = <FeaturedList>[];
      json['featured_list'].forEach((v) {
        featuredList!.add(FeaturedList.fromJson(v));
      });
    }
    if (json['category_based_list'] != null) {
      categoryBasedList = <CategoryBasedList>[];
      json['category_based_list'].forEach((v) {
        categoryBasedList!.add(CategoryBasedList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (featuredList != null) {
      data['featured_list'] = featuredList!.map((v) => v.toJson()).toList();
    }
    if (categoryBasedList != null) {
      data['category_based_list'] =
          categoryBasedList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class FeaturedList {
  int? id;
  int? orderType;
  String? title;
  int? servingSize;
  String? price;
  int? chefCategoryId;
  String? photo;
  int? listingOrder;
  bool? isFeatured;
  bool? isadded;
  num? addedQuantity;

  FeaturedList({
    this.id,
    this.orderType,
    this.title,
    this.servingSize,
    this.price,
    this.chefCategoryId,
    this.photo,
    this.listingOrder,
    this.isFeatured,
    this.isadded,
    this.addedQuantity,
  });

  FeaturedList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    orderType = json['order_type'];
    title = json['title'];
    servingSize = json['serving_size'];
    price = json['price'];
    chefCategoryId = json['chef_category_id'];
    photo = json['photo'];
    listingOrder = json['listing_order'];
    isFeatured = json['is_featured'];
    isadded = json['is_added'];
    addedQuantity = json['added_quantity'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['order_type'] = orderType;
    data['title'] = title;
    data['serving_size'] = servingSize;
    data['price'] = price;
    data['chef_category_id'] = chefCategoryId;
    data['photo'] = photo;
    data['listing_order'] = listingOrder;
    data['is_featured'] = isFeatured;
    data['is_added'] = isadded;
    data['added_quantity'] = addedQuantity;
    return data;
  }
}

class CategoryBasedList {
  Category? category;
  List<CateringDishList>? cateringDishList;

  CategoryBasedList({this.category, this.cateringDishList});

  CategoryBasedList.fromJson(Map<String, dynamic> json) {
    category =
        json['category'] != null ? Category.fromJson(json['category']) : null;
    if (json['catering_dish_list'] != null) {
      cateringDishList = <CateringDishList>[];
      json['catering_dish_list'].forEach((v) {
        cateringDishList!.add(CateringDishList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (category != null) {
      data['category'] = category!.toJson();
    }
    if (cateringDishList != null) {
      data['catering_dish_list'] =
          cateringDishList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Category {
  int? id;
  String? name;

  Category({this.id, this.name});

  Category.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class CateringDishList {
  int? id;
  int? orderType;
  String? title;
  int? servingSize;
  String? price;
  int? chefCategoryId;
  String? photo;
  int? listingOrder;
  bool? isFeatured;
  bool? isadded;
  num? addedQuantity;

  CateringDishList({
    this.id,
    this.orderType,
    this.title,
    this.servingSize,
    this.price,
    this.chefCategoryId,
    this.photo,
    this.listingOrder,
    this.isFeatured,
    this.isadded,
    this.addedQuantity,
  });
  CateringDishList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    orderType = json['order_type'];
    title = json['title'];
    servingSize = json['serving_size'];
    price = json['price'];
    chefCategoryId = json['chef_category_id'];
    photo = json['photo'];
    listingOrder = json['listing_order'];
    isFeatured = json['is_featured'];
    isadded = json['is_added'];
    addedQuantity = json['added_quantity'];
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['order_type'] = orderType;
    data['title'] = title;
    data['serving_size'] = servingSize;
    data['price'] = price;
    data['chef_category_id'] = chefCategoryId;
    data['photo'] = photo;
    data['listing_order'] = listingOrder;
    data['is_featured'] = isFeatured;
    data['is_added'] = isadded;
    data['added_quantity'] = addedQuantity;
    return data;
  }
}
