class ChefDetailsModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  ChefDetailsModel({this.status, this.message, this.statusCode, this.data});

  ChefDetailsModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['status_code'] = statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  Chef? chef;
  bool? isFavourite;

  Data({this.chef, this.isFavourite});

  Data.fromJson(Map<String, dynamic> json) {
    chef = json['chef'] != null ? Chef.fromJson(json['chef']) : null;
    isFavourite = json['is_favourite'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (chef != null) {
      data['chef'] = chef!.toJson();
    }
    data['is_favourite'] = isFavourite;
    return data;
  }
}

class Chef {
  int? chefId;
  String? profilePhoto;
  String? coverPhoto;
  String? description;
  Location? location;
  List<String>? searchTags;
  bool? availableForCatering;
  ChefDetail? chef;
  num? ratingPercentage;
  String? averageRating;
  int? totalRatings;
  num? distance; // Added this field

  Chef({
    this.chefId,
    this.profilePhoto,
    this.coverPhoto,
    this.description,
    this.location,
    this.searchTags,
    this.availableForCatering,
    this.chef,
    this.ratingPercentage,
    this.averageRating,
    this.totalRatings,
    this.distance, // Added this parameter
  });

  Chef.fromJson(Map<String, dynamic> json) {
    chefId = json['chef_id'];
    profilePhoto = json['profile_photo'];
    coverPhoto = json['cover_photo'];
    description = json['description'];
    location =
        json['location'] != null ? Location.fromJson(json['location']) : null;
    searchTags = json['search_tags'] != null
        ? List<String>.from(json['search_tags'])
        : null;
    availableForCatering = json['available_for_catering'];
    chef = json['chef'] != null ? ChefDetail.fromJson(json['chef']) : null;
    ratingPercentage = json['rating_percentage']?.toDouble();
    averageRating = json['average_rating'];
    totalRatings = json['total_ratings'];
    distance = json['distance']?.toDouble(); // Added this line
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['chef_id'] = chefId;
    data['profile_photo'] = profilePhoto;
    data['cover_photo'] = coverPhoto;
    data['description'] = description;
    if (location != null) {
      data['location'] = location!.toJson();
    }
    data['search_tags'] = searchTags;
    data['available_for_catering'] = availableForCatering;
    if (chef != null) {
      data['chef'] = chef!.toJson();
    }
    data['rating_percentage'] = ratingPercentage;
    data['average_rating'] = averageRating;
    data['total_ratings'] = totalRatings;
    data['distance'] = distance; // Added this line
    return data;
  }
}

class Location {
  Crs? crs;
  String? type;
  List<double>? coordinates;

  Location({this.crs, this.type, this.coordinates});

  Location.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? Crs.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates'] != null
        ? List<double>.from(json['coordinates'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (crs != null) {
      data['crs'] = crs!.toJson();
    }
    data['type'] = type;
    data['coordinates'] = coordinates;
    return data;
  }
}

class Crs {
  String? type;
  Properties? properties;

  Crs({this.type, this.properties});

  Crs.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'] != null
        ? Properties.fromJson(json['properties'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    if (properties != null) {
      data['properties'] = properties!.toJson();
    }
    return data;
  }
}

class Properties {
  String? name;

  Properties({this.name});

  Properties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    return data;
  }
}

class ChefDetail {
  String? firstName;
  String? lastName;
  List<OperationDays>? operationDays;
  List<OperationTimes>? operationTimes;

  ChefDetail({
    this.firstName,
    this.lastName,
    this.operationDays,
    this.operationTimes,
  });

  ChefDetail.fromJson(Map<String, dynamic> json) {
    firstName = json['first_name'];
    lastName = json['last_name'];
    if (json['operation_days'] != null) {
      operationDays = <OperationDays>[];
      json['operation_days'].forEach((v) {
        operationDays!.add(OperationDays.fromJson(v));
      });
    }
    if (json['operation_times'] != null) {
      operationTimes = <OperationTimes>[];
      json['operation_times'].forEach((v) {
        operationTimes!.add(OperationTimes.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    if (operationDays != null) {
      data['operation_days'] = operationDays!.map((v) => v.toJson()).toList();
    }
    if (operationTimes != null) {
      data['operation_times'] = operationTimes!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class OperationDays {
  int? dayId;
  Day? day;

  OperationDays({this.dayId, this.day});

  OperationDays.fromJson(Map<String, dynamic> json) {
    dayId = json['day_id'];
    day = json['day'] != null ? Day.fromJson(json['day']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['day_id'] = dayId;
    if (day != null) {
      data['day'] = day!.toJson();
    }
    return data;
  }
}

class Day {
  String? name;

  Day({this.name});

  Day.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    return data;
  }
}

class OperationTimes {
  int? timingId;
  Timing? timing;

  OperationTimes({this.timingId, this.timing});

  OperationTimes.fromJson(Map<String, dynamic> json) {
    timingId = json['timing_id'];
    timing = json['timing'] != null ? Timing.fromJson(json['timing']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['timing_id'] = timingId;
    if (timing != null) {
      data['timing'] = timing!.toJson();
    }
    return data;
  }
}

class Timing {
  String? startTime;
  String? endTime;

  Timing({this.startTime, this.endTime});

  Timing.fromJson(Map<String, dynamic> json) {
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    return data;
  }
}
