{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9896ffa06b6790a09f3ca6fdf2b3bf6e28", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9820cc0539a1ffa7dbfd9c2e78f3d2f8f2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d9ce4e9a9576ee7513d0ff5ffefe33d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9865791095e787370695551c6e85012284", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d9ce4e9a9576ee7513d0ff5ffefe33d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bb8e47063a76f669e619cedcfe815699", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984ead41c0ce5268e9faec8a5464882f55", "guid": "bfdfe7dc352907fc980b868725387e984b46915ff6d9d1b93ebb9280f1c1ced8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e68cf795f43c5837ebb918580b24fc1d", "guid": "bfdfe7dc352907fc980b868725387e98c8be07bd0033b62c7c59bbb4dfaf29c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a1d89c9c003dc97f2398197a03b9df", "guid": "bfdfe7dc352907fc980b868725387e984d91526b2bb6a70b0f3eba5811397427"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b620c591052b57e153a049a85e54ef2", "guid": "bfdfe7dc352907fc980b868725387e98974f02cb8483a42ec79f3857f343172a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98def723b30ff28f27e3f0fd93948c49ef", "guid": "bfdfe7dc352907fc980b868725387e98fff0403816515ccb92213d1b0bb6c996"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cae6d319170eb53be480feb636aeef92", "guid": "bfdfe7dc352907fc980b868725387e98a08dab90e0ab6cbdf900b77b78566991"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5aa907248b60546c33dc249199537f2", "guid": "bfdfe7dc352907fc980b868725387e98e0b666bb14a450f63f70ca8568c10d32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989902b73137acd1a6e4c49f54b86ed936", "guid": "bfdfe7dc352907fc980b868725387e986b446f8c23261783e679fb1c66653079", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd4ca0ba8e1876edadb7b17accbf8f58", "guid": "bfdfe7dc352907fc980b868725387e9850577f35d18349baf33e98bec4ff6ae3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e9738340156c51971216cf328d28631", "guid": "bfdfe7dc352907fc980b868725387e98a82844a3202bcc883e838ed8bffde07c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98776cc992018e32a0973f2b89db1737bf", "guid": "bfdfe7dc352907fc980b868725387e98022e943a071d2697250b68fdba7ff1f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c97a78ccab8b6d348fedee23ee1d713", "guid": "bfdfe7dc352907fc980b868725387e985e5fc43a728c9879c1f81ab5583ff434"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b9b246a667c2c68f08b10a24df29a7", "guid": "bfdfe7dc352907fc980b868725387e98462b39c42143ef2e89124ef3828c2894", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982085398a9db9bdbed2304efa7bee0de2", "guid": "bfdfe7dc352907fc980b868725387e98831d94e9158272f89f351e702a8f57d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883399022cdd0a44d4acdbcd11ae02016", "guid": "bfdfe7dc352907fc980b868725387e98d4081ad973ab3da51f6df7a6c49a6642"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee03146c0517e8c31c683de79c39346", "guid": "bfdfe7dc352907fc980b868725387e98ba6a24ab592fe42cf131a62f2780506b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ceb670d900a030d7e1846a3adb50b82", "guid": "bfdfe7dc352907fc980b868725387e98fde28565f83f75d6a8da980e8b6f9d4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ea3e5d22c3d6687fdfdb507f67bc90e", "guid": "bfdfe7dc352907fc980b868725387e984b57efbb161d4c969dbb9b7882cb79c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854865a943e1fd3458d925e44ff5588a9", "guid": "bfdfe7dc352907fc980b868725387e98186fe05bf0bec50e13348905956aa132"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858f0b1dac60bcd20aa7e2f990f828793", "guid": "bfdfe7dc352907fc980b868725387e98e69d0fa76b5897ecdf76eb39c3cf7856"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985146e282bdc00f1cbf790d1ddc1f437f", "guid": "bfdfe7dc352907fc980b868725387e98fff92c2bee81dc3d543478dc70a40d0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1067e3558f5c345972633f010740537", "guid": "bfdfe7dc352907fc980b868725387e98040300c9fcad24cb5c5fdc5748c03939"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872ec9f44731c5bb4164b6a3e41b76253", "guid": "bfdfe7dc352907fc980b868725387e98d633af9bdd2b4ce57668324bf5ce2c50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0b1aaf5eb4b39b3648a35669602a8fa", "guid": "bfdfe7dc352907fc980b868725387e9874e9a760406c704429d845d73f90c24e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869bb3dc65e0014294b3bb00cd8bfc39a", "guid": "bfdfe7dc352907fc980b868725387e98ebe9a9a66f0001afa346baa7dc6371d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98895b6e986cefb0ea061913753b2d8f60", "guid": "bfdfe7dc352907fc980b868725387e9896abe570586f56f2d033873dccdad054"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5249667805b2e1de12671b7c35536cf", "guid": "bfdfe7dc352907fc980b868725387e981df13b83b4be2ab525dc94431104ddc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1235d26302bbc3ead4ae169811eb3b", "guid": "bfdfe7dc352907fc980b868725387e98db40c15f6116d71116906b87d6f46893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98658b4255c533d9f0000647707c600e24", "guid": "bfdfe7dc352907fc980b868725387e9893a49987a5a26ed35094589c354ded0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d72c4e4aa19a9f2c6abf97c2de18c601", "guid": "bfdfe7dc352907fc980b868725387e980aa1d8d053b809209f81ae71fe67d9c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d60e5b49cc13899e28183430e89758c", "guid": "bfdfe7dc352907fc980b868725387e98ebe34171182499cfc19dba38d71dfa62"}], "guid": "bfdfe7dc352907fc980b868725387e984c6e36a07bf98b79d2dfa191d3087615", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9885e9ca92f571b0cf851e1c3f4e78052e", "guid": "bfdfe7dc352907fc980b868725387e98916beebb63d416db8052ad2b4f03351a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d31058df2886b1d4d6364b641f1bce0", "guid": "bfdfe7dc352907fc980b868725387e9840c54493954c5136b92d37f71f4a4bf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98674581cf084aec471560d58746b6ef5b", "guid": "bfdfe7dc352907fc980b868725387e98035938000ae159271f6b71d5fbb75c01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d42d48d0587485e4f76a450e46864f12", "guid": "bfdfe7dc352907fc980b868725387e98b47ba6fb3fe3a652e185586439725015"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f81be2cbdff85662fedddfb2d2855992", "guid": "bfdfe7dc352907fc980b868725387e98b31883dac138008774c13463e177d83e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ed379ba00ca27bfe07803cfa1c8c649", "guid": "bfdfe7dc352907fc980b868725387e98c6b3d1e1db35226026d6689a74909769"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1918213261f97c980de855c9aaf5fbe", "guid": "bfdfe7dc352907fc980b868725387e98fe8bd110a01d6f53ff4749156a973933"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b44ef4fb4b621092a72131838d5a9ec0", "guid": "bfdfe7dc352907fc980b868725387e9878e546d602051938509097879b67e8b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800f70ed1467a1c85e7e27d3618a9c312", "guid": "bfdfe7dc352907fc980b868725387e984d67f487a4e35365930a19f719ee5474"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce36b83b05ce68fd75de41594ffecc68", "guid": "bfdfe7dc352907fc980b868725387e98140d149d3e4a4f30af5965bfffd8ef3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9b62552623e523fd677049841e88196", "guid": "bfdfe7dc352907fc980b868725387e987e0ac192b636e05ace9b433b7aa16242"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985548f8ca31ccdf40e3252db77da35010", "guid": "bfdfe7dc352907fc980b868725387e981c48de80bdd18f57b8eccfb0439e8564"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983787306144fba9681956847921420859", "guid": "bfdfe7dc352907fc980b868725387e984d36979ebfb27c52f991c737abbb993d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98455e42898fea3177d522dfd02ba7b07a", "guid": "bfdfe7dc352907fc980b868725387e983f456d8b4ad3e1ee26518a1c3cac14ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fba70b156bbc0c0b4f63467bae677f6", "guid": "bfdfe7dc352907fc980b868725387e9827182f6c769fd6cad92b1f93571af9d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3f0e10d8b3eb7e9ba3dedca8e356270", "guid": "bfdfe7dc352907fc980b868725387e98731cfd67af1dd014a53879eb47bc6fe9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869a48ef5833857af0a808d4288dc7f85", "guid": "bfdfe7dc352907fc980b868725387e98417a387c2243f1f3e063cbee0035c0b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884ee9f5249ef306ef16f2516fad9d3de", "guid": "bfdfe7dc352907fc980b868725387e9863dae36979075d1521e9737b9f3cecf0"}], "guid": "bfdfe7dc352907fc980b868725387e98f67798a0de392afdd4497d6885932434", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e986172006604e2b0ff9e7d17018a6f3d8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e98d3b2c7c4c3245b39ff77184fe8d1edcd"}], "guid": "bfdfe7dc352907fc980b868725387e9896b231bfab827aa15936737d417288ee", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984ff26b216bf5c6ec4cd22633dc99b77c", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98c014e067034ab20a513825f0b0abbd00", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}