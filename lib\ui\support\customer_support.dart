import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/support_bloc.dart';
import 'package:db_eats/common/initializer.dart';

class CustomerSupport extends StatefulWidget {
  final int orderId;

  const CustomerSupport({super.key, required this.orderId});

  @override
  _CustomerSupportState createState() => _CustomerSupportState();
}

class _CustomerSupportState extends State<CustomerSupport> {
  String? selectedIssue;
  final TextEditingController _descriptionController = TextEditingController();
  late SupportBloc _supportBloc;

  @override
  void initState() {
    super.initState();
    _supportBloc = SupportBloc();
    _supportBloc.add(ListIssueCategoryEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => _supportBloc,
      child: BlocConsumer<SupportBloc, SupportState>(
        listener: (context, state) {
          if (state is ListIssueCategoryFailed) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is RaiseTicketSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.of(context).pop();
          } else if (state is RaiseTicketFailed) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              title: Text(
                'Contact Support',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              backgroundColor: Color(0xFFF6F3EC),
              leading: IconButton(
                icon: Icon(Icons.close, color: Colors.black87),
                onPressed: () => Navigator.of(context).pop(),
              ),
              centerTitle: true,
            ),
            backgroundColor: Color(0xFFF6F3EC),
            body: state is ListIssueCategoryLoading
                ? Center(child: CupertinoActivityIndicator())
                : SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Card(
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            color: Colors.white,
                            child: Padding(
                              padding: EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras ante diam, viverra vitae ligula vitae, gravida mattitor justo. Aliquam dapibus lorem nec risus porta, quis rhoncus lectus.',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Color(0xFF414346),
                                      fontFamily: 'Inter',fontWeight: FontWeight.w400,
                                      height: 1.4,
                                    ),
                                  ),
                                  SizedBox(height: 20),
                                  Text(
                                    'What Issue Are You Having?',
                                    style: TextStyle(
                                      fontSize: 14,fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter-Semibold',
                                      color: Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  Container(
                                    width: double.infinity,
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 12),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(28),
                                      border: Border.all(
                                          color: Colors.grey.shade300),
                                    ),
                                    child: DropdownButtonHideUnderline(
                                      child: DropdownButton<String>(
                                        value: selectedIssue,
                                        hint: Text(
                                          'Select...',
                                          style: TextStyle(
                                            color: Colors.black54,
                                            fontSize: 16,fontWeight: FontWeight.w400
                                          ),
                                        ),
                                        isExpanded: true,
                                        items: _buildDropdownItems(state),
                                        onChanged: (String? newValue) {
                                          setState(() {
                                            selectedIssue = newValue;
                                          });
                                        },
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 16),
                                  Container(
                                    width: double.infinity,
                                    height: 120,
                                    padding: EdgeInsets.symmetric(horizontal: 12),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(6),
                                      border: Border.all(
                                          color: Colors.grey.shade300),
                                    ),
                                    child: TextField(
                                      controller: _descriptionController,
                                      maxLines: null,
                                      expands: true,
                                      textAlignVertical: TextAlignVertical.top,
                                      decoration: InputDecoration(
                                        hintText: 'Describe the issue...',
                                        hintStyle: TextStyle(
                                          color: Colors.black38,
                                          fontSize: 16,fontWeight: FontWeight.w400
                                        ),
                                        border: InputBorder.none,
                                      ),
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.black87,
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 24),
                                  SizedBox(
                                    width: double.infinity,
                                    height: 48,
                                    child:
                                        BlocBuilder<SupportBloc, SupportState>(
                                      builder: (context, state) {
                                        return ElevatedButton(
                                          onPressed: state is RaiseTicketLoading
                                              ? null
                                              : () =>
                                                  _handleSendMessage(context),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.black87,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(24),
                                            ),
                                            elevation: 0,
                                          ),
                                          child: state is RaiseTicketLoading
                                              ? SizedBox(
                                                  height: 20,
                                                  width: 20,
                                                  child:
                                                      CircularProgressIndicator(
                                                    valueColor:
                                                        AlwaysStoppedAnimation<
                                                                Color>(
                                                            Colors.white),
                                                    strokeWidth: 2,
                                                  ),
                                                )
                                              : Text(
                                                  'Send Message',
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
          );
        },
      ),
    );
  }

  List<DropdownMenuItem<String>> _buildDropdownItems(SupportState state) {
    if (state is ListIssueCategorySuccess) {
      // FIXED: Use correct spelling - issueCategoryModel instead of issueCatogoryModel
      if (Initializer.issueCatogoryModel?.data?.data != null) {
        return Initializer.issueCatogoryModel!.data!.data!
            .where((category) => category.id != null) // Filter out null IDs
            .map<DropdownMenuItem<String>>((category) {
          return DropdownMenuItem<String>(
            value: category.id!.toString(),
            child: Text(
              category.name ?? 'Unknown',
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          );
        }).toList();
      }
    }
    return [];
  }

  void _handleSendMessage(BuildContext context) {
    // Add debug prints to troubleshoot
    print('Selected Issue: $selectedIssue');
    print('Description: ${_descriptionController.text}');

    if (selectedIssue != null &&
        selectedIssue!.isNotEmpty &&
        _descriptionController.text.isNotEmpty) {
      try {
        int categoryId = int.parse(selectedIssue!);
        print('Parsed Category ID: $categoryId'); // Debug print

        // Validate that the category ID is positive
        if (categoryId <= 0) {
          throw FormatException('Invalid category ID');
        }

        final payload = {
          "order_id": widget.orderId,
          "categoryid": categoryId,
          "description": _descriptionController.text.trim(),
        };

        _supportBloc.add(RaiseTicketEvent(payload));
      } catch (e) {
        print('Error parsing category ID: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Invalid category selection. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please fill in all fields'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _supportBloc.close();
    super.dispose();
  }
}
