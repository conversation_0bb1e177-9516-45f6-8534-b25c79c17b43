{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983f3900c3837cb0bc1baf282f05e538fa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e2277be28a487919925c019eadbc3d4d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98053786dff7b5e2110aaf73472b42aa36", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9837d6095d2edb1b19ee11d5bb60efcadb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98053786dff7b5e2110aaf73472b42aa36", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98266c1528ba49ac2bac63a560ee690878", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9853c2e4a593db20fd75712709971c7bd6", "guid": "bfdfe7dc352907fc980b868725387e98e266072598b9c98e52aea7bbded35295", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9800d50e50f4ef8e57b903ef311be596b5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9877005d31cbb8cb385aaa0fde73e31484", "guid": "bfdfe7dc352907fc980b868725387e985e71767b2cf6bcbe4aa7e814e13620dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e2df5a973e76ebb5a6ff265b45a9084", "guid": "bfdfe7dc352907fc980b868725387e9831525010bfb1b1e0c5d26fea61e477e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804be3f9abe8898b51d8f8c276bb087a2", "guid": "bfdfe7dc352907fc980b868725387e98b57b5816d65fc28a088d58c468741c6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b26b0567bc7c7bbda48b5b74c9e3066b", "guid": "bfdfe7dc352907fc980b868725387e988211440440856f50cbf26c7badc1b866"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98518f4440b844ec59bdffb57b85dc3705", "guid": "bfdfe7dc352907fc980b868725387e9835e53fb2e560775f470c652d9b579a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a60699d145beb3e905c1c10c837e1e9", "guid": "bfdfe7dc352907fc980b868725387e986794b6c0939151035c2ab274f1711125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f4b946dfb6449d906c4d97ea2bfec80", "guid": "bfdfe7dc352907fc980b868725387e98f34a32fc4451160f848d719fc5f81f12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea11af9beafb9c663379ae4954553140", "guid": "bfdfe7dc352907fc980b868725387e988e64943a42cf12277e5c7e4f72f2bf24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ed401e1c3043094f75ab7c20615250c", "guid": "bfdfe7dc352907fc980b868725387e98a7f262082f40c1e9a308fa29c64e0cfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a26958da0edd201e2f6cb07ebc2afc6", "guid": "bfdfe7dc352907fc980b868725387e98e83c626b280a1984826e0a8fec0e8118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ec52ce715a441795ec8f84a8d38cc99", "guid": "bfdfe7dc352907fc980b868725387e98d22b444d6d153e8f3e7e449aefa370eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd5dc949c749fc25731d8c18f97cee97", "guid": "bfdfe7dc352907fc980b868725387e98089f83e5952fce913475cd95c38bd99c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896808c52d38d2e325ea21f1b288511cc", "guid": "bfdfe7dc352907fc980b868725387e9890506ea18e7f21cc71daca82d4d212cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98292c1ff3ff18a843daca948a4a339c9a", "guid": "bfdfe7dc352907fc980b868725387e98dea4509592aa0c6fabb9766f37c3e38e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835585d4f79f048be75e0205e57cff047", "guid": "bfdfe7dc352907fc980b868725387e987c4c617ae775f307055be664aec1c2ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887e1f20d69bcaff7b7b7be773f4da045", "guid": "bfdfe7dc352907fc980b868725387e984dc26de453d9cfa4e5eadaa36416751b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a854365526cc88ee68936c140ece3458", "guid": "bfdfe7dc352907fc980b868725387e98ab2ce06f23ea3006eb332485697eb9c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5c701ff20c8306cc309c66cae1de326", "guid": "bfdfe7dc352907fc980b868725387e9845d840f476efcee1af7c183ac5d9af8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98305ccf9a63fe38f03800d0286ae597bf", "guid": "bfdfe7dc352907fc980b868725387e98dce3ee724f424304a4d660f38a6944da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879744f1fe74fd43ab7e9880038f974bd", "guid": "bfdfe7dc352907fc980b868725387e98a73c04e13e52b41ee070d2a467c1e6f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98532e284edd0d89a055c27481b4a9ce18", "guid": "bfdfe7dc352907fc980b868725387e9816762b1be109f65eabe0994f9ca493b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eacacc7a89a6b74a2c2a23b2682d336", "guid": "bfdfe7dc352907fc980b868725387e98e8bd2430c067efd14e4ffb7200b3f64b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fb394a7d2751eeacb8a709b1072d9ab", "guid": "bfdfe7dc352907fc980b868725387e98718446df57e9895d602c6fcd3e141240"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98323b4eb98ba7f29eb217211d60a6ea01", "guid": "bfdfe7dc352907fc980b868725387e98131857d7b303a6374fbc9fd721e9cfba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d52948097b68a944880ecd5fce6b2fcf", "guid": "bfdfe7dc352907fc980b868725387e987dd8c291d9a9e17029c957748eafbb17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b6a92c46af4182e6226061a0c2ee264", "guid": "bfdfe7dc352907fc980b868725387e98f3e08e667b79ef42c07eddba7c2a976b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98430c91449b59aff99ce0bc0576f7240e", "guid": "bfdfe7dc352907fc980b868725387e98fcb311f8096cd0a656a7902a194258f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7ba24c458cab4a5ade0e02035ae62a7", "guid": "bfdfe7dc352907fc980b868725387e9801de043157615b1222c171fbba127ae8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982eeed929142da67b741c51bc10f1e51d", "guid": "bfdfe7dc352907fc980b868725387e98b729a65c86fa445602b60f2c0498ba91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eb59cb4388e14f80a2c82ac047d1ac4", "guid": "bfdfe7dc352907fc980b868725387e989622e82a87fdc3d8adc1917f6d0cfad9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813343b5c4f8c23110c2ae7f6f8c981a7", "guid": "bfdfe7dc352907fc980b868725387e9811424d6f02786bae164d9359f3e22d68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcccfb779ce0482d7e3c3dc5889c6825", "guid": "bfdfe7dc352907fc980b868725387e9871026481ccbfab3f1beb1d91210e6617"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0e7888114caaea2cf8a5e37fa49f63c", "guid": "bfdfe7dc352907fc980b868725387e98280087e742826f7b7aa41c9c25d2a697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a6fc5147c3496ee0d1f41be158ce3f0", "guid": "bfdfe7dc352907fc980b868725387e9839d75e19d3f22529f017625e9384a1a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a6b1e2081e2d4d96db1c1c340003243", "guid": "bfdfe7dc352907fc980b868725387e98a1d55e0acf299eded1f99b334bf3e857"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98facffd06b18c9eb259cce2e425be9304", "guid": "bfdfe7dc352907fc980b868725387e98e2d4a1e3d1aa90bcc74e288af5e4ebb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b3d90783cd4a71c258fd70364c804d0", "guid": "bfdfe7dc352907fc980b868725387e98ab91b99606cee030e5afce8505e821a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b047c4a553829f6b4bd1b7ea1042afe", "guid": "bfdfe7dc352907fc980b868725387e98910412d9f5b863c249eefa42c85fd50b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889801f290b9a741a5af8c38d29bd54c9", "guid": "bfdfe7dc352907fc980b868725387e98467532b9e6540a51e9c961700d776523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a20a33d0e9fb52ca31b1efa4c3de73", "guid": "bfdfe7dc352907fc980b868725387e9851245b6b9117d368fcfabcace1603a5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837e4bd8e8d25288bdd4f8c6a6f3c69d2", "guid": "bfdfe7dc352907fc980b868725387e983c69a79206e5b87de2802ec2a2391d53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b5c0c09a142f4655b79c411e9bfc11f", "guid": "bfdfe7dc352907fc980b868725387e98187490859cb24e2d0e1921e651444b9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98021b2e1c95c3f66f807324cc25f18d4a", "guid": "bfdfe7dc352907fc980b868725387e98c915c74d9f0a28227f77a2c0186bc091"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98076569ab6678e130e74949f625f45faf", "guid": "bfdfe7dc352907fc980b868725387e98d646c134a27df478370327c27db28a44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f59c75f174fb43f26ecd1dbc8119371c", "guid": "bfdfe7dc352907fc980b868725387e98940ad400b820ce8802bee9b0489197e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98437d6a7142c8d8249a4cd87bb80b50ff", "guid": "bfdfe7dc352907fc980b868725387e98c407e37b00ccd454776e0c1afc59af12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804b1f8866231d0d1ec4ea63dd18830c0", "guid": "bfdfe7dc352907fc980b868725387e9868344afce35baf05114ccfd51ee8f325"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b89a0e0a22fdc095b82a8a63e8cebb8c", "guid": "bfdfe7dc352907fc980b868725387e98bed9c58ac8bd697d31385454670d3f23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c3fea58e984bf4f11b46f9882a4b0f2", "guid": "bfdfe7dc352907fc980b868725387e98a8d3ed72e68273aa9b45af1482d0ab81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b96e58ad8ed35d9dcbec119bb71fa98", "guid": "bfdfe7dc352907fc980b868725387e9811c557859e61ed80dc9c2a1d4eba9969"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854499ad231c8b3d267faea9bf8912fb2", "guid": "bfdfe7dc352907fc980b868725387e982650e6f6a724d30b892cf5ae66b63637"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cf9a6328f572bbb05e76ac095e7759f", "guid": "bfdfe7dc352907fc980b868725387e988a819307a82baa0e057f01e2c5c8fda4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c1d8ad0443b2a11bfa448349f8cd81c", "guid": "bfdfe7dc352907fc980b868725387e98d1b72562bc1b600823e710486bfd29b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b7ac6c77bdf7ad2ceab9b82a796b51b", "guid": "bfdfe7dc352907fc980b868725387e9819f99e8789ad2b451e63645768cb993b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c3b45e64118db6b9f026ac976284799", "guid": "bfdfe7dc352907fc980b868725387e98cbd4ed178572905e5f79227502c26d9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98721753b2258fe35bad5aab2b7bb495ba", "guid": "bfdfe7dc352907fc980b868725387e98300021b9b9a4010277e262c7b1bdd899"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821d835fe2418f2104f4a225415c35748", "guid": "bfdfe7dc352907fc980b868725387e9840d414d6d90bca8c0a826d3e7f08c4b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983222a60741e7f691eefaf3a83c99f35e", "guid": "bfdfe7dc352907fc980b868725387e9878d11d2e119bfc334ced087f3d45b874"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989670c90bdb5aec4fc3f9b25085531866", "guid": "bfdfe7dc352907fc980b868725387e98d2c807268d32c38e3aaa6a836f7c4ef7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0c2790606583591da66b3021f86e32c", "guid": "bfdfe7dc352907fc980b868725387e98549a86644f77b1feb8b2c94f79d3f194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866b83d3756312d35d6476c1524790dce", "guid": "bfdfe7dc352907fc980b868725387e986f82fe15cd763d022a41430a93da8a2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ca11f74bba5b0f638d7629bfb6b9e39", "guid": "bfdfe7dc352907fc980b868725387e981130f662e555066c50717a8a8c57c0e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3251cec6fe78d40526508054de0e04a", "guid": "bfdfe7dc352907fc980b868725387e9810fbd4abdf5ccfa1e6d9e2f53193bad0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a42679d4912a6becbb98db56b818f1f", "guid": "bfdfe7dc352907fc980b868725387e98ddb97ae0d0784417cf2d6521ed00a178"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc43ba41064f33cc1441025106f68874", "guid": "bfdfe7dc352907fc980b868725387e98fe230647cd1411ecd5dc2bf2a9d4e2a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985725332a29f40151c2981caee7f00f99", "guid": "bfdfe7dc352907fc980b868725387e984da98442a79ed8a0e1387a0ce5b27b1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4d5ca1f86a2de1c5760fe01b13ccf02", "guid": "bfdfe7dc352907fc980b868725387e98b6d16efc003dad8f3653a0e13ef727ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bef1cd35caa0ab063cf6f6f058dc0542", "guid": "bfdfe7dc352907fc980b868725387e981dc23601da91a33d58485de82cac566e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c10c4e10445fee4d73421f3299d22e1", "guid": "bfdfe7dc352907fc980b868725387e98ff7d8badc1c9d5ee2e1d88269964c805"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6e88325f6f193a5d0b1edc7691d8cb9", "guid": "bfdfe7dc352907fc980b868725387e98c5f061912801e82f57aa72405a11cc49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853cddbe29371b607fe1b8a2046d77dae", "guid": "bfdfe7dc352907fc980b868725387e98372ebd4fa8fe796651da1765a5891a57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98118e412335f3987a9075869c85314696", "guid": "bfdfe7dc352907fc980b868725387e98eb20c013caa5de6a362fdf6ac6c9943a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98604fe2ddca12b65eb4d0de4523e64c19", "guid": "bfdfe7dc352907fc980b868725387e98c871d1ce93c5ed836a06e670bc9f96c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845b9efd5e9d16842e61eeee9d1bbb2e7", "guid": "bfdfe7dc352907fc980b868725387e981f2144de32952af0363dea8b5ef341d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc54b19698d694885c3e01f07e2735d3", "guid": "bfdfe7dc352907fc980b868725387e986448cc18fccf832b75437980db5a5e21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98948e9bb04304f8632d0aeae0e7d4fc83", "guid": "bfdfe7dc352907fc980b868725387e986c25ace66908c54f0b43bf2f789111f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d077fe14bac4ff7b95d972661d1ca62", "guid": "bfdfe7dc352907fc980b868725387e980774b6b4832c0461a0f8629ee85b1feb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a101f4fba863083d7c9c677b73c0001a", "guid": "bfdfe7dc352907fc980b868725387e984bd29858a3836dbea44f997fbdd4e1b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dbca31dc2c495234687faae3cbc5347", "guid": "bfdfe7dc352907fc980b868725387e982e213790a9afe76ce56981f6648c8212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851bd45214eef5ccb961f324702c7a12f", "guid": "bfdfe7dc352907fc980b868725387e98a3b60caba9104f2262e0e30bd6e565ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a9064aa42e672671abd99837eba93cf", "guid": "bfdfe7dc352907fc980b868725387e981fcad40ff117428162e99a7a25ae0c41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ba5363906c18756b5d19533fded46a3", "guid": "bfdfe7dc352907fc980b868725387e98ce34395ba64614458fc90b5bd4385cf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f16463381e806824cf27b2847f6a732", "guid": "bfdfe7dc352907fc980b868725387e985a90a85f1d2f7d5513c41ebb2947feeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b73d9f5efe874d18705fadf29d609d4a", "guid": "bfdfe7dc352907fc980b868725387e985145a37f733cb33167c13152e5b57b13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98014ff9de9b397f1515c502679fba677a", "guid": "bfdfe7dc352907fc980b868725387e98367ba72a0bdff4fe465a78e65e2586e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885b5a86480a4b97f5a743736df1cf9c6", "guid": "bfdfe7dc352907fc980b868725387e9892ca5dcf5d43dba7241c5593929aafee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830ea5d99c0c0c208e108989b62e93e73", "guid": "bfdfe7dc352907fc980b868725387e98bb52774805f03428eb40a2f9b2147a86"}], "guid": "bfdfe7dc352907fc980b868725387e98997d1f3320b96a489bbfeca42b5f2b52", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9820ad3732878a4721ac229b7e03e0956a"}], "guid": "bfdfe7dc352907fc980b868725387e9814822ff19035a835afeb657bb4dcb35b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c9cbb956b62333c02329180fbe03b825", "targetReference": "bfdfe7dc352907fc980b868725387e9890f568fc9b811cd56b08e401eec0f35e"}], "guid": "bfdfe7dc352907fc980b868725387e98b78f48222917d3611c7c397d7f9ae63e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9890f568fc9b811cd56b08e401eec0f35e", "name": "rive_common-rive_common_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9849c5bdbe31e3b466d37bab9271baf60a", "name": "rive_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9884d4dff26bf6b3e66f738069c7476896", "name": "rive_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}