class Listdishtypemodel {
  bool? status;
  String? message;
  int? statusCode;
  DishTypeData? data;

  Listdishtypemodel({this.status, this.message, this.statusCode, this.data});

  Listdishtypemodel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data =
        json['data'] != null ? new DishTypeData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DishTypeData {
  List<DishTypes>? dishTypes;

  DishTypeData({this.dishTypes});

  DishTypeData.fromJson(Map<String, dynamic> json) {
    if (json['dish_types'] != null) {
      dishTypes = <DishTypes>[];
      json['dish_types'].forEach((v) {
        dishTypes!.add(new DishTypes.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.dishTypes != null) {
      data['dish_types'] = this.dishTypes!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DishTypes {
  int? id;
  String? name;

  DishTypes({this.id, this.name});

  DishTypes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}
