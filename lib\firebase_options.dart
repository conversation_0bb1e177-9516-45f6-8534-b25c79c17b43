// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAB1EfOjOUyjRMvaBqoRMzCoDhMGt8j9FY',
    appId: '1:627897874109:web:034b15eb14788626c569d4',
    messagingSenderId: '627897874109',
    projectId: 'eatro-app',
    authDomain: 'eatro-app.firebaseapp.com',
    storageBucket: 'eatro-app.firebasestorage.app',
    measurementId: 'G-K48513QRLT',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBcYc8-ZeU3Ee-xZcR1y9dDr0mImrunkQQ',
    appId: '1:627897874109:android:a1a80b4f037feb27c569d4',
    messagingSenderId: '627897874109',
    projectId: 'eatro-app',
    storageBucket: 'eatro-app.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCnHhZubQJPjHl26i0FfIjt7ie8Hs1IX3U',
    appId: '1:627897874109:ios:adc8f5438029c153c569d4',
    messagingSenderId: '627897874109',
    projectId: 'eatro-app',
    storageBucket: 'eatro-app.firebasestorage.app',
    androidClientId: '627897874109-qheh9sn1gkp4qhm0pal4l2jckmckuqkb.apps.googleusercontent.com',
    iosClientId: '627897874109-r5giqqba4du9mcn39ikco7mf9ejic3ag.apps.googleusercontent.com',
    iosBundleId: 'com.eatro.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCnHhZubQJPjHl26i0FfIjt7ie8Hs1IX3U',
    appId: '1:627897874109:ios:85ca82cbde0c32fcc569d4',
    messagingSenderId: '627897874109',
    projectId: 'eatro-app',
    storageBucket: 'eatro-app.firebasestorage.app',
    androidClientId: '627897874109-qheh9sn1gkp4qhm0pal4l2jckmckuqkb.apps.googleusercontent.com',
    iosClientId: '627897874109-vlp495bnp5btnu7mo1pdi740kvov01dd.apps.googleusercontent.com',
    iosBundleId: 'com.example.dbEats',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAB1EfOjOUyjRMvaBqoRMzCoDhMGt8j9FY',
    appId: '1:627897874109:web:e34e278a71c8bc08c569d4',
    messagingSenderId: '627897874109',
    projectId: 'eatro-app',
    authDomain: 'eatro-app.firebaseapp.com',
    storageBucket: 'eatro-app.firebasestorage.app',
    measurementId: 'G-PKPVR8MPLK',
  );
}
