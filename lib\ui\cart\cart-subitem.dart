import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/cart/cart_checkout_page.dart';
import 'package:db_eats/ui/chef/view_chef2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/data/models/cart/viewcartmodel.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/data/models/meal_plan/timinglistmodel.dart';

class CartPage2 extends StatefulWidget {
  final int chef_id;

  const CartPage2({
    super.key,
    required this.chef_id,
  });

  @override
  State<CartPage2> createState() => _CartPage2State();
}

class _CartPage2State extends State<CartPage2> {
  List<Items>? cartItems;
  double subtotal = 0.0;
  Chef? chefData;
  String? customerTimePreference;
  String? currentAddress;
  Map<int, String> _updatingQuantity = {};
  List<Timings>? _availableTimings;
  bool isCartUpdatingQuantity = false;
  bool isTimePreferenceLoading = false;

  @override
  void initState() {
    super.initState();
    context.read<AccountBloc>().add(ViewCartEvent(widget.chef_id));
    context.read<MealplanBloc>().add(ListTimingEvent());
  }

  String _formatTimeToAmPm(String? time) {
    if (time == null) return '12AM';
    try {
      final timeParts = time.substring(0, 5).split(':');
      int hours = int.parse(timeParts[0]);
      final period = hours >= 12 ? 'PM' : 'AM';
      hours = hours > 12
          ? hours - 12
          : hours == 0
              ? 12
              : hours;
      return '$hours$period';
    } catch (e) {
      return '12AM';
    }
  }

  String _formatDays(List<String>? days) {
    if (days == null || days.isEmpty) return 'N/A';
    return days.map((day) => day[0]).join(', ');
  }

  bool _isTimeSlotPassed(String? endTime) {
    if (endTime == null) return false;
    try {
      final now = DateTime.now();
      final timeParts = endTime.substring(0, 5).split(':');
      final hours = int.parse(timeParts[0]);
      final minutes = int.parse(timeParts[1]);
      final slotEndTime = DateTime(
        now.year,
        now.month,
        now.day,
        hours,
        minutes,
      );
      return now.isAfter(slotEndTime);
    } catch (e) {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isLargeScreen = screenSize.width > 600;

    return MultiBlocProvider(
      providers: [
        BlocProvider<AccountBloc>.value(
          value: context.read<AccountBloc>(),
        ),
        BlocProvider<MealplanBloc>.value(
          value: context.read<MealplanBloc>(),
        ),
      ],
      child: BlocListener<AccountBloc, AccountState>(
        listener: (context, state) {
          if (state is ViewCartSuccess) {
            setState(() {
              cartItems = state.data?.items ?? [];
              subtotal = (state.data?.totalPrice ?? 0).toDouble();
              if (state.data?.chef != null &&
                  state.data!.chef!.chefOperationDays != null &&
                  state.data!.chef!.chefOperationDays!.isNotEmpty) {
                chefData = state.data?.chef;
              }
              currentAddress = state.data?.currentAddress?.addressText;
              if (state.data?.customerTimePreference != null) {
                final startTime = _formatTimeToAmPm(
                    state.data!.customerTimePreference!.startTime);
                final endTime = _formatTimeToAmPm(
                    state.data!.customerTimePreference!.endTime);
                customerTimePreference = '$startTime-$endTime';
              } else {
                customerTimePreference = null;
              }
              _updatingQuantity.clear();
            });
          } else if (state is RemoveCartItemSuccess) {
            context.read<AccountBloc>().add(GetCartCountEvent());
            setState(() {
              _updatingQuantity.clear();
            });
          } else if (state is UpdateCartItemQuantitySuccess) {
            setState(() {
              _updatingQuantity.clear();
            });
          } else if (state is UpdateCartItemQuantityLoading) {
            setState(() {
              isCartUpdatingQuantity = true;
            });
          } else if (state is UpdateCartItemQuantitySuccess ||
              state is UpdateCartItemQuantityFailed) {
            setState(() {
              isCartUpdatingQuantity = false;
              _updatingQuantity.clear();
            });
          }
        },
        child: BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is ListTimingSuccess) {
              setState(() {
                _availableTimings = state.data.data?.timings;
              });
            } else if (state is GettingAddedTimePreferencesLoading) {
              setState(() {
                isTimePreferenceLoading = true;
              });
            } else if (state is GettingAddedTimePreferencesSuccess) {
              setState(() {
                isTimePreferenceLoading = false;
                if (state.data != null) {
                  if (state.data.isDeliverNow == true) {
                    customerTimePreference = null;
                  } else if (state.data.timePreference != null) {
                    final startTime =
                        _formatTimeToAmPm(state.data.timePreference!.startTime);
                    final endTime =
                        _formatTimeToAmPm(state.data.timePreference!.endTime);
                    customerTimePreference = '$startTime-$endTime';
                  } else {
                    customerTimePreference = null;
                  }
                } else {
                  customerTimePreference = null;
                }
              });
            } else if (state is AddTimePreferencesSuccess) {
              context.read<MealplanBloc>().add(GetAddedTimePreferences());
            }
          },
          child: BlocBuilder<AccountBloc, AccountState>(
            builder: (context, state) {
              if (state is ViewCartLoading) {
                return Scaffold(
                  backgroundColor: Colors.white,
                  appBar: AppBar(
                    backgroundColor: Colors.white,
                    elevation: 0,
                    leading: IconButton(
                      icon: const Icon(
                        Icons.arrow_back,
                        color: Colors.black,
                      ),
                      onPressed: () {
                        Navigator.of(context).pop(true);
                      },
                    ),
                    centerTitle: false,
                    title: Text(
                      'Back',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w600,
                        fontSize: isLargeScreen ? 20 : screenSize.width * 0.041,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  body: const SafeArea(
                    child: Center(
                      child: CupertinoActivityIndicator(
                        radius: 10,
                      ),
                    ),
                  ),
                );
              }

              return Scaffold(
                backgroundColor: Colors.white,
                appBar: AppBar(
                  backgroundColor: Colors.white,
                  elevation: 0,
                  leading: IconButton(
                    icon: Icon(
                      Icons.arrow_back,
                      color: Colors.black,
                      size: isLargeScreen ? 32 : screenSize.width * 0.06,
                    ),
                    onPressed: () {
                      Navigator.of(context).pop(true);
                    },
                  ),
                  centerTitle: false,
                  title: Text(
                    'Back',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: isLargeScreen ? 20 : screenSize.width * 0.041,
                      color: Colors.black,
                    ),
                  ),
                  actions: [
                    if (cartItems?.isNotEmpty ?? false)
                      Padding(
                        padding:
                            EdgeInsets.only(right: screenSize.width * 0.04),
                        child: GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ViewChef2(
                                  id: widget.chef_id,
                                  title: chefData?.chefName ?? '',
                                ),
                              ),
                            ).then((_) {
                              context
                                  .read<AccountBloc>()
                                  .add(ViewCartEvent(widget.chef_id));
                            });
                          },
                          child: Row(
                            children: [
                              Icon(
                                Icons.add,
                                size: isLargeScreen
                                    ? 16
                                    : screenSize.width * 0.035,
                                color: const Color(0xFF414346),
                              ),
                              SizedBox(width: screenSize.width * 0.01),
                              IntrinsicWidth(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      'Add Dish',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w600,
                                        fontSize: isLargeScreen
                                            ? 18
                                            : screenSize.width * 0.04,
                                        color: Color(0xff414346),
                                        decoration: TextDecoration.none,
                                      ),
                                    ),
                                    SizedBox(height: 1),
                                    Container(
                                      height: 1,
                                      width: double.infinity,
                                      color: Color(0xff414346),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
                body: SafeArea(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (cartItems?.isNotEmpty ?? false) ...[
                        SizedBox(height: screenSize.height * 0.02),
                        _buildChefInfo(screenSize, isLargeScreen),
                        SizedBox(height: screenSize.height * 0.017),
                        _buildDeliveryInfo(screenSize, isLargeScreen),
                        SizedBox(height: screenSize.height * 0.017),
                      ],
                      Expanded(
                        child: Column(
                          children: [
                            Expanded(
                              child: (cartItems?.isEmpty ?? true)
                                  ? Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.shopping_cart_outlined,
                                            size: isLargeScreen
                                                ? 80
                                                : screenSize.width * 0.2,
                                            color: Colors.grey.shade400,
                                          ),
                                          SizedBox(
                                              height: screenSize.height * 0.02),
                                          Text(
                                            'No items in cart',
                                            style: TextStyle(
                                              fontSize: isLargeScreen
                                                  ? 18
                                                  : screenSize.width * 0.045,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                          SizedBox(
                                              height: screenSize.height * 0.01),
                                          Text(
                                            'Add items to your cart',
                                            style: TextStyle(
                                              fontSize: isLargeScreen
                                                  ? 16
                                                  : screenSize.width * 0.04,
                                              fontWeight: FontWeight.w400,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                          SizedBox(
                                              height: screenSize.height * 0.01),
                                          TextButton(
                                            onPressed: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      ViewChef2(
                                                    id: widget.chef_id,
                                                    title: chefData?.chefName ??
                                                        '',
                                                  ),
                                                ),
                                              ).then((_) {
                                                context.read<AccountBloc>().add(
                                                    ViewCartEvent(
                                                        widget.chef_id));
                                              });
                                            },
                                            child: Text(
                                              'Add Items',
                                              style: TextStyle(
                                                color: const Color.fromARGB(
                                                    255, 10, 10, 10),
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  : ListView.builder(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: screenSize.width * 0.04),
                                      itemCount: (cartItems?.length ?? 0) + 1,
                                      itemBuilder: (context, index) {
                                        if (index == cartItems?.length) {
                                          return Container(
                                            padding: EdgeInsets.symmetric(
                                                vertical:
                                                    screenSize.height * 0.012),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  'Subtotal',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: isLargeScreen
                                                        ? 18
                                                        : screenSize.width *
                                                            0.04,
                                                  ),
                                                ),
                                                Text(
                                                  '\$${subtotal.toStringAsFixed(2)}',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w600,
                                                    fontSize: isLargeScreen
                                                        ? 18
                                                        : screenSize.width *
                                                            0.04,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        }

                                        final item = cartItems![index];
                                        return _buildCartItemRow(
                                          item,
                                          screenSize,
                                          isLargeScreen,
                                          isCartUpdatingQuantity:
                                              isCartUpdatingQuantity,
                                        );
                                      },
                                    ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.fromLTRB(
                          screenSize.width * 0.05,
                          screenSize.height * 0.025,
                          screenSize.width * 0.05,
                          screenSize.height * 0.06,
                        ),
                        child: InkWell(
                          onTap: (cartItems?.isEmpty ?? true) ||
                                  isTimePreferenceLoading ||
                                  customerTimePreference == null
                              ? null
                              : () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => CartCheckoutPage(
                                        chef_id: widget.chef_id,
                                        subtotal: subtotal,
                                        selectedChefsWithDetails: {
                                          'chef': chefData,
                                          'items': cartItems,
                                          'totalPrice': subtotal,
                                          'currentAddress': currentAddress,
                                          'chefPhoto': chefData?.chefPhoto,
                                          'chefName': chefData?.chefName,
                                        },
                                      ),
                                    ),
                                  ).then((_) {
                                    context
                                        .read<AccountBloc>()
                                        .add(ViewCartEvent(widget.chef_id));
                                  });
                                },
                          child: Container(
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(
                                vertical: screenSize.height * 0.017),
                            decoration: BoxDecoration(
                              color: (cartItems?.isEmpty ?? true) ||
                                      isTimePreferenceLoading ||
                                      customerTimePreference == null
                                  ? Colors.grey.shade300
                                  : Colors.black,
                              border:
                                  Border.all(color: const Color(0xFFAAADB1)),
                              borderRadius: BorderRadius.circular(30),
                            ),
                            child: Center(
                              child: Text(
                                'Checkout',
                                style: TextStyle(
                                  color: (cartItems?.isEmpty ?? true) ||
                                          isTimePreferenceLoading ||
                                          customerTimePreference == null
                                      ? Colors.grey.shade600
                                      : Colors.white,
                                  fontSize: isLargeScreen
                                      ? 18
                                      : screenSize.width * 0.04,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildChefInfo(Size screenSize, bool isLargeScreen) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.04),
      child: Row(
        children: [
          CircleAvatar(
            radius: isLargeScreen ? 30 : screenSize.width * 0.06,
            backgroundImage: NetworkImage(
                ServerHelper.imageUrl + (chefData?.chefPhoto ?? '')),
            backgroundColor: Colors.transparent,
          ),
          SizedBox(width: screenSize.width * 0.03),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                chefData?.chefName ?? '',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: isLargeScreen ? 20 : screenSize.width * 0.045,
                ),
              ),
              SizedBox(height: screenSize.height * 0.007),
              Row(
                children: [
                  Image.asset(
                    'assets/icons/calender_3.png',
                    width: isLargeScreen ? 14 : screenSize.width * 0.03,
                    height: isLargeScreen ? 14 : screenSize.width * 0.03,
                  ),
                  SizedBox(width: screenSize.width * 0.01),
                  Text(
                    'Open, ${_formatTimeToAmPm(chefData?.chefOperationTime?.startTime)}-${_formatTimeToAmPm(chefData?.chefOperationTime?.endTime)}, ',
                    style: TextStyle(
                      fontSize: isLargeScreen ? 12 : screenSize.width * 0.025,
                      color: const Color(0xFF414346),
                    ),
                  ),
                  Text(
                    _formatDays(chefData?.chefOperationDays),
                    style: TextStyle(
                      fontSize: isLargeScreen ? 12 : screenSize.width * 0.025,
                      color: const Color(0xFF414346),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCartItemRow(
    Items item,
    Size screenSize,
    bool isLargeScreen, {
    bool isCartUpdatingQuantity = false,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: screenSize.height * 0.01),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFE1E3E6)),
          borderRadius: BorderRadius.circular(8),
        ),
        padding: EdgeInsets.all(screenSize.width * 0.03),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                ServerHelper.imageUrl + (item.dishPhoto ?? ''),
                width: isLargeScreen ? 56 : screenSize.width * 0.12,
                height: isLargeScreen ? 56 : screenSize.width * 0.12,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: isLargeScreen ? 56 : screenSize.width * 0.12,
                    height: isLargeScreen ? 56 : screenSize.width * 0.12,
                    color: Colors.grey.shade300,
                    child: const Icon(Icons.restaurant),
                  );
                },
              ),
            ),
            SizedBox(width: screenSize.width * 0.04),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.dishName ?? '',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                    ),
                  ),
                  SizedBox(height: screenSize.height * 0.005),
                  Row(
                    children: [
                      Text(
                        '\$${item.totalPrice?.toStringAsFixed(2) ?? '0.00'}',
                        style: TextStyle(
                          fontSize:
                              isLargeScreen ? 18 : screenSize.width * 0.037,
                        ),
                      ),
                      SizedBox(width: screenSize.width * 0.02),
                      Padding(
                        padding:
                            EdgeInsets.only(right: screenSize.width * 0.01),
                        child: Container(
                          decoration: BoxDecoration(
                            color: const Color(0xFFE1E3E6),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          padding: EdgeInsets.symmetric(
                            horizontal:
                                isLargeScreen ? 8 : screenSize.width * 0.015,
                            vertical:
                                isLargeScreen ? 2 : screenSize.width * 0.005,
                          ),
                          child: Text(
                            '${item.servingSize?.title}',
                            style: TextStyle(
                              fontSize:
                                  isLargeScreen ? 12 : screenSize.width * 0.025,
                              color: const Color(0xFF1F2122),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Row(
              children: [
                _buildQuantityButton(
                  Icons.remove,
                  () {
                    if (isCartUpdatingQuantity) return;
                    setState(() {
                      _updatingQuantity[item.cartItemId!] = 'remove';
                    });
                    if (item.quantity! <= 1) {
                      setState(() {
                        cartItems?.removeWhere((cartItem) =>
                            cartItem.cartItemId == item.cartItemId);
                        if (cartItems != null) {
                          subtotal = cartItems!.fold(
                              0.0,
                              (sum, cartItem) =>
                                  sum +
                                  ((cartItem.price ?? 0) *
                                      (cartItem.quantity ?? 0)));
                        } else {
                          subtotal = 0.0;
                        }
                        _updatingQuantity.remove(item.cartItemId!);
                      });
                      context.read<AccountBloc>().add(
                            RemoveCartItemEvent({
                              "dish_id": item.dishId,
                              "serving_size_id": item.servingSize?.id,
                            }),
                          );
                    } else {
                      _updateQuantity(
                          item.cartItemId!, item.quantity! - 1, false);
                    }
                  },
                  isDelete: item.quantity == 1,
                  cartItemId: item.cartItemId!,
                  screenSize: screenSize,
                  isLargeScreen: isLargeScreen,
                  isDisabled: isCartUpdatingQuantity,
                ),
                SizedBox(width: screenSize.width * 0.03),
                Text(
                  '${item.quantity ?? 0}',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                  ),
                ),
                SizedBox(width: screenSize.width * 0.03),
                _buildQuantityButton(
                  Icons.add,
                  () {
                    if (isCartUpdatingQuantity) return;
                    setState(() {
                      _updatingQuantity[item.cartItemId!] = 'add';
                    });
                    _updateQuantity(item.cartItemId!, item.quantity! + 1, true);
                  },
                  cartItemId: item.cartItemId!,
                  screenSize: screenSize,
                  isLargeScreen: isLargeScreen,
                  isDisabled: isCartUpdatingQuantity,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuantityButton(
    IconData icon,
    VoidCallback onPressed, {
    bool isDelete = false,
    required int cartItemId,
    required Size screenSize,
    required bool isLargeScreen,
    bool isDisabled = false,
  }) {
    bool isLoadingAdd = _updatingQuantity[cartItemId] == 'add';
    bool isLoadingRemove = _updatingQuantity[cartItemId] == 'remove';
    bool isThisButtonLoading = (icon == Icons.add && isLoadingAdd) ||
        ((icon == Icons.remove || isDelete) && isLoadingRemove);

    if (isDisabled) {
      return SizedBox(
        width: isLargeScreen ? 28 : screenSize.width * 0.06,
        height: isLargeScreen ? 28 : screenSize.width * 0.06,
        child: Center(
          child: Icon(
            isDelete ? Icons.delete_outline : icon,
            size: isLargeScreen ? 18 : screenSize.width * 0.04,
            color: Colors.grey.shade400,
          ),
        ),
      );
    }

    if (isThisButtonLoading) {
      return SizedBox(
        width: isLargeScreen ? 28 : screenSize.width * 0.06,
        height: isLargeScreen ? 28 : screenSize.width * 0.06,
        child: Center(
          child: SizedBox(
            width: isLargeScreen ? 20 : screenSize.width * 0.04,
            height: isLargeScreen ? 20 : screenSize.width * 0.04,
            child: const CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
            ),
          ),
        ),
      );
    }

    return InkWell(
      onTap: onPressed,
      child: Container(
        width: isLargeScreen ? 28 : screenSize.width * 0.06,
        height: isLargeScreen ? 28 : screenSize.width * 0.06,
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFE1E3E6), width: 1),
          shape: BoxShape.circle,
        ),
        child: Icon(
          isDelete ? Icons.delete_outline : icon,
          size: isLargeScreen ? 18 : screenSize.width * 0.04,
          color: isDelete ? Colors.red : const Color(0xFF414346),
        ),
      ),
    );
  }

  void _updateQuantity(int cartItemId, int newQuantity, bool isIncrement) {
    if (!mounted) return;
    final itemIndex =
        cartItems?.indexWhere((item) => item.cartItemId == cartItemId);
    if (itemIndex != null && itemIndex >= 0 && cartItems != null) {
      setState(() {
        cartItems![itemIndex].quantity = newQuantity;
        subtotal = cartItems!.fold(0.0,
            (sum, item) => sum + ((item.price ?? 0) * (item.quantity ?? 0)));
        _updatingQuantity.remove(cartItemId);
      });
      context.read<AccountBloc>().add(
            UpdateCartItemQuantityEvent(cartItemId, newQuantity),
          );
    }
  }

  Widget _buildDeliveryInfo(Size screenSize, bool isLargeScreen) {
    return BlocListener<MealplanBloc, MealPlanState>(
      listener: (context, state) {
        if (state is ListTimingSuccess) {
          setState(() {
            _availableTimings = state.data.data?.timings;
          });
        } else if (state is AddTimePreferencesSuccess) {
          context.read<MealplanBloc>().add(GetAddedTimePreferences());
        }
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Deliver to',
              style: TextStyle(
                fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: screenSize.height * 0.005),
            Row(
              children: [
                Image.asset(
                  'assets/icons/location.png',
                  width: isLargeScreen ? 18 : screenSize.width * 0.04,
                  height: isLargeScreen ? 18 : screenSize.width * 0.04,
                ),
                SizedBox(width: screenSize.width * 0.01),
                Expanded(
                  child: Text(
                    currentAddress ?? '_ _ _',
                    style: TextStyle(
                      fontSize: isLargeScreen ? 16 : screenSize.width * 0.033,
                    ),
                  ),
                ),
                Container(
                  width: 4,
                  height: 4,
                  margin:
                      EdgeInsets.symmetric(horizontal: screenSize.width * 0.02),
                  decoration: const BoxDecoration(
                    color: Colors.black,
                    shape: BoxShape.circle,
                  ),
                ),
                Image.asset(
                  'assets/icons/timer.png',
                  width: isLargeScreen ? 18 : screenSize.width * 0.04,
                  height: isLargeScreen ? 18 : screenSize.width * 0.04,
                ),
                SizedBox(width: screenSize.width * 0.01),
                GestureDetector(
                  onTap: () => _showTimePreferenceModal(
                      context, screenSize, isLargeScreen),
                  child: Row(
                    children: [
                      isTimePreferenceLoading
                          ? SizedBox(
                              width:
                                  isLargeScreen ? 16 : screenSize.width * 0.033,
                              height:
                                  isLargeScreen ? 16 : screenSize.width * 0.033,
                              child: const CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.black),
                              ),
                            )
                          : Text(
                              customerTimePreference ?? 'Select time',
                              style: TextStyle(
                                fontSize: isLargeScreen
                                    ? 16
                                    : screenSize.width * 0.033,
                                color: customerTimePreference == null
                                    ? Colors.grey.shade600
                                    : Colors.black,
                              ),
                            ),
                      SizedBox(width: screenSize.width * 0.01),
                      Image.asset(
                        'assets/icons/up.png',
                        width: isLargeScreen ? 18 : screenSize.width * 0.04,
                        height: isLargeScreen ? 18 : screenSize.width * 0.04,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showTimePreferenceModal(
      BuildContext context, Size screenSize, bool isLargeScreen) {
    context.read<MealplanBloc>().add(ListTimingEvent());
    context.read<MealplanBloc>().add(GetAddedTimePreferences());

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
            top: Radius.circular(screenSize.width * 0.04)),
      ),
      builder: (modalContext) => MultiBlocProvider(
        providers: [
          BlocProvider.value(value: context.read<MealplanBloc>()),
        ],
        child: BlocConsumer<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is ListTimingSuccess) {
              setState(() {
                _availableTimings = state.data.data?.timings;
              });
            } else if (state is GettingAddedTimePreferencesLoading) {
              setState(() {
                isTimePreferenceLoading = true;
              });
            } else if (state is GettingAddedTimePreferencesSuccess) {
              setState(() {
                isTimePreferenceLoading = false;
                if (state.data != null) {
                  if (state.data.isDeliverNow == true) {
                    customerTimePreference = null;
                  } else if (state.data.timePreference != null) {
                    final startTime =
                        _formatTimeToAmPm(state.data.timePreference!.startTime);
                    final endTime =
                        _formatTimeToAmPm(state.data.timePreference!.endTime);
                    customerTimePreference = '$startTime-$endTime';
                  } else {
                    customerTimePreference = null;
                  }
                } else {
                  customerTimePreference = null;
                }
              });
            } else if (state is AddTimePreferencesSuccess) {
              context.read<MealplanBloc>().add(GetAddedTimePreferences());
            }
          },
          builder: (context, state) {
            if (state is ListTimingLoading) {
              return Container(
                height: 200,
                padding: EdgeInsets.all(screenSize.width * 0.04),
                child: const Center(child: CircularProgressIndicator()),
              );
            }

            return Container(
              padding: EdgeInsets.all(screenSize.width * 0.04),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Select Delivery Time',
                        style: TextStyle(
                          fontSize:
                              isLargeScreen ? 20 : screenSize.width * 0.05,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  SizedBox(height: screenSize.height * 0.02),
                  if (_availableTimings != null &&
                      _availableTimings!.isNotEmpty)
                    Flexible(
                      child: ListView.separated(
                        shrinkWrap: true,
                        itemCount: _availableTimings!.length + 1,
                        separatorBuilder: (context, index) => const Divider(),
                        itemBuilder: (context, index) {
                          if (index == 0) {
                            return ListTile(
                              onTap: () {
                                setState(() {
                                  customerTimePreference = null;
                                });
                                context.read<MealplanBloc>().add(
                                      AddTimePreferences({}),
                                    );
                                Navigator.pop(context);
                              },
                              title: const Text('Any time'),
                              trailing: customerTimePreference == null
                                  ? const Icon(Icons.check, color: Colors.black)
                                  : null,
                            );
                          }
                          final timing = _availableTimings![index - 1];
                          final timeSlot =
                              '${_formatTimeToAmPm(timing.startTime)}-${_formatTimeToAmPm(timing.endTime)}';
                          final isSlotPassed =
                              _isTimeSlotPassed(timing.endTime);

                          return ListTile(
                            onTap: isSlotPassed
                                ? null
                                : () {
                                    setState(() {
                                      customerTimePreference = timeSlot;
                                    });
                                    context.read<MealplanBloc>().add(
                                          AddTimePreferences({
                                            "time_preference_id": timing.id
                                          }),
                                        );
                                    Navigator.pop(context);
                                  },
                            title: Text(
                              isSlotPassed ? '$timeSlot (Past)' : timeSlot,
                              style: TextStyle(
                                color: isSlotPassed
                                    ? Colors.grey.shade400
                                    : Colors.black,
                              ),
                            ),
                            trailing: customerTimePreference == timeSlot
                                ? const Icon(Icons.check, color: Colors.black)
                                : null,
                            enabled: !isSlotPassed,
                          );
                        },
                      ),
                    )
                  else
                    Center(
                      child: Text(
                        'No time slots available',
                        style: TextStyle(
                          fontSize: screenSize.width * 0.04,
                        ),
                      ),
                    ),
                  if (customerTimePreference == null &&
                      _availableTimings != null &&
                      _availableTimings!.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: screenSize.height * 0.01),
                      child: Text(
                        'Please select a specific time slot for delivery.',
                        style: TextStyle(
                          fontSize:
                              isLargeScreen ? 14 : screenSize.width * 0.035,
                          color: Colors.red,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
