class ListSaversPlanModel {
  bool? status;
  String? message;
  ListSaversPlanData? data;
  int? statusCode;

  ListSaversPlanModel({this.status, this.message, this.data, this.statusCode});

  ListSaversPlanModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null
        ? new ListSaversPlanData.fromJson(json['data'])
        : null;
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['status_code'] = this.statusCode;
    return data;
  }
}

class ListSaversPlanData {
  List<Plans>? plans;
  SubscribedPlan? subscribedPlan;
  Pagination? pagination;

  ListSaversPlanData({this.plans, this.subscribedPlan, this.pagination});

  ListSaversPlanData.fromJson(Map<String, dynamic> json) {
    if (json['plans'] != null) {
      plans = <Plans>[];
      json['plans'].forEach((v) {
        plans!.add(new Plans.fromJson(v));
      });
    }
    subscribedPlan = json['subscribed_plan'] != null
        ? new SubscribedPlan.fromJson(json['subscribed_plan'])
        : null;
    pagination = json['pagination'] != null
        ? new Pagination.fromJson(json['pagination'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.plans != null) {
      data['plans'] = this.plans!.map((v) => v.toJson()).toList();
    }
    if (this.subscribedPlan != null) {
      data['subscribed_plan'] = this.subscribedPlan!.toJson();
    }
    if (this.pagination != null) {
      data['pagination'] = this.pagination!.toJson();
    }
    return data;
  }
}

class SubscribedPlan {
  int? id;
  int? customerId;
  int? saversPassId;
  String? planName;
  int? planType;
  String? description;
  String? price;
  String? freeDeliveryRadius;
  int? durationDays;
  bool? isFirstOrderDiscountEnabled;
  int? firstOrderDiscountLimit;
  String? firstOrderDiscountPercent;
  bool? isFirstMealplanDiscountEnabled;
  int? firstMealplanDiscountLimit;
  String? firstMealplanDiscountPercent;
  bool? isFirstCateringDiscountEnabled;
  int? firstCateringDiscountLimit;
  String? firstCateringDiscountPercent;
  bool? isServiceFeeDiscountEnabled;
  String? orderServiceFeeDiscountPercent;
  String? cateringServiceFeeDiscountPercent;
  String? mealplanServiceFeeDiscountPercent;
  String? subTotal;
  String? discount;
  String? walletCredits;
  String? taxPercentage;
  String? tax;
  String? total;
  String? startDate;
  String? endDate;
  String? totalSavedAmount;
  String? status;
  String? paymentStatus;
  String? createdAt;
  String? updatedAt;

  SubscribedPlan({
    this.id,
    this.customerId,
    this.saversPassId,
    this.planName,
    this.planType,
    this.description,
    this.price,
    this.freeDeliveryRadius,
    this.durationDays,
    this.isFirstOrderDiscountEnabled,
    this.firstOrderDiscountLimit,
    this.firstOrderDiscountPercent,
    this.isFirstMealplanDiscountEnabled,
    this.firstMealplanDiscountLimit,
    this.firstMealplanDiscountPercent,
    this.isFirstCateringDiscountEnabled,
    this.firstCateringDiscountLimit,
    this.firstCateringDiscountPercent,
    this.isServiceFeeDiscountEnabled,
    this.orderServiceFeeDiscountPercent,
    this.cateringServiceFeeDiscountPercent,
    this.mealplanServiceFeeDiscountPercent,
    this.subTotal,
    this.discount,
    this.walletCredits,
    this.taxPercentage,
    this.tax,
    this.total,
    this.startDate,
    this.endDate,
    this.totalSavedAmount,
    this.status,
    this.paymentStatus,
    this.createdAt,
    this.updatedAt,
  });

  SubscribedPlan.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    customerId = json['customer_id'];
    saversPassId = json['savers_pass_id'];
    planName = json['plan_name'];
    planType = json['plan_type'];
    description = json['description'];
    price = json['price'];
    freeDeliveryRadius = json['free_delivery_radius'];
    durationDays = json['duration_days'];
    isFirstOrderDiscountEnabled = json['is_first_order_discount_enabled'];
    firstOrderDiscountLimit = json['first_order_discount_limit'];
    firstOrderDiscountPercent = json['first_order_discount_percent'];
    isFirstMealplanDiscountEnabled = json['is_first_mealplan_discount_enabled'];
    firstMealplanDiscountLimit = json['first_mealplan_discount_limit'];
    firstMealplanDiscountPercent = json['first_mealplan_discount_percent'];
    isFirstCateringDiscountEnabled = json['is_first_catering_discount_enabled'];
    firstCateringDiscountLimit = json['first_catering_discount_limit'];
    firstCateringDiscountPercent = json['first_catering_discount_percent'];
    isServiceFeeDiscountEnabled = json['is_service_fee_discount_enabled'];
    orderServiceFeeDiscountPercent = json['order_service_fee_discount_percent'];
    cateringServiceFeeDiscountPercent = json['catering_service_fee_discount_percent'];
    mealplanServiceFeeDiscountPercent = json['mealplan_service_fee_discount_percent'];
    subTotal = json['sub_total'];
    discount = json['discount'];
    walletCredits = json['wallet_credits'];
    taxPercentage = json['tax_percentage'];
    tax = json['tax'];
    total = json['total'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    totalSavedAmount = json['total_saved_amount'];
    status = json['status'];
    paymentStatus = json['payment_status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['customer_id'] = this.customerId;
    data['savers_pass_id'] = this.saversPassId;
    data['plan_name'] = this.planName;
    data['plan_type'] = this.planType;
    data['description'] = this.description;
    data['price'] = this.price;
    data['free_delivery_radius'] = this.freeDeliveryRadius;
    data['duration_days'] = this.durationDays;
    data['is_first_order_discount_enabled'] = this.isFirstOrderDiscountEnabled;
    data['first_order_discount_limit'] = this.firstOrderDiscountLimit;
    data['first_order_discount_percent'] = this.firstOrderDiscountPercent;
    data['is_first_mealplan_discount_enabled'] = this.isFirstMealplanDiscountEnabled;
    data['first_mealplan_discount_limit'] = this.firstMealplanDiscountLimit;
    data['first_mealplan_discount_percent'] = this.firstMealplanDiscountPercent;
    data['is_first_catering_discount_enabled'] = this.isFirstCateringDiscountEnabled;
    data['first_catering_discount_limit'] = this.firstCateringDiscountLimit;
    data['first_catering_discount_percent'] = this.firstCateringDiscountPercent;
    data['is_service_fee_discount_enabled'] = this.isServiceFeeDiscountEnabled;
    data['order_service_fee_discount_percent'] = this.orderServiceFeeDiscountPercent;
    data['catering_service_fee_discount_percent'] = this.cateringServiceFeeDiscountPercent;
    data['mealplan_service_fee_discount_percent'] = this.mealplanServiceFeeDiscountPercent;
    data['sub_total'] = this.subTotal;
    data['discount'] = this.discount;
    data['wallet_credits'] = this.walletCredits;
    data['tax_percentage'] = this.taxPercentage;
    data['tax'] = this.tax;
    data['total'] = this.total;
    data['start_date'] = this.startDate;
    data['end_date'] = this.endDate;
    data['total_saved_amount'] = this.totalSavedAmount;
    data['status'] = this.status;
    data['payment_status'] = this.paymentStatus;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}

class Plans {
  int? id;
  int? addedById;
  String? addedByType;
  String? planName;
  int? planType;
  String? description;
  String? price;
  String? freeDeliveryRadius;
  int? durationDays;
  bool? isFirstOrderDiscountEnabled;
  int? firstOrderDiscountLimit;
  String? firstOrderDiscountPercent;
  bool? isFirstMealplanDiscountEnabled;
  int? firstMealplanDiscountLimit;
  String? firstMealplanDiscountPercent;
  bool? isFirstCateringDiscountEnabled;
  int? firstCateringDiscountLimit;
  String? firstCateringDiscountPercent;
  bool? isServiceFeeDiscountEnabled;
  String? orderServiceFeeDiscountPercent;
  String? cateringServiceFeeDiscountPercent;
  String? mealplanServiceFeeDiscountPercent;
  String? status;
  String? createdAt;
  String? updatedAt;

  Plans({
    this.id,
    this.addedById,
    this.addedByType,
    this.planName,
    this.planType,
    this.description,
    this.price,
    this.freeDeliveryRadius,
    this.durationDays,
    this.isFirstOrderDiscountEnabled,
    this.firstOrderDiscountLimit,
    this.firstOrderDiscountPercent,
    this.isFirstMealplanDiscountEnabled,
    this.firstMealplanDiscountLimit,
    this.firstMealplanDiscountPercent,
    this.isFirstCateringDiscountEnabled,
    this.firstCateringDiscountLimit,
    this.firstCateringDiscountPercent,
    this.isServiceFeeDiscountEnabled,
    this.orderServiceFeeDiscountPercent,
    this.cateringServiceFeeDiscountPercent,
    this.mealplanServiceFeeDiscountPercent,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  Plans.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    addedById = json['added_by_id'];
    addedByType = json['added_by_type'];
    planName = json['plan_name'];
    planType = json['plan_type'];
    description = json['description'];
    price = json['price'];
    freeDeliveryRadius = json['free_delivery_radius'];
    durationDays = json['duration_days'];
    isFirstOrderDiscountEnabled = json['is_first_order_discount_enabled'];
    firstOrderDiscountLimit = json['first_order_discount_limit'];
    firstOrderDiscountPercent = json['first_order_discount_percent'];
    isFirstMealplanDiscountEnabled = json['is_first_mealplan_discount_enabled'];
    firstMealplanDiscountLimit = json['first_mealplan_discount_limit'];
    firstMealplanDiscountPercent = json['first_mealplan_discount_percent'];
    isFirstCateringDiscountEnabled = json['is_first_catering_discount_enabled'];
    firstCateringDiscountLimit = json['first_catering_discount_limit'];
    firstCateringDiscountPercent = json['first_catering_discount_percent'];
    isServiceFeeDiscountEnabled = json['is_service_fee_discount_enabled'];
    orderServiceFeeDiscountPercent = json['order_service_fee_discount_percent'];
    cateringServiceFeeDiscountPercent = json['catering_service_fee_discount_percent'];
    mealplanServiceFeeDiscountPercent = json['mealplan_service_fee_discount_percent'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['added_by_id'] = this.addedById;
    data['added_by_type'] = this.addedByType;
    data['plan_name'] = this.planName;
    data['plan_type'] = this.planType;
    data['description'] = this.description;
    data['price'] = this.price;
    data['free_delivery_radius'] = this.freeDeliveryRadius;
    data['duration_days'] = this.durationDays;
    data['is_first_order_discount_enabled'] = this.isFirstOrderDiscountEnabled;
    data['first_order_discount_limit'] = this.firstOrderDiscountLimit;
    data['first_order_discount_percent'] = this.firstOrderDiscountPercent;
    data['is_first_mealplan_discount_enabled'] = this.isFirstMealplanDiscountEnabled;
    data['first_mealplan_discount_limit'] = this.firstMealplanDiscountLimit;
    data['first_mealplan_discount_percent'] = this.firstMealplanDiscountPercent;
    data['is_first_catering_discount_enabled'] = this.isFirstCateringDiscountEnabled;
    data['first_catering_discount_limit'] = this.firstCateringDiscountLimit;
    data['first_catering_discount_percent'] = this.firstCateringDiscountPercent;
    data['is_service_fee_discount_enabled'] = this.isServiceFeeDiscountEnabled;
    data['order_service_fee_discount_percent'] = this.orderServiceFeeDiscountPercent;
    data['catering_service_fee_discount_percent'] = this.cateringServiceFeeDiscountPercent;
    data['mealplan_service_fee_discount_percent'] = this.mealplanServiceFeeDiscountPercent;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}

class Pagination {
  int? totalLength;
  int? page;
  int? limit;

  Pagination({this.totalLength, this.page, this.limit});

  Pagination.fromJson(Map<String, dynamic> json) {
    totalLength = json['totalLength'];
    page = json['page'];
    limit = json['limit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['totalLength'] = this.totalLength;
    data['page'] = this.page;
    data['limit'] = this.limit;
    return data;
  }
}