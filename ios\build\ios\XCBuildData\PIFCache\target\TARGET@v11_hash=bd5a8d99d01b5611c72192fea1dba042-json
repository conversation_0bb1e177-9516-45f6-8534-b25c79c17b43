{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a1299aa917988dde2066b8a92f10efbd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a337c41bd4dde29e72c4fe940357237c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983faeac33dc3e6aa7f75eacf7a5b1c7c7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984c3790424152e8aaa1d7e7c7a9abd885", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983faeac33dc3e6aa7f75eacf7a5b1c7c7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982503162453eb5ff56dad05b1b69cc1ce", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9831541c66897e10d2f2c70ac716af5915", "guid": "bfdfe7dc352907fc980b868725387e98790d4cd6b43f29018bdb1e5282c3831a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc3b8c75c4c46793edc485415f8c4ff9", "guid": "bfdfe7dc352907fc980b868725387e98ae7f269a9d0f69af76102b104e728b25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cb55ac03285ba39b94c9fd7030cbc98", "guid": "bfdfe7dc352907fc980b868725387e9854a2064f0e6df1af113cdc04796527eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6d4e378d399f2a63dbe5772f1da1029", "guid": "bfdfe7dc352907fc980b868725387e987ea9c5f8762416322eb890920c16a116", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892ef6e7a7ab2e5b852ed763300806428", "guid": "bfdfe7dc352907fc980b868725387e98427f014da32712ee9fe3b9accdea1de6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986182de47dace08ae726091120cb9ed07", "guid": "bfdfe7dc352907fc980b868725387e98fef1c899cb33e1c5be5d9ed5cf933fac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d3f0c7c277e2a5b5c78607d19c7e666", "guid": "bfdfe7dc352907fc980b868725387e98dbd933cab3bdd73d42b84722bfcf934a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876516fc09d87de216139abdebba1d39c", "guid": "bfdfe7dc352907fc980b868725387e98ce4980bc077ef0d0a259d5c8e06db3f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e81533698895f0dd52f161d6ba1cf10d", "guid": "bfdfe7dc352907fc980b868725387e98b6f7271867321de7af6adcd05956a85c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879de865eb7c0849ded1c42d84f37d2c4", "guid": "bfdfe7dc352907fc980b868725387e98f5d005c0ed7bf72127b075101bbb4892", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0c1a3113764775f7cc2e84a21240838", "guid": "bfdfe7dc352907fc980b868725387e9879a684661b77288d86b87649835c1422", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3be412b3ab9b4f40c934b678c77dddc", "guid": "bfdfe7dc352907fc980b868725387e989b3b37868386d6cb888bb996cbe42ff0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0b44f7a49bc49ca4b4c965c963c88c0", "guid": "bfdfe7dc352907fc980b868725387e9801ff409006389f1c57b6b6bc5416cfc5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98952807383b9748e5c6f08421e09ea6b8", "guid": "bfdfe7dc352907fc980b868725387e9832c7ac4367c0353068727efbb41385e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802b2911fe016a96ef34c446c485ae062", "guid": "bfdfe7dc352907fc980b868725387e984678ceeafe51d3a2c6771149dbcd29e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802352500155ad1a6df48419991d1c0a2", "guid": "bfdfe7dc352907fc980b868725387e984f06f505c0b5e945b179410e0a412a26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc31b0c84bc8432cf67f312fdbf2d38e", "guid": "bfdfe7dc352907fc980b868725387e985746fa78eb273c384cc846e5a92e4bab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f794d014e8f95ab9def66d6aa1a017c7", "guid": "bfdfe7dc352907fc980b868725387e98b49d4040dd1527c58fe87087c3d045af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6e0d56647d577ea006dca7f266314ad", "guid": "bfdfe7dc352907fc980b868725387e98bff6335b493a330d03ec051ae2a39429", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b664aae7a6e5d4240ee74c87d77278a0", "guid": "bfdfe7dc352907fc980b868725387e98939a4ae126c97e8434d2b17b8d2f6432", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c99e90576dd77afebbeeca4f6f6361c2", "guid": "bfdfe7dc352907fc980b868725387e987ed77c3f15d514c8f3d92ba8a80245ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f866e1db60ad0daf7a6e1eca6be7a31f", "guid": "bfdfe7dc352907fc980b868725387e986a61c289fe9020498cc0147f32189345"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc24cf5a474584508027ecd28a04b6c3", "guid": "bfdfe7dc352907fc980b868725387e984a600935445fdd392552c6560d1c2627", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a73b3b87c475d571aea4d162c3f4c70", "guid": "bfdfe7dc352907fc980b868725387e9880eb575ebd10e65542a8228597ddf02a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f68b445fcf234fc73cbb8f1ad1dd26bf", "guid": "bfdfe7dc352907fc980b868725387e98486b03fde23ce801fb4c8430e6fb0902", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865e1d8cb6829ba42cc3d8c5aee128177", "guid": "bfdfe7dc352907fc980b868725387e98e5c8cc01866ff8e136172fd39ccacc5a"}], "guid": "bfdfe7dc352907fc980b868725387e9838842df756c5172ab6feaef3af7b84af", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fc55a9378f26d41878bc7a97c3cbd94d", "guid": "bfdfe7dc352907fc980b868725387e983abe298006d1c69ad14cbf1dd7fe995f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca657d0582eea6fa50501087f76c878e", "guid": "bfdfe7dc352907fc980b868725387e980c8069a8b03ec2e719ab3229b8608b1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da416ca7b2f64364f66d46815ffd2e52", "guid": "bfdfe7dc352907fc980b868725387e982b39e5502d375813f1ec1d70bdf25439"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c30c410829e4a49173291db550f7afda", "guid": "bfdfe7dc352907fc980b868725387e9848a63b528654a2899c8ca3b808c32f5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3214b09af20cacd837c367c7e5e217d", "guid": "bfdfe7dc352907fc980b868725387e982111df887e97c71212cac18599501354"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ca1a76a08ef10b618af84902958319b", "guid": "bfdfe7dc352907fc980b868725387e98c95efa3449e0417bf1eed91b755a319f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a2bb7e7a2930d6c351d52e983f568ec", "guid": "bfdfe7dc352907fc980b868725387e988e528b84f59cce688f87890418b3d49f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837cde83227a982d2e812af35447e6f0f", "guid": "bfdfe7dc352907fc980b868725387e9855c328bc0d92a0701de1ba531beb7d37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ac551213eb5080c29cc635179a4a352", "guid": "bfdfe7dc352907fc980b868725387e981627bfe49def1dc9bc6c15b8ac1cbdd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98269db2c28f910a22d67c526369ee5a92", "guid": "bfdfe7dc352907fc980b868725387e981446c3b97800f05eff7be222d3e5d992"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dc0531b67df3ce9377c62e97d3fcaad", "guid": "bfdfe7dc352907fc980b868725387e989bc994616c3117fa5130d0270209409c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ebff26aee3487f1570e9b8523014491", "guid": "bfdfe7dc352907fc980b868725387e98d7bbf677fbfad7083d1ab860e4b57e06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb611a3ab567266b3dab650f91161160", "guid": "bfdfe7dc352907fc980b868725387e9837dee0f83dd06f714c1638989e5412c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9a99295eff7fcfe529ee67a4d942723", "guid": "bfdfe7dc352907fc980b868725387e989e14d8e97b609d3e3c3b981d241cac8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac70ff5b61ebf2b5ba54b5e5889b9e1f", "guid": "bfdfe7dc352907fc980b868725387e98c099a1b7569795eeef38d034a1822445"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c18f69b063f29c26a863672696faf724", "guid": "bfdfe7dc352907fc980b868725387e98c9c73e31214c530a961cfa79bdb3273a"}], "guid": "bfdfe7dc352907fc980b868725387e981ae03202bd2f9ce861c24a13c67c68c4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98405c2a7b131127039ecae9a983514c42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e98e920f9a4e4c2729b2700745352c0f49b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e28c510bcdc3834514b09ab79a067e08", "guid": "bfdfe7dc352907fc980b868725387e9829f4d9b83d7d301c8ae1bfc395b1eb8d"}], "guid": "bfdfe7dc352907fc980b868725387e982fead2d4f46f81ad96bf3fd1c9538324", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9884756159f3da622f5976b833c3fc17fb", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98c13cb801d4780455ef41ede97bb7fc69", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}