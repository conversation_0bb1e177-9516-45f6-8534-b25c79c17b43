{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988a4ea4acf5aefca6da2778e13accec24", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e2277be28a487919925c019eadbc3d4d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a59ce6597694e4a750821e9739006558", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9837d6095d2edb1b19ee11d5bb60efcadb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a59ce6597694e4a750821e9739006558", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98266c1528ba49ac2bac63a560ee690878", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac7f42ee129229c9d4f08f7b219ce733", "guid": "bfdfe7dc352907fc980b868725387e98e266072598b9c98e52aea7bbded35295", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9800d50e50f4ef8e57b903ef311be596b5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a930d523350f891502197f238efaea05", "guid": "bfdfe7dc352907fc980b868725387e985e71767b2cf6bcbe4aa7e814e13620dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe1b2414a282ab15dabb76c2b1b81f8d", "guid": "bfdfe7dc352907fc980b868725387e9831525010bfb1b1e0c5d26fea61e477e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e7547dff40c765d3e44574c378d85aa", "guid": "bfdfe7dc352907fc980b868725387e98b57b5816d65fc28a088d58c468741c6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f52d3aea1104e78298517cb5083975ee", "guid": "bfdfe7dc352907fc980b868725387e988211440440856f50cbf26c7badc1b866"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882bbb778a74506aec8ad3c8abd669ccf", "guid": "bfdfe7dc352907fc980b868725387e9835e53fb2e560775f470c652d9b579a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fe1a1308c6cdd8b5ba3f9640355fe45", "guid": "bfdfe7dc352907fc980b868725387e986794b6c0939151035c2ab274f1711125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6f01817d5bebdd943ad022d161c44c0", "guid": "bfdfe7dc352907fc980b868725387e98f34a32fc4451160f848d719fc5f81f12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9d2bbcd79e20f8742f5ebab75003d4c", "guid": "bfdfe7dc352907fc980b868725387e988e64943a42cf12277e5c7e4f72f2bf24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98601c8a88e271521839e36c913c0683f8", "guid": "bfdfe7dc352907fc980b868725387e98a7f262082f40c1e9a308fa29c64e0cfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98047fd5106c8de8b85c925da4f8eac7f1", "guid": "bfdfe7dc352907fc980b868725387e98e83c626b280a1984826e0a8fec0e8118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98821bd9400a891492a174e00eb6e46a73", "guid": "bfdfe7dc352907fc980b868725387e98d22b444d6d153e8f3e7e449aefa370eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987753233ee7c43a1cf39ccf60a8c24b9e", "guid": "bfdfe7dc352907fc980b868725387e98089f83e5952fce913475cd95c38bd99c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e10eacd1654842f8be5dab26a4c53a95", "guid": "bfdfe7dc352907fc980b868725387e9890506ea18e7f21cc71daca82d4d212cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce2454eac627f6a1f59cf86592e66135", "guid": "bfdfe7dc352907fc980b868725387e98dea4509592aa0c6fabb9766f37c3e38e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf91796e2598b0d6d631abe185d68499", "guid": "bfdfe7dc352907fc980b868725387e987c4c617ae775f307055be664aec1c2ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de6063b2efdc2ca15d93ed0e4aa4125b", "guid": "bfdfe7dc352907fc980b868725387e984dc26de453d9cfa4e5eadaa36416751b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b423d7013a4f7cc60f02ac8b52bb2e6", "guid": "bfdfe7dc352907fc980b868725387e98ab2ce06f23ea3006eb332485697eb9c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fe0b28e853f8de63c941b7150766f02", "guid": "bfdfe7dc352907fc980b868725387e9845d840f476efcee1af7c183ac5d9af8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982896b0c65fb8be7643859b314873aab8", "guid": "bfdfe7dc352907fc980b868725387e98dce3ee724f424304a4d660f38a6944da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fa4e8e20d92f03c10c1ba73258e1c66", "guid": "bfdfe7dc352907fc980b868725387e98a73c04e13e52b41ee070d2a467c1e6f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988891b051b3a72cb0eecb88dab06bcad5", "guid": "bfdfe7dc352907fc980b868725387e9816762b1be109f65eabe0994f9ca493b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859919fb1423dbbfdebe5ac25d41ffaac", "guid": "bfdfe7dc352907fc980b868725387e98e8bd2430c067efd14e4ffb7200b3f64b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d92ece10b6cdf07319512356cbb8fbd0", "guid": "bfdfe7dc352907fc980b868725387e98718446df57e9895d602c6fcd3e141240"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803f9f3bf15427ff50bd4d7233145d11b", "guid": "bfdfe7dc352907fc980b868725387e98131857d7b303a6374fbc9fd721e9cfba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cb8e09ac50a0c491779f98c18819a89", "guid": "bfdfe7dc352907fc980b868725387e987dd8c291d9a9e17029c957748eafbb17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98791ca7cf97131f30421917b4237fa8fc", "guid": "bfdfe7dc352907fc980b868725387e98f3e08e667b79ef42c07eddba7c2a976b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a9bc0af46010cf09240ee6d3eca98b3", "guid": "bfdfe7dc352907fc980b868725387e98fcb311f8096cd0a656a7902a194258f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988aaf7173c61e683871e3afdd3b40783e", "guid": "bfdfe7dc352907fc980b868725387e9801de043157615b1222c171fbba127ae8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825a5a6bdc3c90b5866435fcc25c3cbd9", "guid": "bfdfe7dc352907fc980b868725387e98b729a65c86fa445602b60f2c0498ba91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b81356f8afc04928e60238447da4758a", "guid": "bfdfe7dc352907fc980b868725387e989622e82a87fdc3d8adc1917f6d0cfad9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a76f6bbfb4f418b597056e139018d5c6", "guid": "bfdfe7dc352907fc980b868725387e9811424d6f02786bae164d9359f3e22d68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd4992e67f262f5acec0c691aef0b0b5", "guid": "bfdfe7dc352907fc980b868725387e9871026481ccbfab3f1beb1d91210e6617"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f90206a17ac146a4dc25ed0e56a91c9", "guid": "bfdfe7dc352907fc980b868725387e98280087e742826f7b7aa41c9c25d2a697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98477f4208fcf014d852f96286fbc8e503", "guid": "bfdfe7dc352907fc980b868725387e9839d75e19d3f22529f017625e9384a1a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98836afe613d2bfbcb57f6198cae2bf0a3", "guid": "bfdfe7dc352907fc980b868725387e98a1d55e0acf299eded1f99b334bf3e857"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d65523b2ec49aa302cda56aa728b62aa", "guid": "bfdfe7dc352907fc980b868725387e98e2d4a1e3d1aa90bcc74e288af5e4ebb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7aed0575edb3e7793eeaace1cf15950", "guid": "bfdfe7dc352907fc980b868725387e98ab91b99606cee030e5afce8505e821a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff033dd6adcf5a52fa14523c12eff165", "guid": "bfdfe7dc352907fc980b868725387e98910412d9f5b863c249eefa42c85fd50b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985959ebddb87419658af342c176070964", "guid": "bfdfe7dc352907fc980b868725387e98467532b9e6540a51e9c961700d776523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d490835468d22e1c937c0ea8efe0b057", "guid": "bfdfe7dc352907fc980b868725387e9851245b6b9117d368fcfabcace1603a5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986aca2d5c4fe4c8ec511c1444ce07d3a4", "guid": "bfdfe7dc352907fc980b868725387e983c69a79206e5b87de2802ec2a2391d53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98249c863fac6c95b4132ddb7bc71998b4", "guid": "bfdfe7dc352907fc980b868725387e98187490859cb24e2d0e1921e651444b9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984745594fc553280ee36985ffd4844f46", "guid": "bfdfe7dc352907fc980b868725387e98c915c74d9f0a28227f77a2c0186bc091"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f594de370f976663dbc5e89156a17a03", "guid": "bfdfe7dc352907fc980b868725387e98d646c134a27df478370327c27db28a44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad1aa9c448615e94fc4721cb7ef2ac60", "guid": "bfdfe7dc352907fc980b868725387e98940ad400b820ce8802bee9b0489197e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98544c57f1d25b8a24180c9902ace86642", "guid": "bfdfe7dc352907fc980b868725387e98c407e37b00ccd454776e0c1afc59af12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b94a1023bcdd1d991bfdf67d3183465", "guid": "bfdfe7dc352907fc980b868725387e9868344afce35baf05114ccfd51ee8f325"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983059d57e5c0f88121a6f9ed52256b31c", "guid": "bfdfe7dc352907fc980b868725387e98bed9c58ac8bd697d31385454670d3f23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3f513a12cfa6a56b357051ab9065927", "guid": "bfdfe7dc352907fc980b868725387e98a8d3ed72e68273aa9b45af1482d0ab81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bd2d34da4c3b68da6c0f1df7ebc94c1", "guid": "bfdfe7dc352907fc980b868725387e9811c557859e61ed80dc9c2a1d4eba9969"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8d504fabf9e030a228eec0402702aeb", "guid": "bfdfe7dc352907fc980b868725387e982650e6f6a724d30b892cf5ae66b63637"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833332cdd5b541f60048927b07dc2164e", "guid": "bfdfe7dc352907fc980b868725387e988a819307a82baa0e057f01e2c5c8fda4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfa88ff8dd5056a9a4d844148acc4f3f", "guid": "bfdfe7dc352907fc980b868725387e98d1b72562bc1b600823e710486bfd29b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f699fb33a4910e065c2a6b63870024bc", "guid": "bfdfe7dc352907fc980b868725387e9819f99e8789ad2b451e63645768cb993b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fba8d82021364a8fb0057438e8de7030", "guid": "bfdfe7dc352907fc980b868725387e98cbd4ed178572905e5f79227502c26d9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f6b1265693cc51b7a8748686aa4a645", "guid": "bfdfe7dc352907fc980b868725387e98300021b9b9a4010277e262c7b1bdd899"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1892028bf8f5f553b965c6d5f527bda", "guid": "bfdfe7dc352907fc980b868725387e9840d414d6d90bca8c0a826d3e7f08c4b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b386e07dc1f767172e1a3aa599abf09", "guid": "bfdfe7dc352907fc980b868725387e9878d11d2e119bfc334ced087f3d45b874"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9c8431f414a741a5bd9947d4354bf19", "guid": "bfdfe7dc352907fc980b868725387e98d2c807268d32c38e3aaa6a836f7c4ef7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3fec798c25804fd17ac305999787b9d", "guid": "bfdfe7dc352907fc980b868725387e98549a86644f77b1feb8b2c94f79d3f194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b3559248901d1c3a7222fda9279eb07", "guid": "bfdfe7dc352907fc980b868725387e986f82fe15cd763d022a41430a93da8a2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fabe7d9e1e9efa3e70b5fc54af1fdc41", "guid": "bfdfe7dc352907fc980b868725387e981130f662e555066c50717a8a8c57c0e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e332f2e859736507c455cfdae98426be", "guid": "bfdfe7dc352907fc980b868725387e9810fbd4abdf5ccfa1e6d9e2f53193bad0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a0bbfeade12e817d63b6f40a562dca9", "guid": "bfdfe7dc352907fc980b868725387e98ddb97ae0d0784417cf2d6521ed00a178"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4bb65c59cc03819f4029ca6e723bf8e", "guid": "bfdfe7dc352907fc980b868725387e98fe230647cd1411ecd5dc2bf2a9d4e2a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce25b091e297d66b3dfa5ed3eb75fbe3", "guid": "bfdfe7dc352907fc980b868725387e984da98442a79ed8a0e1387a0ce5b27b1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd8e45a6c5fd3f8d203caf91bb865d7a", "guid": "bfdfe7dc352907fc980b868725387e98b6d16efc003dad8f3653a0e13ef727ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834e3ca85efd6e19d4561d2081c134478", "guid": "bfdfe7dc352907fc980b868725387e981dc23601da91a33d58485de82cac566e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc9f65acc048dc9ff0f4c4613c2d1811", "guid": "bfdfe7dc352907fc980b868725387e98ff7d8badc1c9d5ee2e1d88269964c805"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aefb1579c76f5305543e95a5af3ebb57", "guid": "bfdfe7dc352907fc980b868725387e98c5f061912801e82f57aa72405a11cc49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e917163f1e8f53cca1774028141cca0e", "guid": "bfdfe7dc352907fc980b868725387e98372ebd4fa8fe796651da1765a5891a57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c033fcc413d19e98c1bdfa677259846a", "guid": "bfdfe7dc352907fc980b868725387e98eb20c013caa5de6a362fdf6ac6c9943a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a52e1b1fa66e3d32f09576c1785b64b", "guid": "bfdfe7dc352907fc980b868725387e98c871d1ce93c5ed836a06e670bc9f96c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b60122683ac268b38b3f6a6d3030ef20", "guid": "bfdfe7dc352907fc980b868725387e981f2144de32952af0363dea8b5ef341d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98675e171c32e99dad988a778fadcd9c2f", "guid": "bfdfe7dc352907fc980b868725387e986448cc18fccf832b75437980db5a5e21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bea2608a75c5dbbf50985bc9b9c9a8d8", "guid": "bfdfe7dc352907fc980b868725387e986c25ace66908c54f0b43bf2f789111f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4b560ebf130154d298fb2f3deacec2d", "guid": "bfdfe7dc352907fc980b868725387e980774b6b4832c0461a0f8629ee85b1feb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3aa146fffa1974a01679b666f0bb3f3", "guid": "bfdfe7dc352907fc980b868725387e984bd29858a3836dbea44f997fbdd4e1b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec392a5de269947a9913382dc87aa73c", "guid": "bfdfe7dc352907fc980b868725387e982e213790a9afe76ce56981f6648c8212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98580c300aa251b0cba0f6cddd5b732062", "guid": "bfdfe7dc352907fc980b868725387e98a3b60caba9104f2262e0e30bd6e565ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835bec0128be743eb4684aac47752102b", "guid": "bfdfe7dc352907fc980b868725387e981fcad40ff117428162e99a7a25ae0c41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ada42a524a868353823a75abcc887ed6", "guid": "bfdfe7dc352907fc980b868725387e98ce34395ba64614458fc90b5bd4385cf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b22d5575cffd60d82ae3838017f4881b", "guid": "bfdfe7dc352907fc980b868725387e985a90a85f1d2f7d5513c41ebb2947feeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ca4a967bab2398813d14b093df0a22f", "guid": "bfdfe7dc352907fc980b868725387e985145a37f733cb33167c13152e5b57b13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987934ec707d143cba5405ffbb6b0102b6", "guid": "bfdfe7dc352907fc980b868725387e98367ba72a0bdff4fe465a78e65e2586e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b148808321f8fa13c7e5edfcfc1b7f64", "guid": "bfdfe7dc352907fc980b868725387e9892ca5dcf5d43dba7241c5593929aafee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2f3af399ab45003a5f96e8551f07017", "guid": "bfdfe7dc352907fc980b868725387e98bb52774805f03428eb40a2f9b2147a86"}], "guid": "bfdfe7dc352907fc980b868725387e98997d1f3320b96a489bbfeca42b5f2b52", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9820ad3732878a4721ac229b7e03e0956a"}], "guid": "bfdfe7dc352907fc980b868725387e9814822ff19035a835afeb657bb4dcb35b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c9cbb956b62333c02329180fbe03b825", "targetReference": "bfdfe7dc352907fc980b868725387e9890f568fc9b811cd56b08e401eec0f35e"}], "guid": "bfdfe7dc352907fc980b868725387e98b78f48222917d3611c7c397d7f9ae63e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9890f568fc9b811cd56b08e401eec0f35e", "name": "rive_common-rive_common_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9849c5bdbe31e3b466d37bab9271baf60a", "name": "rive_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9884d4dff26bf6b3e66f738069c7476896", "name": "rive_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}