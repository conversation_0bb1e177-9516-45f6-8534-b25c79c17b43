import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/bloc/newmealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/meal_plan/checkout_page.dart';
import 'package:db_eats/ui/meal_plan_new/curated/curated_view_chef_menu.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:db_eats/data/models/new_meal_plan/mealplanprogresslatest.dart'
    as progressLatest;
import 'package:db_eats/data/models/new_meal_plan/newfilteredchefsmodel.dart'
    as newFilter;

class CuratedChefLists extends StatefulWidget {
  final int mealPlanId;
  final int? dayId;
  final String? date;
  final bool isEditing;

  const CuratedChefLists({
    super.key,
    required this.mealPlanId,
    this.dayId,
    this.date,
    this.isEditing = false,
  });

  @override
  State<CuratedChefLists> createState() => _CuratedChefListsState();
}

class _CuratedChefListsState extends State<CuratedChefLists> {
  List<newFilter.Chefs> _chefs = [];
  int _selectedDate = 0;
  int _currentDay = 0;
  final Map<int, Map<String, dynamic>> _selectedChefs = {};
  late final MealplanBloc _mealPlanBloc;
  String _startDate = '';
  String _deliveryTime = '';
  bool _isLoading = true;
  List<String> _selectedWeekdayDates = [];
  int _mealPlanDuration = 0;
  dynamic viewDayData;
  bool _hasFetchedSummary = false;

  @override
  void initState() {
    super.initState();
    _mealPlanBloc = BlocProvider.of<MealplanBloc>(context);

    if (widget.isEditing && widget.dayId != null) {
      _mealPlanBloc.add(ViewDayEvent(widget.dayId!));
    } else {
      if (!_hasFetchedSummary) {
        _hasFetchedSummary = true;
        context.read<NewmealplanBloc>().add(ListNewMealPlanSummaryEvent(
              data: {
                "meal_plan_id": widget.mealPlanId,
              },
            ));
      }
    }

    _selectedDate = 1;
    _currentDay = 1;
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final screenWidth = MediaQuery.of(context).size.width;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  String _formatDate(String date) {
    if (date.isEmpty) return '';
    try {
      final DateTime dateTime = DateTime.parse(date);
      final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      return days[dateTime.weekday - 1];
    } catch (e) {
      return date;
    }
  }

  String _formatTime(String time) {
    if (time.isEmpty) return time;
    try {
      final parts = time.split(':');
      if (parts.length >= 2) {
        int hour = int.parse(parts[0]);
        int minute = int.parse(parts[1]);

        String period = hour >= 12 ? 'PM' : 'AM';
        if (hour > 12) hour -= 12;
        if (hour == 0) hour = 12;

        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
      }
    } catch (e) {}
    return time;
  }

  bool _isDayCompleted(int dayNumber) {
    return _selectedChefs.containsKey(dayNumber);
  }

  List<String> _generateDatesFromSelectedWeekdays(
      String startDate, List<String> selectedWeekdays) {
    if (startDate.isEmpty || selectedWeekdays.isEmpty) return [];

    List<String> dates = [];
    DateTime currentDate;
    try {
      currentDate = DateTime.parse(startDate);
    } catch (e) {
      currentDate = DateTime.now();
    }

    Map<String, int> weekdayMap = {
      'MONDAY': 1,
      'TUESDAY': 2,
      'WEDNESDAY': 3,
      'THURSDAY': 4,
      'FRIDAY': 5,
      'SATURDAY': 6,
      'SUNDAY': 7,
    };

    List<int> selectedWeekdayNumbers = selectedWeekdays
        .map((day) => weekdayMap[day.toUpperCase()] ?? 0)
        .where((day) => day > 0)
        .toList();

    if (selectedWeekdayNumbers.isEmpty) return [];

    selectedWeekdayNumbers.sort();

    DateTime searchDate = currentDate;

    for (int weekdayNumber in selectedWeekdayNumbers) {
      DateTime candidateDate = searchDate;

      while (candidateDate.weekday != weekdayNumber) {
        candidateDate = candidateDate.add(const Duration(days: 1));
      }

      dates.add(candidateDate.toString().split(' ')[0]);

      if (dates.length >= selectedWeekdays.length) break;
    }

    return dates;
  }

  void _onDaySelected(int dayNumber) {
    if (widget.isEditing || _isLoading || _selectedWeekdayDates.isEmpty) return;

    setState(() {
      _selectedDate = dayNumber;
      _currentDay = dayNumber;
      _isLoading = true;
    });

    final progressData = Initializer.mealPlanProgressLatestModel.data;
    if (progressData != null && dayNumber <= _selectedWeekdayDates.length) {
      final selectedDate = _selectedWeekdayDates[dayNumber - 1];
      final weekdayString = _getWeekdayName(selectedDate);

      // print(
      //     'Day selected: $dayNumber, Date: $selectedDate, Weekday: $weekdayString');

      final filterData = {
        "search_keyword": "",
        "latitude": Initializer.latitude ?? "0",
        "longitude": Initializer.longitude ?? "0",
        "serving_size_id": progressData.servingSizeId,
        "time_slot_id": progressData.timeSlotId,
        "start_date":
            progressData.startDate ?? DateTime.now().toString().split(' ')[0],
        "end_date":
            progressData.endDate ?? DateTime.now().toString().split(' ')[0],
        "weekday": weekdayString,
        "cuisine_ids": progressData.cuisines?.map((c) => c.id).toList() ?? [],
        "sub_cuisine_ids":
            progressData.subcuisines?.map((c) => c.id).toList() ?? [],
        "local_cuisine_ids":
            progressData.localcuisines?.map((c) => c.id).toList() ?? [],
        "dietary_id": progressData.dietaryPreferenceId?.toString() ?? "",
        "spice_level_id": progressData.spiceLevelId?.toString() ?? "",
        "meal_plan_id": widget.mealPlanId,
        "meal_plan_duration": _mealPlanDuration,
      };

      context.read<NewmealplanBloc>().add(NewFilterChefsEvent(filterData));
    }
  }

  String _getWeekdayName(String date) {
    try {
      final DateTime dateTime = DateTime.parse(date);
      final days = [
        'MONDAY',
        'TUESDAY',
        'WEDNESDAY',
        'THURSDAY',
        'FRIDAY',
        'SATURDAY',
        'SUNDAY'
      ];
      return days[dateTime.weekday - 1];
    } catch (e) {
      return '';
    }
  }

  void _loadFilteredChefsFromLatest(
      progressLatest.MealPlanProgressLatestData? progressData) {
    if (progressData == null) return;

    final startDate =
        progressData.startDate ?? DateTime.now().toString().split(' ')[0];
    final selectedWeekdays = progressData.selectedWeekdays ?? [];

    _selectedWeekdayDates =
        _generateDatesFromSelectedWeekdays(startDate, selectedWeekdays);
    _mealPlanDuration = selectedWeekdays.length;

    String weekdayForFiltering = '';

    if (widget.isEditing && widget.date != null) {
      weekdayForFiltering = widget.date!;
    } else {
      weekdayForFiltering = _selectedWeekdayDates.isNotEmpty
          ? _getWeekdayName(_selectedWeekdayDates[0])
          : _getWeekdayName(startDate);
    }

    final filterData = {
      "search_keyword": "",
      "latitude": Initializer.latitude ?? "0",
      "longitude": Initializer.longitude ?? "0",
      "serving_size_id": progressData.servingSizeId,
      "time_slot_id": progressData.timeSlotId,
      "start_date":
          progressData.startDate ?? DateTime.now().toString().split(' ')[0],
      "end_date":
          progressData.endDate ?? DateTime.now().toString().split(' ')[0],
      "weekday": weekdayForFiltering,
      "cuisine_ids": progressData.cuisines?.map((c) => c.id).toList() ?? [],
      "sub_cuisine_ids":
          progressData.subcuisines?.map((c) => c.id).toList() ?? [],
      "local_cuisine_ids":
          progressData.localcuisines?.map((c) => c.id).toList() ?? [],
      "dietary_id": progressData.dietaryPreferenceId?.toString() ?? "",
      "spice_level_id": progressData.spiceLevelId?.toString() ?? "",
      "meal_plan_id": widget.mealPlanId,
      "meal_plan_duration": _mealPlanDuration,
    };

    setState(() {
      _startDate = startDate;
      _deliveryTime = progressData.timeSlot != null
          ? '${_formatTime(progressData.timeSlot!.startTime ?? '')} - ${_formatTime(progressData.timeSlot!.endTime ?? '')}'
          : '';
    });

    context.read<NewmealplanBloc>().add(NewFilterChefsEvent(filterData));
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return WillPopScope(
      onWillPop: () async {
        if (widget.isEditing) {
          context.read<NewmealplanBloc>().add(ListNewMealPlanSummaryEvent(
                data: {"meal_plan_id": widget.mealPlanId, 'is_summary': true},
              ));
        }
        return true; // allow pop
      },
      child: MultiBlocListener(
        listeners: [
          BlocListener<MealplanBloc, MealPlanState>(
            listener: (context, state) {
              if (state is ViewDaySuccess) {
                setState(() {
                  viewDayData = state.data;
                  _currentDay = 1;
                  _selectedDate = 1;

                  final progressData =
                      Initializer.mealPlanProgressLatestModel.data;
                  if (progressData?.mealPlanDays != null &&
                      widget.dayId != null) {
                    final dayData = progressData!.mealPlanDays!.firstWhere(
                      (day) => day.id == widget.dayId,
                      orElse: () => progressData.mealPlanDays!.first,
                    );
                    if (dayData.dayNumber != null) {
                      _currentDay = dayData.dayNumber!;
                      _selectedDate = dayData.dayNumber!;
                    }
                  }
                });

                if (widget.isEditing) {
                  final progressData =
                      Initializer.mealPlanProgressLatestModel.data;
                  if (progressData != null) {
                    _loadFilteredChefsFromLatest(progressData);
                  }
                } else {
                  final progressData =
                      Initializer.mealPlanProgressLatestModel.data;
                  if (progressData == null) {
                    if (!_hasFetchedSummary) {
                      _hasFetchedSummary = true;
                      context
                          .read<NewmealplanBloc>()
                          .add(ListNewMealPlanSummaryEvent());
                    }
                  }
                }
              } else if (state is ViewDayFailed) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to load day data: ${state.message}'),
                    backgroundColor: const Color(0xFFE11900),
                  ),
                );
                Navigator.of(context).pop();
              }
            },
          ),
          BlocListener<NewmealplanBloc, NewMealPlanState>(
            listener: (context, state) {
              if (state is ListNewMealPlanSummaryLoading) {
                setState(() {
                  _isLoading = true;
                });
              } else if (state is NewMealPlanSummaryLoaded) {
                if (!widget.isEditing) {
                  final progressData =
                      Initializer.mealPlanProgressLatestModel.data;
                  if (progressData != null) {
                    _loadFilteredChefsFromLatest(progressData);
                  } else {
                    setState(() {
                      _isLoading = false;
                    });
                  }
                }
              } else if (state is NewMealPlanSummaryLoadFailed) {
                setState(() {
                  _isLoading = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(state.error)),
                );
              } else if (state is NewFilterChefsStateLoading) {
                setState(() {
                  _isLoading = true;
                });
              } else if (state is NewFilterChefsStateSuccess) {
                final newFilterData = Initializer.newFilteredChefModel.data;
                setState(() {
                  _chefs = newFilterData?.chefs ?? [];
                  _isLoading = false;
                });
              } else if (state is NewFilterChefsStateLoadFailed) {
                setState(() {
                  _isLoading = false;
                  _chefs = [];
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(state.error)),
                );
              }
            },
          ),
        ],
        child: BlocBuilder<MealplanBloc, MealPlanState>(
          builder: (context, state) {
            if (state is MealPlanProgressLoading) {
              return Scaffold(
                backgroundColor: const Color(0xFFF6F3EC),
                appBar: _buildAppBar(size),
                body: _buildFullScreenShimmer(size),
              );
            }

            final isLoading = _isLoading || state is FilterChefsLoading;
            return Scaffold(
              backgroundColor: const Color(0xFFF6F3EC),
              appBar: _buildAppBar(size),
              body: ListView(
                padding: EdgeInsets.symmetric(vertical: 0),
                //  padding: EdgeInsets.symmetric(vertical: size.height * 0.02),
                children: [
                  // Title
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: size.width * 0.04),
                    child: Text(
                      "Select Chef",
                      style: TextStyle(
                        fontSize: isTablet ? size.width * 0.05 : eighteen,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                        color: const Color(0xFF1F2222),
                      ),
                    ),
                  ),
                  SizedBox(height: size.height * 0.015),
                  // Date and Time information
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: size.width * 0.04),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Left column - Date and delivery time
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Date row
                                  Row(
                                    children: [
                                      Image.asset(
                                        'assets/icons/date_range.png',
                                        width: twelve,
                                        height: twelve,
                                        color: const Color(0xFF1F2222),
                                      ),
                                      SizedBox(width: size.width * 0.02),
                                      Text(
                                        _isLoading
                                            ? "Loading..."
                                            : widget.isEditing
                                                ? _formatDate(
                                                    widget.date ?? _startDate)
                                                : (_currentDay > 0 &&
                                                        _selectedWeekdayDates
                                                            .isNotEmpty)
                                                    ? _formatDate(
                                                        _selectedWeekdayDates[
                                                            _currentDay - 1])
                                                    : "Select Day",
                                        style: TextStyle(
                                          fontSize: twelve,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Inter',
                                          color: const Color(0xFF414346),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: size.height * 0.005),
                                  // Delivery time row
                                  Row(
                                    children: [
                                      Icon(Icons.access_time,
                                          size: twelve,
                                          color: const Color(0xFF1F2222)),
                                      SizedBox(width: size.width * 0.02),
                                      Text(
                                        isLoading
                                            ? "Loading..."
                                            : _deliveryTime,
                                        style: TextStyle(
                                          fontSize: twelve,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Inter',
                                          color: const Color(0xFF414346),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            _mealPlanDuration > 0
                                ? _buildDateSelector(size, isLandscape)
                                : _buildDateSelectorShimmer(size),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: sixteen / 2),
                  // View Filters button
                  // Padding(
                  //   padding: EdgeInsets.symmetric(horizontal: sixteen),
                  //   child: Container(
                  //     height: twentyFour + twentyFour,
                  //     decoration: BoxDecoration(
                  //       borderRadius: BorderRadius.circular(size.width * 0.07),
                  //       border: Border.all(color: const Color(0xFF1F2222)),
                  //     ),
                  //     child: InkWell(
                  //       borderRadius: BorderRadius.circular(size.width * 0.07),
                  //       onTap: () {
                  //         // Handle filter action
                  //       },
                  //       child: Row(
                  //         mainAxisAlignment: MainAxisAlignment.center,
                  //         children: [
                  //           Icon(Icons.tune,
                  //               size: size.width * 0.04,
                  //               color: const Color(0xFF1F2222)),
                  //           SizedBox(width: size.width * 0.02),
                  //           Text(
                  //             "View Filters",
                  //             style: TextStyle(
                  //               fontSize: twelve,
                  //               fontWeight: FontWeight.w600,
                  //               fontFamily: 'Inter',
                  //               color: const Color(0xFF1F2222),
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  SizedBox(height: size.height * 0.02),
                  // Chef Cards
                  isLoading
                      ? _buildChefShimmerGrid(size, isTablet, isLandscape)
                      : _chefs.isEmpty
                          ? _buildNoChefMessage(size)
                          : isTablet
                              ? _buildChefGrid(size, isLandscape)
                              : _buildChefList(size),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(Size size) {
    return AppBar(
      backgroundColor: const Color(0xFFF6F3EC),
      elevation: 0,
      scrolledUnderElevation: 0,
      foregroundColor: const Color(0xFF1F2222),
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: Icon(Icons.arrow_back,
            size: size.width * 0.06, color: const Color(0xFF1F2222)),
        onPressed: () {
          Navigator.of(context).pop();
          if (widget.isEditing) {
            context.read<NewmealplanBloc>().add(ListNewMealPlanSummaryEvent(
                  data: {"meal_plan_id": widget.mealPlanId, 'is_summary': true},
                ));
          }
        },
      ),
      title: const Text(''),
      centerTitle: false,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Color(0xFFF6F3EC),
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
    );
  }

  Widget _buildDateSelector(Size size, bool isLandscape) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final circleSize = (size.width * 0.06).clamp(20.0, 25.0);
        final connectorWidth = (size.width * 0.02).clamp(5.0, 7.0);
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(_mealPlanDuration, (index) {
            final number = index + 1;
            bool isSelected = false;

            if (!_isLoading && _startDate.isNotEmpty) {
              final dates =
                  _selectedWeekdayDates.isNotEmpty ? _selectedWeekdayDates : [];
              if (index < dates.length) {
                if (widget.isEditing) {
                  final dayDate = dates[index];
                  final selectedDateOnly =
                      (widget.date ?? '').split(',')[0].trim();
                  isSelected = dayDate == selectedDateOnly;
                } else {
                  isSelected = _selectedDate == number;
                }
              }
            }

            final isCompleted = _isDayCompleted(number);
            final isFirst = index == 0;
            final isLast = index == _mealPlanDuration - 1;

            String displayDate = '';
            String weekdayName = '';
            if (!_isLoading && _startDate.isNotEmpty) {
              final dates =
                  _selectedWeekdayDates.isNotEmpty ? _selectedWeekdayDates : [];
              if (index < dates.length) {
                final date = DateTime.parse(dates[index]);
                displayDate = '${date.day}/${date.month}';
                weekdayName = _getWeekdayName(dates[index]);
              }
            }

            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (!isFirst)
                  Container(
                    width: connectorWidth,
                    height: 1,
                    color: const Color(0xFFB9B6AD),
                  ),
                Tooltip(
                  message: weekdayName.isNotEmpty
                      ? '$weekdayName ($displayDate)'
                      : displayDate,
                  child: InkWell(
                    onTap:
                        widget.isEditing ? null : () => _onDaySelected(number),
                    borderRadius: BorderRadius.circular(circleSize / 2),
                    child: Container(
                      width: circleSize,
                      height: circleSize,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: (isSelected || isCompleted)
                            ? const Color(0xFF1F2222)
                            : Colors.transparent,
                        border: Border.all(
                          color: (isSelected || isCompleted)
                              ? const Color(0xFF1F2222)
                              : const Color(0xFFB9B6AD),
                          width: 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          "$number",
                          style: TextStyle(
                            fontSize: circleSize * 0.5,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Inter',
                            color: (isSelected || isCompleted)
                                ? Colors.white
                                : const Color(0xFF1F2222),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                if (!isLast)
                  Container(
                    width: connectorWidth,
                    height: 1,
                    color: const Color(0xFFB9B6AD),
                  ),
              ],
            );
          }),
        );
      },
    );
  }

  Widget _buildDateSelectorShimmer(Size size) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final circleSize = (size.width * 0.06).clamp(20.0, 25.0);
        final connectorWidth = (size.width * 0.02).clamp(5.0, 7.0);
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(3, (index) {
              final isFirst = index == 0;
              final isLast = index == 2;

              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (!isFirst)
                    Container(
                      width: connectorWidth,
                      height: 1,
                      color: Colors.white,
                    ),
                  Container(
                    width: circleSize,
                    height: circleSize,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                  if (!isLast)
                    Container(
                      width: connectorWidth,
                      height: 1,
                      color: Colors.white,
                    ),
                ],
              );
            }),
          ),
        );
      },
    );
  }

  Widget _buildChefList(Size size) {
    return Column(
      children: _chefs.map((chef) => _buildChefCard(chef, size)).toList(),
    );
  }

  Widget _buildChefGrid(Size size, bool isLandscape) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: isLandscape ? 3 : 2,
        crossAxisSpacing: size.width * 0.02,
        mainAxisSpacing: size.height * 0.02,
        childAspectRatio: 0.75,
      ),
      itemCount: _chefs.length,
      itemBuilder: (context, index) {
        return _buildChefCard(_chefs[index], size);
      },
    );
  }

  Widget _buildChefCard(newFilter.Chefs chef, Size size) {
    return Container(
        margin: EdgeInsets.fromLTRB(
          sixteen,
          0,
          sixteen,
          sixteen / 2,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(size.width * 0.04),
          border: Border.all(
            color: _selectedChefs.containsValue(chef)
                ? const Color(0xFF1F2222)
                : Colors.transparent,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(size.width * 0.04),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CircleAvatar(
                    radius: twelve,
                    backgroundColor: const Color(0xFFE1DDD5),
                    backgroundImage: chef.profilePhoto != null &&
                            chef.profilePhoto!.isNotEmpty
                        ? NetworkImage(
                            ServerHelper.imageUrl + chef.profilePhoto!)
                        : const AssetImage('assets/images/no_image_avatar.png')
                            as ImageProvider,
                    onBackgroundImageError: (e, stackTrace) {
                      setState(() {
                        chef.profilePhoto = null;
                      });
                    },
                  ),
                  SizedBox(
                    width: twelve,
                  ),
                  Expanded(
                    child: Text(
                      '${chef.chef?.firstName} ${chef.chef?.lastName}',
                      style: TextStyle(
                        fontSize: sixteen,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                        color: const Color(0xFF1F2222),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: sixteen / 2,
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: sixteen / 2,
                  //vertical: sixteen / 4
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFFE1E3E6),
                  borderRadius: BorderRadius.circular(size.width * 0.03),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset(
                      'assets/icons/thump.png',
                      width: twenty / 2,
                      height: twenty / 2,
                      color: const Color(0xFF1F2222),
                    ),
                    SizedBox(width: sixteen / 4),
                    Text(
                      '${chef.ratingPercentage ?? '0'}% (${chef.totalRatings ?? 0})',
                      style: TextStyle(
                        fontSize: twenty / 2,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Inter',
                        color: const Color(0xFF1F2222),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: size.height * 0.01),
              Text(
                '',
                style: TextStyle(
                  fontSize: twelve,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Inter',
                  color: const Color(0xFF414346),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: sixteen / 4),
              Row(
                children: [
                  Image.asset(
                    'assets/icons/calender_2.png',
                    width: twenty / 2,
                    height: twenty / 2,
                    color: const Color(0xFF1F2222),
                  ),
                  SizedBox(width: ten / 5),
                  Text(
                    chef.chef?.operationDays
                            ?.map((day) => day.day?.name?.substring(0, 1) ?? '')
                            .join(", ") ??
                        '',
                    style: TextStyle(
                      fontSize: twelve,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: const Color(0xFF1F2222),
                    ),
                  ),
                ],
              ),
              SizedBox(height: sixteen / 2),
              OutlinedButton(
                onPressed: () async {
                  if (_currentDay > 0 || widget.isEditing) {
                    final result = await Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => CuratedViewChefMenu(
                          mealPlanId: widget.mealPlanId,
                          id: chef.chefId ?? 0,
                          selectedDay:
                              widget.isEditing ? _currentDay : _currentDay,
                          deliveryTime: _deliveryTime,
                          selectedDate: widget.isEditing
                              ? (widget.date ?? _startDate)
                              : _selectedWeekdayDates.isNotEmpty &&
                                      _currentDay <=
                                          _selectedWeekdayDates.length
                                  ? () {
                                      final date = _selectedWeekdayDates[
                                          _currentDay - 1];
                                      print(
                                          'Navigating to chef menu with selectedDate: $date, currentDay: $_currentDay');
                                      return date;
                                    }()
                                  : _startDate,
                          startDate: widget.isEditing ? null : _startDate,
                          endDate: widget.isEditing
                              ? null
                              : Initializer.mealPlanProgressLatestModel.data
                                      ?.endDate ??
                                  _startDate,
                          mealPlanDuration: _mealPlanDuration,
                          isEditing: widget.isEditing,
                          dayId: widget.isEditing ? widget.dayId : null,
                          viewDayData: widget.isEditing ? viewDayData : null,
                          chefLocation: chef.distance != null
                              ? '${chef.distance!.toStringAsFixed(1)} km'
                              : null,
                        ),
                        settings: RouteSettings(
                            arguments: _selectedChefs.values.toList()),
                      ),
                    );

                    if (widget.isEditing && result != null) {
                      if (mounted) {
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                            builder: (context) => CheckoutPage(
                              mealPlanId: widget.mealPlanId,
                            ),
                          ),
                        );
                      }
                    } else if (result != null) {
                      setState(() {
                        _selectedChefs[_currentDay] = result;
                        if (_selectedChefs.length <
                            _selectedWeekdayDates.length) {
                          _currentDay++;
                          _selectedDate = _currentDay;

                          final progressData =
                              Initializer.mealPlanProgressLatestModel.data;
                          if (progressData != null &&
                              _currentDay <= _selectedWeekdayDates.length) {
                            final selectedDate =
                                _selectedWeekdayDates[_currentDay - 1];
                            final weekdayString = _getWeekdayName(selectedDate);

                            final filterData = {
                              "search_keyword": "",
                              "latitude": Initializer.latitude ?? "0",
                              "longitude": Initializer.longitude ?? "0",
                              "serving_size_id": progressData.servingSizeId,
                              "time_slot_id": progressData.timeSlotId,
                              "start_date": progressData.startDate ??
                                  DateTime.now().toString().split(' ')[0],
                              "end_date": progressData.endDate ??
                                  DateTime.now().toString().split(' ')[0],
                              "weekday": weekdayString,
                              "cuisine_ids": progressData.cuisines
                                      ?.map((c) => c.id)
                                      .toList() ??
                                  [],
                              "sub_cuisine_ids": progressData.subcuisines
                                      ?.map((c) => c.id)
                                      .toList() ??
                                  [],
                              "local_cuisine_ids": progressData.localcuisines
                                      ?.map((c) => c.id)
                                      .toList() ??
                                  [],
                              "dietary_id": progressData.dietaryPreferenceId
                                      ?.toString() ??
                                  "",
                              "spice_level_id":
                                  progressData.spiceLevelId?.toString() ?? "",
                              "meal_plan_id": widget.mealPlanId,
                              "meal_plan_duration": _mealPlanDuration,
                            };

                            _isLoading = true;
                            context
                                .read<NewmealplanBloc>()
                                .add(NewFilterChefsEvent(filterData));
                          }
                        }
                      });
                    }
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please select a day first'),
                        backgroundColor: Color(0xFFE11900),
                      ),
                    );
                  }
                },
                style: OutlinedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(size.width * 0.07)),
                  side: const BorderSide(color: Color(0xFF1F2222)),
                  foregroundColor: const Color(0xFF1F2222),
                  minimumSize: Size(0, 0),
                  padding: EdgeInsets.symmetric(
                      vertical: size.height * 0.008,
                      horizontal: size.width * 0.04),
                ),
                child: Text(
                  'View Chef Menu',
                  style: TextStyle(
                    fontSize: twelve,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Inter',
                    color: const Color(0xFF1F2222),
                  ),
                ),
              ),
              SizedBox(height: sixteen / 2),
              SizedBox(
                height: ten * 12 + sixteen,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: chef.chef?.dishes?.length ?? 0,
                  itemBuilder: (context, index) {
                    final dish = chef.chef?.dishes?[index];
                    return Container(
                      width: ten * 15 + eighteen,
                      margin: EdgeInsets.only(
                          right: index == (chef.chef?.dishes?.length ?? 0) - 1
                              ? 0
                              : sixteen / 2),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(size.width * 0.02),
                        border: Border.all(color: const Color(0xFFE1DDD5)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.vertical(
                                top: Radius.circular(size.width * 0.02)),
                            child: dish?.photo != null
                                ? Image.network(
                                    '${ServerHelper.imageUrl}${dish?.photo}',
                                    height: ten * 8 + twelve,
                                    width: double.infinity,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        height: ten * 12,
                                        color: const Color(0xFFE1DDD5),
                                        child: const Center(
                                          child: Icon(Icons.restaurant_menu,
                                              color: Color(0xFF1F2222)),
                                        ),
                                      );
                                    },
                                  )
                                : Container(
                                    height: ten * 12,
                                    color: const Color(0xFFE1DDD5),
                                    child: const Center(
                                      child: Icon(Icons.restaurant_menu,
                                          color: Color(0xFF1F2222)),
                                    ),
                                  ),
                          ),
                          Padding(
                            padding: EdgeInsets.all(twelve),
                            child: Text(
                              dish?.name ?? '',
                              style: TextStyle(
                                fontSize: twelve,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF1F2222),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
              SizedBox(
                height: 12,
              ),
            ],
          ),
        ));
  }

  Widget _buildNoChefMessage(Size size) {
    return Container(
      margin: EdgeInsets.fromLTRB(size.width * 0.04, size.height * 0.04,
          size.width * 0.04, size.height * 0.02),
      padding: EdgeInsets.all(size.width * 0.06),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(size.width * 0.04),
        border: Border.all(color: const Color(0xFFE1DDD5)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            'assets/images/cooking.png',
            width: size.width * 0.2,
            height: size.width * 0.2,
          ),
          SizedBox(height: size.height * 0.02),
          Text(
            'No Chefs Available',
            style: TextStyle(
              fontSize: size.width * 0.045,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
              color: const Color(0xFF1F2222),
            ),
          ),
          SizedBox(height: size.height * 0.01),
          Text(
            'Sorry, there are no chefs available in your area at the moment.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: size.width * 0.035,
              fontWeight: FontWeight.w400,
              fontFamily: 'Inter',
              color: const Color(0xFF414346),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullScreenShimmer(Size size) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: ListView(
        padding: EdgeInsets.symmetric(vertical: size.height * 0.02),
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
            child: Container(
              height: size.height * 0.04,
              width: size.width * 0.4,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          SizedBox(height: size.height * 0.015),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      width: size.width * 0.04,
                      height: size.width * 0.04,
                      color: Colors.white,
                    ),
                    SizedBox(width: size.width * 0.02),
                    Container(
                      width: size.width * 0.4,
                      height: size.height * 0.02,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const Spacer(),
                    Row(
                      children: _mealPlanDuration > 0
                          ? List.generate(
                              _mealPlanDuration,
                              (index) => Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: size.width * 0.01),
                                child: Container(
                                  width: size.width * 0.06,
                                  height: size.width * 0.06,
                                  decoration: const BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ),
                            )
                          : List.generate(
                              3,
                              (index) => Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: size.width * 0.01),
                                child: Container(
                                  width: size.width * 0.06,
                                  height: size.width * 0.06,
                                  decoration: const BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ),
                            ),
                    ),
                  ],
                ),
                SizedBox(height: size.height * 0.01),
                Row(
                  children: [
                    Container(
                      width: size.width * 0.04,
                      height: size.width * 0.04,
                      color: Colors.white,
                    ),
                    SizedBox(width: size.width * 0.02),
                    Container(
                      width: size.width * 0.3,
                      height: size.height * 0.02,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(height: size.height * 0.02),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
            child: Container(
              height: size.height * 0.06,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(size.width * 0.07),
              ),
            ),
          ),
          SizedBox(height: size.height * 0.02),
          _buildChefShimmerGrid(size, false, false),
        ],
      ),
    );
  }

  Widget _buildChefShimmerGrid(Size size, bool isTablet, bool isLandscape) {
    if (isTablet) {
      return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: isLandscape ? 3 : 2,
          crossAxisSpacing: size.width * 0.02,
          mainAxisSpacing: size.height * 0.02,
          childAspectRatio: 0.75,
        ),
        itemCount: 3,
        itemBuilder: (context, index) => _buildChefShimmerCard(size),
      );
    }
    return Column(
      children: List.generate(
        3,
        (index) => Padding(
          padding: EdgeInsets.fromLTRB(
              size.width * 0.04, 0, size.width * 0.04, size.height * 0.02),
          child: _buildChefShimmerCard(size),
        ),
      ),
    );
  }

  Widget _buildChefShimmerCard(Size size) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(size.width * 0.04),
        ),
        child: Padding(
          padding: EdgeInsets.all(size.width * 0.04),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: size.width * 0.12,
                    height: size.width * 0.12,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: size.width * 0.03),
                  Container(
                    width: size.width * 0.4,
                    height: size.height * 0.025,
                    color: Colors.white,
                  ),
                ],
              ),
              SizedBox(height: size.height * 0.015),
              Container(
                width: size.width * 0.2,
                height: size.height * 0.03,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(size.width * 0.03),
                ),
              ),
              SizedBox(height: size.height * 0.015),
              Container(
                width: size.width * 0.6,
                height: size.height * 0.02,
                color: Colors.white,
              ),
              SizedBox(height: size.height * 0.02),
              Container(
                width: size.width * 0.3,
                height: size.height * 0.02,
                color: Colors.white,
              ),
              SizedBox(height: size.height * 0.015),
              Container(
                width: size.width * 0.4,
                height: size.height * 0.05,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(size.width * 0.07),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
