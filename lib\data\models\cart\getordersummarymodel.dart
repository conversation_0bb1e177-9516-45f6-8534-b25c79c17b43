class GetOrderSummaryModel {
  bool? status;
  OrderData? data;
  int? statusCode;

  GetOrderSummaryModel({this.status, this.data, this.statusCode});

  GetOrderSummaryModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? new OrderData.fromJson(json['data']) : null;
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['status_code'] = this.statusCode;
    return data;
  }
}

class OrderData {
  Chef? chef;
  List<Items>? items;
  String? durationText; // NEW FIELD
  num? walletBalance;
  num? totalPrice;
  num? walletCredits;
  num? taxPercentage;
  num? taxesAndFees;
  num? discount;
  num? deliveryFee;
  num? serviceFee;
  num? finalTotal;
  ChefOperationTime? customerTimePreference;
  CurrentAddress? currentAddress;
  num? couponDiscount; // NEW FIELD
  String? couponMessage; // NEW FIELD
  CouponDetails? couponDetails; // NEW FIELD
  String? errorMessage;

  OrderData({
    this.chef,
    this.items,
    this.durationText, // NEW FIELD
    this.walletBalance,
    this.totalPrice,
    this.walletCredits,
    this.taxPercentage,
    this.taxesAndFees,
    this.discount,
    this.deliveryFee,
    this.serviceFee,
    this.finalTotal,
    this.customerTimePreference,
    this.currentAddress,
    this.couponDiscount, // NEW FIELD
    this.couponMessage, // NEW FIELD
    this.couponDetails, // NEW FIELD
    this.errorMessage,
  });

  OrderData.fromJson(Map<String, dynamic> json) {
    chef = json['chef'] != null ? new Chef.fromJson(json['chef']) : null;
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(new Items.fromJson(v));
      });
    }
    durationText = json['duration_text']; // NEW FIELD
    walletBalance = json['wallet_balance'];
    totalPrice = json['total_price'];
    walletCredits = json['wallet_credits'];
    taxPercentage = json['tax_percentage'];
    taxesAndFees = json['taxes_and_fees'];
    discount = json['discount'] != null ? json['discount'].toDouble() : null;
    deliveryFee = json['delivery_fee'] != null
        ? json['delivery_fee'].toDouble()
        : null;
    serviceFee = json['service_fee'] != null
        ? json['service_fee'].toDouble()
        : null;
    finalTotal =
        json['final_total'] != null ? json['final_total'].toDouble() : null;
    customerTimePreference = json['customer_time_preference'] != null
        ? new ChefOperationTime.fromJson(json['customer_time_preference'])
        : null;
    currentAddress = json['current_address'] != null
        ? new CurrentAddress.fromJson(json['current_address'])
        : null;
    couponDiscount = json['coupon_discount']; // NEW FIELD
    couponMessage = json['coupon_message']; // NEW FIELD
    couponDetails = json['coupon_details'] != null
        ? new CouponDetails.fromJson(json['coupon_details'])
        : null; // NEW FIELD
    errorMessage = json['error_message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.chef != null) {
      data['chef'] = this.chef!.toJson();
    }
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    data['duration_text'] = this.durationText; // NEW FIELD
    data['wallet_balance'] = this.walletBalance;
    data['total_price'] = this.totalPrice;
    data['wallet_credits'] = this.walletCredits;
    data['tax_percentage'] = this.taxPercentage;
    data['taxes_and_fees'] = this.taxesAndFees;
    data['discount'] = this.discount;
    data['delivery_fee'] = this.deliveryFee;
    data['service_fee'] = this.serviceFee;
    data['final_total'] = this.finalTotal;
    if (this.customerTimePreference != null) {
      data['customer_time_preference'] = this.customerTimePreference!.toJson();
    }
    if (this.currentAddress != null) {
      data['current_address'] = this.currentAddress!.toJson();
    }
    data['coupon_discount'] = this.couponDiscount; // NEW FIELD
    data['coupon_message'] = this.couponMessage; // NEW FIELD
    if (this.couponDetails != null) {
      data['coupon_details'] = this.couponDetails!.toJson();
    } // NEW FIELD
    data['error_message'] = this.errorMessage;
    return data;
  }
}

// NEW CLASS
class CouponDetails {
  List<dynamic>? selectedDishes;
  List<dynamic>? selectedCategories;
  List<dynamic>? selectedChefs;
  int? id;
  int? addedById;
  String? addedByType;
  int? discountType;
  String? couponCode;
  String? discountName;
  String? description;
  num? discountPercentage;
  num? maxCapPercentageOff;
  String? discountAmount;
  int? perUserLimit;
  int? globalUserLimit;
  int? appliesTo;
  int? userEligibility;
  bool? isFirstOrder;
  bool? isMinimumOrderAmount;
  String? minimumSpendAmount;
  bool? isSelectedDishes;
  bool? isSelectedCategories;
  bool? isSelectedChefs;
  String? startDate;
  String? endDate;
  bool? isAutoApply;
  String? termsNotes;
  String? status;
  String? createdAt;
  String? updatedAt;

  CouponDetails({
    this.selectedDishes,
    this.selectedCategories,
    this.selectedChefs,
    this.id,
    this.addedById,
    this.addedByType,
    this.discountType,
    this.couponCode,
    this.discountName,
    this.description,
    this.discountPercentage,
    this.maxCapPercentageOff,
    this.discountAmount,
    this.perUserLimit,
    this.globalUserLimit,
    this.appliesTo,
    this.userEligibility,
    this.isFirstOrder,
    this.isMinimumOrderAmount,
    this.minimumSpendAmount,
    this.isSelectedDishes,
    this.isSelectedCategories,
    this.isSelectedChefs,
    this.startDate,
    this.endDate,
    this.isAutoApply,
    this.termsNotes,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  CouponDetails.fromJson(Map<String, dynamic> json) {
    selectedDishes = json['selected_dishes'];
    selectedCategories = json['selected_categories'];
    selectedChefs = json['selected_chefs'];
    id = json['id'];
    addedById = json['added_by_id'];
    addedByType = json['added_by_type'];
    discountType = json['discount_type'];
    couponCode = json['coupon_code'];
    discountName = json['discount_name'];
    description = json['description'];
    discountPercentage = json['discount_percentage'] != null
        ? num.tryParse(json['discount_percentage'].toString())
        : null;
    maxCapPercentageOff = json['max_cap_percentage_off'] != null
        ? num.tryParse(json['max_cap_percentage_off'].toString())
        : null;
    discountAmount = json['discount_amount'];
    perUserLimit = json['per_user_limit'];
    globalUserLimit = json['global_user_limit'];
    appliesTo = json['applies_to'];
    userEligibility = json['user_eligibility'];
    isFirstOrder = json['is_first_order'];
    isMinimumOrderAmount = json['is_minimum_order_amount'];
    minimumSpendAmount = json['minimum_spend_amount'];
    isSelectedDishes = json['is_selected_dishes'];
    isSelectedCategories = json['is_selected_categories'];
    isSelectedChefs = json['is_selected_chefs'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    isAutoApply = json['is_auto_apply'];
    termsNotes = json['terms_notes'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['selected_dishes'] = this.selectedDishes;
    data['selected_categories'] = this.selectedCategories;
    data['selected_chefs'] = this.selectedChefs;
    data['id'] = this.id;
    data['added_by_id'] = this.addedById;
    data['added_by_type'] = this.addedByType;
    data['discount_type'] = this.discountType;
    data['coupon_code'] = this.couponCode;
    data['discount_name'] = this.discountName;
    data['description'] = this.description;
    data['discount_percentage'] = this.discountPercentage;
    data['max_cap_percentage_off'] = this.maxCapPercentageOff;
    data['discount_amount'] = this.discountAmount;
    data['per_user_limit'] = this.perUserLimit;
    data['global_user_limit'] = this.globalUserLimit;
    data['applies_to'] = this.appliesTo;
    data['user_eligibility'] = this.userEligibility;
    data['is_first_order'] = this.isFirstOrder;
    data['is_minimum_order_amount'] = this.isMinimumOrderAmount;
    data['minimum_spend_amount'] = this.minimumSpendAmount;
    data['is_selected_dishes'] = this.isSelectedDishes;
    data['is_selected_categories'] = this.isSelectedCategories;
    data['is_selected_chefs'] = this.isSelectedChefs;
    data['start_date'] = this.startDate;
    data['end_date'] = this.endDate;
    data['is_auto_apply'] = this.isAutoApply;
    data['terms_notes'] = this.termsNotes;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}

class Chef {
  int? chefId;
  String? chefName;
  String? chefPhoto;
  List<String>? chefOperationDays;
  ChefOperationTime? chefOperationTime;

  Chef(
      {this.chefId,
      this.chefName,
      this.chefPhoto,
      this.chefOperationDays,
      this.chefOperationTime});

  Chef.fromJson(Map<String, dynamic> json) {
    chefId = json['chef_id'];
    chefName = json['chef_name'];
    chefPhoto = json['chef_photo'];
    chefOperationDays = json['chef_operation_days'].cast<String>();
    chefOperationTime = json['chef_operation_time'] != null
        ? new ChefOperationTime.fromJson(json['chef_operation_time'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['chef_id'] = this.chefId;
    data['chef_name'] = this.chefName;
    data['chef_photo'] = this.chefPhoto;
    data['chef_operation_days'] = this.chefOperationDays;
    if (this.chefOperationTime != null) {
      data['chef_operation_time'] = this.chefOperationTime!.toJson();
    }
    return data;
  }
}

class ChefOperationTime {
  int? id;
  String? startTime;
  String? endTime;

  ChefOperationTime({this.id, this.startTime, this.endTime});

  ChefOperationTime.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['start_time'] = this.startTime;
    data['end_time'] = this.endTime;
    return data;
  }
}

class Items {
  int? cartItemId;
  int? dishId;
  String? dishName;
  String? dishPhoto;
  int? quantity;
  int? servingSizeId;
  int? price;
  int? totalPrice;
  String? notes;

  Items(
      {this.cartItemId,
      this.dishId,
      this.dishName,
      this.dishPhoto,
      this.quantity,
      this.servingSizeId,
      this.price,
      this.totalPrice,
      this.notes});

  Items.fromJson(Map<String, dynamic> json) {
    cartItemId = json['cart_item_id'];
    dishId = json['dish_id'];
    dishName = json['dish_name'];
    dishPhoto = json['dish_photo'];
    quantity = json['quantity'];
    servingSizeId = json['serving_size_id'];
    price = json['price'];
    totalPrice = json['total_price'];
    notes = json['notes'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['cart_item_id'] = this.cartItemId;
    data['dish_id'] = this.dishId;
    data['dish_name'] = this.dishName;
    data['dish_photo'] = this.dishPhoto;
    data['quantity'] = this.quantity;
    data['serving_size_id'] = this.servingSizeId;
    data['price'] = this.price;
    data['total_price'] = this.totalPrice;
    data['notes'] = this.notes;
    return data;
  }
}

class CurrentAddress {
  int? id;
  String? addressText;
  Location? location;
  bool? isCurrent;

  CurrentAddress({this.id, this.addressText, this.location, this.isCurrent});

  CurrentAddress.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    addressText = json['address_text'];
    location = json['location'] != null
        ? new Location.fromJson(json['location'])
        : null;
    isCurrent = json['is_current'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['address_text'] = this.addressText;
    if (this.location != null) {
      data['location'] = this.location!.toJson();
    }
    data['is_current'] = this.isCurrent;
    return data;
  }
}

class Location {
  Crs? crs;
  String? type;
  List<double>? coordinates;

  Location({this.crs, this.type, this.coordinates});

  Location.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? new Crs.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates'].cast<double>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.crs != null) {
      data['crs'] = this.crs!.toJson();
    }
    data['type'] = this.type;
    data['coordinates'] = this.coordinates;
    return data;
  }
}

class Crs {
  String? type;
  Properties? properties;

  Crs({this.type, this.properties});

  Crs.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'] != null
        ? new Properties.fromJson(json['properties'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    if (this.properties != null) {
      data['properties'] = this.properties!.toJson();
    }
    return data;
  }
}

class Properties {
  String? name;

  Properties({this.name});

  Properties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    return data;
  }
}