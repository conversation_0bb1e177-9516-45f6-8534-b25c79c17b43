import 'package:db_eats/ui/deliverytime.dart';
import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Two factor Verification',
      theme: ThemeData(
        primarySwatch: Colors.grey,
        fontFamily: 'Inter',
      ),
      home: const Twofa(),
    );
  }
}

class Twofa extends StatefulWidget {
  final String email;
  final String phone;

  const Twofa({Key? key, this.email = "", this.phone = ""}) : super(key: key);

  @override
  State<Twofa> createState() => _TwofaState();
}

class _TwofaState extends State<Twofa> {
  int _selectedVerificationMethod = -1;

  String maskEmail(String email) {
    if (email.isEmpty) return "j****@gmail.com";
    var parts = email.split('@');
    if (parts.length != 2) return email;
    return "${parts[0][0]}****@${parts[1]}";
  }

  String maskPhone(String phone) {
    if (phone.isEmpty) return "+123****56";
    return "+${phone.substring(0, 3)}****${phone.substring(phone.length - 2)}";
  }

  void _handleVerificationSelection(int index) {
    setState(() {
      _selectedVerificationMethod = index;
    });

    if (index == 0) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => DeliveryTimeScreen(),
        ),
      );
    } else if (index == 1) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => DeliveryTimeScreen(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    String maskedEmail = maskEmail(widget.email);
    String maskedPhone = maskPhone(widget.phone);

    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
                padding: const EdgeInsets.only(left: 16.0, top: 16.0),
                child: Align(
                  alignment: Alignment.topLeft,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.close,
                          color: Colors.black, size: 20),
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      padding: const EdgeInsets.all(8),
                      constraints: const BoxConstraints(),
                      iconSize: 20,
                    ),
                  ),
                )),
            const SizedBox(height: 70),
            const Center(
              child: Text(
                "Verify if it's you",
                style: TextStyle(
                  fontSize: 24,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1F2122),
                ),
              ),
            ),
            const SizedBox(height: 18),
            const Center(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.0),
                child: Text(
                  "This device isn't recognized. For your security, choose how you want to verify.",
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF414346),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            const SizedBox(height: 35),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: _buildSimpleRadioOption(
                title: 'Verify with email',
                value: maskedEmail,
                index: 0,
              ),
            ),
            const SizedBox(height: 12),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: _buildSimpleRadioOption(
                title: 'Verify with phone number',
                value: maskedPhone,
                index: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleRadioOption({
    required String title,
    required String value,
    required int index,
  }) {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: const Color(0xFFF6F3EC),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFB9B6AD),
          width: 1,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () => _handleVerificationSelection(index),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: Radio<int>(
                value: index,
                groupValue: _selectedVerificationMethod,
                onChanged: (int? value) => _handleVerificationSelection(value!),
                activeColor: Colors.black,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
            Expanded(
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '$title ',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Inter',
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    TextSpan(
                      text: value,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: Color(0xFF1F2122),
                      ),
                    ),
                  ],
                ),
                softWrap: true,
                overflow: TextOverflow.visible,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
