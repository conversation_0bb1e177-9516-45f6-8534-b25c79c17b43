import 'dart:ffi';

import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:db_eats/widgets/navbar.dart';
import 'package:flutter/material.dart';

class ChefInfoPage extends StatefulWidget {
  final int id;

  const ChefInfoPage({super.key, required this.id});

  @override
  State<ChefInfoPage> createState() => _ChefInfoPageState();
}

class _ChefInfoPageState extends State<ChefInfoPage>
    with SingleTickerProviderStateMixin {
  final List<Map<String, dynamic>> _chefs = [
    {
      'name': 'Chef V<PERSON>wana<PERSON>',
      'image': 'assets/images/chef_10.png',
      'rating': '82%',
      'reviews': '(49)',
      'tags': ['Indian', 'Vegetarian', 'Casual', 'Healthy'],
      'availability': ['M', 'W', 'F'],
      'dishes': [
        {
          'name': 'Chicken Tandoori',
          'image': 'assets/images/chicken_tandoori.png',
        },
        {
          'name': 'Chicken Tikka',
          'image': 'assets/images/dish.png',
        },
        {
          'name': 'Chicken Biryani',
          'image': 'assets/images/dish_4.png',
        },
      ]
    },
    {
      'name': 'Chef Sandeep M.',
      'image': 'assets/images/chef_7.png',
      'rating': '82%',
      'reviews': '(49)',
      'tags': ['Indian', 'Vegetarian', 'Casual', 'Healthy'],
      'availability': ['T', 'Th', 'S'],
      'dishes': [
        {
          'name': 'Dal Makhani',
          'image': 'assets/images/dish_1.png',
        },
        {
          'name': 'Paneer Tikka',
          'image': 'assets/images/dish_3.png',
        },
        {
          'name': 'Vegetable Biryani',
          'image': 'assets/images/dish_4.png',
        },
      ]
    },
    {
      'name': 'Chef Vishwanathan C.',
      'image': 'assets/images/chef_10.png',
      'rating': '82%',
      'reviews': '(49)',
      'tags': ['Indian', 'Vegetarian', 'Casual', 'Healthy'],
      'availability': ['M', 'W', 'F'],
      'dishes': [
        {
          'name': 'Chicken Tandoori',
          'image': 'assets/images/chicken_tandoori.png',
        },
        {
          'name': 'Chicken Tikka',
          'image': 'assets/images/dish.png',
        },
        {
          'name': 'Chicken Biryani',
          'image': 'assets/images/dish_4.png',
        },
      ]
    },
  ];

  final List<String> _categories = [
    'Featured Items',
    'Main Dishes',
    'Sides',
    'Burgers'
  ];

  final List<Map<String, dynamic>> _featuredItems = [
    {
      'name': 'Chicken Tandoori',
      'image': 'assets/images/chicken_tandoori.png',
      'tags': ['Spicy', 'Grilled', 'High Protein'],
      'rating': '4.8',
      'reviews': '(123)',
      'price': 4.50,
      'servings': 2,
      'selected': false,
      'cooking_time': '35 mins',
      'distance': '1.5 KM',
    },
    {
      'name': 'Chicken Tikka',
      'image': 'assets/images/dish.png',
      'tags': ['Mild', 'Grilled', 'Low Carb'],
      'rating': '4.7',
      'reviews': '(98)',
      'price': 4.50,
      'servings': 2,
      'selected': false,
      'cooking_time': '30 mins',
      'distance': '1.5 KM',
    },
    {
      'name': 'Chicken Biryani',
      'image': 'assets/images/dish_4.png',
      'tags': ['Medium Spicy', 'Rice', 'Traditional'],
      'rating': '4.9',
      'reviews': '(156)',
      'price': 4.50,
      'servings': 2,
      'selected': false,
      'cooking_time': '45 mins',
      'distance': '1.5 KM',
    },
  ];

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);
  }

  int _selectedIndex = 0;

  // List of page widgets to show based on selected tab
  final List<Widget> _pages = [
    const Center(child: Text('Home Page')),
    const Center(child: Text('Orders Page')),
    const Center(child: Text('Catering Page')),
    const Center(child: Text('Messages Page')),
    const Center(child: Text('Account Page')),
  ];
  void _onNavItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  void _openCart() {
    // Handle cart open action
    print('Opening cart');
    Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CartPage(),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC), // Set body background color
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text("Chef Info"),
        backgroundColor: Colors.white, // Optional: app bar background
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Padding(
            padding:
                const EdgeInsets.only(left: 14, right: 14, top: 20, bottom: 0),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              SizedBox(
                height: 147, // extra height to allow avatar to sit below image
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    // Background image
                    Container(
                      width: double.infinity,
                      height: 114,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        image: const DecorationImage(
                          image: AssetImage('assets/images/dish_6.png'),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    // Avatar shifted below using FractionalTranslation
                    Align(
                      alignment: Alignment.bottomRight,
                      child: FractionalTranslation(
                        translation: const Offset(-0.21,
                            -0.03), // shift downward by 50% of its height
                        child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white,
                              width: 5.5,
                            ),
                          ),
                          child: const CircleAvatar(
                            radius: 40,
                            backgroundImage:
                                AssetImage('assets/images/chef_10.png'),
                            backgroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _chefs[widget.id]['name'],
                          style: const TextStyle(
                            fontFamily: 'Inter',
                            fontSize: 28,
                            fontWeight: FontWeight.w600,
                            height: 1.24,
                            letterSpacing: -1,
                            color: Color(0xFF1F2122),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 14),

              // Chef metrics
              Row(
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                    constraints: const BoxConstraints(
                      minWidth: 97,
                      minHeight: 40,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF6F3EC),
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: const Color(0xFFB9B6AD)),
                    ),
                    child: Row(
                      children: const [
                        Icon(Icons.access_time,
                            size: 16, color: Color(0xFF414346)),
                        SizedBox(width: 4),
                        Text(
                          "35 mins",
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            height: 24 / 16,
                            color: Color(0xFF1F2122),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                    constraints: const BoxConstraints(
                      minWidth: 61,
                      minHeight: 40,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF6F3EC),
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: const Color(0xFFB9B6AD)),
                    ),
                    child: Row(
                      children: const [
                        Icon(Icons.star, size: 16, color: Color(0xFF414346)),
                        SizedBox(width: 4),
                        Text(
                          "4.9",
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            height: 24 / 16,
                            color: Color(0xFF1F2122),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                    constraints: const BoxConstraints(
                      minWidth: 87,
                      minHeight: 40,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF6F3EC),
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: const Color(0xFFB9B6AD)),
                    ),
                    child: Row(
                      children: const [
                        Icon(Icons.location_on_outlined,
                            size: 17, color: Color(0xFF414346)),
                        SizedBox(width: 4),
                        Text(
                          "1.5 KM",
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            height: 24 / 16,
                            color: Color(0xFF1F2122),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    width: 40,
                    height: 40,
                    constraints: const BoxConstraints(
                      minWidth: 40,
                      minHeight: 40,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.favorite_border,
                          size: 20, color: Color(0xFF1F2122)),
                      onPressed: () {},
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Cuisine tags
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                child: Wrap(
                  alignment: WrapAlignment.start, // default but you can add i
                  runSpacing: 2,
                  children: ['North Indian', 'Punjabi', 'Clean', 'Desserts']
                      .map((tag) {
                    return Container(
                      padding: const EdgeInsets.only(right: 12),
                      child: Text(
                        tag,
                        style: const TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: Color(0xFF414346),
                          height: 20 / 14,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
              const SizedBox(height: 10),
              // Chef description
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                child: Text(
                  "I grew up in Punjab, where I learned to cook Punjabi dishes from my mother. Cooking has always been a part of my life. I am super excited to introduce you to authentic Punjabi cuisine through my weekly menu. I hope you enjoy my dishes!",
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    letterSpacing: 0,
                    color: Color(0xFF414346),
                  ),
                  textAlign: TextAlign.justify,
                ),
              ),

              const SizedBox(height: 16),
              Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 4.0, horizontal: 2),
                    child: Image.asset(
                      'assets/icons/calender_3.png',
                      width: 20,
                      height: 18,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Text(
                    'Open 9AM-8PM',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Text(
                    'M,W,F,Sat,Sun',
                    style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        letterSpacing: 1),
                  ),
                ],
              ),

              const SizedBox(height: 14),
            ]),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 14, top: 10, bottom: 14),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Category tabs
                SizedBox(
                  height: 30,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 0),
                    children: _categories.asMap().entries.map((entry) {
                      final index = entry.key;
                      final category = entry.value;
                      final isSelected = _tabController.index == index;

                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _tabController.animateTo(index);
                          });
                        },
                        child: Container(
                          margin: const EdgeInsets.only(right: 8),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 0),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? const Color(0xFFB9B6AD)
                                : const Color(0xFFE1DDD5),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Center(
                            child: Text(
                              category,
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 0.08,
                                color: isSelected
                                    ? const Color(0xFF1F2122)
                                    : const Color(0xFF1F2122),
                              ),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
                const SizedBox(height: 8),

                // Filters button
                Padding(
                  padding:
                      const EdgeInsets.only(top: 16,bottom: 16,right: 14),
                  child: Container(
                    height: 44,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24),
                      border: Border.all(color: const Color(0xFF1F2122)),
                    ),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(24),
                      onTap: () {
                        // Handle filter action
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: const [
                          Icon(Icons.tune, size: 16, color: Color(0xFF1F2122)),
                          SizedBox(width: 8),
                          Text(
                            "View Filters",
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Section title
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                  child: Text(
                    "Featured Items",
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1F2122),
                    ),
                  ),
                ),
                const SizedBox(height: 8),

                // Dish items
                SizedBox(
                  height: 335,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.only(right: 16),
                    children: _featuredItems
                        .map((dish) => Container(
                              width: 280,
                              margin: const EdgeInsets.only(right: 16),
                              child: _buildDishCard(dish),
                            ))
                        .toList(),
                  ),
                ),

                SizedBox(
                  height: 18,
                ),

                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                  child: Text(
                    "Main Dishes",
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1F2122),
                    ),
                  ),
                ),
                const SizedBox(height: 8),

                // Dish items
                SizedBox(
                  height: 345,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.only(right: 16),
                    children: _featuredItems
                        .map((dish) => Container(
                              width: 310,
                              margin: const EdgeInsets.only(right: 16),
                              child: _buildDishCard(dish),
                            ))
                        .toList(),
                  ),
                ),

                SizedBox(
                  height: 18,
                ),

                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                  child: Text(
                    "Sides",
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1F2122),
                    ),
                  ),
                ),
                const SizedBox(height: 8),

                // Dish items
                SizedBox(
                  height: 345,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.only(right: 16),
                    children: _featuredItems
                        .map((dish) => Container(
                              width: 310,
                              margin: const EdgeInsets.only(right: 16),
                              child: _buildDishCard(dish),
                            ))
                        .toList(),
                  ),
                ),

                SizedBox(
                  height: 18,
                ),

                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                  child: Text(
                    "Burgers",
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1F2122),
                    ),
                  ),
                ),
                const SizedBox(height: 8),

                // Dish items
                SizedBox(
                  height: 345,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.only(right: 16),
                    children: _featuredItems
                        .map((dish) => Container(
                              width: 310,
                              margin: const EdgeInsets.only(right: 16),
                              child: _buildDishCard(dish),
                            ))
                        .toList(),
                  ),
                ),

                const SizedBox(height: 20),
                // _buildBottomBar
              ],
            ),
          ),
        ]),
      ),
      // bottomNavigationBar:CustomBottomNavBar(
      //   currentIndex: _selectedIndex,
      //   onTap: _onNavItemTapped,
      // ),
      floatingActionButton: CartFloatingActionButton(
        itemCount: 2,
        onPressed: _openCart,
      ),
    );
  }
}

Widget _buildDishCard(Map<String, dynamic> dish) {
  String rating = 90.toString();
  return Container(
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.05),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min, // Ensures column only takes needed space
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
          child: Image.asset(
            dish['image'],
            height: 205, // Increased image height
            width: double.infinity,
            fit: BoxFit.cover,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
              horizontal: 16, vertical: 14), // Increased vertical padding
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                dish['name'],
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1F2122),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment:
                    CrossAxisAlignment.end, // Align items at bottom
                children: [
                  // Left Column (Tags and Price)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Tags Row
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: const Color.fromRGBO(225, 227, 230, 1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                "$rating Servings",
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter',
                                  letterSpacing: 0.2,
                                ),
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: const Color.fromRGBO(225, 227, 230, 1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Image.asset(
                                    'assets/icons/thump.png',
                                    width: 11,
                                    height: 10,
                                    color: Colors.black54,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    "$rating%",
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter',
                                    ),
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    "($rating)",
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20), // Increased spacing
                        // Price
                Text.rich(
  TextSpan(
    children: [
      TextSpan(
        text: '\u0024', // Standard dollar sign
        style: TextStyle(
          fontFamily: 'Inter',
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Color(0xFF1F2122),
        ),
      ),
      TextSpan(
        text: dish['price'].toStringAsFixed(2),
        style: TextStyle(
     
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Color(0xFF1F2122),
        ),
      ),
    ],
  ),
),

                      ],
                    ),
                  ),
                  // Right Column (Add Icon)
                  Container(
                    alignment: Alignment.bottomRight,
                    child: Image.asset(
                      'assets/icons/add.png',
                      width: 32,
                      height: 32,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
