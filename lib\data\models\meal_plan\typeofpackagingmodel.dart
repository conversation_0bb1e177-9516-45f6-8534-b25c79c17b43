class TypeOfPackagingModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  TypeOfPackagingModel({this.status, this.message, this.statusCode, this.data});

  TypeOfPackagingModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<PackagingTypes>? packagingTypes;

  Data({this.packagingTypes});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['packaging_types'] != null) {
      packagingTypes = <PackagingTypes>[];
      json['packaging_types'].forEach((v) {
        packagingTypes!.add(new PackagingTypes.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.packagingTypes != null) {
      data['packaging_types'] =
          this.packagingTypes!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PackagingTypes {
  int? id;
  String? name;

  PackagingTypes({this.id, this.name});

  PackagingTypes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}
