{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981be09ec3af090c293ea3da796f811372", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810cd72872cd2fd78afaeb793b214757d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987bc5535718ccdff782c7fba91783ee89", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cb47282186563331745c391472a7f2a3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987bc5535718ccdff782c7fba91783ee89", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ccb9e539c26399e6e3317bfa2bce1e2e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f59754a4d6e3886742b94cf41bb6bae8", "guid": "bfdfe7dc352907fc980b868725387e9835a79a7188f38faca69b3084e8e1dc18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813906b1a16d433394acb7d0734f2097d", "guid": "bfdfe7dc352907fc980b868725387e98e1b564e65e64c0c1a80c7c67cb1295ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98578be75d14c45531890fea03ff34637b", "guid": "bfdfe7dc352907fc980b868725387e98d91bb873ac67c1c3d1741d5b2316c206", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ae85a1786e50d5def7b63f6f49abe26", "guid": "bfdfe7dc352907fc980b868725387e98d9e9bb209ae22c63dccb7150b25e82bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a6b07cf5c6b168bd5079e1779d86dc8", "guid": "bfdfe7dc352907fc980b868725387e9899f54d068b4960de068b15ec25ab2f35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98485e17684c47628fe95f712e2b8b45b5", "guid": "bfdfe7dc352907fc980b868725387e98710c06a66914d7738f1851ee713512dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca4bfd5ee1ffae5d1f21b1592e850bbe", "guid": "bfdfe7dc352907fc980b868725387e98be80309caa61bd9a4e1a141d234cbae9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98426798eb65843f8de91940da0a99d7c3", "guid": "bfdfe7dc352907fc980b868725387e981fd1c324c318a7ad0bbe5f0d68526e03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897f215327d01e292465faee1fde59f46", "guid": "bfdfe7dc352907fc980b868725387e9859f2d0cd2a5f73b48c85c2f269bf2e24", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e91dadf5cc839c9bf6a7e45841a7470f", "guid": "bfdfe7dc352907fc980b868725387e98df0c91e52fda8c311d600d0bb617da61", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfe3603db217003bc20f8400731bc7ed", "guid": "bfdfe7dc352907fc980b868725387e98a8d35ab28e82c5d3d0e76cf814826f46", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d28b0e52cd4db162a6dcb138ae0c29e", "guid": "bfdfe7dc352907fc980b868725387e981a9f9352366e7fede795fe39dad59851", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cd4bc581811ae42cc5a1c23054bc4d7", "guid": "bfdfe7dc352907fc980b868725387e989a614573cb50ce94c02ac056df184914", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98207222a076e11076cc2a4685ee847bc7", "guid": "bfdfe7dc352907fc980b868725387e985b266aa428f555fb6a60bc22f09f7617", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc3fbc2902644cfeeb971ea5029f675c", "guid": "bfdfe7dc352907fc980b868725387e98368a3fb93afd09a363b84c9095dfe6e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dafd7ecd6d5f2bdaf25667958a2a2d3", "guid": "bfdfe7dc352907fc980b868725387e98c2f8253477f682a0a943f3cf10f521ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880898dc8fb6de46f0bd9dea13f1507bf", "guid": "bfdfe7dc352907fc980b868725387e9874b696088046d200f2f8118c685413cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3d272cd771b6e919f87306bfe6ae075", "guid": "bfdfe7dc352907fc980b868725387e982a8086603c0c3904e30cfe980a5e3129", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3f51fa3e052555a3b00b41870901767", "guid": "bfdfe7dc352907fc980b868725387e980a8ec3d6072e753769546441810ea5d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c32aae221f68c4468dd8b07f282a3cab", "guid": "bfdfe7dc352907fc980b868725387e98dab7b4d02db6a97e12431f24fc437bb4", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874ccb88cb84fd8af466c45bfb4a5015c", "guid": "bfdfe7dc352907fc980b868725387e98d15386fb059ac54b0c6da9a5f7ecf31c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c6f2bbd3eae5bfd54fb4138b6eea88c", "guid": "bfdfe7dc352907fc980b868725387e9886e6bb7cdd2e611cfe72a85be422cefd", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a4b7bb0a4d065b8b5df6dd66883bae67", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984aa81d38ea13c725b78e50cebdebff66", "guid": "bfdfe7dc352907fc980b868725387e98474ba01d02ff679f5546581ff832a00c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b7bbd1d4761504dcbb436c611ca1902", "guid": "bfdfe7dc352907fc980b868725387e9821f55de6efcf3ddee0deef16791c6bc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98793d100f9774e76b6d5974c4a4593c52", "guid": "bfdfe7dc352907fc980b868725387e986e312bdb6cb8a75112cc5d61b5b90c23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b4fea024913093343a1a7311dcd3095", "guid": "bfdfe7dc352907fc980b868725387e9801139eb19aeac11f49d53f6e13b5e4aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980970bbd708492fc40105c3ffd4cbab35", "guid": "bfdfe7dc352907fc980b868725387e98b38fcd2336e7a078b8429763c91bd177"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be5b50dfb8d7af7e296224031bb95d08", "guid": "bfdfe7dc352907fc980b868725387e98603ed4ad081834d3873af75e801ea127"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820ec7938e10a153095bb431c862969b4", "guid": "bfdfe7dc352907fc980b868725387e981e2eb833c32dd2b937c37939806984bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98062f6299c0252844c4f2a8ad49f5db9c", "guid": "bfdfe7dc352907fc980b868725387e980b2734c739bfb8cbef2beb9c8784fabf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98491b20b9f7c05e2e7157f67d927fddf7", "guid": "bfdfe7dc352907fc980b868725387e9804350ee279643486e9e59d369e88bbad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab193588e18ff0489b9acd13bfc6ee39", "guid": "bfdfe7dc352907fc980b868725387e98e500240a62a81ba945928997e7f8d8f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98761a781ef8760dba6652252e2dc35c07", "guid": "bfdfe7dc352907fc980b868725387e98b53b86ea527919e61ace61082d7ce717"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c0cc2655b66c3c6fb7ab80bc7b07807", "guid": "bfdfe7dc352907fc980b868725387e9855df6c063dabe4d5a744d5b380ee13a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb6c3c349ab5891e073da36c39422b17", "guid": "bfdfe7dc352907fc980b868725387e98ae4f15039aa9c8cc6a3f4837f81458e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d921677739742407e858dfc1c0c31c", "guid": "bfdfe7dc352907fc980b868725387e98a90cee6ab0412b5ad1292238d35cf103"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981de71247c8bb8660a09f7f6ab09351e0", "guid": "bfdfe7dc352907fc980b868725387e98263c2d0ddb32c47e4f91e16449dc5cbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4b188bb8213eaec56fab2064027767d", "guid": "bfdfe7dc352907fc980b868725387e98c5292a075e02a2f4ebd2747042324975"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3a9145830a7967f6b1918cba944c429", "guid": "bfdfe7dc352907fc980b868725387e9851e37df58f552377c9b683e03afd2c73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0bafbbe40b4d8c42604048999ac985d", "guid": "bfdfe7dc352907fc980b868725387e989ff7c33ae0bf005d395ab882709ed0d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9d6b7d468974d96ceb1152feba5a115", "guid": "bfdfe7dc352907fc980b868725387e988a4050e7c729f01189e0ae315cb0045f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7de9e53c08b5bc776f6620bb79c9120", "guid": "bfdfe7dc352907fc980b868725387e9803fb20656d3cbae2241f19396ca7a89c"}], "guid": "bfdfe7dc352907fc980b868725387e98bad776054f6f4148962006496a251b3e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98881733260a9dadb34e0a841908187482"}], "guid": "bfdfe7dc352907fc980b868725387e988c04f49bebf954ea596fdcc6406b81e9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985d73e9db0ea170ef461369f127c5b642", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98bf5442a5261c2aa75f250faa20dabcd8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}