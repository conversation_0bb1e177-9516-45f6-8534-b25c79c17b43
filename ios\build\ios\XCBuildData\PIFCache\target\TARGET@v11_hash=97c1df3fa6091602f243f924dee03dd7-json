{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9835c29fcd64451267a82f462947b16d7f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6da72224a0002e2c8d4ad6a4140a101", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98997a972174eafcb79cffbcefd67a0cab", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cab3693a69eb1c553c791758183685f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98997a972174eafcb79cffbcefd67a0cab", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f480631d57f584ae82569893436b3d19", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ba916014f738db90e557517e0f7d855b", "guid": "bfdfe7dc352907fc980b868725387e98faa2d77c0ef7eb87f7f8421e51c1b40a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e3dac75ef5081293a3cce12f493338", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989151ca3941ebf2704e95256e87a1970f", "guid": "bfdfe7dc352907fc980b868725387e98421ae3c3425f12162bb95608984e2dab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98225d8849183384031f3fd553366383a1", "guid": "bfdfe7dc352907fc980b868725387e98b1129dd415ba4f06f295293352794aa7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832e620814dc2d3c2e76ffdfdbb857f6f", "guid": "bfdfe7dc352907fc980b868725387e98c4d6312cb67f31d7f6f6831dd281e8b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dffa96b9cefe150749c1ac39f83736f", "guid": "bfdfe7dc352907fc980b868725387e98f04a5c9a9cc81bfcfa6aaed0b00b6c3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874c2cbd3c834314b9597bb66450b87be", "guid": "bfdfe7dc352907fc980b868725387e9843b79fcddec6fa700bb0007202549f98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b51a91ca2f729fc4f05b6a52a94c2011", "guid": "bfdfe7dc352907fc980b868725387e98c352e5d7dbfbcf376cece1780e77fb1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c88fd4a02606be6886b09cfffa6c5c23", "guid": "bfdfe7dc352907fc980b868725387e980e3bf0955a353ef83519ce8948a63fc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982443f10d78bb6321b5678d9c9a8b01fb", "guid": "bfdfe7dc352907fc980b868725387e982fba501f5a0df4030b4a47f567f7966d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b62609c6fd3ddcebfe7beedf89b283", "guid": "bfdfe7dc352907fc980b868725387e985f0b6bcef15399d61fa1794fdb818de4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803712d87174bf64ac509883eb0ae5364", "guid": "bfdfe7dc352907fc980b868725387e98abea775179281a469ff2dd1142cb783c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e368118d1b2939ff6a70dd091230b9af", "guid": "bfdfe7dc352907fc980b868725387e988990c98018a3c452a437cf64f7a7b3fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e052d2390fe57ab0179447ba47f9049e", "guid": "bfdfe7dc352907fc980b868725387e98b63102342437207e7dcb551a084a95bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6c406384748e40396c6c7105dcc0309", "guid": "bfdfe7dc352907fc980b868725387e98630bd2be35e33853378d0faf1591cb1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c44a92052e9e36fab9c4396b00a6a5a", "guid": "bfdfe7dc352907fc980b868725387e9864d155bcb5d32ec1f6dcd38e047c4c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98785cb41ea4fee0568e7423fca43f856b", "guid": "bfdfe7dc352907fc980b868725387e98a43972be6129f885eea405f17f739dcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6a11d2756caaf16ba3feb95d1398fa5", "guid": "bfdfe7dc352907fc980b868725387e98c448602647b7c43f19fc7f2b0a19882c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98665ecd67e24dd5d416fdcdf74f114fd9", "guid": "bfdfe7dc352907fc980b868725387e98dfaef89466e79b28a06fbf0d65f3fbaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acfcdb50c23b87a23dac78ccd330882c", "guid": "bfdfe7dc352907fc980b868725387e9838606c9f15f987693f19420c77bf380a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98858b47e377b4e6a24c4f21c7d73cbbe0", "guid": "bfdfe7dc352907fc980b868725387e988957ceb19237fd9d69b8a1d6b4cf5425"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c74996ad5e6a96eca7963b2ca9d61c14", "guid": "bfdfe7dc352907fc980b868725387e98e15821a1a930d75a5f3933f083e375fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce7325fdc02273c926a14ad1b4e5cd8c", "guid": "bfdfe7dc352907fc980b868725387e9834028e19953e0b1ed245f3913ae35704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984eb2e46626151a84eeed7cac4a778b44", "guid": "bfdfe7dc352907fc980b868725387e9834aef25fb16e03a76b8bbf16d245c607"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988da82174abc400dd89d2adab2b2a3503", "guid": "bfdfe7dc352907fc980b868725387e985546f740b510dd60f5bc27de71daf43a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985040f4711ddbb4125d43717611eb7599", "guid": "bfdfe7dc352907fc980b868725387e9837f0f173397389d1816969e4e270046a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b9385db1e01666923fa4dbb9499d8cc", "guid": "bfdfe7dc352907fc980b868725387e98610dd3c4324e4d24978b8cd7e06bebc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3f55061b1f72b15b3f74e8ed37a1376", "guid": "bfdfe7dc352907fc980b868725387e9847c83ba16e50506728ebf97d34be80b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd4e7b0a8979c8734353527f62b52e65", "guid": "bfdfe7dc352907fc980b868725387e9894d77dc19091448c9de21434a0e30f0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345c98b47bb00c237ae123333ab89a33", "guid": "bfdfe7dc352907fc980b868725387e983180877e88a88e10d1a87c3cbf76b203"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa76ce9d8cdb285959fee4036df126e7", "guid": "bfdfe7dc352907fc980b868725387e98a0247398fbdb89c3988b46155d18c067"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba268a1ff13af6da4c27fdae77bfd761", "guid": "bfdfe7dc352907fc980b868725387e989a4819ab88a56141908e0669a1910c8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982caf6e2338f29e9ea4cf5f40fd760893", "guid": "bfdfe7dc352907fc980b868725387e982e29b240ad071385cb99642f4151739c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980601e494a4cbc41008c24347a24b932c", "guid": "bfdfe7dc352907fc980b868725387e988c2c14f49e3b26c79b06e7b8b6101921"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982deff7e2a00abf753572f2ec4630cb29", "guid": "bfdfe7dc352907fc980b868725387e98922e57193746f7f5fdd4494b90d735bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98105ab1e7da9d5c845a85b8a2c96b04c8", "guid": "bfdfe7dc352907fc980b868725387e98ba3fb8295e51139e9cce240a1e715d37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811a1bbd2e9c8d1ee5cb4c5d8ce01b3ef", "guid": "bfdfe7dc352907fc980b868725387e9874987f8de6c5b4c86bf766cadd18dedb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0c9c766d6fe787a908bed61257f2775", "guid": "bfdfe7dc352907fc980b868725387e9853de38235b70c71345bc88a3f70fb6ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98177309c3c908dc379b92453ed9a024ac", "guid": "bfdfe7dc352907fc980b868725387e98576d77e91596b9da0d88acba8efaf0c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878e220fe7ffad3e1fe1b33c467d21862", "guid": "bfdfe7dc352907fc980b868725387e989f90dd5960fdec2d95b170b382f26e6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fddbb7a8366057fe4db4ecd0ffdc61e8", "guid": "bfdfe7dc352907fc980b868725387e98d259083229b4eed28de4a364a94222ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cecd5253aa17e35b4c17437a0160aa5", "guid": "bfdfe7dc352907fc980b868725387e98f907c5a490d4761e6ce9b01cae1c968a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a173e66b351b5fc8d6c92f6cb0680cf", "guid": "bfdfe7dc352907fc980b868725387e980d3613196654dfcf6a896937bb373434"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c8bdc4b81b7b639e8d6add9e2fe805c", "guid": "bfdfe7dc352907fc980b868725387e9818cf0c36a9406704d29f6d45ba79c104"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0cdd43942249280f694a8a2c6ddb36b", "guid": "bfdfe7dc352907fc980b868725387e98d09bffc616c2cc7ff369d082d9774c93"}], "guid": "bfdfe7dc352907fc980b868725387e980055fbf3a11bc15129d28ebbe5cc1480", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e987ead9e15fc09a570554fa963eed1137d"}], "guid": "bfdfe7dc352907fc980b868725387e98da9cd099e7190758cfc42156c0f4e548", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8defae1b39d33d1c1b6a7ec3e8cf51c", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e987fd8694c1d36b88c99f78feb0d04dd25", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}