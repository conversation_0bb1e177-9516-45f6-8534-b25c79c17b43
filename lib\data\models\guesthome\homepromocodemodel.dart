class HomePromoCodesModel {
  bool? status;
  String? message;
  int? statusCode;
  HomePromoCodeData? data;

  HomePromoCodesModel({this.status, this.message, this.statusCode, this.data});

  HomePromoCodesModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null
        ? new HomePromoCodeData.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class HomePromoCodeData {
  int? total;
  int? page;
  int? limit;
  List<PromoCodes>? promoCodes;

  HomePromoCodeData({this.total, this.page, this.limit, this.promoCodes});

  HomePromoCodeData.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    page = json['page'];
    limit = json['limit'];
    if (json['promo_codes'] != null) {
      promoCodes = <PromoCodes>[];
      json['promo_codes'].forEach((v) {
        promoCodes!.add(new PromoCodes.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['total'] = this.total;
    data['page'] = this.page;
    data['limit'] = this.limit;
    if (this.promoCodes != null) {
      data['promo_codes'] = this.promoCodes!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PromoCodes {
  int? discountType;
  String? couponCode;
  String? discountName;
  String? description;
  String? discountPercentage;
  String? maxCapPercentageOff;
  Null? discountAmount;

  PromoCodes(
      {this.discountType,
      this.couponCode,
      this.discountName,
      this.description,
      this.discountPercentage,
      this.maxCapPercentageOff,
      this.discountAmount});

  PromoCodes.fromJson(Map<String, dynamic> json) {
    discountType = json['discount_type'];
    couponCode = json['coupon_code'];
    discountName = json['discount_name'];
    description = json['description'];
    discountPercentage = json['discount_percentage'];
    maxCapPercentageOff = json['max_cap_percentage_off'];
    discountAmount = json['discount_amount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['discount_type'] = this.discountType;
    data['coupon_code'] = this.couponCode;
    data['discount_name'] = this.discountName;
    data['description'] = this.description;
    data['discount_percentage'] = this.discountPercentage;
    data['max_cap_percentage_off'] = this.maxCapPercentageOff;
    data['discount_amount'] = this.discountAmount;
    return data;
  }
}
