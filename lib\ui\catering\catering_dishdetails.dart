import 'package:db_eats/bloc/catering_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/catering/cateringdishdetailsmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CateringDishdetails extends StatefulWidget {
  final int cateringRequestId;
  final String dishId;

  const CateringDishdetails(
      {required this.cateringRequestId, required this.dishId, super.key});

  @override
  State<CateringDishdetails> createState() => _CateringDishdetailsState();
}

class _CateringDishdetailsState extends State<CateringDishdetails> {
  int quantity = 1;

  @override
  void initState() {
    super.initState();
    // Load dish details when page opens
    context
        .read<CateringBloc>()
        .add(GetCateringDishDetails({
          "dish_id": widget.dishId,
          "catering_id": widget.cateringRequestId,
        }));
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CateringBloc, CateringState>(
      listener: (context, state) {
        if (state is AddDishToCateringCartSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              duration: const Duration(seconds: 2),
            ),
          );
          Navigator.pop(context);
        }
      },
      child: BlocBuilder<CateringBloc, CateringState>(
        buildWhen: (previous, current) =>
            current is! AddDishToCateringCartLoading,
        builder: (context, state) {
          if (state is GetCateringDishDetailsLoading) {
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          }          if (state is GetCateringDishDetailsSuccess) {
            final dishData = Initializer.cateringDishDetailModel.data?.dish;
            final isAdded = Initializer.cateringDishDetailModel.data?.isAdded ?? false;

            return Scaffold(
              backgroundColor: Colors.white,
              appBar: AppBar(
                backgroundColor: Colors.white,
                elevation: 0,
                automaticallyImplyLeading: false,
                actions: [
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.black),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              body: _buildDishDetailContent(context, dishData, isAdded),
            );
          }

          if (state is GetCateringDishDetailsFailed) {
            return Scaffold(
              body: Center(
                child: Text(
                  state.message,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: forteen,
                    color: Color(0xFF414346),
                  ),
                ),
              ),
            );
          }

          return const Scaffold(
            body: Center(
              child: Text('Something went wrong'),
            ),
          );
        },
      ),
    );
  }
  Widget _buildDishDetailContent(BuildContext context, CateringDish? dishData, bool isAdded) {
    if (dishData == null) return const SizedBox.shrink();

    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(left: sixteen, right: sixteen),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Dish Image
                  ClipRRect(
                    borderRadius: BorderRadius.circular(sixteen / 2),
                    child: Image.network(
                      ServerHelper.imageUrl + (dishData.photo ?? ''),
                      width: double.infinity,
                      height: ten * 16 + forteen,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: double.infinity,
                          height: ten * 16 + forteen,
                          color: Colors.grey[300],
                          child: Icon(Icons.restaurant_menu, size: ten * 5),
                        );
                      },
                    ),
                  ),
                  SizedBox(height: eighteen),

                  // Title
                  Text(
                    dishData.title ?? '',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: twenty,
                    ),
                  ),
                  SizedBox(height: forteen),

                  // Price
                  Text(
                    '\$${dishData.price ?? '0.00'}',
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                    ),
                  ),
                  SizedBox(height: twentyFour + ten),

                  // Description
                  Text(
                    'Description',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: forteen,
                    ),
                  ),
                  SizedBox(height: sixteen / 4),
                  Text(
                    dishData.description ?? '',
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      color: Color(0xFF1F2122),
                      letterSpacing: -0.1,
                      height: 1.35,
                    ),
                  ),
                  SizedBox(height: twenty),

                  // Ingredients
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Ingredients:',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                          color: Color(0xFF1F2122),
                        ),
                      ),
                      SizedBox(width: sixteen / 2),
                      Expanded(
                        child: Text(
                          dishData.ingredients ?? '',
                          style: const TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            fontSize: 14,
                            color: Color(0xFF1F2122),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: sixteen),

                  // Info Containers
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      // Servings
                      _buildInfoContainer(
                        'assets/icons/servings.png',
                        '${dishData.servingSize ?? 0} servings',
                      ),
                      SizedBox(width: sixteen / 2),

                      // Spice Level
                      if (dishData.spiceLevel != null)
                        _buildInfoContainer(
                          'assets/icons/chili.png',
                          dishData.spiceLevel?.name ?? '',
                        ),

                      if (dishData.spiceLevel != null) const SizedBox(width: 8),

                      // Dietary
                      if (dishData.dietary != null)
                        _buildInfoContainer(
                          'assets/icons/organic.png',
                          dishData.dietary?.name ?? '',
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        Container(
          padding: EdgeInsets.only(
              left: sixteen,
              right: sixteen,
              top: eighteen,
              bottom: eighteen + ten + ten),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, -3),
              ),
            ],
          ),
          child: Row(
            children: [
              // Quantity Selector
              Expanded(
                flex: 2,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    _buildQuantityButton(Icons.remove, _decrementQuantity),
                    SizedBox(width: sixteen),
                    Text(
                      '$quantity',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w600,
                        fontSize: forteen,
                      ),
                    ),
                    SizedBox(width: sixteen),
                    _buildQuantityButton(Icons.add, _incrementQuantity),
                  ],
                ),
              ),

              // Add to Catering Button
              Expanded(
                flex: 3,
                child: BlocBuilder<CateringBloc, CateringState>(
                  buildWhen: (previous, current) =>
                      current is AddDishToCateringCartLoading ||
                      current is AddDishToCateringCartSuccess,
                  builder: (context, state) {
                    final isLoading = state is AddDishToCateringCartLoading;
                    return ElevatedButton(
                      onPressed:
                          isLoading ? null : () => _onAddToCatering(dishData),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF1F2122),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(32),
                        ),
                        padding: EdgeInsets.symmetric(
                            horizontal: twentyFour, vertical: twelve),
                      ),                      child: isLoading
                          ? SizedBox(
                              height: twenty,
                              width: twenty,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              isAdded ? 'Update Catering' : 'Add to Catering',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: twelve,
                                fontWeight: FontWeight.w400,
                                letterSpacing: 0.32,
                                color: Colors.white,
                              ),
                            ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuantityButton(IconData icon, VoidCallback onPressed) {
    return Container(
        height: twelve + ten * 2,
        width: twelve + ten * 2,
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: IconButton(
          padding: EdgeInsets.zero,
          icon: Icon(icon, size: sixteen, color: Colors.black),
          onPressed: onPressed,
        ));
  }

  Widget _buildInfoContainer(String iconPath, String text) {
    return Container(
      padding:
          EdgeInsets.symmetric(horizontal: forteen, vertical: twentyFour / 2),
      decoration: BoxDecoration(
        color: const Color(0xFFE1E3E6),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            iconPath,
            width: twentyFour,
            height: twentyFour,
          ),
          SizedBox(height: twelve / 6),
          Text(
            text,
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w500,
              fontSize: twelve,
            ),
          ),
        ],
      ),
    );
  }

  void _decrementQuantity() {
    setState(() {
      if (quantity > 1) {
        quantity--;
      }
    });
  }

  void _incrementQuantity() {
    setState(() {
      quantity++;
    });
  }

  void _onAddToCatering(CateringDish dishData) {
    context.read<CateringBloc>().add(
          AddDishToCateringCart({
            "catering_id": widget.cateringRequestId,
            "dish_id": int.parse(widget.dishId),
            "quantity": quantity
          }),
        );
  }
}
