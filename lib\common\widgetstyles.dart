import 'package:flutter/material.dart';

class WidgetStyles {
  theHeight(BuildContext? context) {
    double height = MediaQuery.of(context!).size.height;
    return height;
  }

  theWidth(BuildContext? context) {
    double width = MediaQuery.of(context!).size.width;
    return width;
  }

  //----------------------------

  static InputBorder? border = OutlineInputBorder(
    borderRadius: BorderRadius.circular(5.0),
    borderSide: const BorderSide(color: Colors.white, width: 2),
  );

  static IconThemeData? iconThemeData = const IconThemeData(
    color: Colors.black,
    size: 18,
  );

  static const TextStyle regular = TextStyle(
    fontFamily: 'IBMPlexSans', // Use the family name defined in pubspec.yaml
  
  );
   static const TextStyle bold = TextStyle(
    fontFamily: 'IBMPlexSans',
    
  );
}
