{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860d29d7b5d1ca1778d4902c6bc276140", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980ff3f560eb2fb1b764738e76e10679a1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980ff3f560eb2fb1b764738e76e10679a1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981759afd9a1b875c74c4bf8bcd6615041", "guid": "bfdfe7dc352907fc980b868725387e98be2c41c899d1273e6d632a56ba8d7e17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98200295998e37337038488ffa9f110cdd", "guid": "bfdfe7dc352907fc980b868725387e9851643e713658d14d29d6829782cbb4b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1fbec41a75f24ba02568bf7cb36c0c9", "guid": "bfdfe7dc352907fc980b868725387e9817ba561fb18b74dd35e69503796de8d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b267af6420bf04ef2f0e797bf85285b5", "guid": "bfdfe7dc352907fc980b868725387e98e24f185acfcc7a37dc0388418630035d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98651e7248e992f0d01abbb953465ed451", "guid": "bfdfe7dc352907fc980b868725387e986fb04a840407641caacced9b7afd0e3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857745b24f2f45936184b4cf4ecbc790c", "guid": "bfdfe7dc352907fc980b868725387e989e7a155999504dc0a9f264b88821207c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc740be1db784e3840ab0ec7c446be4b", "guid": "bfdfe7dc352907fc980b868725387e986e93e86c54e81b4099ac32b2f80331e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c1ffc627ec7a9d7cf45c7a63bffa3a1", "guid": "bfdfe7dc352907fc980b868725387e9892de17e91461b157d2ff868c71245a5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdb38301877be0685bc6f3be5f063138", "guid": "bfdfe7dc352907fc980b868725387e98ccc08afea492b4a1e584294bb007c896", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98520cb7ff827baaf0c247b872f362d990", "guid": "bfdfe7dc352907fc980b868725387e983521b7cd1f6a9460255380c2e8bd3a76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ed5af2db332c82c1bd83dedfe4fb2f3", "guid": "bfdfe7dc352907fc980b868725387e9806f3c8f52df7d5d86b35f27ec804833b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f74be768cb53c8f682a636bb90957774", "guid": "bfdfe7dc352907fc980b868725387e980481fd222799c6dbbb547e04619399d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981867b3e037a071d66560821b1be966be", "guid": "bfdfe7dc352907fc980b868725387e986628c4eb4f0c01134872824bd0b57754", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad3c451424676cf57e2913e2211420c5", "guid": "bfdfe7dc352907fc980b868725387e981ffda07ad10fa61f9d8343f9e5802258", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd856a724596b07340e05feeb88d1bb8", "guid": "bfdfe7dc352907fc980b868725387e9884a940986487d110356e4ea121ddad2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98344a4380ad1cabda3918e814fa6a4157", "guid": "bfdfe7dc352907fc980b868725387e98a035677a5f5af6b2626b66c3934bc772", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f95a3995d22992a08d4f2db340b3cda0", "guid": "bfdfe7dc352907fc980b868725387e980133e3458c89bb95849d196600f559e4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9899ab2fa32bb9f6e7af8ac0bbb9259ff1", "guid": "bfdfe7dc352907fc980b868725387e9869a01b04319795d3ba134280e5b3aad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fde39d7f1d6af9d9434f7b5a4b15c097", "guid": "bfdfe7dc352907fc980b868725387e9887a3f9fbc3dc4cf61ce3bfd34cb48fc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abf21244b62589fdd7c5694a582f5e79", "guid": "bfdfe7dc352907fc980b868725387e985af5e7f6beaafbbd4b695fc9b31294b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af21a699e7cf91efe0137369e306fb8e", "guid": "bfdfe7dc352907fc980b868725387e98fd5c5c15ff3e26167ba0a16259e0280d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877aaa2224c52c643dfb86f6b658a92b1", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d5b76c0485c72b2c4da563974a3fc32", "guid": "bfdfe7dc352907fc980b868725387e98e69ee1ca0e2fa73c4851dfd4917080c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d23ab725b940ed9fce687e953f4f9373", "guid": "bfdfe7dc352907fc980b868725387e98d441388c343649dace73588215b70832"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2ed4fccf10c92e65dc1f45c50bda0e4", "guid": "bfdfe7dc352907fc980b868725387e98caaf5201fdfbf16ea5aee50b6acc5e0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccd217f28d1a0e4d0fafe009cf56c367", "guid": "bfdfe7dc352907fc980b868725387e98bb6368b1c4ab73b09a96040d1bbd3697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5c29c15fbb7851411ef3740dbbd2a25", "guid": "bfdfe7dc352907fc980b868725387e984069ad8f4dd463849cd55f546f840132"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98297323fbd944c854bfa400b03c7dfa23", "guid": "bfdfe7dc352907fc980b868725387e98d9dcbf389b3d1b5a507d5d60a21b0c28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c64fb0ef588cba2dfc38ae34b03d5e4", "guid": "bfdfe7dc352907fc980b868725387e98a95cab8f4d69c86ff793564af7099191"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891b0d77f98a4bf288255398b36543013", "guid": "bfdfe7dc352907fc980b868725387e98c60c36d6b388d64b429d0a4e5aaf86dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cc6b5b2fe0e1d50f60ba477752c2df7", "guid": "bfdfe7dc352907fc980b868725387e98888657137765bca507e1b4362cfde572"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}