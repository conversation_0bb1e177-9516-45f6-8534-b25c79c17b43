class FaqcategoryListModel {
  bool? success;
  List<FAQCatagoryData>? data;

  FaqcategoryListModel({this.success, this.data});

  FaqcategoryListModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    if (json['data'] != null) {
      data = <FAQCatagoryData>[];
      json['data'].forEach((v) {
        data!.add(new FAQCatagoryData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class FAQCatagoryData {
  int? id;
  String? title;
  String? createdAt;
  String? updatedAt;

  FAQCatagoryData({this.id, this.title, this.createdAt, this.updatedAt});

  FAQCatagoryData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}
