class DietaryListModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  DietaryListModel({this.status, this.message, this.statusCode, this.data});

  DietaryListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<Dietaries>? dietaries;

  Data({this.dietaries});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['dietaries'] != null) {
      dietaries = <Dietaries>[];
      json['dietaries'].forEach((v) {
        dietaries!.add(new Dietaries.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.dietaries != null) {
      data['dietaries'] = this.dietaries!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Dietaries {
  int? id;
  String? name;

  Dietaries({this.id, this.name});

  Dietaries.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}
