{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988cc75553b5015dc6dbb70ca075ac73ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f506b0277867f2ff2aaebdb4d47bb305", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ef74ad3cc973f5d4d4d61b492444f6c4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98592c4c1da4c23927204570653cf289eb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ef74ad3cc973f5d4d4d61b492444f6c4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988eb4316319de89ea54160afd2f1736a6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c834098312d540c7cf6154da2e15001e", "guid": "bfdfe7dc352907fc980b868725387e98ffb551216fda40792569cfa41ecfeedb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfa3629c63acb316a43443b57be72056", "guid": "bfdfe7dc352907fc980b868725387e989aa9e604c07ddcffcf99bbda62f5d2c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f19bba1e634ac885dca8a02892f340f6", "guid": "bfdfe7dc352907fc980b868725387e98fc605a16200ab02473f24b76765ecd73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13f19e171c19c2e6547d6e3cf4d4069", "guid": "bfdfe7dc352907fc980b868725387e98466f54c53cfc9b9e2100fc647b2501e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98340779a5d3e4f7ec641086904f3342db", "guid": "bfdfe7dc352907fc980b868725387e98feda98a9b63471e351acb1f18e97b031", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec6f72065e3c1421b0cf5c5c7acacb30", "guid": "bfdfe7dc352907fc980b868725387e98c41065e408ae7d95ccc7edf4f1cc7d75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833836f2141e029193648528f417ef6d1", "guid": "bfdfe7dc352907fc980b868725387e9821b99a6f16162fcff885e71cc3bcd49c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccc414de96e2be74d3f7731adb21a6c4", "guid": "bfdfe7dc352907fc980b868725387e98d9968a8a7b5cc629f4cf33b0721511a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2dde1b6ce29308bb43d5e187bff7b2b", "guid": "bfdfe7dc352907fc980b868725387e98eabcb6c6b6490a2fe72df8f96aecfbbd", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98dca564395ab889e83798d4650929421f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a148bb39a902962f0eb7eb321606d98d", "guid": "bfdfe7dc352907fc980b868725387e983392621c5301415f96d0a2bd8d8f2a9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2c48df34fafdc1fffb38e1f7ae25412", "guid": "bfdfe7dc352907fc980b868725387e981945ebb07a2d93f8f03722e34b7087df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98812a7fc667d2db3a77430771b72c45d5", "guid": "bfdfe7dc352907fc980b868725387e98bcbaee2ad11f9bd7c5c4259ddb964f28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2f69039ef0968a2e5832cc721a086b3", "guid": "bfdfe7dc352907fc980b868725387e98f1dbca9a601e1b3d8cfdad31f6cb387a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af97c19117b604d0860308336c2981a4", "guid": "bfdfe7dc352907fc980b868725387e985f9c2e34616cde653bc46dc4496cfaca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b29d6ff932b5349a531f4458bd0b4378", "guid": "bfdfe7dc352907fc980b868725387e98e8895355048521907d6a641fcfc5c2ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810bec7a6e1975dd5459c466aa700a0e4", "guid": "bfdfe7dc352907fc980b868725387e98adca06b0c9d18df8995e7b1cf4c30e5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff09ec59d348a5117fc9f13699e03edd", "guid": "bfdfe7dc352907fc980b868725387e98676609a61e2cdd739c8fba74f247eac2"}], "guid": "bfdfe7dc352907fc980b868725387e987b4d344fdfc1f6fc600d87f3267bc72e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98f4b45543adc971a6d5692778f38ea149"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e9853878ea418e81cecda30a768240ec3af"}], "guid": "bfdfe7dc352907fc980b868725387e986a35c715676d7f6d02c42c1c10b62281", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983bb113ce029a45bb1fddb6b7ba557e89", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98d09dfc8d4420f6d677feee5a9212f5ad", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e982d7a0002a6ec4e1f44e2f8b0fbb4c2bc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}