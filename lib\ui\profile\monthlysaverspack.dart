import 'package:db_eats/ui/monthlysaverSubscription/monthlySubscription.dart';
import 'package:flutter/material.dart';

class MonthlySaverPassPage extends StatefulWidget {
  @override
  _MonthlySaverPassPageState createState() => _MonthlySaverPassPageState();
}

class _MonthlySaverPassPageState extends State<MonthlySaverPassPage> {
  // List<bool> _faqExpanded = [false, false];
  List<Map<String, String>> _faqData = [
    {
      "question": "What is a Monthly Saver’s Pass?",
      "answer":
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Eget quam netus consectetur magnis. Lorem est ac duis risus semper. Nunc faucibus turpis ac sollicitudin suscipit imperdiet est, cursus.",
    },
    {
      "question": "Lorem ipsum dolor sit amet?",
      "answer":
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Eget quam netus consectetur magnis. Lorem est ac duis risus semper. Nunc faucibus turpis ac sollicitudin suscipit imperdiet est, cursus.",
    },
    {
      "question": "Lorem ipsum dolor sit amet?",
      "answer":
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Eget quam netus consectetur magnis. Lorem est ac duis risus semper. Nunc faucibus turpis ac sollicitudin suscipit imperdiet est, cursus.",
    },
    {
      "question": "Lorem ipsum dolor sit amet?",
      "answer":
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Eget quam netus consectetur magnis. Lorem est ac duis risus semper. Nunc faucibus turpis ac sollicitudin suscipit imperdiet est, cursus.",
    },
    // Add more FAQs here
  ];




  late List<bool> _faqExpanded;

  @override
  void initState() {
    super.initState();
    _faqExpanded = List.filled(_faqData.length, false);
  }


     late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF6F3EC),
        elevation: 0,
        leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.black),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              onPressed: () => Navigator.pop(context),
            ),
      ),
      body: SingleChildScrollView(
          padding:  EdgeInsets.only(left: sixteen, right: sixteen),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Monthly Saver’s Pass",
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: eighteen,
                ),
              ),
               SizedBox(height: ten),
              Container(
                padding:  EdgeInsets.only(
                    top: sixteen, bottom: 0, left: sixteen, right: sixteen),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(sixteen),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Lorem ipsum dolor sit amet, consectetur elit adipiscing, sed do eiusm tem dolor.",
                      style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          fontSize: forteen,
                          height: 1.35),
                    ),
                     SizedBox(height: ten*2.2),
                    Container(
                      padding:  EdgeInsets.only(
                          left: 0, right: 0, top: 0, bottom: forteen),
                      decoration: BoxDecoration(
                        color: Color(0xFFF1F2F3),
                        borderRadius: BorderRadius.circular(sixteen),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ClipRRect(
                            // borderRadius: BorderRadius.circular(twelve),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(sixteen),
                              topRight: Radius.circular(sixteen),
                            ),

                            child: Image.asset(
                              'assets/images/dish_9.png',
                              height: ten*15,
                              width: double.infinity,
                              fit: BoxFit.cover,
                            ),
                          ),
                           SizedBox(height: sixteen/2),
                          Padding(
                            padding:  EdgeInsets.only(left: sixteen, right: sixteen),
                            child: Text(
                              "1 month",
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w500,
                                fontSize: forteen,
                                color: Color(0xFF414346),
                              ),
                            ),
                          ),
                           SizedBox(height: twelve/2),
                          Padding(
                            padding:  EdgeInsets.only(left: sixteen, right: sixteen),
                            child: Text(
                              "\$2.00/mo.",
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w600,
                                fontSize: sixteen,
                              ),
                            ),
                          ),
                           SizedBox(height: twelve/2),
                          Padding(
                            padding:  EdgeInsets.only(left: sixteen, right: sixteen),
                            child: Row(
                              children: [
                                CircleAvatar(
                                  radius: twelve,
                                  backgroundImage:
                                      AssetImage('assets/images/chef.png'),
                                ),
                                 SizedBox(width: sixteen/2),
                                Text(
                                  "Chef John",
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w500,
                                    fontSize: twelve,
                                  ),
                                ),
                              ],
                            ),
                          ),
                           SizedBox(height: twelve),
                          Padding(
                            padding:  EdgeInsets.only(left: sixteen, right: sixteen),
                            child: SizedBox(
                              width: double.infinity,
                              height: ten*4.4,
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => MonthlySubscriptionPage(),
                                    ),
                                  );

                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Color(0xFF1F2122),
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(twentyFour),
                                  ),
                                  elevation: 0,
                                ),
                                child: Padding(
                                  padding: EdgeInsets.all(sixteen/2.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        'Subscribe',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: twelve,
                                          fontWeight: FontWeight.w400,
                                          height: 1.0,
                                          letterSpacing: 0.32,
                                        ),
                                      ),
                                      SizedBox(
                                          width:
                                              sixteen/4), // spacing between text and image
                                      Image.asset(
                                        'assets/icons/right_arrow.png',
                                        width: sixteen,
                                        height: sixteen,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                     SizedBox(height: twenty),
                    Text(
                      "Benefits",
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w600,
                        fontSize: sixteen,
                      ),
                    ),
                     SizedBox(height: twentyFour),
                    ...List.generate(4, (index) {
                      return Padding(
                        padding:  EdgeInsets.only(bottom: sixteen),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Image.asset(
                              'assets/icons/benefit.png',
                              width: ten*3.8,
                              height: ten*3.8,
                            ),
                             SizedBox(height: forteen),
                            Text(
                              'Benefit ${index + 1}',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w600,
                                fontSize: forteen,
                              ),
                            ),
                             SizedBox(height: sixteen/4),
                            Text(
                              'Lorem ipsum dolor sit amet, consectetur elit adipiscing, sed do eiusm tem dolor.',
                              style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w400,
                                  fontSize: twelve,
                                  color: Color(0xFF414346),
                                  height: 1.35,
                                  letterSpacing: -0.2),
                            ),
                          ],
                        ),
                      );
                    }),
                    const SizedBox(height: 2),
                    Text(
                      "Frequently Asked Questions",
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w600,
                        fontSize: sixteen,
                      ),
                    ),
                     SizedBox(height: ten*3.8),
                    Column(
                      children: List.generate(_faqData.length, (index) {
                        return _buildFAQItem(
                          index,
                          _faqData[index]["question"]!,
                          _faqData[index]["answer"]!,
                        );
                      }),
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 30,
              )
            ],
          )),
    );
  }

  Widget _buildFAQItem(int index, String question, String answer) {
    bool isExpanded = _faqExpanded[index];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _faqExpanded[index] = !isExpanded;
            });
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Padding(
                  padding:  EdgeInsets.only(left: twelve),
                  child: Text(
                    question,
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                      fontSize: forteen,
                      height: 1.5,
                    ),
                  ),
                ),
              ),
              Padding(
                padding:  EdgeInsets.only(right: twelve),
                child: AnimatedRotation(
                  duration: Duration(milliseconds: 200),
                  turns: isExpanded ? 0.0 : 0.5,
                  child: Image.asset('assets/icons/down.png',
                      width: twentyFour, height: twentyFour),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 2),
        Divider(color: Color(0xFFE1E3E6), thickness: 1),
        if (isExpanded)
          Padding(
            padding:  EdgeInsets.only(top: sixteen/2, left: twelve, right: twelve),
            child: Text(
              answer,
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
                fontSize: twelve,
                height: 1.33,
                letterSpacing: -0.2,
              ),
            ),
          ),
        SizedBox(height: isExpanded ? ten*3 : twelve),
      ],
    );
  }
}
