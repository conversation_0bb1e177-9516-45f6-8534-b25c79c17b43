class NewFilteredDishesModel {
  bool? status;
  String? message;
  int? statusCode;
  NewFilteredDishesModelData? data;

  NewFilteredDishesModel({
    this.status,
    this.message,
    this.statusCode,
    this.data,
  });

  NewFilteredDishesModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null
        ? NewFilteredDishesModelData.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['status'] = status;
    data['message'] = message;
    data['status_code'] = statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class NewFilteredDishesModelData {
  List<FeaturedList>? featuredList;
  List<CategoryBasedList>? categoryBasedList;

  NewFilteredDishesModelData({this.featuredList, this.categoryBasedList});

  NewFilteredDishesModelData.fromJson(Map<String, dynamic> json) {
    if (json['featured_list'] != null) {
      featuredList = <FeaturedList>[];
      json['featured_list'].forEach((v) {
        featuredList!.add(FeaturedList.fromJson(v));
      });
    }
    if (json['category_based_list'] != null) {
      categoryBasedList = <CategoryBasedList>[];
      json['category_based_list'].forEach((v) {
        categoryBasedList!.add(CategoryBasedList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (featuredList != null) {
      data['featured_list'] = featuredList!.map((v) => v.toJson()).toList();
    }
    if (categoryBasedList != null) {
      data['category_based_list'] =
          categoryBasedList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class FeaturedList {
  int? id;
  String? name;
  int? chefCategoryId;
  String? photo;
  bool? isFeatured;
  List<ServingSizePrices>? servingSizePrices;

  FeaturedList({
    this.id,
    this.name,
    this.chefCategoryId,
    this.photo,
    this.isFeatured,
    this.servingSizePrices,
  });

  FeaturedList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    chefCategoryId = json['chef_category_id'];
    photo = json['photo'];
    isFeatured = json['is_featured'];
    if (json['serving_size_prices'] != null) {
      servingSizePrices = <ServingSizePrices>[];
      json['serving_size_prices'].forEach((v) {
        servingSizePrices!.add(ServingSizePrices.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['chef_category_id'] = chefCategoryId;
    data['photo'] = photo;
    data['is_featured'] = isFeatured;
    if (servingSizePrices != null) {
      data['serving_size_prices'] =
          servingSizePrices!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ServingSizePrices {
  int? id;
  int? servingSizeId;
  String? price;
  ServingSize? servingSize;

  ServingSizePrices({
    this.id,
    this.servingSizeId,
    this.price,
    this.servingSize,
  });

  ServingSizePrices.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    servingSizeId = json['serving_size_id'];
    price = json['price'];
    servingSize = json['serving_size'] != null
        ? ServingSize.fromJson(json['serving_size'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['serving_size_id'] = servingSizeId;
    data['price'] = price;
    if (servingSize != null) {
      data['serving_size'] = servingSize!.toJson();
    }
    return data;
  }
}

class ServingSize {
  int? id;
  String? title;
  int? serves;

  ServingSize({this.id, this.title, this.serves});

  ServingSize.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    serves = json['serves'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['title'] = title;
    data['serves'] = serves;
    return data;
  }
}

class CategoryBasedList {
  Category? category;
  List<DishList>? dishList;

  CategoryBasedList({this.category, this.dishList});

  CategoryBasedList.fromJson(Map<String, dynamic> json) {
    category =
        json['category'] != null ? Category.fromJson(json['category']) : null;
    if (json['dish_list'] != null) {
      dishList = <DishList>[];
      json['dish_list'].forEach((v) {
        dishList!.add(DishList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (category != null) {
      data['category'] = category!.toJson();
    }
    if (dishList != null) {
      data['dish_list'] = dishList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DishList {
  int? id;
  String? name;
  int? chefCategoryId;
  String? photo;
  bool? isFeatured;
  List<ServingSizePrices>? servingSizePrices;

  DishList({
    this.id,
    this.name,
    this.chefCategoryId,
    this.photo,
    this.isFeatured,
    this.servingSizePrices,
  });

  DishList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    chefCategoryId = json['chef_category_id'];
    photo = json['photo'];
    isFeatured = json['is_featured'];
    if (json['serving_size_prices'] != null) {
      servingSizePrices = <ServingSizePrices>[];
      json['serving_size_prices'].forEach((v) {
        servingSizePrices!.add(ServingSizePrices.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['chef_category_id'] = chefCategoryId;
    data['photo'] = photo;
    data['is_featured'] = isFeatured;
    if (servingSizePrices != null) {
      data['serving_size_prices'] =
          servingSizePrices!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Category {
  int? id;
  String? name;

  Category({this.id, this.name});

  Category.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}
