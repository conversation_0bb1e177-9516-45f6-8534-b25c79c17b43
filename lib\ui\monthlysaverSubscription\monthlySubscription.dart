import 'package:db_eats/bloc/saversplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/saversplan/listsaversplanmodel.dart';
import 'package:db_eats/ui/monthlysaverSubscription/checkout_monthlysaver.dart';
import 'package:db_eats/ui/monthlysaverSubscription/subsribe.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MonthlySubscriptionPage extends StatefulWidget {
  const MonthlySubscriptionPage({super.key});

  @override
  State<MonthlySubscriptionPage> createState() =>
      _MonthlySubscriptionPageState();
}

class _MonthlySubscriptionPageState extends State<MonthlySubscriptionPage> {
  late SaversplanBloc _saversplanBloc;
  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double thirty;
  late double sixty;
  late double screenWidth;
  late double screenHeight;

  int _currentPage = 1;
  int _limit = 5;
  bool _isLoadingMore = false;
  bool _hasMorePlans = true;
  final ScrollController _scrollController = ScrollController();
  List<Plans> _allPlans = [];
  int? _loadingPlanId;
  int? _loadingCancelId;
  bool _isShowingSnackBar = false;

  bool _isOtpSent = false;
  String? _currentSubscriptionId;
  final TextEditingController _otpController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _saversplanBloc = SaversplanBloc();
    _scrollController.addListener(_onScroll);
    _saversplanBloc.add(ListSaversPassPlansEvent(
        data: {"page": _currentPage, "limit": _limit}));
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _otpController.dispose();
    _saversplanBloc.close();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
    thirty = screenWidth * 0.076;
    sixty = screenWidth * 0.152;
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _saversplanBloc,
      child: Scaffold(
        backgroundColor: const Color(0xfff6f3ec),
        appBar: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xfff6f3ec),
          elevation: 0,
          scrolledUnderElevation: 0,
          shadowColor: Colors.transparent,
          titleSpacing: 0,
          centerTitle: false,
          title: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
        ),
        body: BlocConsumer<SaversplanBloc, SaversPlanState>(
          listener: (context, state) {
            // Handle email verification states
            if (state is SaversSendResendEmailOtpSuccess) {
              setState(() {
                _isOtpSent = true;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  duration: Duration(seconds: 1),
                ),
              );
            } else if (state is SaversSendResendEmailOtpFailed) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${state.message}'),
                  backgroundColor: Colors.red,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  duration: Duration(seconds: 3),
                ),
              );
            } else if (state is SaversVerifyEmailOtpSuccess) {
              setState(() {
                _isOtpSent = false;
                _currentSubscriptionId = null;
                _otpController.clear();
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  duration: Duration(seconds: 2),
                ),
              );
              _currentPage = 1;
              _allPlans.clear();
              _hasMorePlans = true;
              _saversplanBloc
                  .add(ListSaversPassPlansEvent(data: {"page": 1, "limit": 5}));
            } else if (state is SaversVerifyEmailOtpFailed) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  duration: Duration(seconds: 2),
                ),
              );
            }
          },
          builder: (context, state) {
            if (state is ListSaversPassPlansLoading && _allPlans.isEmpty) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state is ListSaversPassPlansFailed && _allPlans.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('Error: ${state.message}'),
                    SizedBox(height: sixteen),
                    ElevatedButton(
                      onPressed: () {
                        _currentPage = 1;
                        _allPlans.clear();
                        _hasMorePlans = true;
                        _saversplanBloc.add(ListSaversPassPlansEvent(
                            data: {"page": 1, "limit": 5}));
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }

            if (state is ListSaversPassPlansSuccess) {
              final data = Initializer.listSaversPlanModel.data;
              final newPlans = data?.plans ?? [];
              final pagination = data?.pagination;

              if (pagination != null) {
                _hasMorePlans =
                    (pagination.page ?? 1) * (pagination.limit ?? _limit) <
                        (pagination.totalLength ?? 0);
              }

              if (_currentPage == 1) {
                _allPlans = List.from(newPlans);
              } else {
                for (var plan in newPlans) {
                  if (!_allPlans
                      .any((existingPlan) => existingPlan.id == plan.id)) {
                    _allPlans.add(plan);
                  }
                }
              }

              _isLoadingMore = false;
            }

            return _buildContent();
          },
        ),
      ),
    );
  }

  Widget _buildContent() {
    final data = Initializer.listSaversPlanModel.data;
    final subscribedPlan = data?.subscribedPlan;

    return Padding(
      padding: EdgeInsets.only(left: sixteen, right: sixteen),
      child: SingleChildScrollView(
        controller: _scrollController,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: ten / 5),
            Text(
              'Monthly Saver Plan',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w600,
                fontSize: eighteen,
                color: Color(0xff1F2122),
              ),
            ),
            SizedBox(height: sixteen),

            _buildYourPlanSection(subscribedPlan),

            SizedBox(height: twenty),

            // Other Plans
            Text(
              'Other Plans',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: twenty,
                color: Color(0xff1F2122),
              ),
            ),
            SizedBox(height: forteen),

            if (_allPlans.isNotEmpty)
              ..._allPlans.map((plan) => _buildPlanCard(plan)).toList()
            else
              _buildEmptyState(),

            if (_isLoadingMore)
              Container(
                margin: EdgeInsets.symmetric(vertical: sixteen),
                child: Center(
                  child: CircularProgressIndicator(
                    color: Color(0xff1F2122),
                  ),
                ),
              ),

            // if (!_hasMorePlans && _allPlans.isNotEmpty)
            //   Container(
            //     margin: EdgeInsets.symmetric(vertical: sixteen),
            //     child: Center(
            //       child: Text(
            //         'No more plans to load',
            //         style: TextStyle(
            //           fontSize: forteen,
            //           color: Color(0xff414346),
            //         ),
            //       ),
            //     ),
            //   ),

            SizedBox(height: ten * 3),
          ],
        ),
      ),
    );
  }

  Widget _buildYourPlanSection(SubscribedPlan? subscribedPlan) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(eighteen),
      ),
      padding: EdgeInsets.all(eighteen),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Plan',
            style: TextStyle(
              fontFamily: "inter",
              fontWeight: FontWeight.w600,
              fontSize: twenty,
              color: Color(0xff1F2122),
            ),
          ),
          SizedBox(height: forteen),
          if (subscribedPlan != null)
            _buildActiveSubscriptionCard(subscribedPlan)
          else
            _buildNoActiveSubscriptionCard(),
        ],
      ),
    );
  }

  Widget _buildActiveSubscriptionCard(SubscribedPlan subscribedPlan) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF1F2F3),
        borderRadius: BorderRadius.circular(twelve),
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(forteen),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      subscribedPlan.planName ?? 'Regular',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: forteen,
                        color: Color(0xff1F2122),
                      ),
                    ),
                    SizedBox(width: sixteen / 2),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: sixteen / 2,
                        vertical: ten / 5,
                      ),
                      decoration: BoxDecoration(
                        color:
                            _getStatusColor(subscribedPlan.status ?? 'ACTIVE'),
                        borderRadius: BorderRadius.circular(sixteen / 2),
                      ),
                      child: Text(
                        _getStatusText(subscribedPlan.status ?? 'ACTIVE'),
                        style: TextStyle(
                          color: _getStatusTextColor(
                              subscribedPlan.status ?? 'ACTIVE'),
                          fontWeight: FontWeight.w500,
                          fontSize: twelve,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '\$${subscribedPlan.price ?? '45'}/year',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: forteen,
                        color: Color(0xff414346),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: ten),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      'Renewal on: ${_calculateRenewalDate(subscribedPlan.durationDays ?? 365)}',
                      textAlign: TextAlign.end,
                      style: TextStyle(
                        color: Color(0xff414346),
                        fontSize: twelve,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                Divider(
                  color: Color(0xffE1E3E6),
                  thickness: 1,
                  height: twentyFour,
                ),
                SizedBox(height: twelve / 2),
                Text(
                  'Exclusive offers',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: forteen,
                    color: Color(0xff1F2122),
                  ),
                ),
                SizedBox(height: twelve / 2),
                _buildOffersList(subscribedPlan),
              ],
            ),
          ),
          ClipRRect(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(twelve),
              bottomRight: Radius.circular(twelve),
            ),
            child: Container(
              color: const Color(0xFFE1E3E6),
              padding: EdgeInsets.symmetric(vertical: ten),
              child: Row(
                children: [
                  SizedBox(width: twelve),
                  Icon(
                    Icons.celebration_outlined,
                    size: eighteen,
                    color: Color(0xff1F2122),
                  ),
                  SizedBox(width: sixteen / 2),
                  RichText(
                    text: TextSpan(
                      style: TextStyle(
                        color: Color(0xff414346),
                        fontSize: forteen,
                        fontWeight: FontWeight.w400,
                      ),
                      children: [
                        TextSpan(
                          text: 'Delivery Fee Saved: ',
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: twelve,
                            color: Color(0xff414346),
                          ),
                        ),
                        TextSpan(
                          text: '\$${subscribedPlan.total ?? '0'}',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: twelve,
                            color: Color(0xff1F2122),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (subscribedPlan.paymentStatus == 'PENDING' ||
              subscribedPlan.status == 'PENDING' ||
              subscribedPlan.status == 'NOT_VERIFIED')
            Container(
              padding: EdgeInsets.all(sixteen),
              child: Column(
                children: [
                  if (subscribedPlan.status == 'NOT_VERIFIED')
                    Column(
                      children: [
                        if (_isOtpSent &&
                            _currentSubscriptionId ==
                                subscribedPlan.id.toString()) ...[
                          Container(
                            height: screenHeight * 0.050,
                            decoration: BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(0x1A000000),
                                  blurRadius: screenWidth * 0.02,
                                  offset: Offset(0, screenHeight * 0.002),
                                ),
                              ],
                              borderRadius:
                                  BorderRadius.circular(screenWidth * 0.055),
                            ),
                            child: TextField(
                              controller: _otpController,
                              keyboardType: TextInputType.number,
                              maxLength: 6,
                              style: TextStyle(
                                fontSize:
                                    MediaQuery.of(context).size.width * 0.04,
                              ),
                              decoration: InputDecoration(
                                hintText: 'Enter 6-digit OTP',
                                hintStyle: TextStyle(
                                  color: Color(0xff66696D),
                                  fontWeight: FontWeight.w400,
                                  fontSize: forteen,
                                ),
                                counterText: '',
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: screenWidth * 0.045,
                                    vertical: screenHeight * 0.02),
                                filled: true,
                                fillColor: Colors.white,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(
                                      screenWidth * 0.055),
                                  borderSide: BorderSide(
                                      color: Color(0xffE1E3E6),
                                      width: screenWidth * 0.002),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(
                                      screenWidth * 0.055),
                                  borderSide: BorderSide(
                                      color: Color(0xffE1E3E6),
                                      width: screenWidth * 0.002),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(
                                      screenWidth * 0.055),
                                  borderSide: BorderSide(
                                      color: Color(0xff1F2122),
                                      width: screenWidth * 0.003),
                                ),
                              ),
                            ),
                          ),
                          // SizedBox(height: twelve),
                          // Resend OTP button
                          Align(
                            alignment: Alignment.centerRight,
                            child: BlocBuilder<SaversplanBloc, SaversPlanState>(
                              builder: (context, currentState) {
                                return TextButton(
                                  onPressed: (currentState
                                          is SaversSendResendEmailOtpLoading)
                                      ? null
                                      : () => _resendOtp(
                                          subscribedPlan.id.toString()),
                                  child: Text(
                                    (currentState
                                            is SaversSendResendEmailOtpLoading)
                                        ? 'Sending...'
                                        : 'Resend OTP',
                                    style: TextStyle(
                                      color: (currentState
                                              is SaversSendResendEmailOtpLoading)
                                          ? Color(0xff9E9E9E)
                                          : Color(0xff1F2122),
                                      fontWeight: FontWeight.w400,
                                      fontSize: twelve,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          // SizedBox(height: twelve),
                        ],
                        SizedBox(
                          width: double.infinity,
                          child: BlocBuilder<SaversplanBloc, SaversPlanState>(
                            builder: (context, currentState) {
                              return ElevatedButton(
                                onPressed: _isOtpSent &&
                                        _currentSubscriptionId ==
                                            subscribedPlan.id.toString()
                                    ? ((currentState
                                            is SaversVerifyEmailOtpLoading)
                                        ? null
                                        : () => _verifyOtp(
                                            subscribedPlan.id.toString()))
                                    : ((currentState
                                            is SaversSendResendEmailOtpLoading)
                                        ? null
                                        : () => _sendOtp(
                                            subscribedPlan.id.toString())),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      const Color.fromARGB(255, 5, 5, 5),
                                  disabledBackgroundColor:
                                      const Color(0xff9E9E9E),
                                  shape: RoundedRectangleBorder(
                                    borderRadius:
                                        BorderRadius.circular(ten * 3),
                                  ),
                                  padding:
                                      EdgeInsets.symmetric(vertical: sixteen),
                                  elevation: 0,
                                ),
                                child: _isOtpSent &&
                                        _currentSubscriptionId ==
                                            subscribedPlan.id.toString()
                                    ? ((currentState
                                            is SaversVerifyEmailOtpLoading)
                                        ? SizedBox(
                                            height: sixteen * 1.2,
                                            width: sixteen * 1.2,
                                            child: CircularProgressIndicator(
                                              color: Colors.white,
                                              strokeWidth: 2,
                                            ),
                                          )
                                        : Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.check_circle,
                                                color: Colors.white,
                                                size: sixteen,
                                              ),
                                              SizedBox(width: sixteen / 2),
                                              Text(
                                                'Verify OTP',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.w400,
                                                  fontSize: twelve,
                                                ),
                                              ),
                                            ],
                                          ))
                                    : ((currentState
                                            is SaversSendResendEmailOtpLoading)
                                        ? SizedBox(
                                            height: sixteen * 1.2,
                                            width: sixteen * 1.2,
                                            child: CircularProgressIndicator(
                                              color: Colors.white,
                                              strokeWidth: 2,
                                            ),
                                          )
                                        : Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.verified_user,
                                                color: Colors.white,
                                                size: sixteen,
                                              ),
                                              SizedBox(width: sixteen / 2),
                                              Text(
                                                'Verify Email',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.w400,
                                                  fontSize: twelve,
                                                ),
                                              ),
                                            ],
                                          )),
                              );
                            },
                          ),
                        ),
                      ],
                    )
                  else if (subscribedPlan.paymentStatus == 'PENDING' &&
                      subscribedPlan.status != 'NOT_VERIFIED')
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => BlocProvider.value(
                                value: _saversplanBloc,
                                child: CheckoutMonthlysaver(
                                  subscriptionId: subscribedPlan.id!,
                                ),
                              ),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(ten * 3),
                          ),
                          padding: EdgeInsets.symmetric(vertical: sixteen),
                          elevation: 0,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.payment,
                              color: Colors.white,
                              size: sixteen,
                            ),
                            SizedBox(width: sixteen / 2),
                            Text(
                              'Complete Payment',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w400,
                                fontSize: twelve,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  if ((subscribedPlan.paymentStatus == 'PENDING' &&
                          subscribedPlan.status != 'NOT_VERIFIED') ||
                      subscribedPlan.status == 'NOT_VERIFIED')
                    SizedBox(height: twelve),
                  SizedBox(
                    width: double.infinity,
                    child: BlocBuilder<SaversplanBloc, SaversPlanState>(
                      builder: (context, state) {
                        return BlocListener<SaversplanBloc, SaversPlanState>(
                          listenWhen: (previous, current) {
                            return previous != current;
                          },
                          listener: (context, state) {
                            if (state is CancelSubscribeSaversPassLoading) {
                              setState(() {
                                _loadingCancelId = subscribedPlan.id;
                              });
                            } else {
                              setState(() {
                                _loadingCancelId = null;
                              });
                            }

                            if (state is CancelSubscribeSaversPassSuccess) {
                              if (!_isShowingSnackBar) {
                                _isShowingSnackBar = true;
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(
                                      SnackBar(
                                        content: Text(state.message),
                                        backgroundColor: Colors.green,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(twelve),
                                        ),
                                        behavior: SnackBarBehavior.floating,
                                        duration: const Duration(seconds: 1),
                                      ),
                                    )
                                    .closed
                                    .then((_) {
                                  _isShowingSnackBar = false;
                                });
                                _currentPage = 1;
                                _allPlans.clear();
                                _hasMorePlans = true;
                                _saversplanBloc.add(ListSaversPassPlansEvent(
                                    data: {"page": 1, "limit": 5}));
                              }
                            } else if (state
                                is CancelSubscribeSaversPassFailed) {
                              if (!_isShowingSnackBar) {
                                _isShowingSnackBar = true;
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(
                                      SnackBar(
                                        content: Text(state.message),
                                        backgroundColor: Colors.red,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(twelve),
                                        ),
                                        behavior: SnackBarBehavior.floating,
                                        duration: const Duration(seconds: 1),
                                      ),
                                    )
                                    .closed
                                    .then((_) {
                                  _isShowingSnackBar = false;
                                });
                              }
                            }
                          },
                          child: OutlinedButton(
                            onPressed: () {
                              _showCancelConfirmationDialog(subscribedPlan.id!);
                            },
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(color: Colors.red, width: 1),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(ten * 3),
                              ),
                              padding: EdgeInsets.symmetric(vertical: sixteen),
                            ),
                            child: _loadingCancelId == subscribedPlan.id
                                ? SizedBox(
                                    height: sixteen,
                                    width: sixteen,
                                    child: CircularProgressIndicator(
                                      color: Colors.red,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.cancel_outlined,
                                        color: Colors.red,
                                        size: sixteen,
                                      ),
                                      SizedBox(width: sixteen / 2),
                                      Text(
                                        'Cancel Subscription',
                                        style: TextStyle(
                                          color: Colors.red,
                                          fontWeight: FontWeight.w400,
                                          fontSize: twelve,
                                        ),
                                      ),
                                    ],
                                  ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNoActiveSubscriptionCard() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(twelve),
        border: Border.all(
          color: const Color(0xFFE1E3E6),
          width: 1,
        ),
      ),
      padding: EdgeInsets.all(sixteen),
      child: Column(
        children: [
          Icon(
            Icons.info_outline,
            size: twentyFour,
            color: Color(0xff414346),
          ),
          SizedBox(height: twelve),
          Text(
            'No Active Plan',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: sixteen,
              color: Color(0xff1F2122),
            ),
          ),
          SizedBox(height: ten),
          Text(
            'You don\'t have any active subscription plan. Choose from the plans below to start saving on your orders.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: forteen,
              color: Color(0xff414346),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanCard(Plans plan) {
    return Container(
        margin: EdgeInsets.only(bottom: sixteen),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(eighteen),
        ),
        child: Padding(
          padding: EdgeInsets.all(eighteen),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFF1F2F3),
                  borderRadius: BorderRadius.circular(twelve),
                ),
                child: Padding(
                  padding: EdgeInsets.all(forteen),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            plan.planName ?? '_',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: forteen,
                              color: Color(0xff1F2122),
                            ),
                          ),
                          SizedBox(width: sixteen / 2),
                          if (plan.planType == 2)
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: sixteen / 2,
                                vertical: ten / 5,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFE3F2FD),
                                borderRadius:
                                    BorderRadius.circular(sixteen / 2),
                              ),
                              child: Text(
                                'Student',
                                style: TextStyle(
                                  color: Color(0xFF1976D2),
                                  fontWeight: FontWeight.w500,
                                  fontSize: ten,
                                ),
                              ),
                            ),
                          const Spacer(),
                          Text(
                            '\$${plan.price ?? '_'}/${_getDurationText(plan.durationDays ?? 365)}',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: forteen,
                              color: Color(0xff414346),
                            ),
                          ),
                        ],
                      ),
                      if (plan.description != null) ...[
                        SizedBox(height: ten),
                        Text(
                          plan.description!,
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: twelve,
                            color: Color(0xff414346),
                          ),
                        ),
                      ],
                      Divider(
                        color: const Color(0xffE1E3E6),
                        thickness: 1,
                        height: twentyFour,
                      ),
                      Text(
                        'Exclusive offers',
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: forteen,
                          color: Color(0xff1F2122),
                        ),
                      ),
                      SizedBox(height: twelve / 2),
                      _buildOffersList(plan),
                      SizedBox(height: ten),
                      if (plan.planType == 2)
                        Align(
                          alignment: Alignment.centerRight,
                          child: TextButton(
                            onPressed: () async {
                              final result = await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => BlocProvider(
                                    create: (context) => SaversplanBloc(),
                                    child: VerifyStudentStatusPage(
                                        planId: plan.id),
                                  ),
                                ),
                              );

                              if (result == true) {
                                _currentPage = 1;
                                _allPlans.clear();
                                _hasMorePlans = true;
                                _saversplanBloc.add(ListSaversPassPlansEvent(
                                    data: {"page": 1, "limit": 5}));
                              }
                            },
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.zero,
                              minimumSize: const Size(0, 0),
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: IntrinsicWidth(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Verify As Student',
                                    style: TextStyle(
                                      color: Color(0xff1F2122),
                                      fontWeight: FontWeight.w400,
                                      fontSize: twelve,
                                    ),
                                  ),
                                  SizedBox(height: ten / 5),
                                  Container(
                                    height: 1,
                                    width: double.infinity,
                                    color: Color(0xff1F2122),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      SizedBox(height: sixteen / 2),
                    ],
                  ),
                ),
              ),
              if (plan.planType == 1)
                Column(
                  children: [
                    SizedBox(height: twenty),
                    SizedBox(
                      width: double.infinity,
                      child: BlocBuilder<SaversplanBloc, SaversPlanState>(
                        builder: (context, subscriptionState) {
                          return BlocListener<SaversplanBloc, SaversPlanState>(
                            listenWhen: (previous, current) {
                              return previous != current;
                            },
                            listener: (context, state) {
                              if (state is SubscribeSaversPassSuccess) {
                                if (_loadingPlanId == plan.id) {
                                  setState(() {
                                    _loadingPlanId = null;
                                  });
                                }

                                if (!_isShowingSnackBar) {
                                  _isShowingSnackBar = true;
                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(
                                        SnackBar(
                                          content: Text(state.message),
                                          backgroundColor: Colors.green,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(twelve),
                                          ),
                                          behavior: SnackBarBehavior.floating,
                                          duration: const Duration(seconds: 1),
                                        ),
                                      )
                                      .closed
                                      .then((_) {
                                    _isShowingSnackBar = false;
                                  });
                                  _currentPage = 1;
                                  _allPlans.clear();
                                  _hasMorePlans = true;
                                  _saversplanBloc.add(ListSaversPassPlansEvent(
                                      data: {"page": 1, "limit": 5}));
                                }
                              } else if (state is SubscribeSaversPassFailed) {
                                // Reset loading state
                                if (_loadingPlanId == plan.id) {
                                  setState(() {
                                    _loadingPlanId = null;
                                  });
                                }

                                if (!_isShowingSnackBar) {
                                  _isShowingSnackBar = true;
                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(
                                        SnackBar(
                                          content: Text(state.message),
                                          backgroundColor: Colors.red,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(twelve),
                                          ),
                                          behavior: SnackBarBehavior.floating,
                                          duration: const Duration(seconds: 1),
                                        ),
                                      )
                                      .closed
                                      .then((_) {
                                    _isShowingSnackBar = false;
                                  });
                                }
                              }
                            },
                            child: ElevatedButton(
                              onPressed: (_loadingPlanId == plan.id)
                                  ? null
                                  : () {
                                      setState(() {
                                        _loadingPlanId = plan.id;
                                      });

                                      final subscriptionData = {
                                        "savers_pass_id": plan.id,
                                      };
                                      _saversplanBloc.add(
                                          SubscribeSaversPassEvent(
                                              data: subscriptionData));
                                    },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.black,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(ten * 3),
                                ),
                                padding:
                                    EdgeInsets.symmetric(vertical: sixteen),
                                elevation: 0,
                              ),
                              child: (_loadingPlanId == plan.id)
                                  ? SizedBox(
                                      height: sixteen,
                                      width: sixteen,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          'Subscribe',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w400,
                                            fontSize: twelve,
                                          ),
                                        ),
                                        SizedBox(width: sixteen / 2),
                                        Icon(
                                          Icons.arrow_forward,
                                          color: Colors.white,
                                          size: sixteen,
                                        ),
                                      ],
                                    ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ));
  }

  Widget _buildOffersList(dynamic plan) {
    List<Widget> offers = [];

    if (plan is Plans && plan.isFirstOrderDiscountEnabled == true) {
      offers.add(_buildOfferItem(
          '${plan.firstOrderDiscountPercent}% Off First Order'));
    } else if (plan is SubscribedPlan &&
        plan.isFirstOrderDiscountEnabled == true) {
      offers.add(_buildOfferItem(
          '${plan.firstOrderDiscountPercent}% Off First Order'));
    } else if (plan is Map && plan['is_first_order_discount_enabled'] == true) {
      offers.add(_buildOfferItem(
          '${plan['first_order_discount_percent']}% Off First Order'));
    }

    if (plan is Plans && plan.isFirstMealplanDiscountEnabled == true) {
      offers.add(_buildOfferItem(
          '${plan.firstMealplanDiscountPercent}% Off First Meal Plan'));
    } else if (plan is SubscribedPlan &&
        plan.isFirstMealplanDiscountEnabled == true) {
      offers.add(_buildOfferItem(
          '${plan.firstMealplanDiscountPercent}% Off First Meal Plan'));
    } else if (plan is Map &&
        plan['is_first_mealplan_discount_enabled'] == true) {
      offers.add(_buildOfferItem(
          '${plan['first_mealplan_discount_percent']}% Off First Meal Plan'));
    }

    if (plan is Plans && plan.isFirstCateringDiscountEnabled == true) {
      offers.add(_buildOfferItem(
          '${plan.firstCateringDiscountPercent}% Off First Catering'));
    } else if (plan is SubscribedPlan &&
        plan.isFirstCateringDiscountEnabled == true) {
      offers.add(_buildOfferItem(
          '${plan.firstCateringDiscountPercent}% Off First Catering'));
    } else if (plan is Map &&
        plan['is_first_catering_discount_enabled'] == true) {
      offers.add(_buildOfferItem(
          '${plan['first_catering_discount_percent']}% Off First Catering'));
    }

    if (plan is Plans && plan.isServiceFeeDiscountEnabled == true) {
      offers.add(_buildOfferItem(
          '${plan.orderServiceFeeDiscountPercent}% Off Service Fee'));
    } else if (plan is SubscribedPlan &&
        plan.isServiceFeeDiscountEnabled == true) {
      offers.add(_buildOfferItem(
          '${plan.orderServiceFeeDiscountPercent}% Off Service Fee'));
    } else if (plan is Map && plan['is_service_fee_discount_enabled'] == true) {
      offers.add(_buildOfferItem(
          '${plan['order_service_fee_discount_percent']}% Off Service Fee'));
    }

    if (offers.isEmpty) {
      offers = [
        _buildOfferItem('_'),
        _buildOfferItem('_'),
      ];
    }

    return Column(children: offers);
  }

  Widget _buildOfferItem(String offer) {
    return Padding(
      padding: EdgeInsets.only(bottom: sixteen / 4),
      child: Row(
        children: [
          Icon(Icons.check, size: forteen, color: Color(0xff1F2122)),
          SizedBox(width: twelve / 2),
          Expanded(
            child: Text(
              offer,
              style: TextStyle(
                fontSize: twelve,
                fontWeight: FontWeight.w400,
                color: Color(0xff1F2122),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(eighteen),
      ),
      padding: EdgeInsets.all(twentyFour),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.inbox_outlined,
              size: eighteen * 3,
              color: Color(0xff414346),
            ),
            SizedBox(height: sixteen),
            Text(
              'No plans available',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: sixteen,
                color: Color(0xff1F2122),
              ),
            ),
            SizedBox(height: ten),
            Text(
              'Please check back later for available subscription plans.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: forteen,
                color: Color(0xff414346),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCancelConfirmationDialog(int subscriptionId) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(sixteen),
              ),
              child: Container(
                padding: EdgeInsets.all(twentyFour),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(sixteen),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Warning Icon
                    Container(
                      width: sixty,
                      height: sixty,
                      decoration: BoxDecoration(
                        color: Color(0xFFFEF2F2),
                        borderRadius: BorderRadius.circular(thirty),
                      ),
                      child: Icon(
                        Icons.warning_amber_rounded,
                        color: Color(0xFFDC2626),
                        size: thirty,
                      ),
                    ),
                    SizedBox(height: twenty),

                    // Title
                    Text(
                      'Cancel Subscription',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w600,
                        fontSize: eighteen,
                        color: Color(0xff1F2122),
                      ),
                    ),
                    SizedBox(height: twelve),

                    // Description
                    Text(
                      'Are you sure you want to cancel your subscription? You will lose access to all benefits and this action cannot be undone.',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: forteen,
                        fontWeight: FontWeight.w400,
                        color: Color(0xff6B7280),
                        height: 1.5,
                      ),
                    ),
                    SizedBox(height: twentyFour),

                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _loadingCancelId == subscriptionId
                                ? null
                                : () {
                                    Navigator.of(context).pop();
                                  },
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(
                                  color: Color(0xffE1E3E6), width: 1),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(twelve),
                              ),
                              padding: EdgeInsets.symmetric(vertical: forteen),
                            ),
                            child: Text(
                              'Keep Plan',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                color: Color(0xff374151),
                                fontSize: forteen,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: twelve),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _loadingCancelId == subscriptionId
                                ? null
                                : () {
                                    Navigator.of(context).pop();
                                    _saversplanBloc.add(
                                        CancelSubscribeSaversPassEvent(
                                            data: {"id": subscriptionId}));
                                  },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Color.fromARGB(255, 3, 3, 3),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(twelve),
                              ),
                              padding: EdgeInsets.symmetric(vertical: forteen),
                              elevation: 0,
                            ),
                            child: _loadingCancelId == subscriptionId
                                ? SizedBox(
                                    height: sixteen,
                                    width: sixteen,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : Text(
                                    'Cancel Plan',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      color: Colors.white,
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'ACTIVE':
        return const Color(0xFFCEF8E0);
      case 'PENDING':
      case 'NOT_VERIFIED':
        return const Color(0xFFFFF3CD);
      case 'CANCELLED':
      case 'EXPIRED':
        return const Color(0xFFF8D7DA);
      default:
        return const Color(0xFFE1E3E6);
    }
  }

  Color _getStatusTextColor(String status) {
    switch (status.toUpperCase()) {
      case 'ACTIVE':
        return Color(0xFF007A4D);
      case 'PENDING':
      case 'NOT_VERIFIED':
        return Color(0xFF856404);
      case 'CANCELLED':
      case 'EXPIRED':
        return Color(0xFF721C24);
      default:
        return Color(0xFF495057);
    }
  }

  String _getStatusText(String status) {
    switch (status.toUpperCase()) {
      case 'ACTIVE':
        return 'Active';
      case 'PENDING':
        return 'Pending';
      case 'NOT_VERIFIED':
        return 'Not Verified';
      case 'CANCELLED':
        return 'Cancelled';
      case 'EXPIRED':
        return 'Expired';
      default:
        return status;
    }
  }

  String _calculateRenewalDate(int durationDays) {
    final renewalDate = DateTime.now().add(Duration(days: durationDays));
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${months[renewalDate.month - 1]} ${renewalDate.day.toString().padLeft(2, '0')}, ${renewalDate.year}';
  }

  String _getDurationText(int days) {
    if (days >= 365) {
      return 'year';
    } else if (days >= 30) {
      return 'month';
    } else {
      return 'days';
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      _loadMorePlans();
    }
  }

  void _loadMorePlans() {
    if (!_isLoadingMore && _hasMorePlans) {
      setState(() {
        _isLoadingMore = true;
      });
      _currentPage++;
      _saversplanBloc.add(ListSaversPassPlansEvent(
          data: {"page": _currentPage, "limit": _limit}));
    }
  }

  void _sendOtp(String subscriptionId) {
    setState(() {
      _currentSubscriptionId = subscriptionId;
    });

    final otpData = {
      "id": subscriptionId,
    };

    _saversplanBloc.add(
      SaversSendResendEmailOtp(data: otpData),
    );
  }

  void _verifyOtp(String subscriptionId) {
    if (_otpController.text.isEmpty || _otpController.text.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid 6-digit OTP'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final verificationData = {
      "id": subscriptionId,
      "otp": _otpController.text,
    };

    _saversplanBloc.add(
      SaversVerifyEmailOtp(data: verificationData),
    );
  }

  void _resendOtp(String subscriptionId) {
    final otpData = {
      "id": subscriptionId,
    };

    _saversplanBloc.add(
      SaversSendResendEmailOtp(data: otpData),
    );
  }
}
