// ignore_for_file: deprecated_member_use, avoid_unnecessary_containers

import 'package:flutter/material.dart';

class FilterOptionsBar extends StatefulWidget {
  final Function(Map<String, String>) onFilterChange;

  const FilterOptionsBar({
    super.key,
    required this.onFilterChange,
  });

  @override
  _FilterOptionsBarState createState() => _FilterOptionsBarState();
}

class _FilterOptionsBarState extends State<FilterOptionsBar> {
  final Set<String> _selectedFilters = {};
  final Map<String, TextEditingController> _filterControllers = {};
  final Map<String, String> _activeFilters = {};
  bool _showFilters = false;

  final List<FilterOption> filterOptions = [
    FilterOption(display: 'Name', field: 'name'),
    FilterOption(display: 'Aadhar Number', field: 'adharno'),
    FilterOption(display: 'Alias', field: 'alias'),
    FilterOption(display: 'Address', field: 'address'),
    FilterOption(display: "Father's Name", field: 'fathers<PERSON><PERSON>'),
    FilterOption(display: "Mother's Name", field: 'mother'),
    FilterOption(display: "Wife's Name", field: 'wife'),
    FilterOption(display: 'Children', field: 'children'),
    FilterOption(display: 'Siblings', field: 'siblings'),
    FilterOption(display: 'Occupation', field: 'occupation'),
    FilterOption(display: 'Gender', field: 'sex'),
    FilterOption(display: 'Mobile', field: 'mobile'),
  ];

  @override
  void dispose() {
    for (var controller in _filterControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _applyFilter(String display, String value) {
    if (value.length > 2) {
      final option =
          filterOptions.firstWhere((option) => option.display == display);
      setState(() {
        _activeFilters[option.field] = value;
      });
      widget.onFilterChange(_activeFilters);
    }
  }

  void _removeFilter(String filterKey) {
    setState(() {
      _activeFilters.remove(filterKey);
      // Also remove from selected filters
      final displayName = _getDisplayName(filterKey);
      _selectedFilters.remove(displayName);
      _filterControllers[displayName]?.clear();
    });
    widget.onFilterChange(_activeFilters);
  }

  String _getDisplayName(String fieldName) {
    return filterOptions
        .firstWhere((option) => option.field == fieldName)
        .display;
  }

  void _toggleFilters() {
    setState(() {
      _showFilters = !_showFilters;

      // Clear everything when closing the filters
      if (!_showFilters) {
        _selectedFilters.clear();
        _activeFilters.clear();
        _filterControllers.forEach((_, controller) => controller.clear());
        widget.onFilterChange(_activeFilters);
      }
    });
  }

  Widget _buildActiveFiltersDisplay() {
    if (_activeFilters.isEmpty) return const SizedBox.shrink();

    return Container(
      // decoration: BoxDecoration(
      //   color: Colors.grey[50],
      //   borderRadius: BorderRadius.circular(8),
      //   // border: Border.all(color: Colors.grey[200]!),
      // ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Wrap(
              spacing: 3,
              runSpacing: 8,
              children: _activeFilters.entries.map((entry) {
                return Chip(
                  label: Text(
                    '${_getDisplayName(entry.key)}: ${entry.value}',
                    style: const TextStyle(fontSize: 12),
                  ),
                  deleteIcon: const Icon(Icons.close, size: 16),
                  onDeleted: () => _removeFilter(entry.key),
                  backgroundColor: const Color(0xFFCD853F).withOpacity(0.1),
                  labelStyle: const TextStyle(color: Color(0xFFCD853F)),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  '     Please select the main filter criteria for search',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(
                    _showFilters ? Icons.filter_list_off : Icons.filter_list,
                    color: const Color(0xFFCD853F),
                  ),
                  onPressed: _toggleFilters,
                ),
              ],
            ),
            if (_showFilters) ...[
              const SizedBox(height: 12),
              SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: filterOptions.map((filter) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Text(filter.display,
                            style: const TextStyle(fontSize: 13)),
                        selected: _selectedFilters.contains(filter.display),
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _selectedFilters.add(filter.display);
                              _filterControllers.putIfAbsent(filter.display,
                                  () => TextEditingController());
                            } else {
                              _selectedFilters.remove(filter.display);
                              _filterControllers[filter.display]?.clear();
                              // Remove from active filters when deselected
                              _activeFilters.remove(filter.field);
                              widget.onFilterChange(_activeFilters);
                            }
                          });
                        },
                        backgroundColor: Colors.grey[100],
                        selectedColor: const Color(0xFFCD853F).withOpacity(0.2),
                        labelStyle: TextStyle(
                          color: _selectedFilters.contains(filter.display)
                              ? const Color(0xFFCD853F)
                              : Colors.black87,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
              if (_selectedFilters.isNotEmpty) ...[
                const SizedBox(height: 12),
                Column(
                  children: _selectedFilters.map((filterDisplay) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: TextField(
                        controller: _filterControllers[filterDisplay],
                        decoration: InputDecoration(
                          hintText: 'Enter $filterDisplay',
                          hintStyle: TextStyle(
                              color: Colors.grey[800],
                              fontSize: 12,
                              fontWeight: FontWeight.bold),
                          filled: true,
                          fillColor: Colors.grey[50],
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide.none,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                        ),
                        textInputAction: TextInputAction.done,
                        onChanged: (value) =>
                            _applyFilter(filterDisplay, value),
                        onSubmitted: (value) =>
                            _applyFilter(filterDisplay, value),
                      ),
                    );
                  }).toList(),
                ),
              ],
              _buildActiveFiltersDisplay(),
            ],
          ],
        ),
      ),
    );
  }
}

class FilterOption {
  final String display;
  final String field;

  FilterOption({required this.display, required this.field});
}
