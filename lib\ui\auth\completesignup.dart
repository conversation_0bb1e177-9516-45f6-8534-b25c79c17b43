import 'package:db_eats/ui/auth/verifysignup.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/main_bloc.dart';

class CompleteSignup extends StatefulWidget {
  final String email;
  const CompleteSignup({Key? key, required this.email}) : super(key: key);

  @override
  State<CompleteSignup> createState() => _CompleteSignupState();
}

class _CompleteSignupState extends State<CompleteSignup> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  bool _agreedToTerms = false;

  bool _phoneError = false;
  bool _firstNameError = false;
  bool _lastNameError = false;
  bool _passwordError = false;
  bool _confirmPasswordError = false;

  String _phoneErrorText = '';
  String _firstNameErrorText = '';
  String _lastNameErrorText = '';
  String _passwordErrorText = '';
  String _confirmPasswordErrorText = '';

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _emailController.text = widget.email;
  }

  void _resetErrors() {
    setState(() {
      _phoneError = false;
      _firstNameError = false;
      _lastNameError = false;
      _passwordError = false;
      _confirmPasswordError = false;
    });
  }

  bool _validatePhone(String value) {
    final baseTextSize = getResponsiveSize(context);
    if (value.isEmpty) {
      setState(() {
        _phoneError = true;
        _phoneErrorText = 'Phone number is required';
      });
      return false;
    }
    if (value.length != 10) {
      setState(() {
        _phoneError = true;
        _phoneErrorText = 'Phone number must be 10 digits';
      });
      return false;
    }
    setState(() {
      _phoneError = false;
      _phoneErrorText = '';
    });
    return true;
  }

  bool _validateFirstName(String value) {
    final baseTextSize = getResponsiveSize(context);
    if (value.isEmpty) {
      setState(() {
        _firstNameError = true;
        _firstNameErrorText = 'First name is required';
      });
      return false;
    }
    setState(() {
      _firstNameError = false;
      _firstNameErrorText = '';
    });
    return true;
  }

  bool _validateLastName(String value) {
    final baseTextSize = getResponsiveSize(context);
    if (value.isEmpty) {
      setState(() {
        _lastNameError = true;
        _lastNameErrorText = 'Last name is required';
      });
      return false;
    }
    setState(() {
      _lastNameError = false;
      _lastNameErrorText = '';
    });
    return true;
  }

  bool _validatePassword(String value) {
    final baseTextSize = getResponsiveSize(context);
    if (value.isEmpty) {
      setState(() {
        _passwordError = true;
        _passwordErrorText = 'Password is required';
      });
      return false;
    }
    if (value.length < 6) {
      setState(() {
        _passwordError = true;
        _passwordErrorText = 'Password must be at least 6 characters';
      });
      return false;
    }
    setState(() {
      _passwordError = false;
      _passwordErrorText = '';
    });
    return true;
  }

  bool _validateConfirmPassword(String value) {
    final baseTextSize = getResponsiveSize(context);
    if (value != _passwordController.text) {
      setState(() {
        _confirmPasswordError = true;
        _confirmPasswordErrorText = 'Passwords do not match';
      });
      return false;
    }
    setState(() {
      _confirmPasswordError = false;
      _confirmPasswordErrorText = '';
    });
    return true;
  }

  bool _validateFields() {
    _resetErrors();

    // Sequential validation
    if (!_validatePhone(_phoneController.text)) {
      return false;
    }

    if (!_validateFirstName(_firstNameController.text)) {
      return false;
    }

    if (!_validateLastName(_lastNameController.text)) {
      return false;
    }

    if (!_validatePassword(_passwordController.text)) {
      return false;
    }

    if (!_validateConfirmPassword(_confirmPasswordController.text)) {
      return false;
    }

    if (!_agreedToTerms) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please agree to terms of use')),
      );
      return false;
    }

    return true;
  }

  double getResponsiveSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return 12;
    if (width < 600) return 14;
    if (width < 900) return 16;
    return 18;
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final baseTextSize = getResponsiveSize(context);

    return BlocListener<MainBloc, MainState>(
      listener: (context, state) {
        if (state is SigningUp) {
          setState(() => _isLoading = true);
        } else {
          setState(() => _isLoading = false);
          if (state is SignUpSuccess) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => Verifysignup(
                  email: widget.email,
                  phone: _phoneController.text,
                  countryCode: state.countryCode,
                ),
              ),
            );
          } else if (state is SignUpFailed) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          }
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: size.width * 0.05,
                vertical: size.height * 0.02,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Close button
                  Container(
                    width: size.width * 0.1,
                    height: size.width * 0.1,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.close,
                        size: baseTextSize * 1.2,
                        color: Colors.black,
                      ),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.all(size.width * 0.02),
                      constraints: const BoxConstraints(),
                    ),
                  ),

                  SizedBox(height: size.height * 0.06),

                  Text(
                    'Sign up',
                    style: TextStyle(
                      fontSize:
                          isLandscape ? size.height * 0.04 : baseTextSize * 1.5,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: Color(0xFF1F2122),
                    ),
                  ),

                  SizedBox(height: size.height * 0.02),

                  // Form fields with responsive spacing
                  _buildResponsiveFormField(
                    context: context,
                    label: 'Email Address',
                    controller: _emailController,
                    keyboardType: TextInputType.emailAddress,
                    hintText: '<EMAIL>',
                    baseTextSize: baseTextSize,
                  ),

                  SizedBox(height: size.height * 0.02),

                  // Phone field
                  _buildResponsiveFormField(
                    context: context,
                    label: 'Phone Number',
                    controller: _phoneController,
                    keyboardType: TextInputType.phone,
                    hintText: 'Enter phone number',
                    isError: _phoneError,
                    errorText: _phoneErrorText,
                    onChanged: (value) => _validatePhone(value),
                    baseTextSize: baseTextSize,
                  ),

                  SizedBox(height: size.height * 0.02),

                  // First Name field
                  _buildResponsiveFormField(
                    context: context,
                    label: 'First Name',
                    controller: _firstNameController,
                    hintText: 'Enter your first name',
                    isError: _firstNameError,
                    errorText: _firstNameErrorText,
                    onChanged: (value) {
                      if (_firstNameError) _validateFirstName(value);
                    },
                    baseTextSize: baseTextSize,
                  ),

                  SizedBox(height: size.height * 0.02),

                  // Last Name field
                  _buildResponsiveFormField(
                    context: context,
                    label: 'Last Name',
                    controller: _lastNameController,
                    hintText: 'Enter your last name',
                    isError: _lastNameError,
                    errorText: _lastNameErrorText,
                    onChanged: (value) {
                      if (_lastNameError) _validateLastName(value);
                    },
                    baseTextSize: baseTextSize,
                  ),

                  SizedBox(height: size.height * 0.02),

                  // Password field
                  _buildResponsiveFormField(
                    context: context,
                    label: 'Password',
                    controller: _passwordController,
                    obscureText: true,
                    hintText: 'Enter your password',
                    isError: _passwordError,
                    errorText: _passwordErrorText,
                    onChanged: (value) => _validatePassword(value),
                    baseTextSize: baseTextSize,
                  ),

                  SizedBox(height: size.height * 0.02),

                  // Retype Password field
                  _buildResponsiveFormField(
                    context: context,
                    label: 'Retype Password',
                    controller: _confirmPasswordController,
                    obscureText: true,
                    hintText: 'Enter your password',
                    isError: _confirmPasswordError,
                    errorText: _confirmPasswordErrorText,
                    onChanged: (value) => _validateConfirmPassword(value),
                    baseTextSize: baseTextSize,
                  ),

                  SizedBox(height: size.height * 0.03),

                  // Sign Up button
                  SizedBox(
                    width: double.infinity,
                    height:
                        isLandscape ? size.height * 0.08 : size.height * 0.06,
                    child: ElevatedButton(
                      onPressed: _isLoading
                          ? null
                          : () {
                              if (_validateFields()) {
                                context.read<MainBloc>().add(
                                      SignUpEvent(
                                        email: widget.email,
                                        firstName: _firstNameController.text,
                                        lastName: _lastNameController.text,
                                        phone: _phoneController.text,
                                        password: _passwordController.text,
                                      ),
                                    );
                              }
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.black,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        elevation: 0,
                      ),
                      child: _isLoading
                          ? SizedBox(
                              height: baseTextSize,
                              width: baseTextSize,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              'Sign Up',
                              style: TextStyle(
                                fontSize: baseTextSize * 1.15  // = 16.1 (very close to 16)
,
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                    ),
                  ),

                  SizedBox(height: size.height * 0.02),

                  // Terms checkbox with responsive sizing
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: baseTextSize * 1.2,
                        height: baseTextSize * 1.2,
                        child: Checkbox(
                          value: _agreedToTerms,
                          onChanged: (value) {
                            setState(() {
                              _agreedToTerms = value ?? false;
                            });
                          },
                          activeColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                          side: const BorderSide(color: Colors.black),
                        ),
                      ),
                      SizedBox(width: size.width * 0.02),
                      Expanded(
                        child: Text(
                          'By joining I agree to DB Eat\'s terms of use',
                          style: TextStyle(
                            fontSize: baseTextSize * 0.9,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF414346),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveFormField({
    required BuildContext context,
    required String label,
    required TextEditingController controller,
    required double baseTextSize,
    TextInputType keyboardType = TextInputType.text,
    bool obscureText = false,
    String? hintText,
    bool isError = false,
    String? errorText,
    Function(String)? onChanged,
  }) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final inputFieldHeight =
        isLandscape ? size.height * 0.08 : size.height * 0.06;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: baseTextSize,
            // fontWeight: FontWeight.w500,
            fontFamily: 'Inter-medium',
            color: Color(0xFF1F2122),
          ),
        ),
        SizedBox(height: size.height * 0.01),
        Container(
          height: inputFieldHeight,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(size.width * 0.08),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: controller,
            onChanged: onChanged,
            style: TextStyle(
              fontSize: baseTextSize,
              height: 1.0,
            ),
            decoration: InputDecoration(
              filled: true,
              fillColor: Colors.white,
              hintText: hintText,
              hintStyle: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
                fontSize: baseTextSize * 0.9,
                color: Color(0xFF66696D),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: size.width * 0.05,
                vertical: size.height * 0.015,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(size.width * 0.06),
                borderSide: BorderSide(
                  color: isError ? Colors.red : const Color(0xFFE1E3E6),
                  width: isError ? 1.5 : 1.0,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(size.width * 0.06),
                borderSide: BorderSide(
                  color: isError ? Colors.red : const Color(0xFFE1E3E6),
                  width: isError ? 1.5 : 1.0,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(size.width * 0.06),
                borderSide: BorderSide(
                  color: isError ? Colors.red : Colors.black,
                  width: 1.5,
                ),
              ),
              errorStyle: TextStyle(
                fontSize: baseTextSize * 0.8,
                color: Colors.red,
                fontFamily: 'Inter',
              ),
            ),
            keyboardType: keyboardType,
            obscureText: obscureText,
          ),
        ),
        if (isError && errorText != null) ...[
          SizedBox(height: size.height * 0.01),
          Text(
            errorText,
            style: TextStyle(
              color: Colors.red,
              fontSize: baseTextSize * 0.8,
              fontFamily: 'Inter',
            ),
          ),
        ],
      ],
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }
}
