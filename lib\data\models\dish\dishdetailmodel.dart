class DishDetailModel {
  bool? status;
  String? message;
  int? statusCode;
  DishDetailsData? data;

  DishDetailModel({this.status, this.message, this.statusCode, this.data});

  DishDetailModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? DishDetailsData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['status_code'] = statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DishDetailsData {
  Dish? dish;
  bool? isFavourite;

  DishDetailsData({this.dish, this.isFavourite});

  DishDetailsData.fromJson(Map<String, dynamic> json) {
    dish = json['dish'] != null ? Dish.fromJson(json['dish']) : null;
    isFavourite = json['is_favourite'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (dish != null) {
      data['dish'] = dish!.toJson();
    }
    data['is_favourite'] = isFavourite;
    return data;
  }
}

class Dish {
  int? id;
  String? name;
  int? dishtypeId;
  int? chefCategoryId;
  int? cuisineId;
  int? subCuisineId;
  int? localCuisineId;
  String? photo;
  String? description;
  String? ingredients;
  int? leadTimeId;
  int? dietaryId;
  int? spiceLevelId;
  String? status;
  Dishtype? dishtype;
  Dishtype? chefCategory;
  Dishtype? cuisine;
  Dishtype? subCuisine;
  Dishtype? localCuisine;
  List<ServingSizePrices>? servingSizePrices;
  Dishtype? spiceLevel;
  Dishtype? dietary;

  Dish({
    this.id,
    this.name,
    this.dishtypeId,
    this.chefCategoryId,
    this.cuisineId,
    this.subCuisineId,
    this.localCuisineId,
    this.photo,
    this.description,
    this.ingredients,
    this.leadTimeId,
    this.dietaryId,
    this.spiceLevelId,
    this.status,
    this.dishtype,
    this.chefCategory,
    this.cuisine,
    this.subCuisine,
    this.localCuisine,
    this.servingSizePrices,
    this.spiceLevel,
    this.dietary,
  });

  Dish.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    dishtypeId = json['dishtype_id'];
    chefCategoryId = json['chef_category_id'];
    cuisineId = json['cuisine_id'];
    subCuisineId = json['sub_cuisine_id'];
    localCuisineId = json['local_cuisine_id'];
    photo = json['photo'];
    description = json['description'];
    ingredients = json['ingredients'];
    leadTimeId = json['lead_time_id'];
    dietaryId = json['dietary_id'];
    spiceLevelId = json['spice_level_id'];
    status = json['status'];
    dishtype =
        json['dishtype'] != null ? Dishtype.fromJson(json['dishtype']) : null;
    chefCategory = json['chef_category'] != null
        ? Dishtype.fromJson(json['chef_category'])
        : null;
    cuisine =
        json['cuisine'] != null ? Dishtype.fromJson(json['cuisine']) : null;
    subCuisine = json['sub_cuisine'] != null
        ? Dishtype.fromJson(json['sub_cuisine'])
        : null;
    localCuisine = json['local_cuisine'] != null
        ? Dishtype.fromJson(json['local_cuisine'])
        : null;
    if (json['serving_size_prices'] != null) {
      servingSizePrices = <ServingSizePrices>[];
      json['serving_size_prices'].forEach((v) {
        servingSizePrices!.add(ServingSizePrices.fromJson(v));
      });
    }
    spiceLevel = json['spice_level'] != null
        ? Dishtype.fromJson(json['spice_level'])
        : null;
    dietary =
        json['dietary'] != null ? Dishtype.fromJson(json['dietary']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['dishtype_id'] = dishtypeId;
    data['chef_category_id'] = chefCategoryId;
    data['cuisine_id'] = cuisineId;
    data['sub_cuisine_id'] = subCuisineId;
    data['local_cuisine_id'] = localCuisineId;
    data['photo'] = photo;
    data['description'] = description;
    data['ingredients'] = ingredients;
    data['lead_time_id'] = leadTimeId;
    data['dietary_id'] = dietaryId;
    data['spice_level_id'] = spiceLevelId;
    data['status'] = status;
    if (dishtype != null) {
      data['dishtype'] = dishtype!.toJson();
    }
    if (chefCategory != null) {
      data['chef_category'] = chefCategory!.toJson();
    }
    if (cuisine != null) {
      data['cuisine'] = cuisine!.toJson();
    }
    if (subCuisine != null) {
      data['sub_cuisine'] = subCuisine!.toJson();
    }
    if (localCuisine != null) {
      data['local_cuisine'] = localCuisine!.toJson();
    }
    if (servingSizePrices != null) {
      data['serving_size_prices'] =
          servingSizePrices!.map((v) => v.toJson()).toList();
    }
    if (spiceLevel != null) {
      data['spice_level'] = spiceLevel!.toJson();
    }
    if (dietary != null) {
      data['dietary'] = dietary!.toJson();
    }
    return data;
  }
}

class Dishtype {
  int? id;
  String? name;

  Dishtype({this.id, this.name});

  Dishtype.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class ServingSizePrices {
  int? id;
  int? servingSizeId;
  String? price;
  ServingSize? servingSize;

  ServingSizePrices(
      {this.id, this.servingSizeId, this.price, this.servingSize});

  ServingSizePrices.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    servingSizeId = json['serving_size_id'];
    price = json['price'];
    servingSize = json['serving_size'] != null
        ? ServingSize.fromJson(json['serving_size'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['serving_size_id'] = servingSizeId;
    data['price'] = price;
    if (servingSize != null) {
      data['serving_size'] = servingSize!.toJson();
    }
    return data;
  }
}

class ServingSize {
  int? id;
  String? title;
  int? serves;

  ServingSize({this.id, this.title, this.serves});

  ServingSize.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    serves = json['serves'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['serves'] = serves;
    return data;
  }
}
