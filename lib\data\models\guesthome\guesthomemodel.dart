class GuestHomeModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  GuestHomeModel({this.status, this.message, this.statusCode, this.data});

  GuestHomeModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['status_code'] = statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<Deal>? deals;
  List<Dishes>? dishes;
  List<TopRatedChefs>? topRatedChefs;
  List<TopRatedChefs>? popularChefsNear;

  Data({this.deals, this.dishes, this.topRatedChefs, this.popularChefsNear});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['deals'] != null) {
      deals = <Deal>[];
      json['deals'].forEach((v) {
        deals!.add(Deal.fromJson(v));
      });
    }
    if (json['dishes'] != null) {
      dishes = <Dishes>[];
      json['dishes'].forEach((v) {
        dishes!.add(Dishes.fromJson(v));
      });
    }
    if (json['top_rated_chefs'] != null) {
      topRatedChefs = <TopRatedChefs>[];
      json['top_rated_chefs'].forEach((v) {
        topRatedChefs!.add(TopRatedChefs.fromJson(v));
      });
    }
    if (json['popular_chefs_near'] != null) {
      popularChefsNear = <TopRatedChefs>[];
      json['popular_chefs_near'].forEach((v) {
        popularChefsNear!.add(TopRatedChefs.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (deals != null) {
      data['deals'] = deals!.map((v) => v.toJson()).toList();
    }
    if (dishes != null) {
      data['dishes'] = dishes!.map((v) => v.toJson()).toList();
    }
    if (topRatedChefs != null) {
      data['top_rated_chefs'] = topRatedChefs!.map((v) => v.toJson()).toList();
    }
    if (popularChefsNear != null) {
      data['popular_chefs_near'] =
          popularChefsNear!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Deal {
  int? id;
  int? dealType;
  String? title;
  String? description;
  String? discountPercentage;
  String? discountAmount;
  String? minimumSpendAmount;
  String? startDate;
  String? endDate;
  String? status;
  DealChef? chef;

  Deal({
    this.id,
    this.dealType,
    this.title,
    this.description,
    this.discountPercentage,
    this.discountAmount,
    this.minimumSpendAmount,
    this.startDate,
    this.endDate,
    this.status,
    this.chef,
  });

  Deal.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    dealType = json['deal_type'];
    title = json['title'];
    description = json['description'];
    discountPercentage = json['discount_percentage'];
    discountAmount = json['discount_amount'];
    minimumSpendAmount = json['minimum_spend_amount'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    status = json['status'];
    chef = json['chef'] != null ? DealChef.fromJson(json['chef']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['deal_type'] = dealType;
    data['title'] = title;
    data['description'] = description;
    data['discount_percentage'] = discountPercentage;
    data['discount_amount'] = discountAmount;
    data['minimum_spend_amount'] = minimumSpendAmount;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['status'] = status;
    if (chef != null) {
      data['chef'] = chef!.toJson();
    }
    return data;
  }
}

class DealChef {
  String? firstName;
  String? lastName;
  DealChefProfile? profile;

  DealChef({this.firstName, this.lastName, this.profile});

  DealChef.fromJson(Map<String, dynamic> json) {
    firstName = json['first_name'];
    lastName = json['last_name'];
    profile = json['profile'] != null
        ? DealChefProfile.fromJson(json['profile'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    if (profile != null) {
      data['profile'] = profile!.toJson();
    }
    return data;
  }
}

class DealChefProfile {
  String? profilePhoto;
  String? coverPhoto;

  DealChefProfile({this.profilePhoto, this.coverPhoto});

  DealChefProfile.fromJson(Map<String, dynamic> json) {
    profilePhoto = json['profile_photo'];
    coverPhoto = json['cover_photo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['profile_photo'] = profilePhoto;
    data['cover_photo'] = coverPhoto;
    return data;
  }
}

class Dishes {
  int? id;
  String? name;
  String? photo;
  List<ServingSizePrices>? servingSizePrices;

  Dishes({this.id, this.name, this.photo, this.servingSizePrices});

  Dishes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
    if (json['serving_size_prices'] != null) {
      servingSizePrices = <ServingSizePrices>[];
      json['serving_size_prices'].forEach((v) {
        servingSizePrices!.add(ServingSizePrices.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['photo'] = photo;
    if (servingSizePrices != null) {
      data['serving_size_prices'] =
          servingSizePrices!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ServingSizePrices {
  int? id;
  int? servingSizeId;
  String? price;
  ServingSize? servingSize;

  ServingSizePrices(
      {this.id, this.servingSizeId, this.price, this.servingSize});

  ServingSizePrices.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    servingSizeId = json['serving_size_id'];
    price = json['price'];
    servingSize = json['serving_size'] != null
        ? ServingSize.fromJson(json['serving_size'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['serving_size_id'] = servingSizeId;
    data['price'] = price;
    if (servingSize != null) {
      data['serving_size'] = servingSize!.toJson();
    }
    return data;
  }
}

class ServingSize {
  int? id;
  String? title;
  int? serves;

  ServingSize({this.id, this.title, this.serves});

  ServingSize.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    serves = json['serves'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['serves'] = serves;
    return data;
  }
}

class TopRatedChefs {
  int? chefId;
  String? profilePhoto;
  String? coverPhoto;
  Location? location;
  List<String>? searchTags;
  double? distance;
  Chef? chef;
  num? ratingPercentage;
  String? averageRating;
  num? totalRatings;

  TopRatedChefs(
      {this.chefId,
      this.profilePhoto,
      this.coverPhoto,
      this.location,
      this.searchTags,
      this.distance,
      this.chef,
      this.ratingPercentage,
      this.averageRating,
      this.totalRatings});

  TopRatedChefs.fromJson(Map<String, dynamic> json) {
    chefId = json['chef_id'];
    profilePhoto = json['profile_photo'];
    coverPhoto = json['cover_photo'];
    location =
        json['location'] != null ? Location.fromJson(json['location']) : null;
    searchTags = json['search_tags'] != null
        ? List<String>.from(json['search_tags'])
        : []; // Changed from null to empty list
    distance = json['distance']?.toDouble();
    chef = json['chef'] != null ? Chef.fromJson(json['chef']) : null;
    ratingPercentage = json['rating_percentage']?.toDouble();
    averageRating = json['average_rating'];
    totalRatings = json['total_ratings'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['chef_id'] = chefId;
    data['profile_photo'] = profilePhoto;
    data['cover_photo'] = coverPhoto;
    if (location != null) {
      data['location'] = location!.toJson();
    }
    data['search_tags'] = searchTags;
    data['distance'] = distance;
    if (chef != null) {
      data['chef'] = chef!.toJson();
    }
    data['rating_percentage'] = ratingPercentage;
    data['average_rating'] = averageRating;
    data['total_ratings'] = totalRatings;
    return data;
  }
}

class Location {
  Crs? crs;
  String? type;
  List<double>? coordinates;

  Location({this.crs, this.type, this.coordinates});

  Location.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? Crs.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates'].cast<double>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (crs != null) {
      data['crs'] = crs!.toJson();
    }
    data['type'] = type;
    data['coordinates'] = coordinates;
    return data;
  }
}

class Crs {
  String? type;
  Properties? properties;

  Crs({this.type, this.properties});

  Crs.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'] != null
        ? Properties.fromJson(json['properties'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    if (properties != null) {
      data['properties'] = properties!.toJson();
    }
    return data;
  }
}

class Properties {
  String? name;

  Properties({this.name});

  Properties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    return data;
  }
}

class Chef {
  String? firstName;
  String? lastName;
  List<OperationDays>? operationDays;

  Chef({this.firstName, this.lastName, this.operationDays});

  Chef.fromJson(Map<String, dynamic> json) {
    firstName = json['first_name'];
    lastName = json['last_name'];
    if (json['operation_days'] != null) {
      operationDays = <OperationDays>[];
      json['operation_days'].forEach((v) {
        operationDays!.add(OperationDays.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    if (operationDays != null) {
      data['operation_days'] = operationDays!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class OperationDays {
  int? dayId;
  Properties? day;

  OperationDays({this.dayId, this.day});

  OperationDays.fromJson(Map<String, dynamic> json) {
    dayId = json['day_id'];
    day = json['day'] != null ? Properties.fromJson(json['day']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['day_id'] = dayId;
    if (day != null) {
      data['day'] = day!.toJson();
    }
    return data;
  }
}
