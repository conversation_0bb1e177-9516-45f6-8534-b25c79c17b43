{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982aaec622388e1762c97b2fd2b7266d0e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983563bbf7f0ed36bc3809ff5c2abc7724", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838f88a713611b26d5a146d669edf0e1a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985535b3816ea6fa2e4de13334c3621773", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838f88a713611b26d5a146d669edf0e1a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cf47798e4ad471eb6328a1e054d3a085", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9852e0addb21e2d7aa0f5d7625212e3b94", "guid": "bfdfe7dc352907fc980b868725387e986924fbd82b11e9256490f59c981cbb9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828a8212b505f2e2797bcec9fa56cf484", "guid": "bfdfe7dc352907fc980b868725387e9870c000a32c0baf206fb3ae1a66174bf9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dea6fc1e110faac879c47e698c679754", "guid": "bfdfe7dc352907fc980b868725387e986ddde8a502adc08c025da17cfedbb809", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98802ac3812b8538d1f36c01aa4e8f98f7", "guid": "bfdfe7dc352907fc980b868725387e98af1f035f08e8344db3725288c052ec3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd5d06e0983378ab33a3db4b7aed958f", "guid": "bfdfe7dc352907fc980b868725387e98a5e49037e1dfa46aefb96817c09ee22f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d673b3ce893a7455e185259d0a6d2d5d", "guid": "bfdfe7dc352907fc980b868725387e98cc4c76ddeab4b5c6074db61ef35f35e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1d1bec8986909546570b497ec65ceee", "guid": "bfdfe7dc352907fc980b868725387e982a988493ff568edba20805920e152f0f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bff3d2cbcc8d3f18eeb3da0b728f899f", "guid": "bfdfe7dc352907fc980b868725387e9888255712a1712a3f3a1509f358faf62c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb565de2d9f8fad04e5690a2c949af6c", "guid": "bfdfe7dc352907fc980b868725387e984ea0b62f1b17d5501dff249b8fdbdf9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7e2e45e757cd1a8e5f221697d5adb83", "guid": "bfdfe7dc352907fc980b868725387e9847168ce5e1dad1ddcf6263836781c50d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98118cbfdd1581f5242842541d994ddbcc", "guid": "bfdfe7dc352907fc980b868725387e98f37db6e669f4bea47bc61e9493d5b4a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98639eddc22b09d3ef7e57a03c565e316c", "guid": "bfdfe7dc352907fc980b868725387e98a582d6933943b0f3d5f1f2518094a858", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985425b6bbc8a649fc60ed5e69fbd66b13", "guid": "bfdfe7dc352907fc980b868725387e9856783182badaa58d37f778b0de8ae7e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1fbf3209e712c269d3e1b0efe7407e0", "guid": "bfdfe7dc352907fc980b868725387e98ae20dace7259fecfe91f5a9dfff3ea28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984da42d5541631fc7325660fc33f23c41", "guid": "bfdfe7dc352907fc980b868725387e983053b7a534f441c062c415e3ce3c6ffe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cef1443d232bb70e4477410f6dab1f8", "guid": "bfdfe7dc352907fc980b868725387e98606ce825c5733249c94d644da3ab756a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc961074f8a879732b94055041e797d2", "guid": "bfdfe7dc352907fc980b868725387e98ec937df32d768e3a208f0a8ffb5205dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840db25f8922f45dc36ab08488e96b9a4", "guid": "bfdfe7dc352907fc980b868725387e989d1771151626dfee89d90427c930a773", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98168adcd9081f99d1cdfe6c7c207d4714", "guid": "bfdfe7dc352907fc980b868725387e986868c1dd0c837ec1419463d1fd515956", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843b52d5f188266d44edef58cfc4d45c5", "guid": "bfdfe7dc352907fc980b868725387e98ff10b457a17cf8c184e54870d3cb2a54", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c3c31c4bbbcd9cd3360a45908dec70a", "guid": "bfdfe7dc352907fc980b868725387e98dfc99ac07f81a14615c3a91da78ee595", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a5ac93693fa4f1c56fe0bf37c67b92c", "guid": "bfdfe7dc352907fc980b868725387e981e40febba9f6be793ebedf3e35eb21fc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9818a0aad5e8fc8537b855e893d9070009", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98570f9cb2f8340fba16c899a77afc8d3f", "guid": "bfdfe7dc352907fc980b868725387e98b3516a402faa702466e2843bbc3788ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de338375113d2bf7b25f11fe6d1ca2d9", "guid": "bfdfe7dc352907fc980b868725387e98a27770f2782f63d009aa1b48601d3568"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98415627a93ef7363afd54bfcf4a8d4887", "guid": "bfdfe7dc352907fc980b868725387e9811ac2a78221c184503b840c5d5abadd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989edfd1a543ea8855bea1404d9a618cd4", "guid": "bfdfe7dc352907fc980b868725387e988cb9f77886c7dfdafee0e56e738ad184"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98121364798d0c381e1398725b1412d740", "guid": "bfdfe7dc352907fc980b868725387e981b9b244645b468759d8b1d506f0a8ee9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801ce7d2f862c93032c7840a8e4a8f512", "guid": "bfdfe7dc352907fc980b868725387e98cca5338fb29d506bb7037559e679b02c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98507e0d62cfa23cf54b210d1c01bf4cdd", "guid": "bfdfe7dc352907fc980b868725387e9837f94d5c07d930d52613fb5154755014"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988624cc1028e81db024c8c5e158f3d586", "guid": "bfdfe7dc352907fc980b868725387e98eb8d706181eab242d7504b16d7738c34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b471b09003e26800efa9743cedd0635c", "guid": "bfdfe7dc352907fc980b868725387e98f984b0da76303755f368f3fd9adc4433"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1e59af7f23bcc3441d3f40dfb9117b1", "guid": "bfdfe7dc352907fc980b868725387e98991159e5ca102e81b3c39dbe9d3a8867"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862d59d981dabe7b766589ee9c3834fa1", "guid": "bfdfe7dc352907fc980b868725387e987ff0a71646f29134297c7b69f8fa53a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809e8a654b35b0507c2272148ead6c220", "guid": "bfdfe7dc352907fc980b868725387e98a8106d3b585f4b024c000ab4afe360ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982979bb64438c8da47fc3886bc1d38336", "guid": "bfdfe7dc352907fc980b868725387e9853060feb9d86eed435069ad8c53e3e5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac0407a1b5bb3254a510a6ba72c43150", "guid": "bfdfe7dc352907fc980b868725387e9894a87b04c9b8a0666f768081dd9d61a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1e83e6423aa676a19167aabfaa64c41", "guid": "bfdfe7dc352907fc980b868725387e989ed3254d3e8bc4ef4ea8052384d00c11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813c6525f80cfe045210e65b4462e09d4", "guid": "bfdfe7dc352907fc980b868725387e98e68db741e490aeacd0e6d328acfa210e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b79e1e65aee2846b11bdeb3e4e3767ce", "guid": "bfdfe7dc352907fc980b868725387e988f673fe524fe54e8cbd931e5528a5f60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13a6403d0bdbe43f89c19417fe7cd53", "guid": "bfdfe7dc352907fc980b868725387e984add6fc0c542c2981f5acdb5dd33f395"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3393aaa12efa7e258e5a9185db549dc", "guid": "bfdfe7dc352907fc980b868725387e988446e191d5fed45a0da61c7ed8f6d2c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fba1a55832ed94cd91e6e338d14ce433", "guid": "bfdfe7dc352907fc980b868725387e98b79da882cc2180f937e9bb10e26cb490"}], "guid": "bfdfe7dc352907fc980b868725387e98d5a31c69fdc87e4a33de1579af83cddf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98e8ddcc78a0c78301dcda1c18e63ddb02"}], "guid": "bfdfe7dc352907fc980b868725387e988da6a367d57e80095052b821aa713c85", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98dbd73324ddf2b34f7b6c272c602cb1da", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98b51f6d076a905f1c65c333964e61ffda", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}