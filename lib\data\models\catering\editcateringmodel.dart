class EditCateringRequestModel {
  bool? status;
  String? message;
  int? statusCode;

  EditCateringRequestModel({this.status, this.message, this.statusCode});

  EditCateringRequestModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    return data;
  }
}
