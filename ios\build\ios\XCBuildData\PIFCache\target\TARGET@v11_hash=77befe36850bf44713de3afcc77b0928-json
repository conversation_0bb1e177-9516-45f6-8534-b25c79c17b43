{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981ba31bfbc82cc0697974d45fcff163aa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c828715269dcf18eafd3d54869c3fa9a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98be4a26a834ccd6c4970347a96c3caf15", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5255e10e8d79e2770504cd0bebded31", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98be4a26a834ccd6c4970347a96c3caf15", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985782b6d857e4be1e09c71908f1724be3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9840fe700dfb334077afd9e5df782d476b", "guid": "bfdfe7dc352907fc980b868725387e98334a0e72e735f04f9be0043715fbccf9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9883d5d299db514e626f6a1b6aa114c172", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982e26fc23672a1d4545889c2d315dab61", "guid": "bfdfe7dc352907fc980b868725387e98019425f62ad502e97c07412fd8a7f515"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d46695b03a1b0fa7be043ad6c8c3754", "guid": "bfdfe7dc352907fc980b868725387e984205cbfa8873151fbde19acb4eadfa38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824fc624c705d20579a22825ceb10b9df", "guid": "bfdfe7dc352907fc980b868725387e98188623aca620f77f5f0f8f16806ffc85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c41477b602031daa984aeddc100db068", "guid": "bfdfe7dc352907fc980b868725387e98aeee6f9b6e4f317cb69132ea10ec0bfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abab9b169ee393d37bab23fb2ee932dd", "guid": "bfdfe7dc352907fc980b868725387e98ffa20d9bed97a33ac857aa5954d77af5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bf6c989e10c98bfd5ee7b0dac618acc", "guid": "bfdfe7dc352907fc980b868725387e9815d3121d9452c45929928a0e980bc1f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac962b5d266521efc547dbb2fdfda4d0", "guid": "bfdfe7dc352907fc980b868725387e983ae7a0f1fd73f1316b7ad8653f3e5213"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989365e4c6334998c59234bf87e8eb7ceb", "guid": "bfdfe7dc352907fc980b868725387e9891f3f77f0f045d7c20dc695646e6c21a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a90e20fccd5391732b268d48520b4310", "guid": "bfdfe7dc352907fc980b868725387e98230dbe3edb8e7433cbd399f0d9f078a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842b66097e329aeefde4e0e31fccc5544", "guid": "bfdfe7dc352907fc980b868725387e983fe50b9f83d5367d1bd7a7929886d292"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864984a842d9f7223b03368c23813d428", "guid": "bfdfe7dc352907fc980b868725387e98ecc10ab962fea5708c94c1e57735d43f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4742968c41d7dbb6bbe8ee1f14ab15b", "guid": "bfdfe7dc352907fc980b868725387e98b8ae453a9f8879fceddc88ffb7d6978e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cb53f6ab9ad55fc93226724cf6340cf", "guid": "bfdfe7dc352907fc980b868725387e98df4c77b44ce4e8f41554099090e77de3"}], "guid": "bfdfe7dc352907fc980b868725387e9896f0bd34a8f75f5f900281aef6895321", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9821aa36632a4f0fe9b2fccadd0bf8455e"}], "guid": "bfdfe7dc352907fc980b868725387e9865adea5f8eb621b980e30c4cf2f64d57", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9816368af103db968ee59525e4f425aa3a", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98b4ab211ba516ea4ba3074a15f138c77c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}