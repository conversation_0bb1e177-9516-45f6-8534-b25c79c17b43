import 'dart:developer';
import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/chef/viewchefdetailsmodel.dart';
import 'package:db_eats/data/models/chef/viewdishesmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/ui/catering/dishdetail.dart';
import 'package:db_eats/ui/filter_dish_model.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:db_eats/widgets/location_header.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';

class ViewChef2 extends StatefulWidget {
  final int id;
  final String? title;
  final double? latitude;
  final double? longitude;
  final String? distance;
  final bool? fromCheckout;
  final String? fromPage;

  const ViewChef2({
    super.key,
    required this.id,
    this.title,
    this.latitude,
    this.longitude,
    this.distance,
    this.fromCheckout,
    this.fromPage,
  });

  @override
  State<ViewChef2> createState() => _ViewChef2State();
}

class _ViewChef2State extends State<ViewChef2>
    with SingleTickerProviderStateMixin {
  ChefDetailsModel? chefDetails;
  DishesListModel? dishesData;
  late TabController _tabController;
  List<String> _categories = [];
  double screenWidth = 0;
  double screenHeight = 0;
  // For bottom nav bar
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HomeBloc>().add(ViewChefDetailsEvent(
            data: {
              "chef_id": widget.id.toString(),
              "latitude": widget.latitude?.toString() ?? Initializer.latitude,
              "longitude":
                  widget.longitude?.toString() ?? Initializer.longitude,
            },
          ));
      context.read<HomeBloc>().add(GetDishesListEvent(
            data: {
              "chef_id": widget.id.toString(),
              "search_keyword": "",
              "packaging_type_id": "",
              "cuisine_ids": "",
              "sub_cuisine_ids": "",
              "local_cuisine_ids": "",
              "dietary_preference_id": "",
              "spice_level_id": ""
            },
          ));
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final mq = MediaQuery.of(context);
    screenWidth = mq.size.width;
    screenHeight = mq.size.height;
  }

  Future<void> _navigateBack() async {
    if (widget.fromCheckout == true) {
      int count = 0;
      Navigator.of(context).popUntil((_) => count++ >= 2);
      return;
    }

    final latitude = double.tryParse(Initializer.latitude ?? '0') ?? 0.0;
    final longitude = double.tryParse(Initializer.longitude ?? '0') ?? 0.0;

    if (widget.fromPage == 'TopRatedChefs') {
      context.read<HomeBloc>().add(
            GetTopRatedChefsEvent(latitude: latitude, longitude: longitude),
          );
    } else if (widget.fromPage == 'RecommendedChefs') {
      context.read<HomeBloc>().add(
            GetRecommendedChefsEvent(latitude: latitude, longitude: longitude),
          );
    } else if (widget.fromPage == 'RecommendedChefsNearMe') {
      context.read<HomeBloc>().add(
            GetPopularChefsNearEvent(latitude: latitude, longitude: longitude),
          );
    } else if (widget.fromPage == 'home') {
      final savedFilters = await Initializer.getAppliedFilters();
      final requestData = <String, dynamic>{
        ...?savedFilters,
        'latitude': Initializer.latitude,
        'longitude': Initializer.longitude,
      };
      context.read<HomeBloc>().add(GetHomeDataEvent(data: requestData));
    } else {
      final savedFilters = await Initializer.getAppliedFilters();
      if (savedFilters != null) {
        final requestData = <String, dynamic>{
          ...?savedFilters,
          'latitude': Initializer.latitude,
          'longitude': Initializer.longitude,
        };
        context.read<HomeBloc>().add(GetHomeDataEvent(data: requestData));
      }
    }
    Navigator.pop(context);
  }

  void _openCart() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CartPage()),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WithNavBar(
      currentIndex: _currentIndex,
      child: WillPopScope(
        onWillPop: () async {
          _navigateBack();
          return true;
        },
        child: Scaffold(
          backgroundColor: const Color(0xFFF6F3EC),
          floatingActionButton: CartFloatingActionButton(
            itemCount: Initializer.cartCount ?? 0,
            onPressed: _openCart,
          ),
          body: SafeArea(
            child: Column(
              children: [
                const LocationHeader(),
                Expanded(
                  child: MultiBlocListener(
                    listeners: [
                      BlocListener<HomeBloc, HomeState>(
                        listener: (context, state) {
                          if (state is ChefDetailsSuccess) {
                            setState(() {
                              chefDetails = state.data;
                            });
                          }
                          if (state is DishesListSuccess) {
                            setState(() {
                              dishesData = state.data;
                              _categories = dishesData?.data?.categoryBasedList
                                      ?.map((cat) => cat.category?.name ?? '')
                                      .toList() ??
                                  [];
                              _tabController = TabController(
                                  length: _categories.length, vsync: this);
                            });
                          }
                        },
                      ),
                      BlocListener<AccountBloc, AccountState>(
                        listener: (context, state) {
                          if (state is AddFavouriteChefSuccess) {
                            // Update the local chef details
                            if (chefDetails?.data != null) {
                              setState(() {
                                chefDetails!.data!.isFavourite = true;
                              });
                            }
                          } else if (state is RemoveFavouriteChefSuccess) {
                            // Update the local chef details
                            if (chefDetails?.data != null) {
                              setState(() {
                                chefDetails!.data!.isFavourite = false;
                              });
                            }
                          }
                        },
                      ),
                    ],
                    child: BlocBuilder<HomeBloc, HomeState>(
                      builder: (context, state) {
                        if (state is DishesListLoading ||
                            state is LoadingChefDetails) {
                          return _buildShimmerLoading(
                              context, screenWidth, screenHeight);
                        }
                        return SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: screenWidth * 0.04,
                                  vertical: screenHeight * 0.01,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      height: screenHeight * 0.18,
                                      child: Stack(
                                        clipBehavior: Clip.none,
                                        children: [
                                          Container(
                                            width: double.infinity,
                                            height: screenHeight * 0.148,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      screenWidth * 0.03),
                                            ),
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      screenWidth * 0.03),
                                              child: Stack(
                                                children: [
                                                  Shimmer.fromColors(
                                                    baseColor:
                                                        Colors.grey[300]!,
                                                    highlightColor:
                                                        Colors.grey[100]!,
                                                    child: Container(
                                                      width: double.infinity,
                                                      height:
                                                          screenHeight * 0.148,
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                  Image.network(
                                                    ServerHelper.imageUrl +
                                                        (chefDetails?.data?.chef
                                                                ?.coverPhoto ??
                                                            ''),
                                                    width: double.infinity,
                                                    height:
                                                        screenHeight * 0.148,
                                                    fit: BoxFit.cover,
                                                    loadingBuilder: (context,
                                                        child,
                                                        loadingProgress) {
                                                      if (loadingProgress ==
                                                          null) {
                                                        return child;
                                                      }
                                                      return Shimmer.fromColors(
                                                        baseColor:
                                                            Colors.grey[300]!,
                                                        highlightColor:
                                                            Colors.grey[100]!,
                                                        child: Container(
                                                          width:
                                                              double.infinity,
                                                          height: screenHeight *
                                                              0.148,
                                                          color: Colors.white,
                                                        ),
                                                      );
                                                    },
                                                    errorBuilder: (context,
                                                        error, stackTrace) {
                                                      return Image.asset(
                                                        'assets/images/placeholder.png',
                                                        width: double.infinity,
                                                        height: screenHeight *
                                                            0.148,
                                                        fit: BoxFit.cover,
                                                      );
                                                    },
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          Align(
                                            alignment: Alignment.bottomRight,
                                            child: FractionalTranslation(
                                              translation:
                                                  const Offset(-0.21, -0.03),
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  border: Border.all(
                                                    color:
                                                        const Color(0xFFF6F3EC),
                                                    width: screenWidth * 0.015,
                                                  ),
                                                ),
                                                child: CircleAvatar(
                                                  radius: screenWidth * 0.099,
                                                  backgroundImage: NetworkImage(
                                                    ServerHelper.imageUrl +
                                                        (chefDetails?.data?.chef
                                                                ?.profilePhoto ??
                                                            ''),
                                                  ),
                                                  backgroundColor:
                                                      const Color(0xFFF6F3EC),
                                                  onBackgroundImageError:
                                                      (exception,
                                                          stackTrace) {},
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                '${chefDetails?.data?.chef?.chef?.firstName ?? ''} ${chefDetails?.data?.chef?.chef?.lastName ?? ''}',
                                                style: TextStyle(
                                                  fontFamily: 'Inter',
                                                  fontSize: screenWidth * 0.045,
                                                  fontWeight: FontWeight.w600,
                                                  color:
                                                      const Color(0xFF1F2122),
                                                ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: screenHeight * 0.017),
                                    Row(
                                      children: [
                                        _buildMetricContainer(
                                          context,
                                          screenWidth,
                                          icon: Icons.star,
                                          text: chefDetails
                                                  ?.data?.chef?.averageRating ??
                                              '0.0',
                                        ),
                                        SizedBox(width: screenWidth * 0.02),
                                        _buildMetricContainer(
                                          context,
                                          screenWidth,
                                          icon: Icons.location_on_outlined,
                                          text: _formatDistance(chefDetails
                                              ?.data?.chef?.distance
                                              ?.toDouble()),
                                        ),
                                        SizedBox(width: screenWidth * 0.018),
                                        _buildFavoriteButton(
                                            context, screenWidth),
                                      ],
                                    ),
                                    SizedBox(height: screenHeight * 0.016),
                                    Wrap(
                                      spacing: screenWidth * 0.03,
                                      runSpacing: screenHeight * 0.005,
                                      children: (chefDetails
                                                  ?.data?.chef?.searchTags ??
                                              [])
                                          .map((tag) => Text(
                                                tag,
                                                style: TextStyle(
                                                  fontFamily: 'Inter',
                                                  fontSize: screenWidth * 0.035,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      const Color(0xFF414346),
                                                ),
                                              ))
                                          .toList(),
                                    ),
                                    SizedBox(height: screenHeight * 0.02),
                                    Text(
                                      chefDetails?.data?.chef?.description ??
                                          '',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: screenWidth * 0.035,
                                        fontWeight: FontWeight.w400,
                                        color: const Color(0xFF414346),
                                      ),
                                    ),
                                    SizedBox(height: screenHeight * 0.017),
                                    if ((chefDetails?.data?.chef?.chef
                                                    ?.operationDays !=
                                                null &&
                                            (chefDetails
                                                    ?.data
                                                    ?.chef
                                                    ?.chef
                                                    ?.operationDays
                                                    ?.isNotEmpty ??
                                                false)) ||
                                        (chefDetails?.data?.chef?.chef
                                                    ?.operationTimes !=
                                                null &&
                                            (chefDetails
                                                    ?.data
                                                    ?.chef
                                                    ?.chef
                                                    ?.operationTimes
                                                    ?.isNotEmpty ??
                                                false)))
                                      Row(
                                        children: [
                                          Image.asset(
                                            'assets/icons/calender_3.png',
                                            width: screenWidth * 0.05,
                                            height: screenWidth * 0.045,
                                            color: const Color(0xFF414346),
                                          ),
                                          SizedBox(width: screenWidth * 0.04),
                                          Text(
                                            _formatOperationTimes(chefDetails
                                                ?.data
                                                ?.chef
                                                ?.chef
                                                ?.operationTimes),
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w600,
                                              fontSize: screenWidth * 0.035,
                                            ),
                                          ),
                                          SizedBox(width: screenWidth * 0.04),
                                          Text(
                                            _formatOperationDays(chefDetails
                                                ?.data
                                                ?.chef
                                                ?.chef
                                                ?.operationDays),
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w400,
                                              fontSize: screenWidth * 0.035,
                                              letterSpacing: 1,
                                            ),
                                          ),
                                        ],
                                      ),
                                    SizedBox(height: screenHeight * 0.017),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: screenWidth * 0.04),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      height: screenHeight * 0.04,
                                      child: ListView(
                                        scrollDirection: Axis.horizontal,
                                        children: _categories
                                            .asMap()
                                            .entries
                                            .map((entry) {
                                          final index = entry.key;
                                          final category = entry.value;
                                          final isSelected =
                                              _tabController.index == index;
                                          return GestureDetector(
                                            onTap: () {
                                              setState(() {
                                                _tabController.animateTo(index);
                                              });
                                            },
                                            child: Container(
                                              margin: EdgeInsets.only(
                                                  right: screenWidth * 0.02),
                                              padding: EdgeInsets.symmetric(
                                                horizontal: screenWidth * 0.03,
                                                vertical: screenHeight * 0.0010,
                                              ),
                                              decoration: BoxDecoration(
                                                color: isSelected
                                                    ? const Color(0xFFB9B6AD)
                                                    : const Color(0xFFE1DDD5),
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        screenWidth * 0.09),
                                              ),
                                              child: Center(
                                                child: Text(
                                                  category,
                                                  style: TextStyle(
                                                    fontFamily: 'Inter-medium',
                                                    fontSize:
                                                        screenWidth * 0.030,
                                                    // fontWeight: FontWeight.w600,
                                                    color:
                                                        const Color(0xFF1F2122),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        }).toList(),
                                      ),
                                    ),
                                    SizedBox(height: screenHeight * 0.02),
                                    Container(
                                      height: screenHeight * 0.05,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                            screenWidth * 0.09),
                                        border: Border.all(
                                            color: const Color(0xFF1F2122)),
                                      ),
                                      child: InkWell(
                                        borderRadius: BorderRadius.circular(
                                            screenWidth * 0.03),
                                        onTap: () {
                                          showModalBottomSheet(
                                            context: context,
                                            isScrollControlled: true,
                                            backgroundColor: Colors.transparent,
                                            builder: (context) =>
                                                FilterDishModel(
                                              chefId: widget.id,
                                              screenWidth: screenWidth,
                                            ),
                                          );
                                        },
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(Icons.tune,
                                                size: screenWidth * 0.03,
                                                color: const Color(0xFF1F2122)),
                                            SizedBox(width: screenWidth * 0.02),
                                            Text(
                                              "View Filters",
                                              style: TextStyle(
                                                fontSize: screenWidth * 0.035,
                                                fontWeight: FontWeight.w600,
                                                color: const Color(0xFF1F2122),
                                                fontFamily: 'Inter',
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: screenHeight * 0.02),
                                    Text(
                                      "Featured Items",
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: screenWidth * 0.05,
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                    SizedBox(height: screenHeight * 0.017),
                                    _buildFeaturedItems(
                                        context, screenWidth, screenHeight),
                                    SizedBox(height: screenHeight * 0.01),
                                    _buildCategoryContent(
                                        context, screenWidth, screenHeight),
                                    SizedBox(height: screenHeight * 0.08),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMetricContainer(BuildContext context, double screenWidth,
      {required IconData icon, required String text}) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.03, vertical: screenWidth * 0.015),
      decoration: BoxDecoration(
        color: const Color(0xFFF6F3EC),
        borderRadius: BorderRadius.circular(screenWidth * 0.02),
        border: Border.all(color: const Color(0xFFE1E3E6)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: screenWidth * 0.04, color: const Color(0xFF414346)),
          SizedBox(width: screenWidth * 0.01),
          Text(
            text,
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: screenWidth * 0.035,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF1F2122),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteButton(BuildContext context, double screenWidth) {
    return Container(
      width: screenWidth * 0.08,
      height: screenWidth * 0.08,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(screenWidth * 0.02),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: BlocBuilder<AccountBloc, AccountState>(
          builder: (context, state) {
            bool isFavorite = chefDetails?.data?.isFavourite ?? false;

            // Handle loading states
            if (state is AddFavouriteChefLoading ||
                state is RemoveFavouriteChefLoading) {
              return SizedBox(
                width: screenWidth * 0.05,
                height: screenWidth * 0.05,
                child: CupertinoActivityIndicator(
                  radius: screenWidth * 0.010,
                  color: const Color(0xFF1F2122),
                ),
              );
            }

            return Icon(
              isFavorite ? Icons.favorite : Icons.favorite_border,
              color: isFavorite ? Colors.red : const Color(0xFF1F2122),
              size: screenWidth * 0.05,
            );
          },
        ),
        onPressed: () {
          final isFavorite = chefDetails?.data?.isFavourite ?? false;
          final chefId = chefDetails?.data?.chef?.chefId ?? 0;

          print('Debug: Current isFavorite: $isFavorite, chefId: $chefId');

          if (isFavorite) {
            print('Debug: Calling RemoveFavouriteChefEvent');
            context.read<AccountBloc>().add(RemoveFavouriteChefEvent(chefId));
          } else {
            print('Debug: Calling AddFavouriteChefEvent');
            context.read<AccountBloc>().add(AddFavouriteChefEvent(chefId));
          }
        },
        padding: EdgeInsets.zero,
        tooltip: chefDetails?.data?.isFavourite ?? false
            ? 'Remove from favorites'
            : 'Add to favorites',
      ),
    );
  }

  String _formatOperationTimes(List<dynamic>? operationTimes) {
    if (operationTimes == null || operationTimes.isEmpty) return 'Open: N/A';
    var time = operationTimes.first;
    String startTime = time.timing?.startTime ?? 'N/A';
    String endTime = time.timing?.endTime ?? 'N/A';
    String formattedStart = _formatTimeString(startTime);
    String formattedEnd = _formatTimeString(endTime);
    return 'Open $formattedStart-$formattedEnd';
  }

  String _formatTimeString(String timeStr) {
    if (timeStr == 'N/A') return timeStr;
    try {
      List<String> parts = timeStr.split(':');
      if (parts.length < 2) return timeStr;
      int hour = int.parse(parts[0]);
      bool isPM = hour >= 12;
      if (hour > 12) hour -= 12;
      if (hour == 0) hour = 12;
      return '$hour${isPM ? 'PM' : 'AM'}';
    } catch (e) {
      return timeStr;
    }
  }

  String _formatOperationDays(List<dynamic>? operationDays) {
    if (operationDays == null || operationDays.isEmpty) return 'N/A';
    Map<String, String> dayAbbreviations = {
      'mon': 'M',
      'tue': 'T',
      'wed': 'W',
      'thu': 'Th',
      'fri': 'F',
      'sat': 'Sat',
      'sun': 'Sun',
    };
    List<String> formattedDays = operationDays
        .map((day) => dayAbbreviations[day.day?.name?.toLowerCase()] ?? '')
        .where((day) => day.isNotEmpty)
        .toList();
    List<String> dayOrder = ['M', 'T', 'W', 'Th', 'F', 'Sat', 'Sun'];
    formattedDays
        .sort((a, b) => dayOrder.indexOf(a).compareTo(dayOrder.indexOf(b)));
    return formattedDays.isEmpty ? 'N/A' : formattedDays.join(', ');
  }

  bool _isDishInCart(int dishId) {
    final featuredDish = dishesData?.data?.featuredList
        ?.firstWhere((d) => d.id == dishId, orElse: () => FeaturedList());
    if (featuredDish?.inCart == true) {
      return true;
    }
    if (dishesData?.data?.categoryBasedList != null) {
      for (final category in dishesData!.data!.categoryBasedList!) {
        if (category.dishList != null) {
          final categoryDish = category.dishList!
              .firstWhere((d) => d.id == dishId, orElse: () => DishList());
          if (categoryDish.inCart == true) {
            return true;
          }
        }
      }
    }
    return false;
  }

  Widget _buildFeaturedItems(
      BuildContext context, double screenWidth, double screenHeight) {
    if (dishesData?.data?.featuredList == null ||
        dishesData!.data!.featuredList!.isEmpty) {
      return const SizedBox();
    }
    return SizedBox(
      height: screenHeight * 0.31,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.only(right: screenWidth * 0.04),
        children: dishesData!.data!.featuredList!
            .map((dish) => Container(
                  width: screenWidth * 0.58,
                  margin: EdgeInsets.only(right: screenWidth * 0.03),
                  child: _buildDishCard(context, screenWidth, screenHeight, {
                    'id': dish.id ?? 0,
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices?.first.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices?.first.servingSize?.title ?? '',
                    'serving_size_id':
                        dish.servingSizePrices?.first.servingSize?.id ?? 0,
                  }),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategorySection(BuildContext context, double screenWidth,
      double screenHeight, String category) {
    final categoryList = dishesData?.data?.categoryBasedList
        ?.firstWhere((cat) => cat.category?.name == category,
            orElse: () => CategoryBasedList())
        .dishList;
    if (categoryList == null || categoryList.isEmpty) return const SizedBox();
    return SizedBox(
      height: screenHeight * 0.31,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.only(right: screenWidth * 0.04),
        children: categoryList
            .map((dish) => Container(
                  width: screenWidth * 0.58,
                  margin: EdgeInsets.only(right: screenWidth * 0.03),
                  child: _buildDishCard(context, screenWidth, screenHeight, {
                    'id': dish.id ?? 0,
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices?.first.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices?.first.servingSize?.title ?? '',
                    'serving_size_id':
                        dish.servingSizePrices?.first.servingSize?.id ?? 0,
                  }),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategoryContent(
      BuildContext context, double screenWidth, double screenHeight) {
    if ((dishesData?.data?.featuredList?.isEmpty ?? true) &&
        ((dishesData?.data?.categoryBasedList?.isEmpty ?? true) ||
            (dishesData?.data?.categoryBasedList
                    ?.every((category) => category.dishList?.isEmpty ?? true) ??
                true))) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/icons/no_data.png',
              width: screenWidth * 0.3,
              height: screenWidth * 0.3,
            ),
            SizedBox(height: screenHeight * 0.02),
            Text(
              'No dishes available',
              style: TextStyle(
                fontSize: screenWidth * 0.045,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF1F2122),
                fontFamily: 'Inter',
              ),
            ),
          ],
        ),
      );
    }
    if (dishesData?.data?.categoryBasedList == null ||
        dishesData!.data!.categoryBasedList!.isEmpty) {
      return const SizedBox();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: dishesData!.data!.categoryBasedList!.map((category) {
        final categoryName = category.category?.name ?? '';
        final dishes = category.dishList ?? [];
        if (dishes.isEmpty) return const SizedBox();
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: screenHeight * 0.01),
              child: Text(
                categoryName,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: screenWidth * 0.05,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1F2122),
                ),
              ),
            ),
            SizedBox(height: screenHeight * 0.01),
            _buildCategorySection(
                context, screenWidth, screenHeight, categoryName),
            SizedBox(height: screenHeight * 0.02),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildShimmerLoading(
      BuildContext context, double screenWidth, double screenHeight) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    height: screenHeight * 0.14,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(screenWidth * 0.03),
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Row(
                    children: [
                      Container(
                        width: screenWidth * 0.15,
                        height: screenWidth * 0.15,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.04),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              height: screenWidth * 0.05,
                              width: screenWidth * 0.5,
                              color: Colors.white,
                            ),
                            SizedBox(height: screenHeight * 0.01),
                            Container(
                              height: screenWidth * 0.035,
                              width: screenWidth * 0.3,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Row(
                    children: List.generate(
                      4,
                      (index) => Container(
                        margin: EdgeInsets.only(right: screenWidth * 0.02),
                        width: screenWidth * 0.2,
                        height: screenWidth * 0.08,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.03),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Container(
                    height: screenHeight * 0.1,
                    color: Colors.white,
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Container(
                    height: screenWidth * 0.05,
                    width: screenWidth * 0.4,
                    color: Colors.white,
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  SizedBox(
                    height: screenHeight * 0.04,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children: List.generate(
                        4,
                        (index) => Container(
                          margin: EdgeInsets.only(right: screenWidth * 0.02),
                          width: screenWidth * 0.25,
                          height: screenWidth * 0.08,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.03),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Container(
                    width: screenWidth * 0.4,
                    height: screenWidth * 0.05,
                    color: Colors.white,
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  SizedBox(
                    height: screenHeight * 0.33,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      padding: EdgeInsets.only(right: screenWidth * 0.04),
                      children: List.generate(
                        3,
                        (index) => _buildShimmerDishCard(
                            context, screenWidth, screenHeight),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerDishCard(
      BuildContext context, double screenWidth, double screenHeight) {
    return Container(
      width: screenWidth * 0.58,
      height: screenHeight * 0.2,
      margin: EdgeInsets.only(right: screenWidth * 0.03),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(screenWidth * 0.03),
      ),
      child: Column(
        children: [
          Container(
            height: screenHeight * 0.18,
            color: Colors.white,
          ),
          Padding(
            padding: EdgeInsets.all(screenWidth * 0.04),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: screenWidth * 0.4,
                  height: screenWidth * 0.04,
                  color: Colors.white,
                ),
                SizedBox(height: screenHeight * 0.01),
                Container(
                  width: screenWidth * 0.3,
                  height: screenWidth * 0.035,
                  color: Colors.white,
                ),
                SizedBox(height: screenHeight * 0.02),
                Container(
                  width: screenWidth * 0.2,
                  height: screenWidth * 0.05,
                  color: Colors.white,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDishCard(BuildContext context, double screenWidth,
      double screenHeight, Map<String, dynamic> dish) {
    final priceDouble = double.tryParse(dish['price'] ?? '0.00') ?? 0.0;
    const rating = "90";

    return GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => DishDetailPage(
                dishId: dish['id'].toString(),
                chefId: chefDetails?.data?.chef?.chefId ?? 0,
              ),
            ),
          );
        },
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(screenWidth * 0.03),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.vertical(
                    top: Radius.circular(screenWidth * 0.03)),
                child: Image.network(
                  ServerHelper.imageUrl + (dish['photo'] ?? ''),
                  height: screenHeight * 0.18,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: screenHeight * 0.18,
                      width: double.infinity,
                      color: Colors.grey[200],
                      child: const Center(child: Text('Image not available')),
                    );
                  },
                ),
              ),
              Padding(
                padding: EdgeInsets.all(screenWidth * 0.035),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      dish['name'] ?? 'Unknown Dish',
                      style: TextStyle(
                        fontSize: screenWidth * 0.035,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF1F2122),
                        fontFamily: 'Inter',
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: screenHeight * 0.012),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Wrap(
                                spacing: screenWidth * 0.02,
                                runSpacing: screenHeight * 0.01,
                                children: [
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: screenWidth * 0.015,
                                      vertical: screenWidth * 0.005,
                                    ),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFE1E3E6),
                                      borderRadius: BorderRadius.circular(
                                          screenWidth * 0.03),
                                    ),
                                    child: Text(
                                      "${dish['serving_size'].split(' ').first} Servings",
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.025,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Inter',
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: screenHeight * 0.015),
                              Text.rich(
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text: '\$',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: screenWidth * 0.03,
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                    TextSpan(
                                      text: priceDouble.toStringAsFixed(2),
                                      style: TextStyle(
                                        fontFamily: 'Roboto',
                                        fontSize: screenWidth * 0.03,
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        BlocListener<AccountBloc, AccountState>(
                          listener: (context, state) {
                            if (state is AddToCartSuccess) {
                              ScaffoldMessenger.of(context)
                                ..hideCurrentSnackBar()
                                ..showSnackBar(
                                  SnackBar(
                                    behavior: SnackBarBehavior.floating,
                                    margin: EdgeInsets.only(
                                      bottom:
                                          MediaQuery.of(context).size.height *
                                              0.04,
                                      left: 20,
                                      right: 20,
                                    ),
                                    duration: const Duration(seconds: 1),
                                    backgroundColor: Colors.black87,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(19),
                                    ),
                                    content: Row(
                                      children: [
                                        const Icon(Icons.check_circle,
                                            color: Colors.greenAccent),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Text(
                                            state.message,
                                            style: const TextStyle(
                                              fontSize: 12,
                                              color: Colors.white,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              context
                                  .read<AccountBloc>()
                                  .add(GetCartCountEvent());
                              context.read<HomeBloc>().add(GetDishesListEvent(
                                    data: {
                                      "chef_id": widget.id.toString(),
                                      "search_keyword": "",
                                      "packaging_type_id": "",
                                      "cuisine_ids": "",
                                      "sub_cuisine_ids": "",
                                      "local_cuisine_ids": "",
                                      "dietary_preference_id": "",
                                      "spice_level_id": ""
                                    },
                                  ));
                            } else if (state is AddToCartFailed) {
                              ScaffoldMessenger.of(context)
                                ..hideCurrentSnackBar()
                                ..showSnackBar(
                                  SnackBar(
                                    content: Text(state.message),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                            }
                            if (state is GetCartCountSuccess) {
                              setState(() {});
                            }
                          },
                          child: BlocBuilder<AccountBloc, AccountState>(
                            builder: (context, state) {
                              final isLoading = state is AddToCartLoading &&
                                  state.dishId == dish['id'];
                              final bool isInCart = _isDishInCart(dish['id']);
                              return GestureDetector(
                                onTap: isLoading || isInCart
                                    ? null
                                    : () {
                                        if (dish['id'] == 0) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            const SnackBar(
                                              content: Text('Invalid dish ID'),
                                              backgroundColor: Colors.red,
                                            ),
                                          );
                                          return;
                                        }
                                        if (dish['serving_size_id'] == 0) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            const SnackBar(
                                              content: Text(
                                                  'Invalid serving size ID'),
                                              backgroundColor: Colors.red,
                                            ),
                                          );
                                          return;
                                        }
                                        context.read<AccountBloc>().add(
                                              AddToCartEvent({
                                                'chef_id': chefDetails
                                                        ?.data?.chef?.chefId ??
                                                    0,
                                                'chef_dish_id': dish['id'],
                                                'quantity': 1,
                                                'serving_size_id':
                                                    dish['serving_size_id'],
                                              }),
                                            );
                                      },
                                child: isLoading
                                    ? SizedBox(
                                        width: screenWidth * 0.06,
                                        height: screenWidth * 0.06,
                                        child: const CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.black),
                                        ),
                                      )
                                    : isInCart
                                        ? Container(
                                            width: screenWidth * 0.06,
                                            height: screenWidth * 0.06,
                                            decoration: const BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: Color(0xFF1F2122),
                                            ),
                                            child: Icon(
                                              Icons.check,
                                              color: Colors.white,
                                              size: screenWidth * 0.04,
                                            ),
                                          )
                                        : Image.asset(
                                            'assets/icons/add.png',
                                            width: screenWidth * 0.06,
                                            height: screenWidth * 0.06,
                                            semanticLabel: 'Add to cart',
                                          ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }
}

String _formatDistance(double? distance) {
  if (distance == null) return '0';
  final kilometers = distance / 1000;
  return '${kilometers.toStringAsFixed(1)} km';
}
