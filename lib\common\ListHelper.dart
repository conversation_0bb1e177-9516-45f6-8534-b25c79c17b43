// ignore_for_file: file_names

import 'package:flutter/material.dart';

class ListHelper {
  static Widget buildSavedFormsListView(List<Map<String, dynamic>> savedForms,
      Set<int> selectedIndices, Function(int) onSelectionChanged) {
    return ListView.separated(
      itemCount: savedForms.length,
      separatorBuilder: (context, index) => const SizedBox(height: 16.0),
      itemBuilder: (context, index) {
        var form = savedForms[index];
        final dateCreated = form['datecreated'] ?? 'N/A';
        late String date, time;

        if (dateCreated != 'N/A') {
          final dateTime = dateCreated.split(' ');
          date = dateTime.first;
          time = dateTime.length > 1 ? dateTime[1] : 'N/A';
        } else {
          date = 'N/A';
          time = 'N/A';
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        form['formname'] ?? 'No form name',
                        style: const TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 10.0),
                      Row(
                        children: [
                          const Icon(Icons.calendar_today,
                              size: 13, color: Colors.blue),
                          const SizedBox(width: 4.0),
                          Text(
                            date,
                            style: const TextStyle(fontSize: 11),
                          ),
                          const SizedBox(width: 20.0),
                          Text(
                            time,
                            style: const TextStyle(fontSize: 11),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Checkbox(
                  value: selectedIndices.contains(index),
                  onChanged: (value) {
                    onSelectionChanged(index);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
