{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981be09ec3af090c293ea3da796f811372", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9816492c973f618c57f9fdcedd7315978e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987bc5535718ccdff782c7fba91783ee89", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f91153efa2fe722728db1691eafdee72", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987bc5535718ccdff782c7fba91783ee89", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9806c16ee5941c2b9e0e88daf19eb102b7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f59754a4d6e3886742b94cf41bb6bae8", "guid": "bfdfe7dc352907fc980b868725387e982d36481b257a46c5fca6d16cba558e27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813906b1a16d433394acb7d0734f2097d", "guid": "bfdfe7dc352907fc980b868725387e989b9e706a1c5d59f13cb84c6c79a64a25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98578be75d14c45531890fea03ff34637b", "guid": "bfdfe7dc352907fc980b868725387e9882ae2bf5df43d434b2f45f432542dbc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ae85a1786e50d5def7b63f6f49abe26", "guid": "bfdfe7dc352907fc980b868725387e98714eeafc35f354d549fa71b407b434a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a6b07cf5c6b168bd5079e1779d86dc8", "guid": "bfdfe7dc352907fc980b868725387e98786f4828d5387cd9e92b372b1c67a61e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98485e17684c47628fe95f712e2b8b45b5", "guid": "bfdfe7dc352907fc980b868725387e98902315d7878f950f4f6a4323e7e19484", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca4bfd5ee1ffae5d1f21b1592e850bbe", "guid": "bfdfe7dc352907fc980b868725387e98b55be2e903af6b8c282be5cff0b40bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98426798eb65843f8de91940da0a99d7c3", "guid": "bfdfe7dc352907fc980b868725387e982cddac4b5d5b640ce678dccb985ab7e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897f215327d01e292465faee1fde59f46", "guid": "bfdfe7dc352907fc980b868725387e98c69b8fc8d105c61b4cd17e8333d44a8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e91dadf5cc839c9bf6a7e45841a7470f", "guid": "bfdfe7dc352907fc980b868725387e987c7262f6bb4c1299e26d6ebbbdb37f21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfe3603db217003bc20f8400731bc7ed", "guid": "bfdfe7dc352907fc980b868725387e98065e8d82102f90e23a0fb27adc424e98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d28b0e52cd4db162a6dcb138ae0c29e", "guid": "bfdfe7dc352907fc980b868725387e98d8373dba5656c392717c98bffc03772d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cd4bc581811ae42cc5a1c23054bc4d7", "guid": "bfdfe7dc352907fc980b868725387e982877a68c5ac61c0ce0a8fb448d620301", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98207222a076e11076cc2a4685ee847bc7", "guid": "bfdfe7dc352907fc980b868725387e988657871d0eb40ddfb9afd4da445dce22", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc3fbc2902644cfeeb971ea5029f675c", "guid": "bfdfe7dc352907fc980b868725387e9858c1cb3b8192e16761e27180ee5d5b6a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dafd7ecd6d5f2bdaf25667958a2a2d3", "guid": "bfdfe7dc352907fc980b868725387e98768b472c7fff6ddaaa87a5f448dfdb6c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880898dc8fb6de46f0bd9dea13f1507bf", "guid": "bfdfe7dc352907fc980b868725387e987e1bd33450db4dada3950eceed52a87d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3d272cd771b6e919f87306bfe6ae075", "guid": "bfdfe7dc352907fc980b868725387e9874ef9ea67eb536be8da3c12370c4fe59", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3f51fa3e052555a3b00b41870901767", "guid": "bfdfe7dc352907fc980b868725387e98651ab6c2591da60843578bf33b5fc8f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c32aae221f68c4468dd8b07f282a3cab", "guid": "bfdfe7dc352907fc980b868725387e98d91e95ba4dafd38d88bd06f1c4b01585", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874ccb88cb84fd8af466c45bfb4a5015c", "guid": "bfdfe7dc352907fc980b868725387e98996b91e3b19aa4c301e29105efa47428", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c6f2bbd3eae5bfd54fb4138b6eea88c", "guid": "bfdfe7dc352907fc980b868725387e98dc4f0c44d51138f76fe4fd71920dec89", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98dd64f208c11dac9b793d1e365aadc9be", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984aa81d38ea13c725b78e50cebdebff66", "guid": "bfdfe7dc352907fc980b868725387e98286157d220d928e75cd23763457340ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b7bbd1d4761504dcbb436c611ca1902", "guid": "bfdfe7dc352907fc980b868725387e9857866fb76e4309d1d0224f21b27d26fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98793d100f9774e76b6d5974c4a4593c52", "guid": "bfdfe7dc352907fc980b868725387e981106775e452e7185ea8fbd44a471546c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b4fea024913093343a1a7311dcd3095", "guid": "bfdfe7dc352907fc980b868725387e9871aebb859d5e643bc82395bad4e202ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980970bbd708492fc40105c3ffd4cbab35", "guid": "bfdfe7dc352907fc980b868725387e984098eaae06fb9f08cdcc86b0336d6e43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be5b50dfb8d7af7e296224031bb95d08", "guid": "bfdfe7dc352907fc980b868725387e98dec64dc71db59f5c87df53ee561de8af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820ec7938e10a153095bb431c862969b4", "guid": "bfdfe7dc352907fc980b868725387e982dd869b59ef94929993600e0c8196987"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98062f6299c0252844c4f2a8ad49f5db9c", "guid": "bfdfe7dc352907fc980b868725387e98d48ae99ed7bda3aaaef2f17555e8890c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98491b20b9f7c05e2e7157f67d927fddf7", "guid": "bfdfe7dc352907fc980b868725387e98ece955db296ee38ab8db7538ad81ce06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab193588e18ff0489b9acd13bfc6ee39", "guid": "bfdfe7dc352907fc980b868725387e988e7eda1376718c0f548e638de61cd38a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98761a781ef8760dba6652252e2dc35c07", "guid": "bfdfe7dc352907fc980b868725387e98897a02d6e13a51cb84c674585808ad00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c0cc2655b66c3c6fb7ab80bc7b07807", "guid": "bfdfe7dc352907fc980b868725387e980a5539eb77f8169c91dea1e5f9700366"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb6c3c349ab5891e073da36c39422b17", "guid": "bfdfe7dc352907fc980b868725387e981d57d7ce5d2111b53208966accd02fc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d921677739742407e858dfc1c0c31c", "guid": "bfdfe7dc352907fc980b868725387e989b80259bf90830f7bb808cc4d30769a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981de71247c8bb8660a09f7f6ab09351e0", "guid": "bfdfe7dc352907fc980b868725387e983fae41ec90c90ab0222c2644a5b5835d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4b188bb8213eaec56fab2064027767d", "guid": "bfdfe7dc352907fc980b868725387e9861e957a49b23f85ff15b9ed497065d31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3a9145830a7967f6b1918cba944c429", "guid": "bfdfe7dc352907fc980b868725387e9800496ef5fe73280e0378b4c5b69dfa54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0bafbbe40b4d8c42604048999ac985d", "guid": "bfdfe7dc352907fc980b868725387e985336f68c268f3ad29e4fb8d6e53826d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9d6b7d468974d96ceb1152feba5a115", "guid": "bfdfe7dc352907fc980b868725387e98586b5d10c4e5538d9dee16ed643fcb2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7de9e53c08b5bc776f6620bb79c9120", "guid": "bfdfe7dc352907fc980b868725387e98fc6a9d0e98892367c03e879f8dd10b80"}], "guid": "bfdfe7dc352907fc980b868725387e98af095487fabb930a4ddf589ec102c4b4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98f4f470c666667b31af0499891180730e"}], "guid": "bfdfe7dc352907fc980b868725387e98a6e200cf08d06c1883dd96aecf74cddc", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985858a7fa4e0e2abc40fbe4404ba1d9e4", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98527322a7814f667c9cd4abf1040bacb3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}