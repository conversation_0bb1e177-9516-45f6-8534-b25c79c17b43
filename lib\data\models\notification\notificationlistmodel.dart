class NotificationListModel {
  bool? status;
  String? message;
  int? statusCode;
  NotificationData? data;

  NotificationListModel(
      {this.status, this.message, this.statusCode, this.data});

  NotificationListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null
        ? new NotificationData.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class NotificationData {
  int? total;
  int? page;
  int? limit;
  List<Notifications>? notifications;
  int? unreadCount;

  NotificationData(
      {this.total,
      this.page,
      this.limit,
      this.notifications,
      this.unreadCount});

  NotificationData.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    page = json['page'];
    limit = json['limit'];
    if (json['notifications'] != null) {
      notifications = <Notifications>[];
      json['notifications'].forEach((v) {
        notifications!.add(new Notifications.fromJson(v));
      });
    }
    unreadCount = json['unread_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['total'] = this.total;
    data['page'] = this.page;
    data['limit'] = this.limit;
    if (this.notifications != null) {
      data['notifications'] =
          this.notifications!.map((v) => v.toJson()).toList();
    }
    data['unread_count'] = this.unreadCount;
    return data;
  }
}

class Notifications {
  int? id;
  String? title;
  String? description;
  int? type;
  int? targetId;
  String? status;
  String? createdAt;

  Notifications({
    this.id,
    this.title,
    this.description,
    this.type,
    this.targetId,
    this.status,
    this.createdAt,
  });

  Notifications.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
    type = json['type'];
    targetId = json['target_id'];
    status = json['status'];
    createdAt = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['description'] = this.description;
    data['type'] = this.type;
    data['target_id'] = this.targetId;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    return data;
  }

  Notifications copyWith({
    int? id,
    String? title,
    String? description,
    int? type,
    int? targetId,
    String? status,
    String? createdAt,
  }) {
    return Notifications(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      targetId: targetId ?? this.targetId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
