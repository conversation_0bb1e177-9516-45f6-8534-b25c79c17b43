// ignore_for_file: deprecated_member_use, unnecessary_const
import 'dart:developer';
import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/guesthome/homemodel.dart';
import 'package:db_eats/data/models/guesthome/listsearchedaddressmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/storage/localstorage.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/ui/chef/popular_chefs_near.dart';
import 'package:db_eats/ui/chef/recommended_chefs.dart';
import 'package:db_eats/ui/chef/top_rated_chefs.dart';
import 'package:db_eats/ui/chef/view_chef2.dart';
import 'package:db_eats/ui/deals/dealslist.dart';
import 'package:db_eats/ui/filter_model.dart';
import 'package:db_eats/ui/meal_plan/choose_plan.dart';
import 'package:db_eats/ui/meal_plan_new/choose_plan_step1.dart';
import 'package:db_eats/ui/monthlysaverSubscription/monthlySubscription.dart';
import 'package:db_eats/widgets/cheflocationmodal.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:db_eats/widgets/location_header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shimmer/shimmer.dart';

class Home2 extends StatefulWidget {
  const Home2({super.key});

  @override
  State<Home2> createState() => _Home2State();
}

class _Home2State extends State<Home2> {
  late final MealplanBloc _mealplanBloc;
  double? _currentLatitude;
  double? _currentLongitude;
  List<SearchedAddressData>? _savedAddresses;
  bool _loadingAddresses = false;
  List promoCodes = [];

  @override
  void initState() {
    super.initState();
    _mealplanBloc = MealplanBloc();
    _initializePlayerId();
    _loadAddresses();
    // _mealplanBloc.add(ListTimingEvent());
    // _mealplanBloc.add(GetAddedTimePreferences());
    // context.read<HomeBloc>().add(SetPlayerIdEvent());
    context.read<AccountBloc>().add(GetCartCountEvent());
    context.read<HomeBloc>().add(GetFilterDataEvent());
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  Future<void> _initializePlayerId() async {
    OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
    OneSignal.initialize("************************************");

    OneSignal.Notifications.requestPermission(true);

    // Observer: triggered when the player ID is updated
    OneSignal.User.pushSubscription.addObserver((state) async {
      final playerId = state.current.id;
      log('Player ID changed: $playerId');
      if (playerId != null && playerId.isNotEmpty) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('onesignal_player_id', playerId);
        LocalStorage.savePlayerId(playerId);
        log('Player ID saved to SharedPreferences: $playerId');

        // Dispatch event to HomeBloc
        await ServerHelper.post1(
          '/v1/customer/notification/update-id',
          {'notification_id': playerId},
        );
      }
    });

    // Fallback: delay and manually retrieve
    Future.delayed(const Duration(seconds: 2), () async {
      final playerId = OneSignal.User.pushSubscription.id;
      if (playerId != null && playerId.isNotEmpty) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('onesignal_player_id', playerId);
        LocalStorage.savePlayerId(playerId);
        log('Player ID saved after delay: $playerId');

        await ServerHelper.post1(
          '/v1/customer/notification/update-id',
          {'notification_id': playerId},
        );
      }
    });

    // Note: Notification listeners are already set up in main.dart
    // No need to duplicate them here
  }

  Future<void> _loadAddresses() async {
    if (_loadingAddresses) return;

    setState(() => _loadingAddresses = true);
    try {
      context.read<AccountBloc>().add(ListSearchAddressesEvent());
    } catch (e) {
      log('Error loading addresses: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load addresses: $e')),
      );
    }
  }

  Future<void> _makeHomeDataRequest(double lat, double lng) async {
    final savedFilters = await Initializer.getAppliedFilters();
    if (savedFilters != null) {
      final requestData = <String, dynamic>{
        ...savedFilters,
        'latitude': lat,
        'longitude': lng,
      };
      context.read<HomeBloc>().add(GetHomeDataEvent(data: requestData));
      context.read<HomeBloc>().add(GetPromoCodesEvent(data: requestData));
    } else {
      context.read<HomeBloc>().add(GetPromoCodesEvent(data: <String, dynamic>{
            'latitude': lat,
            'longitude': lng,
          }));
      context.read<HomeBloc>().add(GetHomeDataEvent(
            data: <String, dynamic>{
              'latitude': lat,
              'longitude': lng,
            },
          ));
    }
  }

  @override
  void dispose() {
    _mealplanBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<HomeBloc>.value(
          value: BlocProvider.of<HomeBloc>(context),
        ),
        BlocProvider<MealplanBloc>.value(
          value: _mealplanBloc,
        ),
      ],
      child: BlocListener<AccountBloc, AccountState>(
        listener: (context, state) {
          if (state is ListSearchAddressesSuccess) {
            setState(() {
              _loadingAddresses = false;
              final model = Initializer.listSearchedAddressesModel;
              if (model != null) {
                _savedAddresses = model.data;
              } else {
                _savedAddresses = null;
              }
            });
            if (_savedAddresses != null && _savedAddresses!.isNotEmpty) {
              final lat = _savedAddresses!.first.location?.coordinates?[1];
              final lng = _savedAddresses!.first.location?.coordinates?[0];
              if (lat != null && lng != null) {
                setState(() {
                  _currentLatitude = lat;
                  _currentLongitude = lng;
                });
                Initializer().setCoordinates(lat, lng);
                _makeHomeDataRequest(lat, lng);
              }
            }
          } else if (state is EditSearchAddressSuccess) {
            _loadAddresses();
          } else if (state is AddSearchAddressSuccess) {
            _loadAddresses();
          }
        },
        child: Scaffold(
          backgroundColor: const Color.fromRGBO(246, 243, 236, 1),
          body: SafeArea(
            child: Stack(
              children: [
                Column(
                  children: [
                    const LocationHeader(),
                    SizedBox(height: screenHeight * 0.01),
                    Expanded(
                      child: BlocListener<HomeBloc, HomeState>(
                        listener: (context, state) {
                          if (state is PromoCodesSuccess) {
                            setState(() {
                              promoCodes =
                                  Initializer.promoCodeModel.data?.promoCodes ??
                                      [];
                            });
                          }
                        },
                        child: BlocConsumer<HomeBloc, HomeState>(
                          listener: (context, state) {
                            if (state is HomeDataSuccess) {
                              setState(() {});
                            }
                          },
                          builder: (context, state) {
                            if (_loadingAddresses) {
                              return _buildShimmerLoading(
                                  context, screenWidth, screenHeight);
                            }
                            if (_loadingAddresses || state is HomeDataLoading) {
                              return _buildShimmerLoading(
                                  context, screenWidth, screenHeight);
                            }
                            if (!_loadingAddresses &&
                                (_savedAddresses == null ||
                                    _savedAddresses!.isEmpty)) {
                              return const ChefLocationModal();
                            }
                            if (state is HomeDataSuccess) {
                              final data = state.data.data;

                              return SingleChildScrollView(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _buildFilterButton(context, screenWidth),
                                    SizedBox(height: screenHeight * 0.02),
                                    _buildSectionHeader(
                                        context, screenWidth, 'Explore Deals'),
                                    SizedBox(height: screenHeight * 0.02),
                                    _buildPromoCodeCards(context, screenWidth,
                                        screenHeight, promoCodes),
                                    SizedBox(height: screenHeight * 0.02),
                                    _buildSectionHeader(context, screenWidth,
                                        'Top-Rated Chefs'),
                                    SizedBox(height: screenHeight * 0.02),
                                    if (data?.topRatedChefs?.isEmpty ?? true)
                                      _buildNoDataMessage(
                                          context,
                                          screenWidth,
                                          screenHeight,
                                          'No top rated chefs available')
                                    else
                                      _buildTopRatedChefs(
                                          context,
                                          screenWidth,
                                          screenHeight,
                                          data!.topRatedChefs ?? []),
                                    SizedBox(height: screenHeight * 0.03),
                                    _buildSectionHeader(context, screenWidth,
                                        'Recommended For You'),
                                    SizedBox(height: screenHeight * 0.02),
                                    if (data?.recommendedChefs?.isEmpty ?? true)
                                      _buildNoDataMessage(
                                          context,
                                          screenWidth,
                                          screenHeight,
                                          'No recommended chefs available')
                                    else
                                      _buildRecommendedChefs(
                                          context,
                                          screenWidth,
                                          screenHeight,
                                          data!.recommendedChefs ?? []),
                                    // SizedBox(height: screenHeight * 0.03),
                                    // _buildMealPlanCard(
                                    //     context, screenWidth, screenHeight),
                                    _buildMealPlanCard2(
                                        context, screenWidth, screenHeight),
                                    _buildMonthlySaverCard(
                                        context, screenWidth, screenHeight),
                                    SizedBox(height: screenHeight * 0.02),
                                    _buildSectionHeader(context, screenWidth,
                                        'Most Popular Near You'),
                                    SizedBox(height: screenHeight * 0.02),
                                    if (data?.popularChefsNear?.isEmpty ?? true)
                                      _buildNoDataMessage(
                                          context,
                                          screenWidth,
                                          screenHeight,
                                          'No popular chefs in your area')
                                    else
                                      _buildPopularChefs(
                                          context,
                                          screenWidth,
                                          screenHeight,
                                          data!.popularChefsNear ?? []),
                                    SizedBox(height: screenHeight * 0.08),
                                  ],
                                ),
                              );
                            }

                            return _buildShimmerLoading(
                                context, screenWidth, screenHeight);
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          floatingActionButton: CartFloatingActionButton(
            itemCount: Initializer.cartCount ?? 0,
            onPressed: _openCart,
          ),
        ),
      ),
    );
  }

  void _openCart() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CartPage(),
      ),
    );
  }

  Widget _buildFilterButton(BuildContext context, double screenWidth) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.04, vertical: screenWidth * 0.03),
      child: InkWell(
        onTap: () {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            builder: (context) {
              return FilterModal(
                screenWidth: screenWidth,
              );
            },
          );
        },
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: screenWidth * 0.035),
          decoration: BoxDecoration(
            border: Border.all(
                color: const Color.fromRGBO(31, 33, 34, 1), width: 1.5),
            borderRadius: BorderRadius.circular(screenWidth * 0.075),
            color: const Color.fromRGBO(246, 243, 236, 1),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.tune,
                  size: screenWidth * 0.045,
                  color: Colors.grey[800],
                  semanticLabel: 'Filters'),
              SizedBox(width: screenWidth * 0.02),
              Text(
                'View Filters',
                style: TextStyle(
                  fontSize: screenWidth * 0.03,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Inter',
                  color: const Color.fromRGBO(31, 33, 34, 1),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(
      BuildContext context, double screenWidth, String title) {
    return Padding(
      padding: EdgeInsets.fromLTRB(screenWidth * 0.04, screenWidth * 0.03,
          screenWidth * 0.04, screenWidth * 0.01),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: screenWidth * 0.04584,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
              height: 1,
            ),
          ),
          GestureDetector(
              onTap: () {
                if (title == 'Top-Rated Chefs') {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const TopRatedChefsPage()));
                } else if (title == 'Recommended For You') {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const RecommendedChefsPage()));
                } else if (title == 'Most Popular Near You') {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const PopularChefsNearPage()));
                } else if (title == 'Explore Deals') {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const DealsOfTheDay()));
                }
              },
              child: IntrinsicWidth(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'See All',
                      style: TextStyle(
                        fontSize: screenWidth * 0.03,
                        fontWeight: FontWeight.w600,
                        height: 0.9,
                        color: Colors.black,
                      ),
                    ),
                    SizedBox(height: screenWidth * 0.0025),
                    Container(
                      height: 1.2,
                      width: double.infinity,
                      color: Colors.black54,
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildPromoCodeCards(BuildContext context, double screenWidth,
      double screenHeight, List promoCodes) {
    if (promoCodes.isEmpty) {
      return Padding(
        padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.04,
          vertical: screenHeight * 0.02,
        ),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.local_offer_outlined,
                size: screenWidth * 0.1,
                color: const Color(0xFF66696D),
              ),
              SizedBox(height: screenHeight * 0.01),
              Text(
                'No promocodes available',
                style: TextStyle(
                  fontSize: screenWidth * 0.035,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Inter',
                  color: const Color(0xFF66696D),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SizedBox(
      height: screenHeight * 0.11,
      child: ListView.separated(
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        scrollDirection: Axis.horizontal,
        itemCount: promoCodes.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) {
          final code = promoCodes[index];
          return Container(
            width: screenWidth * 0.45,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth * 0.03),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: screenWidth * 0.02,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.all(screenWidth * 0.03),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    code.discountName ?? code.couponCode ?? '',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: screenWidth * 0.03565,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: screenHeight * 0.005),
                  LayoutBuilder(
                    builder: (context, constraints) {
                      final description = code.description ?? '';
                      final charLimit = (constraints.maxWidth / 5).floor();
                      String displayText = description;
                      bool showEllipsis = false;
                      if (description.length > charLimit * 2) {
                        displayText =
                            description.substring(0, charLimit * 2 - 3) + '...';
                        showEllipsis = true;
                      }
                      return Text(
                        displayText,
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          fontSize: screenWidth * 0.03,
                          color: Colors.grey[700],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      );
                    },
                  ),
                  SizedBox(height: screenHeight * 0.01),
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: screenWidth * 0.02,
                          vertical: screenWidth * 0.01,
                        ),
                        decoration: BoxDecoration(
                          color: const Color.fromARGB(255, 7, 7, 7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          code.couponCode ?? '',
                          style: TextStyle(
                            // fontWeight: FontWeight.bold,
                            fontSize: screenWidth * 0.028,
                            color: Colors.white,
                            fontFamily: 'Inter',
                            letterSpacing: 1.2,
                          ),
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.02),
                      Text(
                        '${code.discountPercentage ?? ''}% OFF',
                        style: TextStyle(
                          color: Colors.green[800],
                          fontWeight: FontWeight.w600,
                          fontSize: screenWidth * 0.032,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  String _capitalizeFirst(String text) {
    if (text.isEmpty) return '';
    return text[0].toUpperCase() + (text.length > 1 ? text.substring(1) : '');
  }

  Widget _buildMealPlanCard2(
      BuildContext context, double screenWidth, double screenHeight) {
    return Padding(
      padding: EdgeInsets.all(screenWidth * 0.04),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(screenWidth * 0.04),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(screenWidth * 0.04),
                topRight: Radius.circular(screenWidth * 0.04),
              ),
              child: Image.asset(
                'assets/images/weekly_plan.png',
                width: double.infinity,
                height: screenHeight * 0.2,
                fit: BoxFit.cover,
              ),
            ),
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.03),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFFFBE16),
                          shape: BoxShape.circle,
                        ),
                        width: screenWidth * 0.067,
                        height: screenWidth * 0.067,
                        child: Padding(
                          padding: EdgeInsets.all(screenWidth * 0.02),
                          child: Image.asset(
                            'assets/icons/date_range.png',
                            width: screenWidth * 0.04,
                            height: screenWidth * 0.04,
                            fit: BoxFit.contain,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.02),
                      Text(
                        'Weekly Meal Plan',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: screenWidth * 0.04584,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.008),
                  Text(
                    'Subscribe to curated dishes delivered weekly. Dishes starting at \$9, skip or cancel anytime.',
                    style: TextStyle(
                      color: const Color(0xFFAAADB1),
                      fontSize: screenWidth * 0.03,
                      height: 1.4,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.01),
                  Padding(
                    padding: EdgeInsets.only(left: screenWidth * 0.02),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(Icons.circle,
                                size: screenWidth * 0.015,
                                color: const Color(0xFFAAADB1)),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Discounted Pricing',
                                style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontWeight: FontWeight.w400,
                                    fontSize: screenWidth * 0.03)),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.003),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(Icons.circle,
                                size: screenWidth * 0.015,
                                color: const Color(0xFFAAADB1)),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Free Delivery',
                                style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontWeight: FontWeight.w400,
                                    fontSize: screenWidth * 0.03)),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.003),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(Icons.circle,
                                size: screenWidth * 0.015,
                                color: const Color(0xFFAAADB1)),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Top-Rated Chefs',
                                style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontWeight: FontWeight.w400,
                                    fontSize: screenWidth * 0.03)),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.015),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                            builder: (context) => const ChoosePlanStep1()),
                      );
                    },
                    child: Container(
                      width: double.infinity,
                      padding:
                          EdgeInsets.symmetric(vertical: screenWidth * 0.035),
                      decoration: BoxDecoration(
                        border: Border.all(color: const Color(0xFFAAADB1)),
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.075),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Start A Meal Plan',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: screenWidth * 0.03,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(width: screenWidth * 0.02),
                          Icon(Icons.arrow_forward,
                              color: Colors.white, size: screenWidth * 0.04),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlySaverCard(
      BuildContext context, double screenWidth, double screenHeight) {
    return Padding(
        padding: EdgeInsets.all(screenWidth * 0.04),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(screenWidth * 0.04),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(screenWidth * 0.04),
                  topRight: Radius.circular(screenWidth * 0.04),
                ),
                child: Image.asset(
                  'assets/images/monthly_saver.png',
                  width: double.infinity,
                  height: screenHeight * 0.19,
                  fit: BoxFit.cover,
                ),
              ),
              Padding(
                padding: EdgeInsets.all(screenWidth * 0.03),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          decoration: const BoxDecoration(
                            color: Color(0xFFFFBE16),
                            shape: BoxShape.circle,
                          ),
                          width: screenWidth * 0.067,
                          height: screenWidth * 0.067,
                          child: Padding(
                            padding: EdgeInsets.all(screenWidth * 0.02),
                            child: Image.asset(
                              'assets/icons/percent.png',
                              width: screenWidth * 0.04,
                              height: screenWidth * 0.04,
                              fit: BoxFit.contain,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        SizedBox(width: screenWidth * 0.02),
                        Text(
                          "Monthly Saver's Pass",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: screenWidth * 0.04584,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Text(
                      'The all-in-one plan you need to unlock exclusive benefits across DB.',
                      style: TextStyle(
                        color: const Color(0xFFAAADB1),
                        fontSize: screenWidth * 0.03,
                        fontWeight: FontWeight.w400,
                        height: 1.4,
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Padding(
                      padding: EdgeInsets.only(left: screenWidth * 0.02),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(Icons.circle,
                                  size: screenWidth * 0.015,
                                  color: const Color(0xFFAAADB1)),
                              SizedBox(width: screenWidth * 0.02),
                              Text('Unlimited Free Delivery (Capped at \$2.0)',
                                  style: TextStyle(
                                      color: const Color(0xFFAAADB1),
                                      fontWeight: FontWeight.w400,
                                      fontSize: screenWidth * 0.03)),
                            ],
                          ),
                          SizedBox(height: screenHeight * 0.003),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(Icons.circle,
                                  size: screenWidth * 0.015,
                                  color: const Color(0xFFAAADB1)),
                              SizedBox(width: screenWidth * 0.02),
                              Text('Up to 30% Off Restaurants',
                                  style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontSize: screenWidth * 0.03,
                                    fontWeight: FontWeight.w400,
                                  )),
                            ],
                          ),
                          SizedBox(height: screenHeight * 0.003),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(Icons.circle,
                                  size: screenWidth * 0.015,
                                  color: const Color(0xFFAAADB1)),
                              SizedBox(width: screenWidth * 0.02),
                              Text('Surprise Perks',
                                  style: TextStyle(
                                    color: const Color(0xFFAAADB1),
                                    fontSize: screenWidth * 0.03,
                                    fontWeight: FontWeight.w400,
                                  )),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.016),
                    InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => MonthlySubscriptionPage()),
                        );
                      },
                      child: Container(
                        width: double.infinity,
                        padding:
                            EdgeInsets.symmetric(vertical: screenWidth * 0.035),
                        decoration: BoxDecoration(
                          border: Border.all(color: const Color(0xFFAAADB1)),
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.075),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Start A Meal Plan',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: screenWidth * 0.03,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SizedBox(width: screenWidth * 0.02),
                            Icon(Icons.arrow_forward,
                                color: Colors.white, size: screenWidth * 0.04),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 2),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  Widget _buildTopRatedChefs(BuildContext context, double screenWidth,
      double screenHeight, List<ChefData> chefs) {
    return SizedBox(
      height: ten * 26,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: chefs.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) {
          final chef = chefs[index];
          final name =
              '${chef.chef?.firstName ?? ''} ${chef.chef?.lastName ?? ''}';
          final availability = chef.chef?.operationDays
                  ?.map((day) => day.day?.name?.substring(0, 1) ?? '')
                  .join(', ') ??
              '';
          final cuisines = chef.searchTags?.join(', ') ?? 'Various Cuisines';
          final distance = _formatDistance(chef.distance);
          final rating = '${chef.ratingPercentage}% (${chef.totalRatings})';
          final prepTime = '30-45 mins';

          return _buildChefCard(
            context: context,
            screenWidth: screenWidth,
            screenHeight: screenHeight,
            id: chef.chefId ?? 0,
            image: ServerHelper.imageUrl + (chef.profilePhoto ?? ''),
            name: name,
            cuisines: cuisines,
            rating: rating,
            distance: distance,
            availability: availability,
            dishImage: ServerHelper.imageUrl + (chef.coverPhoto ?? ''),
            prepTime: prepTime,
          );
        },
      ),
    );
  }

  Widget _buildChefCard({
    required BuildContext context,
    required double screenWidth,
    required double screenHeight,
    required String image,
    required String name,
    required String cuisines,
    required String rating,
    required String distance,
    required String availability,
    required String dishImage,
    required String prepTime,
    required int id,
  }) {
    return GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ViewChef2(
                id: id,
                title: name,
                latitude: _currentLatitude ?? 0,
                longitude: _currentLongitude ?? 0,
                distance: distance,
                fromPage: 'home',
              ),
            ),
          );
        },
        child: Container(
          width: ten * 23,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(screenWidth * 0.03),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.01),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                clipBehavior: Clip.none,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(screenWidth * 0.03),
                      topRight: Radius.circular(screenWidth * 0.03),
                    ),
                    child: Image.network(
                      dishImage,
                      width: double.infinity,
                      height: ten * 12,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: double.infinity,
                            height: ten * 12,
                            color: Colors.white,
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: double.infinity,
                          height: ten * 12,
                          color: Colors.grey[300],
                          child: Icon(Icons.image_not_supported,
                              color: Colors.grey[600]),
                        );
                      },
                    ),
                  ),
                  Positioned(
                    top: screenWidth * 0.04,
                    left: screenWidth * 0.04,
                    child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.02,
                            vertical: screenWidth * 0.01),
                        decoration: BoxDecoration(
                          color: const Color.fromARGB(0, 0, 0, 0),
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.03),
                        ),
                        child: SizedBox.shrink()),
                  ),
                  Positioned(
                    left: screenWidth * 0.04,
                    bottom: -screenWidth * 0.05,
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 3),
                      ),
                      child: CircleAvatar(
                        radius: ten + twelve,
                        backgroundImage: NetworkImage(image),
                        onBackgroundImageError: (exception, stackTrace) {
                          print('Error loading chef image: $exception');
                        },
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: screenHeight * 0.03),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: TextStyle(
                        fontSize: forteen,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Inter',
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.008),
                    Text(
                      cuisines,
                      style: TextStyle(
                        fontSize: twelve,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: const Color.fromRGBO(65, 67, 70, 1),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: screenWidth * 0.015,
                              vertical: screenWidth * 0.005),
                          decoration: BoxDecoration(
                            color: const Color.fromRGBO(225, 227, 230, 1),
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.03),
                          ),
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/icons/thump.png',
                                width: screenWidth * 0.0275,
                                height: screenWidth * 0.025,
                                color: Color(0xff1F2122),
                              ),
                              SizedBox(width: screenWidth * 0.01),
                              Text(
                                rating,
                                style: TextStyle(
                                  fontSize: ten,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter',
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: screenWidth * 0.02),
                        Row(
                          children: [
                            Icon(Icons.location_on_outlined,
                                size: screenWidth * 0.03,
                                color: const Color(0xFF1F2122)),
                            SizedBox(width: screenWidth * 0.005),
                            Text(
                              distance,
                              style: TextStyle(
                                fontSize: ten,
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Inter',
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(height: screenHeight * 0.008),
                    Row(
                      children: [
                        Image.asset(
                          'assets/icons/calender_2.png',
                          width: screenWidth * 0.03,
                          height: screenWidth * 0.0325,
                          color: Colors.black54,
                        ),
                        SizedBox(width: screenWidth * 0.01),
                        Text(
                          availability,
                          style: TextStyle(
                            fontSize: twelve,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            color: Color(0xff414346),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  Widget _buildRecommendedChefs(BuildContext context, double screenWidth,
      double screenHeight, List<ChefData> chefs) {
    return SizedBox(
      height: ten * 26,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: chefs.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) {
          final chef = chefs[index];
          final name =
              '${chef.chef?.firstName ?? ''} ${chef.chef?.lastName ?? ''}';
          final availability = chef.chef?.operationDays
                  ?.map((day) => day.day?.name?.substring(0, 1) ?? '')
                  .join(', ') ??
              '';
          final cuisines = chef.searchTags?.join(', ') ?? 'Various Cuisines';
          final distance = _formatDistance(chef.distance);
          final rating = '${chef.ratingPercentage}% (${chef.totalRatings})';
          final prepTime = '30-45 mins';

          return _buildChefCard(
            context: context,
            screenWidth: screenWidth,
            screenHeight: screenHeight,
            id: chef.chefId ?? 0,
            image: ServerHelper.imageUrl + (chef.profilePhoto ?? ''),
            name: name,
            cuisines: cuisines,
            rating: rating,
            distance: distance,
            availability: availability,
            dishImage: ServerHelper.imageUrl + (chef.coverPhoto ?? ''),
            prepTime: prepTime,
          );
        },
      ),
    );
  }

  Widget _buildPopularChefs(BuildContext context, double screenWidth,
      double screenHeight, List<ChefData> chefs) {
    return SizedBox(
      height: ten * 26,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: chefs.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) {
          final chef = chefs[index];
          final name =
              '${chef.chef?.firstName ?? ''} ${chef.chef?.lastName ?? ''}';
          final availability = chef.chef?.operationDays
                  ?.map((day) => day.day?.name?.substring(0, 1) ?? '')
                  .join(', ') ??
              '';
          final cuisines = chef.searchTags?.join(', ') ?? 'Various Cuisines';
          final distance = _formatDistance(chef.distance);
          final rating = '${chef.ratingPercentage}% (${chef.totalRatings})';
          final prepTime = '30-45 mins';

          return _buildChefCard(
            context: context,
            screenWidth: screenWidth,
            screenHeight: screenHeight,
            id: chef.chefId ?? 0,
            image: ServerHelper.imageUrl + (chef.profilePhoto ?? ''),
            name: name,
            cuisines: cuisines,
            rating: rating,
            distance: distance,
            availability: availability,
            dishImage: ServerHelper.imageUrl + (chef.coverPhoto ?? ''),
            prepTime: prepTime,
          );
        },
      ),
    );
  }

  Widget _buildNoDataMessage(BuildContext context, double screenWidth,
      double screenHeight, String message) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
      child: Center(
        child: Text(
          message,
          style: TextStyle(
            fontSize: screenWidth * 0.04,
            fontWeight: FontWeight.w400,
            fontFamily: 'Inter',
            color: const Color(0xFF66696D),
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildShimmerLoading(
      BuildContext context, double screenWidth, double screenHeight) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
                horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                width: double.infinity,
                height: screenHeight * 0.06,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(screenWidth * 0.075),
                ),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                  width: screenWidth * 0.4,
                  height: screenHeight * 0.03,
                  color: Colors.white),
            ),
          ),
          SizedBox(height: screenHeight * 0.02),
          SizedBox(
            height: screenHeight * 0.25,
            child: ListView.separated(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
              scrollDirection: Axis.horizontal,
              itemCount: 3,
              separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
              itemBuilder: (_, __) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: screenWidth * 0.45,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(screenWidth * 0.03),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: screenHeight * 0.03),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                width: screenWidth * 0.4,
                height: screenHeight * 0.03,
                color: Colors.white,
              ),
            ),
          ),
          SizedBox(height: screenHeight * 0.02),
          SizedBox(
            height: screenHeight * 0.32,
            child: ListView.separated(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
              scrollDirection: Axis.horizontal,
              itemCount: 3,
              separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
              itemBuilder: (_, __) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: screenWidth * 0.6,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(screenWidth * 0.03),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: screenHeight * 0.03),
          Padding(
            padding: EdgeInsets.all(screenWidth * 0.04),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                width: double.infinity,
                height: screenHeight * 0.4,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(screenWidth * 0.04),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDistance(double? distance) {
    if (distance == null) return 'Unknown';
    final kilometers = distance / 1000;
    return '${kilometers.toStringAsFixed(1)} KM';
  }
}
