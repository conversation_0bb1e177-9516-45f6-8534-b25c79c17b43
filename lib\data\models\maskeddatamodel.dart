class MaskedPhoneEmailModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  MaskedPhoneEmailModel(
      {this.status, this.message, this.statusCode, this.data});

  MaskedPhoneEmailModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? maskedEmail;
  String? maskedPhone;

  Data({this.maskedEmail, this.maskedPhone});

  Data.fromJson(Map<String, dynamic> json) {
    maskedEmail = json['masked_email'];
    maskedPhone = json['masked_phone'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['masked_email'] = this.maskedEmail;
    data['masked_phone'] = this.maskedPhone;
    return data;
  }
}
