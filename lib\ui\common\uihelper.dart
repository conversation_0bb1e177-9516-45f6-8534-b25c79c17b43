import 'package:flutter/material.dart';

class UiHelper {
  static const Color primaryBlack = Color(0xFF1F2122);
  static const Color secondaryBlack = Color(0xff414346);
  static const Color grey = Color(0xffE1E3E6);
  static const Color white = Color(0xFFFFFFFF);

  static Widget horizontalDivider({
    Color color = grey,
    double height = 1.0,
    double width = double.infinity,
  }) {
    return Container(height: height, width: width, color: color);
  }
}

class DottedDivider extends StatelessWidget {
  final double height;
  final Color color;

  const DottedDivider({
    this.height = 18,
    this.color = const Color(0xFFE1E3E6),
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      child: Center(
        child: LayoutBuilder(
          builder: (context, constraints) {
            final boxWidth = constraints.constrainWidth();
            const dashWidth = 2.0;
            const dashSpace = 2.5;
            final dashCount = (boxWidth / (dashWidth + dashSpace)).floor();

            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: List.generate(dashCount, (_) {
                return Container(
                  width: dashWidth,
                  height: 1,
                  color: color,
                );
              }),
            );
          },
        ),
      ),
    );
  }
}

class ResponsiveUtils {
  // Base dimensions for Redmi Note 10 Pro (your reference device)
  static const double baseWidth = 393.0; // Redmi Note 10 Pro width
  static const double baseHeight = 851.0; // Redmi Note 10 Pro height

  // Get current screen dimensions
  static Size getScreenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  // Get scale factors
  static double getWidthScale(BuildContext context) {
    return getScreenSize(context).width / baseWidth;
  }

  static double getHeightScale(BuildContext context) {
    return getScreenSize(context).height / baseHeight;
  }

  // Get average scale factor (for uniform scaling)
  static double getScale(BuildContext context) {
    final widthScale = getWidthScale(context);
    final heightScale = getHeightScale(context);
    return (widthScale + heightScale) / 2;
  }

  // Responsive width function
  static double w(BuildContext context, double width) {
    return width * getWidthScale(context);
  }

  // Responsive height function
  static double h(BuildContext context, double height) {
    return height * getHeightScale(context);
  }

  // Responsive size function (uses average scale)
  static double s(BuildContext context, double size) {
    return size * getScale(context);
  }

  // Responsive font size function
  static double f(BuildContext context, double fontSize) {
    final scale = getScale(context);
    // Limit font scaling to prevent extreme sizes
    final constrainedScale = scale.clamp(0.8, 1.3);
    return fontSize * constrainedScale;
  }

  // Responsive padding function
  static EdgeInsets p(BuildContext context, double left, double top,
      double right, double bottom) {
    return EdgeInsets.fromLTRB(
      w(context, left),
      h(context, top),
      w(context, right),
      h(context, bottom),
    );
  }

  // Responsive symmetric padding
  static EdgeInsets pSymmetric(BuildContext context,
      {double vertical = 0, double horizontal = 0}) {
    return EdgeInsets.symmetric(
      vertical: h(context, vertical),
      horizontal: w(context, horizontal),
    );
  }

  // Responsive all padding
  static EdgeInsets pAll(BuildContext context, double padding) {
    return EdgeInsets.all(s(context, padding));
  }

  // Responsive border radius
  static BorderRadius br(BuildContext context, double radius) {
    return BorderRadius.circular(s(context, radius));
  }

  // Responsive size for widgets
  static Size size(BuildContext context, double width, double height) {
    return Size(w(context, width), h(context, height));
  }

  // Check if screen is small (phones)
  static bool isSmallScreen(BuildContext context) {
    return getScreenSize(context).width < 600;
  }

  // Check if screen is medium (tablets)
  static bool isMediumScreen(BuildContext context) {
    final width = getScreenSize(context).width;
    return width >= 600 && width < 1200;
  }

  // Check if screen is large (desktops)
  static bool isLargeScreen(BuildContext context) {
    return getScreenSize(context).width >= 1200;
  }

  // Get responsive container constraints
  static BoxConstraints getPopupConstraints(BuildContext context) {
    final screenWidth = getScreenSize(context).width;
    if (isSmallScreen(context)) {
      return BoxConstraints(maxWidth: screenWidth * 0.9);
    } else if (isMediumScreen(context)) {
      return BoxConstraints(maxWidth: screenWidth * 0.6);
    } else {
      return const BoxConstraints(maxWidth: 500);
    }
  }
}
