import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/order_bloc.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/dish/dishdetailmodel.dart';

class DishData {
  final String title;
  final double price;
  final String description;
  final String ingredients;
  final String imageUrl;
  final int servings;
  final bool isSpicy;
  final bool isOrganic;

  DishData({
    required this.title,
    required this.price,
    required this.description,
    required this.ingredients,
    required this.imageUrl,
    required this.servings,
    required this.isSpicy,
    required this.isOrganic,
  });

  factory DishData.fromDishDetailModel(DishDetailModel model) {
    final dish = model.data?.dish;
    final servingSizePrice = dish?.servingSizePrices?.isNotEmpty == true
        ? dish!.servingSizePrices!.first
        : null;

    return DishData(
      title: dish?.name ?? 'Dish Title',
      price: double.tryParse(servingSizePrice?.price ?? '19.99') ?? 19.99,
      description: dish?.description ?? 'No description available',
      ingredients: dish?.ingredients ?? 'No ingredients listed',
      imageUrl: dish?.photo ?? 'assets/images/dish_1.png',
      servings: servingSizePrice?.servingSize?.serves ?? 2,
      isSpicy: dish?.spiceLevel?.name != null &&
          ['spicy', 'medium', 'hot']
              .contains(dish!.spiceLevel!.name!.toLowerCase()),
      isOrganic: dish?.dietary?.name != null &&
          dish!.dietary!.name!.toLowerCase().contains('organic'),
    );
  }
}

class DishDetailPage extends StatefulWidget {
  final String dishId;
  final int? chefId;

  const DishDetailPage({
    Key? key,
    required this.dishId,
    this.chefId,
  }) : super(key: key);

  @override
  State<DishDetailPage> createState() => _DishDetailPageState();
}

class _DishDetailPageState extends State<DishDetailPage> {
  int quantity = 1;
  bool isFavorite = false;
  bool _favoriteStateOverride = false;
  ServingSizePrices? selectedServingSize;

  @override
  void initState() {
    super.initState();
    final int? parsedDishId = int.tryParse(widget.dishId);
    if (parsedDishId != null) {
      context.read<OrderBloc>().add(ListDishDetails(parsedDishId));
    } else {
      context.read<OrderBloc>().add(ListDishDetails(0));
    }
  }

  double _scaleFactor(BuildContext context) =>
      MediaQuery.of(context).size.width / 400;

  double _padding(BuildContext context) => 16 * _scaleFactor(context);
  double _fontSizeLarge(BuildContext context) => 24 * _scaleFactor(context);
  double _fontSizeMedium(BuildContext context) => 18 * _scaleFactor(context);
  double _fontSizeSmall(BuildContext context) => 14 * _scaleFactor(context);
  double _iconSize(BuildContext context) => 24 * _scaleFactor(context);
  double _imageHeight(BuildContext context) =>
      MediaQuery.of(context).size.height * 0.2;

  void _onToggleFavorite(int dishId) {
    context.read<AccountBloc>().add(AddFavouritesEvent(dishId));
  }

  void _onAddToCart(DishData dishData, DishDetailModel model) {
    if (widget.chefId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Chef information unavailable',
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              fontSize: _fontSizeSmall(context),
            ),
          ),
          backgroundColor: Colors.black,
        ),
      );
      return;
    }

    if (model.data?.dish?.servingSizePrices?.isNotEmpty == true &&
        model.data!.dish!.servingSizePrices!.length > 1) {
      _showServingSizeDialog(context, dishData, model);
    } else {
      final int? parsedDishId = int.tryParse(widget.dishId);
      if (parsedDishId != null) {
        context.read<AccountBloc>().add(AddToCartEvent({
              'chef_id': widget.chefId!,
              'chef_dish_id': parsedDishId,
              'quantity': 1,
              'serving_size_id':
                  model.data?.dish?.servingSizePrices?.isNotEmpty == true
                      ? model.data!.dish!.servingSizePrices!.first.servingSizeId
                      : 1,
            }));
      }
    }
  }

  void _showServingSizeDialog(
      BuildContext context, DishData dishData, DishDetailModel model) {
    ServingSizePrices? tempSelectedServingSize =
        selectedServingSize ?? model.data!.dish!.servingSizePrices!.first;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setDialogState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(_padding(context)),
              ),
              backgroundColor: Colors.white,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final double dialogWidth =
                      MediaQuery.of(context).size.width > 600
                          ? 400
                          : MediaQuery.of(context).size.width * 0.85;

                  return Container(
                    width: dialogWidth,
                    padding: EdgeInsets.all(_padding(context)),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Select Serving Size',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w600,
                                fontSize: _fontSizeMedium(context),
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.close,
                                color: Colors.black,
                                size: _iconSize(context),
                              ),
                              padding: EdgeInsets.zero,
                              onPressed: () =>
                                  Navigator.of(dialogContext).pop(),
                            ),
                          ],
                        ),
                        SizedBox(height: _padding(context)),
                        ...?model.data?.dish?.servingSizePrices?.map((size) {
                          return GestureDetector(
                            onTap: () {
                              setDialogState(() {
                                tempSelectedServingSize = size;
                              });
                            },
                            child: Container(
                              margin: EdgeInsets.only(
                                  bottom: _padding(context) * 0.5),
                              padding: EdgeInsets.symmetric(
                                horizontal: _padding(context),
                                vertical: _padding(context) * 0.75,
                              ),
                              decoration: BoxDecoration(
                                color: tempSelectedServingSize?.id == size.id
                                    ? Colors.black
                                    : const Color(0xFFE1E3E6),
                                borderRadius: BorderRadius.circular(25),
                                border: Border.all(
                                  color: tempSelectedServingSize?.id == size.id
                                      ? Colors.black
                                      : Colors.grey[300]!,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Flexible(
                                    child: Text(
                                      size.servingSize?.title ?? 'Size',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w500,
                                        fontSize: _fontSizeSmall(context),
                                        color: tempSelectedServingSize?.id ==
                                                size.id
                                            ? Colors.white
                                            : const Color(0xFF1F2122),
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  Text(
                                    '\$${double.tryParse(size.price ?? '0.00')?.toStringAsFixed(2) ?? '0.00'}',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w600,
                                      fontSize: _fontSizeSmall(context),
                                      color:
                                          tempSelectedServingSize?.id == size.id
                                              ? Colors.white
                                              : const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                        SizedBox(height: _padding(context)),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton(
                              onPressed: () =>
                                  Navigator.of(dialogContext).pop(),
                              child: Text(
                                'Cancel',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w500,
                                  fontSize: _fontSizeSmall(context),
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                            ),
                            SizedBox(width: _padding(context) * 0.5),
                            ElevatedButton(
                              onPressed: () {
                                if (widget.chefId == null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Chef information unavailable',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontWeight: FontWeight.w400,
                                          fontSize: _fontSizeSmall(context),
                                        ),
                                      ),
                                      backgroundColor: Colors.black,
                                    ),
                                  );
                                  return;
                                }
                                setState(() {
                                  selectedServingSize = tempSelectedServingSize;
                                });
                                final int? parsedDishId =
                                    int.tryParse(widget.dishId);
                                if (parsedDishId != null) {
                                  context
                                      .read<AccountBloc>()
                                      .add(AddToCartEvent({
                                        'chef_id': widget.chefId!,
                                        'chef_dish_id': parsedDishId,
                                        'quantity': 1,
                                        'serving_size_id':
                                            tempSelectedServingSize
                                                    ?.servingSizeId ??
                                                1,
                                      }));
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.black,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                padding: EdgeInsets.symmetric(
                                  horizontal: _padding(context),
                                  vertical: _padding(context) * 0.75,
                                ),
                              ),
                              child: Text(
                                'Add to Cart',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w600,
                                  fontSize: _fontSizeSmall(context),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: Icon(Icons.close,
                color: Colors.black, size: _iconSize(context)),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
      body: BlocListener<AccountBloc, AccountState>(
        listener: (context, state) {
          if (state is AddFavouritesSuccess) {
            setState(() {
              isFavorite = true;
              _favoriteStateOverride = true;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state.message,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: _fontSizeSmall(context),
                  ),
                ),
                backgroundColor: Colors.black,
              ),
            );
          } else if (state is AddFavouritesFailed) {
            setState(() {
              isFavorite = false;
              _favoriteStateOverride = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state.message,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: _fontSizeSmall(context),
                  ),
                ),
                backgroundColor: Colors.black,
              ),
            );
          } else if (state is AddToCartSuccess) {
            Navigator.of(context, rootNavigator: true).pop();
          } else if (state is AddToCartFailed) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state.message,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: _fontSizeSmall(context),
                  ),
                ),
                backgroundColor: Colors.black,
              ),
            );
          }
        },
        child: BlocBuilder<OrderBloc, OrderState>(
          builder: (context, state) {
            if (state is ListDishDetailsLoading) {
              return Center(
                child: CircularProgressIndicator(
                  color: Colors.black,
                ),
              );
            } else if (state is ListDishDetailsSuccess) {
              final dishData =
                  DishData.fromDishDetailModel(Initializer.dishDetailModel);
              if (!_favoriteStateOverride) {
                isFavorite =
                    Initializer.dishDetailModel.data?.isFavourite ?? false;
              }
              selectedServingSize ??= Initializer.dishDetailModel.data?.dish
                          ?.servingSizePrices?.isNotEmpty ==
                      true
                  ? Initializer
                      .dishDetailModel.data!.dish!.servingSizePrices!.first
                  : null;
              return _buildDishDetailContent(
                  context, dishData, Initializer.dishDetailModel);
            } else if (state is ListDishDetailsFailed) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(state.message),
                    ElevatedButton(
                      onPressed: () {
                        final int? parsedDishId = int.tryParse(widget.dishId);
                        if (parsedDishId != null) {
                          context
                              .read<OrderBloc>()
                              .add(ListDishDetails(parsedDishId));
                        }
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }
            return const Center(
                child: Text('Invalid dish ID or no data available'));
          },
        ),
      ),
    );
  }

  Widget _buildDishDetailContent(
      BuildContext context, DishData dishData, DishDetailModel model) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.all(_padding(context)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ClipRRect(
                    borderRadius:
                        BorderRadius.circular(9 * _scaleFactor(context)),
                    child: model.data?.dish?.photo != null
                        ? Image.network(
                            ServerHelper.imageUrl + model.data!.dish!.photo!,
                            width: double.infinity,
                            height: _imageHeight(context),
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: double.infinity,
                                height: _imageHeight(context),
                                color: Colors.grey[300],
                                child: Icon(Icons.restaurant_menu,
                                    size: _iconSize(context) * 2),
                              );
                            },
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Container(
                                width: double.infinity,
                                height: _imageHeight(context),
                                color: Colors.grey[100],
                                child: const Center(
                                    child: CircularProgressIndicator()),
                              );
                            },
                          )
                        : Container(
                            width: double.infinity,
                            height: _imageHeight(context),
                            color: Colors.grey[300],
                            child: Icon(Icons.restaurant_menu,
                                size: _iconSize(context) * 2),
                          ),
                  ),
                  SizedBox(height: _padding(context)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          dishData.title,
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w600,
                            fontSize: _fontSizeLarge(context),
                          ),
                        ),
                      ),
                      Row(
                        children: [
                          _buildActionButton(
                            context,
                            iconPath: 'assets/icons/favorites.png',
                            onPressed: () => _onToggleFavorite(
                                int.tryParse(widget.dishId) ?? 0),
                            tooltip: 'Add to favorites',
                            child: Icon(
                              isFavorite
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              color: isFavorite
                                  ? Colors.red
                                  : const Color(0xFF1F2122),
                              size: _iconSize(context),
                            ),
                          ),
                          SizedBox(width: _padding(context) * 0.5),
                          _buildActionButton(
                            context,
                            iconPath: 'assets/icons/shopping_cart2.png',
                            onPressed: () => _onAddToCart(dishData, model),
                            tooltip: 'Add to cart',
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: _padding(context)),
                  Text(
                    '\$${double.tryParse(selectedServingSize?.price ?? dishData.price.toString())?.toStringAsFixed(2) ?? dishData.price.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: _fontSizeLarge(context),
                    ),
                  ),
                  SizedBox(height: _padding(context)),
                  if (model.data?.dish?.servingSizePrices?.isNotEmpty == true)
                    Wrap(
                      spacing: 4.0 * _scaleFactor(context),
                      runSpacing: 6.0 * _scaleFactor(context),
                      children: model.data!.dish!.servingSizePrices!
                          .map((size) => ChoiceChip(
                                label: Text(
                                  '${size.servingSize?.title ?? 'Size'} (\$${double.tryParse(size.price ?? '0.00')?.toStringAsFixed(2) ?? '0.00'})',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: _fontSizeSmall(context) * 0.8,
                                    color: selectedServingSize?.id == size.id
                                        ? Colors.white
                                        : Colors.black,
                                  ),
                                ),
                                selected: selectedServingSize?.id == size.id,
                                onSelected: (bool selected) {
                                  if (selected) {
                                    setState(() {
                                      selectedServingSize = size;
                                    });
                                  }
                                },
                                selectedColor: Colors.black,
                                backgroundColor: const Color(0xFFE1E3E6),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(
                                      25 * _scaleFactor(context)),
                                  side: BorderSide(
                                    color: selectedServingSize?.id == size.id
                                        ? Colors.black
                                        : Colors.grey[300]!,
                                  ),
                                ),
                                showCheckmark: false,
                                labelPadding: EdgeInsets.symmetric(
                                    horizontal: 8 * _scaleFactor(context)),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 2 * _scaleFactor(context),
                                    vertical: 3 * _scaleFactor(context)),
                              ))
                          .toList(),
                    ),
                  SizedBox(height: _padding(context)),
                  Text(
                    'Description',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: _fontSizeMedium(context),
                    ),
                  ),
                  SizedBox(height: _padding(context) * 0.25),
                  Text(
                    dishData.description,
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: _fontSizeSmall(context),
                      color: const Color(0xFF1F2122),
                      letterSpacing: -0.1,
                      height: 1.35,
                    ),
                  ),
                  SizedBox(height: _padding(context)),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Ingredients:',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w600,
                          fontSize: _fontSizeSmall(context),
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                      SizedBox(width: _padding(context) * 0.5),
                      Expanded(
                        child: Text(
                          dishData.ingredients,
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            fontSize: _fontSizeSmall(context),
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: _padding(context)),
                  Wrap(
                    spacing: _padding(context) * 0.5,
                    runSpacing: _padding(context) * 0.5,
                    children: [
                      if (selectedServingSize?.servingSize?.serves != null)
                        _buildInfoContainer(
                          'assets/icons/servings.png',
                          '${selectedServingSize?.servingSize?.serves} servings',
                        ),
                      if (model.data?.dish?.spiceLevel?.name != null)
                        _buildInfoContainer(
                          'assets/icons/chili.png',
                          model.data!.dish!.spiceLevel!.name ?? '',
                        ),
                      if (model.data?.dish?.dietary?.name != null)
                        _buildInfoContainer(
                          'assets/icons/organic.png',
                          model.data!.dish!.dietary!.name ?? '',
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required String iconPath,
    required VoidCallback onPressed,
    required String tooltip,
    Widget? child,
  }) {
    final double buttonSize = MediaQuery.of(context).size.width * 0.1;
    return Container(
      width: buttonSize,
      height: buttonSize,
      decoration: BoxDecoration(
        color: const Color(0xFFE1E3E6),
        borderRadius: BorderRadius.circular(buttonSize * 0.25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: child ??
            Image.asset(
              iconPath,
              width: _iconSize(context),
              height: _iconSize(context),
            ),
        onPressed: onPressed,
        padding: EdgeInsets.zero,
        tooltip: tooltip,
      ),
    );
  }

  Widget _buildInfoContainer(String iconPath, String text,
      {bool useNetworkImage = false}) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: _padding(context) * 0.875,
          vertical: _padding(context) * 0.5),
      decoration: BoxDecoration(
        color: const Color(0xFFE1E3E6),
        borderRadius: BorderRadius.circular(8 * _scaleFactor(context)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (useNetworkImage)
            Image.network(
              iconPath,
              width: _iconSize(context) * 0.833,
              height: _iconSize(context) * 0.917,
              errorBuilder: (context, error, stackTrace) {
                return Icon(Icons.image_not_supported,
                    size: _iconSize(context) * 0.833);
              },
            )
          else
            Image.asset(
              iconPath,
              width: _iconSize(context) * 0.833,
              height: _iconSize(context) * 0.917,
            ),
          SizedBox(height: _padding(context) * 0.125),
          Text(
            text,
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w500,
              fontSize: _fontSizeSmall(context) * 0.857,
            ),
          ),
        ],
      ),
    );
  }
}
