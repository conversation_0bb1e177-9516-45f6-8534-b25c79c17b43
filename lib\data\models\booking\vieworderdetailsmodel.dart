class ViewOrderDetailsModel {
  bool? status;
  ViewOrderDetailsData? data;
  int? statusCode;

  ViewOrderDetailsModel({this.status, this.data, this.statusCode});

  ViewOrderDetailsModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null
        ? ViewOrderDetailsData.fromJson(json['data'])
        : null;
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['status_code'] = statusCode;
    return data;
  }
}

class ViewOrderDetailsData {
  int? id;
  String? orderNumber;
  Chef? chef;
  List<Items>? items;
  double? subtotal;
  double? deliveryFee;
  double? discount;
  num? serviceFeePercentage;
  num? serviceFee;
  num? walletCredits;
  num? taxPercentage;
  num? taxesAndFees;
  double? total;
  String? deliveryDate;
  String? deliveryTime;
  Address? address;
  DeliveryTimes? deliveryTimes;
  DropOffOption? dropOffOption;
  String? dropOffInstructions;
  TimeSlot? timeSlot;
  String? status;
  String? paymentStatus;
  String? createdAt;
  String? updatedAt;
  List<Coupon>? coupons;
  AssignedDriver? assignedDriver;

  ViewOrderDetailsData({
    this.id,
    this.orderNumber,
    this.chef,
    this.items,
    this.subtotal,
    this.deliveryFee,
    this.discount,
    this.serviceFeePercentage,
    this.serviceFee,
    this.walletCredits,
    this.taxPercentage,
    this.taxesAndFees,
    this.total,
    this.deliveryDate,
    this.deliveryTime,
    this.address,
    this.deliveryTimes,
    this.dropOffOption,
    this.dropOffInstructions,
    this.timeSlot,
    this.status,
    this.paymentStatus,
    this.createdAt,
    this.updatedAt,
    this.coupons,
    this.assignedDriver,
  });

  ViewOrderDetailsData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    orderNumber = json['order_number'];
    chef = json['chef'] != null ? Chef.fromJson(json['chef']) : null;
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(Items.fromJson(v));
      });
    }
    subtotal = json['subtotal'] != null ? json['subtotal'].toDouble() : null;
    deliveryFee =
        json['delivery_fee'] != null ? json['delivery_fee'].toDouble() : null;
    discount = json['discount'] != null ? json['discount'].toDouble() : null;
    serviceFeePercentage = json['service_fee_percentage'] != null
        ? json['service_fee_percentage']
        : null;
    serviceFee =
        json['service_fee'] != null ? json['service_fee'].toDouble() : null;
    walletCredits = json['wallet_credits'] != null
        ? json['wallet_credits'].toDouble()
        : null;
    taxPercentage = json['tax_percentage'] != null
        ? json['tax_percentage'].toDouble()
        : null;
    taxesAndFees = json['taxes_and_fees'] != null
        ? json['taxes_and_fees'].toDouble()
        : null;
    total = json['total'] != null ? json['total'].toDouble() : null;
    deliveryDate = json['delivery_date'];
    deliveryTime = json['delivery_time'];
    address =
        json['address'] != null ? Address.fromJson(json['address']) : null;
    deliveryTimes = json['delivery_times'] != null
        ? DeliveryTimes.fromJson(json['delivery_times'])
        : null;
    dropOffOption = json['drop_off_option'] != null
        ? DropOffOption.fromJson(json['drop_off_option'])
        : null;
    dropOffInstructions = json['drop_off_instructions'];
    timeSlot =
        json['time_slot'] != null ? TimeSlot.fromJson(json['time_slot']) : null;
    status = json['status'];
    paymentStatus = json['payment_status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    if (json['coupons'] != null) {
      coupons = <Coupon>[];
      json['coupons'].forEach((v) {
        coupons!.add(Coupon.fromJson(v));
      });
    }
    assignedDriver = json['assigned_driver'] != null
        ? AssignedDriver.fromJson(json['assigned_driver'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['order_number'] = orderNumber;
    if (chef != null) {
      data['chef'] = chef!.toJson();
    }
    if (items != null) {
      data['items'] = items!.map((v) => v.toJson()).toList();
    }
    data['subtotal'] = subtotal;
    data['delivery_fee'] = deliveryFee;
    data['discount'] = discount;
    data['service_fee_percentage'] = serviceFeePercentage;
    data['service_fee'] = serviceFee;
    data['wallet_credits'] = walletCredits;
    data['tax_percentage'] = taxPercentage;
    data['taxes_and_fees'] = taxesAndFees;
    data['total'] = total;
    data['delivery_date'] = deliveryDate;
    data['delivery_time'] = deliveryTime;
    if (address != null) {
      data['address'] = address!.toJson();
    }
    if (deliveryTimes != null) {
      data['delivery_times'] = deliveryTimes!.toJson();
    }
    if (dropOffOption != null) {
      data['drop_off_option'] = dropOffOption!.toJson();
    }
    data['drop_off_instructions'] = dropOffInstructions;
    if (timeSlot != null) {
      data['time_slot'] = timeSlot!.toJson();
    }
    data['status'] = status;
    data['payment_status'] = paymentStatus;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    if (coupons != null) {
      data['coupons'] = coupons!.map((v) => v.toJson()).toList();
    }
    if (assignedDriver != null) {
      data['assigned_driver'] = assignedDriver!.toJson();
    }
    return data;
  }
}

class Chef {
  int? id;
  String? name;
  String? photo;
  Location? location;

  Chef({this.id, this.name, this.photo, this.location});

  Chef.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
    location =
        json['location'] != null ? Location.fromJson(json['location']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['photo'] = photo;
    if (location != null) {
      data['location'] = location!.toJson();
    }
    return data;
  }
}

class Items {
  int? id;
  Dish? dish;
  ServingSize? servingSize;
  int? quantity;
  double? price;
  double? totalPrice;
  String? notes;

  Items({
    this.id,
    this.dish,
    this.servingSize,
    this.quantity,
    this.price,
    this.totalPrice,
    this.notes,
  });

  Items.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    dish = json['dish'] != null ? Dish.fromJson(json['dish']) : null;
    servingSize = json['serving_size'] != null
        ? ServingSize.fromJson(json['serving_size'])
        : null;
    quantity = json['quantity'];
    price = json['price'] != null ? json['price'].toDouble() : null;
    totalPrice =
        json['total_price'] != null ? json['total_price'].toDouble() : null;
    notes = json['notes'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    if (dish != null) {
      data['dish'] = dish!.toJson();
    }
    if (servingSize != null) {
      data['serving_size'] = servingSize!.toJson();
    }
    data['quantity'] = quantity;
    data['price'] = price;
    data['total_price'] = totalPrice;
    data['notes'] = notes;
    return data;
  }
}

class Dish {
  int? id;
  String? name;
  String? photo;

  Dish({this.id, this.name, this.photo});

  Dish.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['photo'] = photo;
    return data;
  }
}

class ServingSize {
  int? id;

  ServingSize({this.id});

  ServingSize.fromJson(Map<String, dynamic> json) {
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    return data;
  }
}

class Address {
  Location? location;
  String? addressText;
  String? buildingType;
  String? houseNumber;
  String? landmark;

  Address({
    this.location,
    this.addressText,
    this.buildingType,
    this.houseNumber,
    this.landmark,
  });

  Address.fromJson(Map<String, dynamic> json) {
    location =
        json['location'] != null ? Location.fromJson(json['location']) : null;
    addressText = json['address_text'];
    buildingType = json['building_type'];
    houseNumber = json['house_number'];
    landmark = json['landmark'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (location != null) {
      data['location'] = location!.toJson();
    }
    data['address_text'] = addressText;
    data['building_type'] = buildingType;
    data['house_number'] = houseNumber;
    data['landmark'] = landmark;
    return data;
  }
}

class DeliveryTimes {
  int? id;
  String? name;
  String? description;
  String? cost;

  DeliveryTimes({this.id, this.name, this.description, this.cost});

  DeliveryTimes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    cost = json['cost'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['cost'] = cost;
    return data;
  }
}

class DropOffOption {
  int? id;
  String? name;

  DropOffOption({this.id, this.name});

  DropOffOption.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class TimeSlot {
  int? id;
  String? startTime;
  String? endTime;

  TimeSlot({this.id, this.startTime, this.endTime});

  TimeSlot.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    return data;
  }
}

class Location {
  Crs? crs;
  String? type;
  List<double>? coordinates;

  Location({this.crs, this.type, this.coordinates});

  Location.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? Crs.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates'] != null
        ? List<double>.from(json['coordinates'].map((x) => x.toDouble()))
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (crs != null) {
      data['crs'] = crs!.toJson();
    }
    data['type'] = type;
    data['coordinates'] = coordinates;
    return data;
  }
}

class Crs {
  String? type;
  Properties? properties;

  Crs({this.type, this.properties});

  Crs.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'] != null
        ? Properties.fromJson(json['properties'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    if (properties != null) {
      data['properties'] = properties!.toJson();
    }
    return data;
  }
}

class Properties {
  String? name;

  Properties({this.name});

  Properties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    return data;
  }
}

class Coupon {
  int? id;
  String? couponCode;

  Coupon({this.id, this.couponCode});

  Coupon.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    couponCode = json['coupon_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['coupon_code'] = couponCode;
    return data;
  }
}

class AssignedDriver {
  String? firstName;
  String? lastName;
  String? profilePicture;
  String? phone;
  String? profilePictureUrl;
  int? driverDeliveryOrderId;
  String? driverDeliveryOrderStatus;
  DriverCurrentLocation? driverCurrentLocation;

  AssignedDriver({
    this.firstName,
    this.lastName,
    this.profilePicture,
    this.phone,
    this.profilePictureUrl,
    this.driverDeliveryOrderId,
    this.driverDeliveryOrderStatus,
    this.driverCurrentLocation,
  });

  AssignedDriver.fromJson(Map<String, dynamic> json) {
    firstName = json['first_name'];
    lastName = json['last_name'];
    profilePicture = json['profile_picture'];
    phone = json['phone'];
    profilePictureUrl = json['profile_picture_url'];
    driverDeliveryOrderId = json['driver_delivery_order_id'];
    driverDeliveryOrderStatus = json['driver_delivery_order_status'];
    driverCurrentLocation = json['driver_current_location'] != null
        ? DriverCurrentLocation.fromJson(json['driver_current_location'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    data['profile_picture'] = profilePicture;
    data['phone'] = phone;
    data['profile_picture_url'] = profilePictureUrl;
    data['driver_delivery_order_id'] = driverDeliveryOrderId;
    data['driver_delivery_order_status'] = driverDeliveryOrderStatus;
    if (driverCurrentLocation != null) {
      data['driver_current_location'] = driverCurrentLocation!.toJson();
    }
    return data;
  }
}

class DriverCurrentLocation {
  Location? location;
  String? address;
  String? city;
  bool? status;
  String? updatedAt;

  DriverCurrentLocation({
    this.location,
    this.address,
    this.city,
    this.status,
    this.updatedAt,
  });

  DriverCurrentLocation.fromJson(Map<String, dynamic> json) {
    location =
        json['location'] != null ? Location.fromJson(json['location']) : null;
    address = json['address'];
    city = json['city'];
    status = json['status'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (location != null) {
      data['location'] = location!.toJson();
    }
    data['address'] = address;
    data['city'] = city;
    data['status'] = status;
    data['updated_at'] = updatedAt;
    return data;
  }
}