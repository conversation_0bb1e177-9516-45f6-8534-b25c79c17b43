{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a2ade4ceda366bb9c5ad27e509beef80", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9859ed185bddda9fac98fb955dbede7af8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9859c3b18b4a5885185381bdef62fc52cc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad3933a0aa7d10d295b0b450348462c6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9859c3b18b4a5885185381bdef62fc52cc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9800a96723981eef88cf31d8c7236d90f0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c1c365d200bf919c30ee6a1e3a1c68bd", "guid": "bfdfe7dc352907fc980b868725387e98c4f5c98f0da774a21e280eab7fe057a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e08443e265a4384ee7159de0506f7b18", "guid": "bfdfe7dc352907fc980b868725387e9839fc6c7c664204e244b373b460a9432f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee2c26e4ff559e6d0b844b134a2a46b3", "guid": "bfdfe7dc352907fc980b868725387e98427a666d7183895e3731d9813f412e20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cced89c82fedbacd8508579fddd4fde9", "guid": "bfdfe7dc352907fc980b868725387e98e6727dcc8f1485e917b74fd4bef0a6ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8f3cee9585814513f2f9d7384881a8", "guid": "bfdfe7dc352907fc980b868725387e98df277450d913184a2fa4d6f95009ce33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3c4f4a616e1fbe47a0fa776089ce40d", "guid": "bfdfe7dc352907fc980b868725387e98eb2550ce05cbbca7cd449a6c4ff75f87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d91e4f988a27d39b81c1d094761dc69", "guid": "bfdfe7dc352907fc980b868725387e98638198299c71ff4f47d5a92a029a6b66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819f8d46ffd52fbf3125d945630c893c7", "guid": "bfdfe7dc352907fc980b868725387e983f2105b19a45887ec0cd99b8f4098a91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b48ea192efd526f8331be5d166f053a", "guid": "bfdfe7dc352907fc980b868725387e98705be49b2174f3047c35324fab7fba8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98869339e2e1052c1d5f0e1e84776b29b7", "guid": "bfdfe7dc352907fc980b868725387e98552ebb95fb1f5d91334db314a2781a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fb64f87f090d0ffdec279ebb7492033", "guid": "bfdfe7dc352907fc980b868725387e9890339403453f254130360d2034e1ca48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c66917c18ae579dfc2542f08d7b05550", "guid": "bfdfe7dc352907fc980b868725387e983a5f24f9d5e80f37cf291255e141080c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e6b0041e0ad77b64e79f44708425247", "guid": "bfdfe7dc352907fc980b868725387e98100c4dd103ffa81658874e2b2d85d5e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffe54d7bf84a20bd698c8a4202a5cd47", "guid": "bfdfe7dc352907fc980b868725387e98617f042988b16886e7cc20ec65a58165"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988da05b1b9177013bac72be1f47080638", "guid": "bfdfe7dc352907fc980b868725387e9865b0896451f9d6cf2283a039fbf63cec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861f55af26aa6b360f95f7c2d612996dd", "guid": "bfdfe7dc352907fc980b868725387e9817b2cd278806279c92d05559de20d64a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989190abc27b32a9e4c66173b014670d3c", "guid": "bfdfe7dc352907fc980b868725387e989387f43106c5ec26f7c28c4ccdaf7a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854760c29fd31f9df6302076f7050fab8", "guid": "bfdfe7dc352907fc980b868725387e98a579ec35ea48344f451f640f5133a97c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4bf4d24f0e7978dea4600af87156004", "guid": "bfdfe7dc352907fc980b868725387e98247ce5c43b725c1b4d8c5b17a0915e14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857946820cc4fb9c39a7a0834c46b8b57", "guid": "bfdfe7dc352907fc980b868725387e988f1107f583b9dbd85850564d62dbe5ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daacf90710d66d155d9b8b693c076c73", "guid": "bfdfe7dc352907fc980b868725387e987199af7a0850f7694381ed347c4a2089", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841f7c47604a3293cc4d4d45546aaf205", "guid": "bfdfe7dc352907fc980b868725387e984c3ddb72446e455ac3be9c0c8232f685"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98316d32137f42a2ba106661dad5b4830e", "guid": "bfdfe7dc352907fc980b868725387e988e030a7305ce8e1c8acf4e05f72438fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981667ff4abc26fc6bdaa3561ee043791d", "guid": "bfdfe7dc352907fc980b868725387e98a6603a3db649375f87186c6acf3277e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876b19da0b4ff7416f732e3c4da3acc71", "guid": "bfdfe7dc352907fc980b868725387e984378d226f2fd781453f3a6279bdd9a5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98008e1784797f7256fea5890116281b28", "guid": "bfdfe7dc352907fc980b868725387e983aa61192ae40fcb1e7747889e964445d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa3c1dc2d395ff91d82b33e6b05c71b3", "guid": "bfdfe7dc352907fc980b868725387e98091769d6d3ea2dad8c8511b5d34270ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4adf4440eba14ecef39d9ee314e8629", "guid": "bfdfe7dc352907fc980b868725387e989879c076e1ae8cea441a6b6f2a225e71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8edaff1ebeb677f32b68a531c092970", "guid": "bfdfe7dc352907fc980b868725387e98950714569cbb291ac99cde4b378f4651", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98270c1412825ee04043305479b149fd0a", "guid": "bfdfe7dc352907fc980b868725387e984a1d9ef740af0331a31f3d263123ae9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98654663b520bf9ce286ccce8ea6fe8e8d", "guid": "bfdfe7dc352907fc980b868725387e985e82d340fc8c2378a4a2148d02ddaf80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e48637f3ccdc102acd2c7386bafa7d7", "guid": "bfdfe7dc352907fc980b868725387e980a2586542aee47f50069b4e609e95eb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989319b5657bfb81717b37cc5f32beb3d1", "guid": "bfdfe7dc352907fc980b868725387e98ff329000b4f20b17a27ea21ce0626977"}], "guid": "bfdfe7dc352907fc980b868725387e981aded8a02e4b1e337a315b029e4bfb91", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987f24eda75d5cf8b2a9108207ec761036", "guid": "bfdfe7dc352907fc980b868725387e983e6dcd61148781ca855fe92f7db09b2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c599ee0cc13aa6ffc2a6803428548d6", "guid": "bfdfe7dc352907fc980b868725387e9866e5064fd7a9125a6b713372d680c524"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc1d574974b4de542c4e6c9c4eb33190", "guid": "bfdfe7dc352907fc980b868725387e9872d600e0be7a46c569226c942040fa57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1c2dec4a720ba820ed3fa3bd413af74", "guid": "bfdfe7dc352907fc980b868725387e98ee427f7940a070d745623c66d8c1b97b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98738b061f3cc48c44ce95a391af8e43b0", "guid": "bfdfe7dc352907fc980b868725387e98808c3f107c3150d987a622f7d1bbda56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c92385f8918ead20ae01952707c57dbc", "guid": "bfdfe7dc352907fc980b868725387e9861eaa32b5adcdd7d6ee6553abcbf3d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987796cc384f9805f01401f20fa3e2094b", "guid": "bfdfe7dc352907fc980b868725387e9811c8c0afe8469cb9c707bf9746a7ac2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98059beb9b1b3e45c7c8a09fe3117a5d7d", "guid": "bfdfe7dc352907fc980b868725387e9897e87d5e4218ee912acae6064cce6234"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da4ddbf15aecf4f87d3c14bb4ec612b2", "guid": "bfdfe7dc352907fc980b868725387e98d83895c00a5d47fee98859d5c618a8d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d9f2ec13d22d48297ad28d49a29eca9", "guid": "bfdfe7dc352907fc980b868725387e987e404fabd4e635a6a708573622e6eed2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6dacbec876c2b26ebbbfeb8e7311d52", "guid": "bfdfe7dc352907fc980b868725387e98c4f914657109c26cd7a882f091e4852b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebef8438ae82d3495f81e0586a6b3ae5", "guid": "bfdfe7dc352907fc980b868725387e9855dbb60637d32e33e42287f4d9fc792c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98140ec64b0d739e0c3b22bdc92c05f687", "guid": "bfdfe7dc352907fc980b868725387e98158466ccbc0debc03eb00b4ca0c3afc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6be3133aac137b011b1ad78e6fccc07", "guid": "bfdfe7dc352907fc980b868725387e98801320b4a527d37dd02e224865a958b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b21fdf5ca403ea7fd38c690ba567c646", "guid": "bfdfe7dc352907fc980b868725387e98d7099315dcf4f878890c3932418d5154"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899e3b3fabb3ce420f9a140c0ac58efad", "guid": "bfdfe7dc352907fc980b868725387e985966cde74b01bd2ba48d65abbebc9c21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab84f455914b4788a6b9e7c768086478", "guid": "bfdfe7dc352907fc980b868725387e9828395b9104b94e746bda0055712ef66f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f50813d37472ab048ffe818c15442f42", "guid": "bfdfe7dc352907fc980b868725387e981f81db25bccfef163e8f58db59961417"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887810b8322fc41bee130726f64952c6d", "guid": "bfdfe7dc352907fc980b868725387e9839786429cdc16b47ba317f6fc105d1bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884286a8f1d0cf26c869f6ff5f99c05cc", "guid": "bfdfe7dc352907fc980b868725387e9874acb33857fc8088fa06c76d5b5baaf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bdc1805156b55975aeca1222dddf100", "guid": "bfdfe7dc352907fc980b868725387e98b97d363b0575d23b26a4f5b8b703ec17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e409b424813cd8bb6a8df53782db743", "guid": "bfdfe7dc352907fc980b868725387e985519cd16c390bfceaab7853a823fb459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4d1abc6181bec7d57eaeecbc8f86fc5", "guid": "bfdfe7dc352907fc980b868725387e98b36f75eed1c780aaf0f08cec0eb278b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989923aa0865ab7de5f599085010f2a320", "guid": "bfdfe7dc352907fc980b868725387e98e41590211de7b295360237849f5f3e4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3a9886020494151ae24b9932a081eb6", "guid": "bfdfe7dc352907fc980b868725387e98943ae06af83ca3f01d2ab544826ef749"}], "guid": "bfdfe7dc352907fc980b868725387e98a321da4fc5869c3c238cb23e0891a680", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f682db8b9537d169509768c065a6cc9e", "guid": "bfdfe7dc352907fc980b868725387e983171ca607709ba70b7546ebed4586225"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f888991ae5b24f9f379f21bce18f2a85", "guid": "bfdfe7dc352907fc980b868725387e98c2d16e607333c752087b1e4969575d2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e985bf343262a16656a91a507b09bce39f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b13654359f4e9c63153b5fd1f8fd8617", "guid": "bfdfe7dc352907fc980b868725387e98130ec0c2334a400e82d3ad32be151f71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e98d54612bc5779951b16ff59fe9db93e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b194461b3d65889636e9bae473ca683", "guid": "bfdfe7dc352907fc980b868725387e9825424d0feb24a4df1e3c56fdb9919796"}], "guid": "bfdfe7dc352907fc980b868725387e9897accaa9157e6d4daa68cd6abfaa8465", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9892138fcdebd2c18a4559605a27420448", "targetReference": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5"}], "guid": "bfdfe7dc352907fc980b868725387e98e408fa91ecb9a202b7a733d7c8b68761", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5", "name": "GoogleSignIn-GoogleSignIn"}], "guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815509a5aa54606eda7171e744ada7414", "name": "GoogleSignIn.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}