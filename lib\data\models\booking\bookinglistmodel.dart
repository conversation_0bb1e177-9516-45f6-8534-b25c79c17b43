class OngoingBookinglistModel {
  bool? status;
  Data? data;
  int? statusCode;

  OngoingBookinglistModel({this.status, this.data, this.statusCode});

  OngoingBookinglistModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['status_code'] = statusCode;
    return data;
  }
}

class Data {
  List<Bookings>? bookings;
  Pagination? pagination;

  Data({this.bookings, this.pagination});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['bookings'] != null) {
      bookings = <Bookings>[];
      json['bookings'].forEach((v) {
        bookings!.add(Bookings.fromJson(v));
      });
    }
    pagination = json['pagination'] != null
        ? Pagination.fromJson(json['pagination'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (bookings != null) {
      data['bookings'] = bookings!.map((v) => v.toJson()).toList();
    }
    if (pagination != null) {
      data['pagination'] = pagination!.toJson();
    }
    return data;
  }
}

class Bookings {
  int? id;
  String? orderNumber;
  Chef? chef;
  int? itemsCount;
  double? total;
  String? status;
  String? paymentStatus;
  String? createdAt;

  Bookings(
      {this.id,
      this.orderNumber,
      this.chef,
      this.itemsCount,
      this.total,
      this.status,
      this.paymentStatus,
      this.createdAt});

  Bookings.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    orderNumber = json['order_number'];
    chef = json['chef'] != null ? Chef.fromJson(json['chef']) : null;
    itemsCount = json['items_count'];
    total = json['total'] != null ? (json['total'] as num).toDouble() : null;
    status = json['status'];
    paymentStatus = json['payment_status'];
    createdAt = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['order_number'] = orderNumber;
    if (chef != null) {
      data['chef'] = chef!.toJson();
    }
    data['items_count'] = itemsCount;
    data['total'] = total;
    data['status'] = status;
    data['payment_status'] = paymentStatus;
    data['created_at'] = createdAt;
    return data;
  }
}

class Chef {
  int? id;
  String? name;
  String? photo;
  List<String>? chefOperationDays;
  ChefOperationTime? chefOperationTime;

  Chef(
      {this.id,
      this.name,
      this.photo,
      this.chefOperationDays,
      this.chefOperationTime});

  Chef.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
    if (json['chef_operation_days'] != null) {
      chefOperationDays = List<String>.from(json['chef_operation_days']);
    }
    chefOperationTime = json['chef_operation_time'] != null
        ? ChefOperationTime.fromJson(json['chef_operation_time'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['photo'] = photo;
    data['chef_operation_days'] = chefOperationDays;
    if (chefOperationTime != null) {
      data['chef_operation_time'] = chefOperationTime!.toJson();
    }
    return data;
  }
}

class ChefOperationTime {
  int? id;
  String? startTime;
  String? endTime;

  ChefOperationTime({this.id, this.startTime, this.endTime});

  ChefOperationTime.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    return data;
  }
}

class Pagination {
  int? total;
  int? page;
  int? pages;
  int? limit;

  Pagination({this.total, this.page, this.pages, this.limit});

  Pagination.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    page = json['page'];  
    pages = json['pages'];
    limit = json['limit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total'] = total;
    data['page'] = page;
    data['pages'] = pages;
    data['limit'] = limit;
    return data;
  }
}
