import 'dart:developer';
import 'dart:io';

import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/verifyrefreshtokenmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
// Add these imports for token handling
import 'package:db_eats/storage/localstorage.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChefBloc extends Bloc<ChefEvent, ChefState> {
  ChefBloc() : super(ChefInitial()) {
    on<AddChefRatingEvent>(_onAddChefRating);
    // Add handler for RefreshTokenEvent
    on<RefreshTokenEvent>(_onRefreshToken);
  }

  Future<void> _onAddChefRating(
      AddChefRatingEvent event, Emitter<ChefState> emit) async {
    emit(ChefRatingLoading());
    try {
      // Convert the data to string values for form data
      final formData = Map<String, String>.from({
        'chef_id': event.data['chef_id'].toString(),
        'order_id': event.data['order_id'].toString(),
        'star_rating': event.data['star_rating'].toString(),
        'comment': event.data['comment'] ?? '',
      });

      log('Sending formData: $formData'); // Add this for debugging

      final response = await ServerHelper.uploadFiles(
        '/v1/customer/chef_rating/add',
        'dish_photo',
        event.images,
        additionalData: formData,
      );
      log('Chef Rating Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (response['status'] == true) {
        emit(ChefRatingAdded(
            response['message'] ?? 'Rating and photos added successfully'));
      } else {
        emit(ChefRatingError(response['message'] ?? 'Failed to add rating'));
      }
    } catch (e) {
      log('Error adding chef rating: $e');
      emit(ChefRatingError('Error occurred while adding chef rating'));
    }
  }

  // Add refresh token handler
  Future<void> _onRefreshToken(
      RefreshTokenEvent event, Emitter<ChefState> emit) async {
    emit(RefreshTokenLoading());
    try {
      Initializer.verifyRefreshTokenModel = VerifyRefreshTokenModel.fromJson(
          await ServerHelper.getrefresh(
              '/v1/customer/auth/verify-refresh-token'));
      if (Initializer.verifyRefreshTokenModel.status == true) {
        await LocalStorage.setAccessToken(
            Initializer.verifyRefreshTokenModel.data?.accessToken ?? "");
        emit(RefreshTokenSuccess());
        if (event.nextEvent != null) {
          add(event.nextEvent!);
        }
      } else if (Initializer.verifyRefreshTokenModel.statusCode == 401) {
        emit(RefreshTokenFailed());
        // await LocalStorage.setAccessToken('');
        // await LocalStorage.setRefreshToken('');
        // Navigator.of(context).pushAndRemoveUntil(
        //   MaterialPageRoute(builder: (context) => const Home()),
        //   (route) => false,
        // );
      }
    } catch (e) {
      emit(RefreshTokenFailed());
    }
  }
}

abstract class ChefEvent {}

class AddChefRatingEvent extends ChefEvent {
  final Map<String, dynamic> data;
  final List<File> images;
  AddChefRatingEvent(this.data, this.images);
}

// Add RefreshTokenEvent
class RefreshTokenEvent extends ChefEvent {
  final String refreshToken;
  final ChefEvent? nextEvent;
  RefreshTokenEvent({required this.refreshToken, required this.nextEvent});
}

abstract class ChefState {}

class ChefInitial extends ChefState {}

class ChefRatingLoading extends ChefState {}

class ChefRatingAdded extends ChefState {
  final String message;
  ChefRatingAdded(this.message);
}

class ChefRatingError extends ChefState {
  final String message;
  ChefRatingError(this.message);
}

// Add RefreshToken states
class RefreshTokenLoading extends ChefState {}

class RefreshTokenSuccess extends ChefState {}

class RefreshTokenFailed extends ChefState {}
