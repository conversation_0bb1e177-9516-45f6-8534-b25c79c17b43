class MealPlanLocal {
  final int mealPlanId;
  final String date;
  final int chefId;
  final String chefName;
  final String chefImage;
  final List<MealItemLocal> items;

  MealPlanLocal({
    required this.mealPlanId,
    required this.date,
    required this.chefId,
    required this.chefName,
    required this.chefImage,
    required this.items,
  });

  Map<String, dynamic> toMap() {
    return {
      'meal_plan_id': mealPlanId,
      'date': date,
      'chef_id': chefId,
      'chef_name': chefName,
      'chef_image': chefImage,
      'items': items.map((item) => item.toMap()).toList(),
    };
  }

  static MealPlanLocal fromMap(Map<String, dynamic> map) {
    return MealPlanLocal(
      mealPlanId: map['meal_plan_id'],
      date: map['date'],
      chefId: map['chef_id'],
      chefName: map['chef_name'],
      chefImage: map['chef_image'],
      items: List<MealItemLocal>.from(
        map['items']?.map((x) => MealItemLocal.fromMap(x)) ?? [],
      ),
    );
  }
}

class MealItemLocal {
  final int menuItemId;
  final String name;
  final String image;
  final double price;
  final String servings;

  MealItemLocal({
    required this.menuItemId,
    required this.name,
    required this.image,
    required this.price,
    required this.servings,
  });

  Map<String, dynamic> toMap() {
    return {
      'chef_menu_item_id': menuItemId,
      'name': name,
      'image': image,
      'price': price,
      'servings': servings,
    };
  }

  static MealItemLocal fromMap(Map<String, dynamic> map) {
    return MealItemLocal(
      menuItemId: map['chef_menu_item_id'],
      name: map['name'],
      image: map['image'],
      price: map['price'],
      servings: map['servings'],
    );
  }
}
