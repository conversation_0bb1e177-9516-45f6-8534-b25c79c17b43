import 'package:flutter/material.dart';

class AddressEditPage extends StatefulWidget {
  final String initialAddress;
  final String initialState;
  final String initialCity;
  final String initialZipCode;

  const AddressEditPage({
    super.key,
    required this.initialAddress,
    required this.initialState,
    required this.initialCity,
    required this.initialZipCode,
  });

  @override
  State<AddressEditPage> createState() => _AddressEditPageState();
}

class _AddressEditPageState extends State<AddressEditPage> {
  late TextEditingController _addressController;
  late TextEditingController _stateController;
  late TextEditingController _cityController;
  late TextEditingController _zipCodeController;

  bool _showAddressError = false;
  bool _showStateError = false;
  bool _showCityError = false;
  bool _showZipCodeError = false;

  @override
  void initState() {
    super.initState();
    _addressController = TextEditingController(text: widget.initialAddress);
    _stateController = TextEditingController(text: widget.initialState);
    _cityController = TextEditingController(text: widget.initialCity);
    _zipCodeController = TextEditingController(text: widget.initialZipCode);
  }

  @override
  void dispose() {
    _addressController.dispose();
    _stateController.dispose();
    _cityController.dispose();
    _zipCodeController.dispose();
    super.dispose();
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required bool showError,
    String? errorText,
    String? hintText,
    VoidCallback? onValidate,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Inter',
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Color(0xFF1F2122),
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          onChanged: (value) {
            if (showError) {
              setState(() {
                switch (controller) {
                  case var c when c == _addressController:
                    _showAddressError = false;
                    break;
                  case var c when c == _stateController:
                    _showStateError = false;
                    break;
                  case var c when c == _cityController:
                    _showCityError = false;
                    break;
                  case var c when c == _zipCodeController:
                    _showZipCodeError = false;
                    break;
                }
              });
            }
          },
          onEditingComplete: onValidate,
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.all(16),
            hintText: hintText,
            hintStyle: const TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              fontSize: 16,
              color: Color(0xFF66696D),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: showError ? Colors.red : const Color(0xFFE1E3E6),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: showError ? Colors.red : const Color(0xFFE1E3E6),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: Color(0xFF1F2122),
                width: 1.5,
              ),
            ),
            errorText: showError ? errorText : null,
            errorStyle: const TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              color: Colors.red,
            ),
          ),
          cursorColor: const Color(0xFF1F2122),
          style: const TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w400,
            fontSize: 16,
            color: Color(0xFF1F2122),
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  bool _validateAddress() {
    final isValid = _addressController.text.trim().isNotEmpty;
    setState(() {
      _showAddressError = !isValid;
    });
    return isValid;
  }

  bool _validateState() {
    final isValid = _stateController.text.trim().isNotEmpty;
    setState(() {
      _showStateError = !isValid;
    });
    return isValid;
  }

  bool _validateCity() {
    final isValid = _cityController.text.trim().isNotEmpty;
    setState(() {
      _showCityError = !isValid;
    });
    return isValid;
  }

  bool _validateZipCode() {
    final isValid = _zipCodeController.text.trim().isNotEmpty;
    setState(() {
      _showZipCodeError = !isValid;
    });
    return isValid;
  }

  bool _validateAll() {
    final addressValid = _validateAddress();
    final stateValid = _validateState();
    final cityValid = _validateCity();
    final zipCodeValid = _validateZipCode();

    return addressValid && stateValid && cityValid && zipCodeValid;
  }

  void _saveAddress() {
    if (_validateAll()) {
      Navigator.of(context).pop({
        'address': _addressController.text.trim(),
        'state': _stateController.text.trim(),
        'city': _cityController.text.trim(),
        'zipCode': _zipCodeController.text.trim(),
      });
    }
  }

  void _clearAllFields() {
    setState(() {
      _addressController.clear();
      _stateController.clear();
      _cityController.clear();
      _zipCodeController.clear();
      _showAddressError = false;
      _showStateError = false;
      _showCityError = false;
      _showZipCodeError = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF6F3EC),
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF1F2122)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Edit Address',
          style: TextStyle(
            color: Color(0xFF1F2122),
            fontSize: 18,
            fontWeight: FontWeight.w600,
            fontFamily: 'Inter',
            height: 1.24,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Delivery Address',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  height: 1.24,
                  color: Color(0xFF1F2122),
                ),
              ),
              const SizedBox(height: 24),

              _buildTextField(
                controller: _addressController,
                label: 'Street Address',
                showError: _showAddressError,
                errorText: 'Please enter a street address',
                hintText: 'Enter street address',
                onValidate: _validateAddress,
              ),

              _buildTextField(
                controller: _stateController,
                label: 'State',
                showError: _showStateError,
                errorText: 'Please enter a state',
                hintText: 'Enter state',
                onValidate: _validateState,
              ),

              _buildTextField(
                controller: _cityController,
                label: 'City',
                showError: _showCityError,
                errorText: 'Please enter a city',
                hintText: 'Enter city',
                onValidate: _validateCity,
              ),

              _buildTextField(
                controller: _zipCodeController,
                label: 'Zip Code',
                showError: _showZipCodeError,
                errorText: 'Please enter a zip code',
                hintText: 'Enter zip code',
                onValidate: _validateZipCode,
              ),

              const SizedBox(height: 8),

              // Clear Address Button
              Align(
                alignment: Alignment.centerLeft,
                child: TextButton(
                  onPressed: _clearAllFields,
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: const Size(30, 20),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: const Text(
                    'Clear Address',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: Color.fromARGB(255, 7, 7, 7),
                      decoration: TextDecoration.underline,
                      decorationThickness: 1.5,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding:
            const EdgeInsets.only(top: 16, bottom: 48, left: 16, right: 16),
        child: SizedBox(
          width: double.infinity,
          height: 52,
          child: ElevatedButton(
            onPressed: _saveAddress,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF1F2122),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(26),
              ),
              elevation: 0,
            ),
            child: const Text(
              'Save Address',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: 16,
                fontWeight: FontWeight.w600,
                height: 1.0,
                letterSpacing: 0.32,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
