import 'package:db_eats/ui/profile/modifyaddress.dart' as modify_address;
import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/data/models/guesthome/listaddressmodel.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SelectAddressPage extends StatefulWidget {
  final AddressData? currentAddress;

  const SelectAddressPage({
    super.key,
    this.currentAddress,
  });

  @override
  _SelectAddressPageState createState() => _SelectAddressPageState();
}

class _SelectAddressPageState extends State<SelectAddressPage> {
  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;

  List<AddressData>? addresses;
  bool isLoading = true;
  AddressData? selectedAddress;
  int? makingCurrentAddressId;
  int? deletingAddressId;

  @override
  void initState() {
    super.initState();
    selectedAddress = widget.currentAddress;
    context.read<AccountBloc>().add(ListAddressesEvent());
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;
    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  void _selectAddress(AddressData address) {
    setState(() {
      selectedAddress = address;
    });
  }

  void _confirmAddressSelection() {
    if (selectedAddress != null) {
      if (selectedAddress!.isCurrent != true) {
        _showChangeAddressConfirmation(selectedAddress!);
      } else {
        // If already current, just return with the selected address
        Navigator.pop(context, selectedAddress);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AccountBloc, AccountState>(
      listener: (context, state) {
        if (state is ListAddressesSuccess) {
          setState(() {
            addresses = state.data;
            isLoading = false;

            // Update selected address if it was the current one
            if (selectedAddress == null &&
                addresses != null &&
                addresses!.isNotEmpty) {
              selectedAddress = addresses!.firstWhere(
                (address) => address.isCurrent == true,
                orElse: () => addresses!.first,
              );
            } else if (selectedAddress != null && addresses != null) {
              // Update the selected address object with fresh data
              final updatedAddress = addresses!.firstWhere(
                (address) => address.id == selectedAddress!.id,
                orElse: () => selectedAddress!,
              );
              selectedAddress = updatedAddress;
            }
          });
        } else if (state is ListAddressesFailed) {
          setState(() {
            isLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is ListAddressesLoading) {
          setState(() {
            isLoading = true;
          });
        } else if (state is EditAddressLoading) {
          // Handle make current loading state
        } else if (state is EditAddressSuccess) {
          setState(() {
            makingCurrentAddressId = null;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.green,
            ),
          );
          // Return the selected address after successfully making it current
          Navigator.pop(context, selectedAddress);
        } else if (state is EditAddressFailed) {
          setState(() {
            makingCurrentAddressId = null;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is DeleteAddressLoading) {
          // Handle delete loading state
        } else if (state is DeleteAddressSuccess) {
          setState(() {
            deletingAddressId = null;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.green,
            ),
          );
          context.read<AccountBloc>().add(ListAddressesEvent());
        } else if (state is DeleteAddressFailed) {
          setState(() {
            deletingAddressId = null;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF6F3EC),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              size: twentyFour,
              color: const Color(0xFF1F2122),
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          title: Text(
            'Select Address',
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w600,
              fontSize: eighteen,
              color: const Color(0xFF1F2122),
            ),
          ),
          // centerTitle: true,
        ),
        body: Column(
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(sixteen),
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: const Color(0xFFF6F3EC),
                    borderRadius: BorderRadius.circular(twelve),
                  ),
                  padding: EdgeInsets.all(sixteen),
                  child: Column(
                    children: [
                      if (isLoading)
                        const Expanded(
                          child: Center(
                            child: CircularProgressIndicator(),
                          ),
                        )
                      else if (addresses == null || addresses!.isEmpty)
                        Expanded(
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'No saved addresses',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: sixteen,
                                    color: const Color(0xFF66696D),
                                  ),
                                ),
                                SizedBox(height: sixteen),
                                OutlinedButton(
                                  onPressed: () async {
                                    final result = await Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            modify_address.ModifyAddressPage(
                                          type: "save",
                                        ),
                                      ),
                                    );
                                    if (result != null) {
                                      context
                                          .read<AccountBloc>()
                                          .add(ListAddressesEvent());
                                    }
                                  },
                                  style: OutlinedButton.styleFrom(
                                    backgroundColor: Colors.white,
                                    side: const BorderSide(
                                      color: Color(0xFF1F2122),
                                      width: 1,
                                    ),
                                    padding: EdgeInsets.symmetric(
                                      vertical: twelve,
                                      horizontal: twenty,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius:
                                          BorderRadius.circular(twenty),
                                    ),
                                  ),
                                  child: Text(
                                    'Add new Address',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      else
                        Expanded(
                          child: Column(
                            children: [
                              ...addresses!.take(3).map((address) {
                                final index = addresses!
                                    .take(3)
                                    .toList()
                                    .indexOf(address);
                                return Column(
                                  children: [
                                    BlocBuilder<AccountBloc, AccountState>(
                                      buildWhen: (previous, current) {
                                        return current is EditAddressLoading ||
                                            current is EditAddressSuccess ||
                                            current is EditAddressFailed ||
                                            current is DeleteAddressLoading ||
                                            current is DeleteAddressSuccess ||
                                            current is DeleteAddressFailed;
                                      },
                                      builder: (context, state) {
                                        bool isMakingCurrent =
                                            state is EditAddressLoading &&
                                                makingCurrentAddressId ==
                                                    address.id;
                                        bool isDeleting =
                                            state is DeleteAddressLoading &&
                                                deletingAddressId == address.id;

                                        return _buildAddressCard(
                                          addressData: address,
                                          isSelected:
                                              selectedAddress?.id == address.id,
                                          isMakingCurrent: isMakingCurrent,
                                          isDeleting: isDeleting,
                                          onTap: () => _selectAddress(address),
                                        );
                                      },
                                    ),
                                    if (index < addresses!.take(3).length - 1)
                                      SizedBox(height: sixteen),
                                  ],
                                );
                              }).toList(),
                              SizedBox(height: sixteen),
                              _buildAddNewAddressButton(),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              color: Colors.white,
              padding: EdgeInsets.all(sixteen),
              child: SafeArea(
                child: SizedBox(
                  width: double.infinity,
                  height: twentyFour * 2,
                  child: ElevatedButton(
                    onPressed: selectedAddress != null &&
                            makingCurrentAddressId == null
                        ? _confirmAddressSelection
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1F2122),
                      disabledBackgroundColor: const Color(0xFFB9B6AD),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(twenty),
                      ),
                    ),
                    child: makingCurrentAddressId != null
                        ? SizedBox(
                            height: twenty,
                            width: twenty,
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            'Continue',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w600,
                              fontSize: sixteen,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressCard({
    required AddressData addressData,
    required bool isSelected,
    required VoidCallback onTap,
    bool isMakingCurrent = false,
    bool isDeleting = false,
  }) {
    return GestureDetector(
      onTap: isMakingCurrent || isDeleting ? null : onTap,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : const Color(0xFFF6F3EC),
          borderRadius: BorderRadius.circular(ten),
          border: Border.all(
            color: isSelected ? Colors.white : const Color(0xFFB9B6AD),
            width: 1,
          ),
        ),
        padding: EdgeInsets.all(twelve),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Address',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w500,
                    fontSize: forteen,
                    height: 16 / 14,
                    letterSpacing: 0.28,
                    color: const Color(0xFF1F2122),
                  ),
                ),
                Container(
                  width: twenty,
                  height: twenty,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: const Color(0xFF1F2122),
                      width: 2,
                    ),
                    color: Colors.transparent,
                  ),
                  child: isSelected
                      ? Container(
                          margin: EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: const Color(0xFF1F2122),
                          ),
                        )
                      : null,
                ),
              ],
            ),
            SizedBox(height: forteen),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  children: [
                    Image.asset(
                      'assets/icons/location.png',
                      width: sixteen,
                      height: sixteen,
                      color: const Color(0xFF1F2122),
                    ),
                  ],
                ),
                SizedBox(width: ten),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _formatAddress(addressData),
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w500,
                          fontSize: forteen,
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                      SizedBox(height: ten),
                      Row(
                        children: [
                          if (!isMakingCurrent && !isDeleting)
                            GestureDetector(
                              onTap: () async {
                                final shouldDelete =
                                    await _showDeleteConfirmation(context);
                                if (shouldDelete == true) {
                                  setState(() {
                                    deletingAddressId = addressData.id;
                                  });
                                  context
                                      .read<AccountBloc>()
                                      .add(DeleteAddressEvent(addressData.id!));
                                }
                              },
                              child: Text(
                                'Remove',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: twelve,
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF414346),
                                ),
                              ),
                            ),
                          if (!isMakingCurrent && !isDeleting)
                            SizedBox(width: sixteen),
                          if (!isMakingCurrent && !isDeleting)
                            GestureDetector(
                              onTap: () async {
                                final result = await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        modify_address.ModifyAddressPage(
                                      type: "edit",
                                      addressId: addressData.id,
                                    ),
                                  ),
                                );
                                if (result != null) {
                                  context
                                      .read<AccountBloc>()
                                      .add(ListAddressesEvent());
                                }
                              },
                              child: Text(
                                'Edit',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: twelve,
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF414346),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatAddress(AddressData addressData) {
    List<String> addressParts = [];

    if (addressData.houseNumber?.isNotEmpty == true) {
      addressParts.add(addressData.houseNumber!);
    }

    if (addressData.addressText?.isNotEmpty == true) {
      addressParts.add(addressData.addressText!);
    }

    if (addressData.buildingType?.isNotEmpty == true) {
      addressParts.add(addressData.buildingType!);
    }

    if (addressData.landmark?.isNotEmpty == true) {
      addressParts.add('Near ${addressData.landmark}');
    }

    return addressParts.join('\n');
  }

  Future<bool?> _showDeleteConfirmation(BuildContext context) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            constraints: const BoxConstraints(maxWidth: 400),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const SizedBox(width: 24),
                    Text(
                      'Confirm',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w600,
                        fontSize: eighteen,
                        color: const Color(0xFF1F2122),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: const Icon(
                        Icons.close,
                        size: 24,
                        color: Colors.black54,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Text(
                  'Are you sure you want to delete this address?',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: forteen,
                    color: const Color(0xFF66696D),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        style: OutlinedButton.styleFrom(
                          backgroundColor: Colors.white,
                          side: const BorderSide(
                              color: Color(0xFFB9B6AD), width: 1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(twenty),
                          ),
                          padding: EdgeInsets.symmetric(vertical: twelve),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w500,
                            fontSize: forteen,
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF1F2122),
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(twenty),
                          ),
                          padding: EdgeInsets.symmetric(vertical: twelve),
                        ),
                        child: Text(
                          'Delete',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w600,
                            fontSize: forteen,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAddNewAddressButton() {
    return Center(
      child: GestureDetector(
        onTap: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => modify_address.ModifyAddressPage(
                type: "save",
              ),
            ),
          );
          if (result != null) {
            context.read<AccountBloc>().add(ListAddressesEvent());
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 19, vertical: 5),
          decoration: BoxDecoration(
            color: const Color(0xFFF6F3EC),
            border: Border.all(
              color: const Color(0xFF1F2122),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(30),
          ),
          child: Text(
            'Add New Address',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF1F2122),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _showChangeAddressConfirmation(AddressData address) async {
    final shouldChange = await showDialog<bool>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            constraints: const BoxConstraints(maxWidth: 400),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const SizedBox(width: 24),
                    Text(
                      'Change Address',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w600,
                        fontSize: eighteen,
                        color: const Color(0xFF1F2122),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: const Icon(
                        Icons.close,
                        size: 24,
                        color: Colors.black54,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Text(
                  'Do you want to change the address?',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: forteen,
                    color: const Color(0xFF66696D),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        style: OutlinedButton.styleFrom(
                          backgroundColor: Colors.white,
                          side: const BorderSide(
                              color: Color(0xFFB9B6AD), width: 1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(twenty),
                          ),
                          padding: EdgeInsets.symmetric(vertical: twelve),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w500,
                            fontSize: forteen,
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF1F2122),
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(twenty),
                          ),
                          padding: EdgeInsets.symmetric(vertical: twelve),
                        ),
                        child: Text(
                          'Confirm',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w600,
                            fontSize: forteen,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );

    if (shouldChange == true) {
      setState(() {
        makingCurrentAddressId = address.id;
      });

      final addressData = {
        "id": address.id,
        "address_text": address.addressText,
        "building_type": address.buildingType,
        "house_number": address.houseNumber,
        "landmark": address.landmark,
        "latitude": address.location?.coordinates?[1],
        "longitude": address.location?.coordinates?[0],
        "is_current": true,
      };

      context.read<AccountBloc>().add(EditAddressEvent(addressData));
    }
  }
}
