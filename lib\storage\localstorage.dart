import 'dart:developer';

import 'package:db_eats/common/helper.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalStorage {
  static saveUserid(var userid) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('userid_local', userid);
  }

  static getUserid() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('userid_local');
  }

  static saveFcmToken(String fcmToken) async {
    final sharedPreferences = await Helper.locaStorageInst();
    sharedPreferences.setString('fcmToken', fcmToken);
  }

  static savePlayerId(String playerId) async {
    final sharedPreferences = await Helper.locaStorageInst();
    sharedPreferences.setString('playerId', playerId);
  }

  static getPlayerId() async {
    final sharedPreferences = await Helper.locaStorageInst();
    return sharedPreferences.getString('playerId');
  }

  static getFcmToken() async {
    final sharedPreferences = await Helper.locaStorageInst();
    return sharedPreferences.getString('fcmToken');
  }

  static clearAll() async {
    final pref = await SharedPreferences.getInstance();
    await pref.clear();
  }

  static Future<void> setAccessToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('access_token', token);
    log('Access token set: $token');
  }

  static Future<void> setRefreshToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('refresh_token', token);
    log('Refresh token set: $token');
  }

  static Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('access_token');
  }

  static Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('refresh_token');
  }

  static Future<void> clearTokens() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('access_token');
    await prefs.remove('refresh_token');
    log('Access and refresh tokens cleared');
  }
}
