{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988100370e484b3e03b6684d9044d87bdc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985abeabd1c80ff3d60f35cc1cf9d050e8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d9003e093a0109e538a4b771317e145", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fd4914145b9662498db0be46b9b53e8e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d9003e093a0109e538a4b771317e145", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fe46d33b3db4a3722bb2debe4dd47d82", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c35be7c6c8e2083be8ea745075cbf13", "guid": "bfdfe7dc352907fc980b868725387e9873be32058ed8f87cbca21064fd52d615"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870cdedbb6f2e5284158be3551e39d352", "guid": "bfdfe7dc352907fc980b868725387e982507266ce6e9d541d70f2a7b426b84bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981329831b1a29790e9eb5f82d20904f79", "guid": "bfdfe7dc352907fc980b868725387e98a3992e31c66e9aab1f2dc0256048da94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c8e5dbf235bd64b101ac0f2820349d", "guid": "bfdfe7dc352907fc980b868725387e980768c39831b6bcee8931b8ad60d964fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f43ee1eedeb63d571dfcf470bdf05569", "guid": "bfdfe7dc352907fc980b868725387e988e025c6194a5c5709a2913398c7819d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881d125a7635f05aa39a92c096394ba2c", "guid": "bfdfe7dc352907fc980b868725387e98bb8e9beb0ab15e8fe198cacb61c2567a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb40ab29380d9bbdc05d5c9ca76bbde0", "guid": "bfdfe7dc352907fc980b868725387e98429239583e8e42347a475eed74a189cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb03b5b16edb9d702639fc546da543ae", "guid": "bfdfe7dc352907fc980b868725387e98a8c96312c74a5601b9f4437f69a20170"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873f789676c9327814ea25c3c556a8fc8", "guid": "bfdfe7dc352907fc980b868725387e9863e6e0dc8b42a5fba7fabe07431b1c3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bce699addc8e5bfdce438b5aa27310b8", "guid": "bfdfe7dc352907fc980b868725387e98ee1c67758cae90213311bb2979cc0762"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c006e1b599d34be49a0ead4ace0c6e9", "guid": "bfdfe7dc352907fc980b868725387e98cc598e2b9bbdd4508282004ac6d28057", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860aed83cddb33454ddb3a4552a81ad22", "guid": "bfdfe7dc352907fc980b868725387e98d04908157c4e74f760f6f9f73996a0fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98422c3776b6d754c443dd0a7ff311630d", "guid": "bfdfe7dc352907fc980b868725387e98d3e104b610083876cd0c77c07721b2b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98136bcb9550471ee4f78fb2243fc10084", "guid": "bfdfe7dc352907fc980b868725387e981cc02045e241aa34515a34214f0c3575"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b469b71820171644f1fa71ee3790d595", "guid": "bfdfe7dc352907fc980b868725387e988c7797f64c428bff6e61130828499ac0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7e5c1f21ef640f596dcd6aa2f89dba3", "guid": "bfdfe7dc352907fc980b868725387e98fe331eb05aad7edea256616dc4db4575"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898d2b42608b040550ac70b4e47af07a5", "guid": "bfdfe7dc352907fc980b868725387e987cb3cc72ea8006e1c39e2e3a51c9b58d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a73f161d4bf3aeba0a504361dad423", "guid": "bfdfe7dc352907fc980b868725387e980c376b9afeaa3c6d44088596584971fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e141557469be245d288ec9a9b4e24be2", "guid": "bfdfe7dc352907fc980b868725387e98e12ad978813a042aaa5c8b0a82394d08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880adaf4508ea3e67325397bf3de8bf1f", "guid": "bfdfe7dc352907fc980b868725387e98ac0d8d720764615f2aa61843657fb7ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98304e2a9e1ad5e60ba52cb64baad142fe", "guid": "bfdfe7dc352907fc980b868725387e98dac74319180da716009297ce5eaf5e8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984927632949e4ba84669342b57cd53627", "guid": "bfdfe7dc352907fc980b868725387e98783d583d3d29ce088b8ebb0b6c8686e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0a24cfe66bf3732cd08d4eee5c08fc5", "guid": "bfdfe7dc352907fc980b868725387e98adfc34e3fd011ba858d67b6129c8a67b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988f596564e2a201f54ee97bf754c7aa3b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9896ca253cfb966693573df1c0d8d841c3", "guid": "bfdfe7dc352907fc980b868725387e9811fcfb179fe415a05383b6a958299e13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819c03e0a981100ddc8806afddb30da6e", "guid": "bfdfe7dc352907fc980b868725387e98791a5bb127335393a9b4c3ceea59d8ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b4052f4d9db16af32bddd2ea037eb56", "guid": "bfdfe7dc352907fc980b868725387e988f0b578109512f01a4d2b9ccf4b933bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831f24ae52833b507d498870f0f08f41f", "guid": "bfdfe7dc352907fc980b868725387e986189d43a3f94ee0e95a30e68cfaa698d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acebf844a521479be77b7053301a8270", "guid": "bfdfe7dc352907fc980b868725387e9860a39919dc9bc920d02defd6b677d84c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3df93b7d9a4edaee75a60b04c5274c6", "guid": "bfdfe7dc352907fc980b868725387e98092854d7dfecdbc78ea36cd05c97c0f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98099eaf6e3fb8a36f695b771f7d1cbf9c", "guid": "bfdfe7dc352907fc980b868725387e986cf1652ebcf926488bb34c6cce6e1457"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d92c3a2d5beb2365e9f3d1854115fb4", "guid": "bfdfe7dc352907fc980b868725387e9857564e398f1b1fb0ed23b91427fd41a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b786499bcaba8f65ee03d3baa360741c", "guid": "bfdfe7dc352907fc980b868725387e988f9add960939d11cf9fda55379d538c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807a9558537b488ecebea34ee358ab1a9", "guid": "bfdfe7dc352907fc980b868725387e9855ef0a08d8068858cd43a5d6dda7144b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb99a24d9e263e567da9b7c9b8d838c2", "guid": "bfdfe7dc352907fc980b868725387e984256a5c266ec40fa710a68999dab4d9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f9094605aee4b752d27e94def75df05", "guid": "bfdfe7dc352907fc980b868725387e9842abc9e2dc5891169d58d3f0a8d38d86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98964ce726dfd0d76a234660a197298c23", "guid": "bfdfe7dc352907fc980b868725387e983e638cf3128203cc5179620aa4f80d8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f0cf5e067d1d9c84189e087b767d478", "guid": "bfdfe7dc352907fc980b868725387e98fc736087b13da4650fe9544c40413cd3"}], "guid": "bfdfe7dc352907fc980b868725387e98a560f12a3b6996bac6fce87ee69556f6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98941bbc643861197598f7bf9ab05aa863"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b194461b3d65889636e9bae473ca683", "guid": "bfdfe7dc352907fc980b868725387e983b8c2150169f915fe5f5466693d73dd5"}], "guid": "bfdfe7dc352907fc980b868725387e9805d57cef42ef2ccf2e0e4ffbb486d065", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cb547b0ec3e3ece9be89b5a062543d9f", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9803665c3c012bd9d966de4d74e8ec1302", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}