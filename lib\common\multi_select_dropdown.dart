import 'package:flutter/material.dart';

class MultiSelectItem {
  final String id;
  final String value;
  bool isSelected;

  MultiSelectItem({
    required this.id,
    required this.value,
    this.isSelected = false,
  });
}

class MultiSelectDropdown extends StatefulWidget {
  final List<String> items;
  final Function(List<String>) onSelectionChanged;
  final List<String> initialSelection;
  final String hint;
  final bool isRequired;

  const MultiSelectDropdown({
    super.key,
    required this.items,
    required this.onSelectionChanged,
    this.initialSelection = const [],
    this.hint = 'Select',
    this.isRequired = false,
  });

  @override
  State<MultiSelectDropdown> createState() => _MultiSelectDropdownState();
}

class _MultiSelectDropdownState extends State<MultiSelectDropdown> {
  late List<MultiSelectItem> _items;
  bool _isOpen = false;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  static const double _dropdownHeight = 200.0;

  @override
  void initState() {
    super.initState();
    _initializeItems();
  }

  void _initializeItems() {
    _items = widget.items.map((item) {
      try {
        final parts = item.split('(');
        return MultiSelectItem(
          id: parts[0].trim(),
          value: parts[1].replaceAll(')', '').trim(),
          isSelected: widget.initialSelection.contains(
            parts[1].replaceAll(')', '').trim(),
          ),
        );
      } catch (e) {
        return MultiSelectItem(
          id: item,
          value: item,
          isSelected: widget.initialSelection.contains(item),
        );
      }
    }).toList();
  }

  @override
  void didUpdateWidget(covariant MultiSelectDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialSelection != widget.initialSelection) {
      _initializeItems();
    }
  }

  void _toggleDropdown() {
    if (_isOpen) {
      _removeOverlay();
    } else {
      _showOverlay();
    }
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry?.remove();
      _overlayEntry = null;
      setState(() {
        _isOpen = false;
      });
    }
  }

  void _showOverlay() {
    if (_overlayEntry == null) {
      final overlay = Overlay.of(context);
      _overlayEntry = _createOverlayEntry();
      overlay.insert(_overlayEntry!);
      setState(() {
        _isOpen = true;
      });
    }
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    final screenHeight = MediaQuery.of(context).size.height;
    final spaceBelow = screenHeight - offset.dy - size.height;
    final spaceAbove = offset.dy;

    final showAbove =
        spaceBelow < (_dropdownHeight + 50) && spaceAbove > spaceBelow;

    return OverlayEntry(
      maintainState: true,
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _removeOverlay,
        child: Stack(
          children: [
            Positioned(
              width: size.width,
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                offset: Offset(
                  0.0,
                  showAbove ? -(_dropdownHeight + 50) - 5.0 : size.height + 5.0,
                ),
                child: Material(
                  elevation: 4.0,
                  borderRadius: BorderRadius.circular(12.0),
                  color: Colors.white,
                  child: StatefulBuilder(
                    builder: (context, setOverlayState) => Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          height: _dropdownHeight,
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: ListView.builder(
                            itemCount: _items.length,
                            itemBuilder: (context, index) {
                              return CheckboxListTile(
                                title: Text(
                                  _items[index].value,
                                  style: TextStyle(
                                    fontSize: 12.0,
                                    color: _items[index].isSelected
                                        ? Colors.green
                                        : Colors
                                            .black, // Change color for selected items
                                  ),
                                ),
                                value: _items[index].isSelected,
                                onChanged: (bool? value) {
                                  // Update the state for the current checkbox
                                  setOverlayState(() {
                                    _items[index].isSelected = value ?? false;
                                  });

                                  // Optionally, trigger a full state update
                                  setState(() {});
                                },
                                dense: true,
                                controlAffinity:
                                    ListTileControlAffinity.leading,
                              );
                            },
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            border: Border(
                              top: BorderSide(color: Colors.grey.shade300),
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16.0, vertical: 8.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextButton(
                                onPressed: _removeOverlay,
                                child: const Text('Cancel'),
                              ),
                              TextButton(
                                onPressed: _confirmSelection,
                                style: TextButton.styleFrom(
                                  foregroundColor:
                                      const Color.fromARGB(255, 10, 165, 85),
                                ),
                                child: const Text('OK'),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _confirmSelection() {
    if (!mounted) return;

    // Gather the selected values
    final selectedValues = _items
        .where((item) => item.isSelected)
        .map((item) => item.value)
        .toList();

    // Trigger the callback to inform parent widget
    widget.onSelectionChanged(selectedValues);

    // Close the dropdown
    _removeOverlay();
  }

  String get _displayText {
    final selectedItems = _items
        .where((item) => item.isSelected)
        .map((item) => item.value)
        .toList();
    return selectedItems.isEmpty ? widget.hint : selectedItems.join(', ');
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: InkWell(
        onTap: _toggleDropdown,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 14.0, horizontal: 16.0),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(
              color: _isOpen
                  ? const Color.fromARGB(255, 10, 165, 85)
                  : Colors.grey.shade300,
              width: _isOpen ? 2.0 : 1.0,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  _displayText,
                  style: TextStyle(
                    fontSize: 16.0,
                    fontWeight: FontWeight.w400,
                    color: _displayText == widget.hint
                        ? Colors.black54
                        : Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Icon(
                _isOpen ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                color: Colors.black,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _removeOverlay(); // Clean up the overlay if still active
    super.dispose();
  }
}
