import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/guesthome/listsearchedaddressmodel.dart';
import 'package:db_eats/data/models/meal_plan/timinglistmodel.dart'
    as meal_plan;
import 'package:db_eats/ui/search/global_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;

class Prediction {
  final String? description;
  final String? placeId;
  Prediction({this.description, this.placeId});
}

class LocationHeader extends StatefulWidget {
  const LocationHeader({Key? key}) : super(key: key);

  @override
  State<LocationHeader> createState() => _LocationHeaderState();
}

class _LocationHeaderState extends State<LocationHeader> {
  String? get _googleApiKey => Initializer.googleapikey;
  bool _showLocationMenu = false;
  bool _showTimeOptions = false;
  double? _currentLatitude;
  double? _currentLongitude;
  List<meal_plan.Timings>? _availableTimings;
  List<SearchedAddressData>? _savedAddresses;
  SearchedAddressData? _currentAddressData;
  bool _loadingAddresses = false;
  bool _isAddressUpdating = false;
  bool _isSearchMode = false;
  String? _currentSearchQuery;
  final TextEditingController _addressController = TextEditingController();
  List<Prediction> _searchResults = [];
  bool _showPredictions = false;
  bool _isSearching = false;
  int? _selectedTimeId;
  String _selectedDeliveryTime = 'Loading...';
  String _currentlySelectedTimeRange = '7:00 AM - 7:30 AM';
  Timer? _debounce;
  double screenWidth = 0;
  double screenHeight = 0;

  @override
  void initState() {
    super.initState();
    if (_googleApiKey == null || _googleApiKey!.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Google API key is missing')),
        );
      });
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAddresses();
      context.read<MealplanBloc>().add(ListTimingEvent());
      context.read<MealplanBloc>().add(GetAddedTimePreferences());
      _loadCurrentAddressAndTime();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final mq = MediaQuery.of(context);
    screenWidth = mq.size.width;
    screenHeight = mq.size.height;
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _addressController.dispose();
    super.dispose();
  }

  Future<void> _loadAddresses() async {
    if (_loadingAddresses) return;
    setState(() => _loadingAddresses = true);
    context.read<AccountBloc>().add(ListSearchAddressesEvent());
  }

  void _loadCurrentAddressAndTime() {
    final timeData = Initializer.addedTimePreferenceModel.data;
    if (timeData != null) {
      setState(() {
        _selectedDeliveryTime = timeData.isDeliverNow == true
            ? 'ASAP'
            : timeData.timePreference != null
                ? '${_formatTimeToAMPM(timeData.timePreference!.startTime)} - ${_formatTimeToAMPM(timeData.timePreference!.endTime)}'
                : 'ASAP';
        _currentlySelectedTimeRange = _selectedDeliveryTime;
        _selectedTimeId = timeData.timePreference?.id;
      });
    }
    _currentLatitude = Initializer().getLatitude;
    _currentLongitude = Initializer().getLongitude;
  }

  Future<void> _makeHomeDataRequest(double lat, double lng) async {
    final savedFilters = await Initializer.getAppliedFilters();
    final requestData = <String, dynamic>{
      if (savedFilters != null) ...savedFilters,
      'latitude': lat,
      'longitude': lng,
    };
    context.read<HomeBloc>().add(GetHomeDataEvent(data: requestData));
  }

  Future<Position?> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Location services are disabled')),
        );
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Location permission denied')),
          );
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Location permission permanently denied')),
        );
        return null;
      }

      return await Geolocator.getCurrentPosition();
    } catch (e) {
      log('Error getting location: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to get location: $e')),
      );
      return null;
    }
  }

  String _formatTimeToAMPM(String? time) {
    if (time == null || time.isEmpty) return '';
    try {
      final parts = time.split(':');
      int hour = int.parse(parts[0]);
      final minute = parts[1].split(' ')[0]; // Remove AM/PM if present
      final period = hour >= 12 ? 'PM' : 'AM';
      hour = hour % 12 == 0 ? 12 : hour % 12;
      return '$hour:$minute $period';
    } catch (e) {
      return time;
    }
  }

  // Add this method to better handle search state
  void _clearSearchPredictions() {
    setState(() {
      _searchResults = [];
      _showPredictions = false;
      _isSearching = false;
    });
  }

  Future<void> searchPlaces(String query) async {
    // Clear predictions immediately if query is empty
    if (query.trim().isEmpty) {
      _clearSearchPredictions();
      return;
    }

    if (_googleApiKey == null || _googleApiKey!.isEmpty) {
      _clearSearchPredictions();
      return;
    }

    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () async {
      // Double-check the query is still not empty after debounce
      if (query.trim().isEmpty) {
        _clearSearchPredictions();
        return;
      }

      setState(() => _isSearching = true);
      try {
        final url = Uri.parse(
          'https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${Uri.encodeComponent(query.trim())}&key=$_googleApiKey',
        );
        final response = await http.get(url);
        if (response.statusCode == 200) {
          final json = jsonDecode(response.body);
          if (json['status'] == 'OK') {
            setState(() {
              _searchResults = (json['predictions'] as List)
                  .map((p) => Prediction(
                        description: p['description'],
                        placeId: p['place_id'],
                      ))
                  .toList();
              _showPredictions = _searchResults.isNotEmpty;
            });
          } else {
            _clearSearchPredictions();
          }
        } else {
          throw Exception('Failed to fetch places: \\${response.statusCode}');
        }
      } catch (e) {
        log('Error searching places: \\${e}');
        _clearSearchPredictions();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to search places: \\${e}')),
        );
      } finally {
        setState(() => _isSearching = false);
      }
    });
  }

  Future<void> selectPlace(Prediction prediction) async {
    setState(() {
      _isSearching = true;
      _isAddressUpdating = true;
      _currentAddressData = null;
    });
    try {
      final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/place/details/json?place_id=${prediction.placeId}&fields=geometry&key=$_googleApiKey',
      );
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        if (json['status'] == 'OK') {
          final location = json['result']['geometry']['location'];
          final lat = location['lat'];
          final lng = location['lng'];

          context.read<AccountBloc>().add(
                AddSearchAddressEvent({
                  "latitude": lat,
                  "longitude": lng,
                  "address_text": prediction.description,
                }),
              );

          setState(() {
            _currentLatitude = lat;
            _currentLongitude = lng;
            _addressController.text = prediction.description ?? '';
            _showPredictions = false;
            _searchResults = [];
            _showLocationMenu = false;
          });

          Initializer().setCoordinates(lat, lng);
          await Initializer.saveAddress(prediction.description ?? '');
          _makeHomeDataRequest(lat, lng);
        } else {
          throw Exception('Place details API error: ${json['status']}');
        }
      } else {
        throw Exception(
            'Failed to fetch place details: ${response.statusCode}');
      }
    } catch (e) {
      log('Error selecting place: $e');
      setState(() {
        _isAddressUpdating = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to select place: $e')),
      );
    } finally {
      setState(() => _isSearching = false);
    }
  }

  void _performSearch(Map<String, dynamic> searchData, String searchQuery) {
    setState(() {
      _isSearchMode = true;
      _currentSearchQuery = searchQuery;
    });
    context.read<HomeBloc>().add(GetHomeDataEvent(data: searchData));
  }

  void _clearSearch() {
    setState(() {
      _isSearchMode = false;
      _currentSearchQuery = null;
      _showPredictions = false;
      _searchResults = [];
    });
    if (_currentLatitude != null && _currentLongitude != null) {
      _makeHomeDataRequest(_currentLatitude!, _currentLongitude!);
    }
  }

  Widget _buildScheduleTimeOptionWidget() {
    if (_availableTimings == null || _availableTimings!.isEmpty) {
      return Padding(
        padding: EdgeInsets.all(screenWidth * 0.04),
        child: Text(
          'No time slots available',
          style: TextStyle(
            fontSize: screenWidth * 0.035,
            fontWeight: FontWeight.w400,
            fontFamily: 'Inter',
            color: const Color(0xFF1F2122),
          ),
        ),
      );
    }

    if (_selectedTimeId == null &&
        _currentlySelectedTimeRange == '7:00 AM - 7:30 AM') {
      final firstTiming = _availableTimings!.first;
      _currentlySelectedTimeRange =
          '${_formatTimeToAMPM(firstTiming.startTime)} - ${_formatTimeToAMPM(firstTiming.endTime)}';
      _selectedTimeId = firstTiming.id;
    }

    return Column(
      children: [
        Column(
          children: _availableTimings!.map((timing) {
            final timeSlot =
                '${_formatTimeToAMPM(timing.startTime)} - ${_formatTimeToAMPM(timing.endTime)}';
            final isSelected = timeSlot == _currentlySelectedTimeRange;
            return InkWell(
              onTap: () {
                setState(() {
                  _currentlySelectedTimeRange = timeSlot;
                  _selectedTimeId = timing.id;
                });
              },
              child: Container(
                padding: EdgeInsets.symmetric(
                    horizontal: screenWidth * 0.06,
                    vertical: screenHeight * 0.015),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      timeSlot,
                      style: TextStyle(
                        fontSize: screenWidth * 0.035,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w400,
                        fontFamily: 'Inter',
                        color: const Color(0xFF1F2122),
                      ),
                    ),
                    if (isSelected)
                      Icon(Icons.check,
                          color: const Color(0xFF1F2122),
                          size: screenWidth * 0.05),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
        Padding(
          padding: EdgeInsets.all(screenWidth * 0.04),
          child: Column(
            children: [
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    if (_selectedTimeId != null) {
                      context.read<MealplanBloc>().add(
                            AddTimePreferences(
                                {"time_preference_id": _selectedTimeId}),
                          );
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('Please select a time slot')),
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black,
                    shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.06)),
                    padding: EdgeInsets.symmetric(vertical: screenWidth * 0.03),
                  ),
                  child: Text(
                    'Schedule',
                    style: TextStyle(
                      fontSize: screenWidth * 0.035,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
              ),
              SizedBox(height: screenHeight * 0.01),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: () {
                    context.read<MealplanBloc>().add(AddTimePreferences({}));
                    setState(() {
                      _selectedDeliveryTime = 'ASAP';
                      _showTimeOptions = false;
                      _showLocationMenu = false;
                      _selectedTimeId = null;
                    });
                  },
                  style: OutlinedButton.styleFrom(
                    backgroundColor: Colors.white,
                    side: const BorderSide(color: Color(0xFFE1E3E6)),
                    shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.06)),
                    padding: EdgeInsets.symmetric(vertical: screenWidth * 0.03),
                  ),
                  child: Text(
                    'Deliver Now',
                    style: TextStyle(
                      fontSize: screenWidth * 0.035,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF1F2122),
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAddressListItem(SearchedAddressData address) {
    return InkWell(
      onTap: () {
        setState(() {
          _isAddressUpdating = true;
          _currentAddressData = null;
        });
        context.read<AccountBloc>().add(EditSearchAddressEvent({
              "id": address.id ?? 0,
              "latitude": address.location?.coordinates?[1] ?? 0.0,
              "longitude": address.location?.coordinates?[0] ?? 0.0,
              "address_text": address.addressText ?? "",
            }));
      },
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: screenHeight * 0.01),
        child: Row(
          children: [
            Icon(Icons.location_on_outlined, size: screenWidth * 0.06),
            SizedBox(width: screenWidth * 0.04),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    address.addressText ?? 'No address text',
                    style: TextStyle(
                      fontSize: screenWidth * 0.035,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: const Color(0xFF1F2122),
                    ),
                  ),
                  if (_savedAddresses != null &&
                      _savedAddresses!.isNotEmpty &&
                      identical(address, _savedAddresses!.first))
                    Text(
                      'Current address',
                      style: TextStyle(
                        fontSize: screenWidth * 0.03,
                        color: Colors.green,
                      ),
                    ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: screenWidth * 0.03,
              color: const Color(0xFF1F2122),
            ),
          ],
        ),
      ),
    );
  }

  // Add this method to handle menu state changes
  void _toggleLocationMenu() {
    setState(() {
      _showLocationMenu = !_showLocationMenu;
      _showTimeOptions = false;
      // Clear search predictions when closing menu
      if (!_showLocationMenu) {
        _clearSearchPredictions();
        _addressController.clear();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AccountBloc>.value(value: context.read<AccountBloc>()),
        BlocProvider<MealplanBloc>.value(value: context.read<MealplanBloc>()),
        BlocProvider<HomeBloc>.value(value: context.read<HomeBloc>()),
      ],
      child: BlocListener<AccountBloc, AccountState>(
        listener: (context, state) {
          if (state is ListSearchAddressesSuccess) {
            setState(() {
              _loadingAddresses = false;
              _isAddressUpdating = false;
              final model = Initializer.listSearchedAddressesModel;
              _savedAddresses = model?.data ?? [];
              _currentAddressData = _savedAddresses?.isNotEmpty == true
                  ? _savedAddresses!.first
                  : null;
              if (_currentAddressData?.location?.coordinates != null) {
                _currentLatitude =
                    _currentAddressData!.location!.coordinates![1];
                _currentLongitude =
                    _currentAddressData!.location!.coordinates![0];
                Initializer()
                    .setCoordinates(_currentLatitude!, _currentLongitude!);
                _makeHomeDataRequest(_currentLatitude!, _currentLongitude!);
              }
            });
          } else if (state is EditSearchAddressSuccess) {
            setState(() {
              _isAddressUpdating = false;
              _showLocationMenu = false;
            });
            if (_currentLatitude != null && _currentLongitude != null) {
              Initializer()
                  .setCoordinates(_currentLatitude!, _currentLongitude!);
              _makeHomeDataRequest(_currentLatitude!, _currentLongitude!);
            }
            _loadAddresses();
          } else if (state is AddSearchAddressSuccess) {
            setState(() {
              _isAddressUpdating = false;
            });
            context.read<AccountBloc>().add(ListSearchAddressesEvent());
          }
        },
        child: BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is ListTimingSuccess) {
              setState(() {
                _availableTimings = state.data.data?.timings;
              });
            } else if (state is GettingAddedTimePreferencesSuccess) {
              if (state.data != null) {
                setState(() {
                  _selectedDeliveryTime = state.data.isDeliverNow == true
                      ? 'ASAP'
                      : state.data.timePreference != null
                          ? '${_formatTimeToAMPM(state.data.timePreference!.startTime)} - ${_formatTimeToAMPM(state.data.timePreference!.endTime)}'
                          : 'ASAP';
                  _currentlySelectedTimeRange = _selectedDeliveryTime;
                  _selectedTimeId = state.data.timePreference?.id;
                });
              }
            } else if (state is AddTimePreferencesSuccess) {
              context.read<MealplanBloc>().add(GetAddedTimePreferences());
              setState(() {
                _showTimeOptions = false;
                _showLocationMenu = false;
              });
            }
          },
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: screenWidth * 0.04,
                    vertical: screenHeight * 0.015),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: _toggleLocationMenu,
                      child: Icon(
                        Icons.menu,
                        size: screenWidth * 0.06,
                        color: const Color(0xFF1F2122),
                        semanticLabel: 'Open location menu',
                      ),
                    ),
                    SizedBox(width: screenWidth * 0.03),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          GestureDetector(
                            onTap: _toggleLocationMenu,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      Icons.location_on_outlined,
                                      size: screenWidth * 0.035,
                                      color: const Color(0xFF1F2122),
                                    ),
                                    SizedBox(width: screenWidth * 0.01),
                                    Expanded(
                                      child: _isAddressUpdating
                                          ? SizedBox(
                                              width: screenWidth * 0.6,
                                              child: Row(
                                                children: [
                                                  SizedBox(
                                                    width: screenWidth * 0.02,
                                                    height: screenWidth * 0.02,
                                                    child:
                                                        CircularProgressIndicator(
                                                      strokeWidth: 1,
                                                      valueColor:
                                                          AlwaysStoppedAnimation<
                                                                  Color>(
                                                              const Color(
                                                                  0xFF1F2122)),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            )
                                          : Text(
                                              _currentAddressData
                                                      ?.addressText ??
                                                  'Loading address...',
                                              style: TextStyle(
                                                fontSize: screenWidth * 0.03,
                                                fontWeight: FontWeight.w500,
                                                fontFamily: 'Inter',
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: screenHeight * 0.005),
                                Row(
                                  children: [
                                    Icon(
                                      Icons.access_time,
                                      size: screenWidth * 0.03,
                                      color: const Color(0xFF1F2122),
                                    ),
                                    SizedBox(width: screenWidth * 0.015),
                                    InkWell(
                                      onTap: () {
                                        setState(() {
                                          _showLocationMenu = true;
                                          _showTimeOptions = true;
                                        });
                                      },
                                      child: Text(
                                        _selectedDeliveryTime,
                                        style: TextStyle(
                                          fontSize: screenWidth * 0.03,
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Inter',
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: () async {
                        final searchResult = await Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const GlobalSearchScreen()),
                        );
                        if (searchResult != null && mounted) {
                          final searchQuery =
                              searchResult['searchQuery'] as String?;
                          final searchData = searchResult['searchData']
                              as Map<String, dynamic>?;
                          if (searchQuery != null && searchData != null) {
                            _performSearch(searchData, searchQuery);
                          } else if (_isSearchMode) {
                            _clearSearch();
                          }
                        } else if (_isSearchMode) {
                          _clearSearch();
                        } else if (_currentLatitude != null &&
                            _currentLongitude != null) {
                          _makeHomeDataRequest(
                              _currentLatitude!, _currentLongitude!);
                        }
                      },
                      child: Icon(
                        Icons.search,
                        size: screenWidth * 0.06,
                        color: const Color(0xFF1F2122),
                        semanticLabel: 'Search',
                      ),
                    ),
                  ],
                ),
              ),
              if (_showTimeOptions)
                Container(
                  color: const Color(0xFFF6F3EC),
                  constraints: BoxConstraints(maxHeight: screenHeight * 0.6),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            IconButton(
                              icon: Icon(Icons.arrow_back,
                                  color: const Color(0xFF1F2122),
                                  size: screenWidth * 0.05),
                              onPressed: () {
                                setState(() {
                                  _showTimeOptions = false;
                                });
                              },
                            ),
                            Expanded(
                              child: Center(
                                child: Text(
                                  'Schedule Delivery',
                                  style: TextStyle(
                                    fontSize: screenWidth * 0.045,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF1F2122),
                                    fontFamily: 'Inter',
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: screenWidth * 0.15),
                          ],
                        ),
                        _buildScheduleTimeOptionWidget(),
                      ],
                    ),
                  ),
                )
              else if (_showLocationMenu)
                Container(
                  color: Colors.white,
                  constraints: BoxConstraints(maxHeight: screenHeight * 0.7),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: EdgeInsets.all(screenWidth * 0.04),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Enter Your Street And House Number',
                                style: TextStyle(
                                  fontSize: screenWidth * 0.035,
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF1F2122),
                                  fontFamily: 'Inter',
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.015),
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: const Color(0xFFE1E3E6), width: 1),
                                  borderRadius:
                                      BorderRadius.circular(screenWidth * 0.09),
                                ),
                                padding: EdgeInsets.symmetric(
                                    horizontal: screenWidth * 0.045,
                                    vertical: 0),
                                height: screenHeight * 0.055,
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: TextField(
                                        controller: _addressController,
                                        style: TextStyle(
                                          fontSize: screenWidth * 0.035,
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Inter',
                                          color: const Color(0xFF1F2122),
                                        ),
                                        onChanged: (value) {
                                          searchPlaces(value);
                                          if (value.trim().isEmpty) {
                                            setState(() {
                                              _showPredictions = false;
                                              _searchResults = [];
                                            });
                                          }
                                        },
                                        decoration: InputDecoration(
                                          hintText: 'Street, Postal code',
                                          hintStyle: TextStyle(
                                            color: const Color(0xFF66696D),
                                            fontSize: screenWidth * 0.035,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Inter',
                                          ),
                                          border: InputBorder.none,
                                          contentPadding: EdgeInsets.zero,
                                          isDense: true,
                                        ),
                                      ),
                                    ),
                                    TextButton.icon(
                                      onPressed: () async {
                                        setState(() {
                                          _isAddressUpdating = true;
                                          _currentAddressData = null;
                                        });
                                        final position =
                                            await _getCurrentLocation();
                                        if (position != null && mounted) {
                                          final placemarks =
                                              await placemarkFromCoordinates(
                                                  position.latitude,
                                                  position.longitude);
                                          if (placemarks.isNotEmpty) {
                                            final place = placemarks[0];
                                            final address =
                                                '${place.street}, ${place.subLocality}, ${place.locality}, ${place.postalCode}';
                                            context.read<AccountBloc>().add(
                                                  AddSearchAddressEvent({
                                                    "latitude":
                                                        position.latitude,
                                                    "longitude":
                                                        position.longitude,
                                                    "address_text": address,
                                                  }),
                                                );
                                            setState(() {
                                              _currentLatitude =
                                                  position.latitude;
                                              _currentLongitude =
                                                  position.longitude;
                                              _showLocationMenu = false;
                                            });
                                            Initializer().setCoordinates(
                                                position.latitude,
                                                position.longitude);
                                            await Initializer.saveAddress(
                                                address);
                                            _makeHomeDataRequest(
                                                position.latitude,
                                                position.longitude);
                                          }
                                        } else {
                                          setState(() {
                                            _isAddressUpdating = false;
                                          });
                                        }
                                      },
                                      icon: Icon(Icons.my_location,
                                          size: screenWidth * 0.05,
                                          color: const Color(0xFF1F2122)),
                                      label: Text(
                                        'Locate me',
                                        style: TextStyle(
                                          fontSize: screenWidth * 0.030,
                                          fontWeight: FontWeight.w600,
                                          fontFamily: 'Inter',
                                          color: const Color(0xFF1F2122),
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                      style: TextButton.styleFrom(
                                        padding: EdgeInsets.zero,
                                        minimumSize: Size.zero,
                                        tapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (_isSearching)
                                Padding(
                                  padding:
                                      EdgeInsets.only(top: screenHeight * 0.01),
                                  child: const Center(
                                      child: CircularProgressIndicator()),
                                )
                              else if (_showPredictions &&
                                  _searchResults.isNotEmpty &&
                                  _addressController.text.trim().isNotEmpty)
                                Container(
                                  height: screenHeight * 0.15,
                                  margin:
                                      EdgeInsets.only(top: screenHeight * 0.01),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(
                                        screenWidth * 0.03),
                                    border: Border.all(
                                        color: const Color(0xFFE1E3E6),
                                        width: 1),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.15),
                                        blurRadius: 12,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: ListView.separated(
                                      padding: EdgeInsets.zero,
                                      shrinkWrap: true,
                                      itemCount: _searchResults.length,
                                      separatorBuilder: (_, __) =>
                                          const Divider(
                                        height: 1,
                                        color: Color(0xFFE1E3E6),
                                      ),
                                      itemBuilder: (_, index) {
                                        final prediction =
                                            _searchResults[index];
                                        return ListTile(
                                          dense: true,
                                          contentPadding: EdgeInsets.symmetric(
                                            horizontal: screenWidth * 0.04,
                                            vertical: screenHeight * 0.005,
                                          ),
                                          title: Text(
                                            prediction.description ?? '',
                                            style: TextStyle(
                                              fontSize: screenWidth * 0.035,
                                              fontFamily: 'Inter',
                                            ),
                                          ),
                                          onTap: () => selectPlace(prediction),
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              SizedBox(height: screenHeight * 0.03),
                              Text(
                                'Saved Addresses',
                                style: TextStyle(
                                  fontSize: screenWidth * 0.04,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: 'Inter',
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.02),
                              if (_loadingAddresses)
                                const Center(child: CircularProgressIndicator())
                              else if (_savedAddresses == null ||
                                  _savedAddresses!.isEmpty)
                                const Center(child: Text('No saved addresses'))
                              else
                                ...(_savedAddresses!
                                    .skip(1)
                                    .take(2)
                                    .map((address) =>
                                        _buildAddressListItem(address))
                                    .toList()),
                              SizedBox(height: screenHeight * 0.02),
                              InkWell(
                                onTap: () {
                                  setState(() {
                                    _showTimeOptions = true;
                                  });
                                },
                                child: Row(
                                  children: [
                                    Icon(Icons.schedule,
                                        size: screenWidth * 0.06),
                                    SizedBox(width: screenWidth * 0.04),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Time preference',
                                            style: TextStyle(
                                              fontSize: screenWidth * 0.035,
                                              fontWeight: FontWeight.w500,
                                              fontFamily: 'Inter',
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                          Text(
                                            _selectedDeliveryTime,
                                            style: TextStyle(
                                              fontSize: screenWidth * 0.03,
                                              color: Colors.grey,
                                              fontFamily: 'Inter',
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Icon(
                                      Icons.arrow_forward_ios,
                                      size: screenWidth * 0.03,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              const Divider(
                  height: 2, color: Color.fromARGB(255, 219, 212, 212)),
            ],
          ),
        ),
      ),
    );
  }
}
