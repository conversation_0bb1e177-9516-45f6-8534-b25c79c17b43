// import 'package:db_eats/utils/colors.dart';
// import 'package:db_eats/utils/dotteddivider.dart';
// import 'package:flutter/material.dart';

// class Orderconfiemationmain extends StatefulWidget {
//   const Orderconfiemationmain({super.key});

//   @override
//   State<Orderconfiemationmain> createState() => _OrderconfiemationmainState();
// }

// class _OrderconfiemationmainState extends State<Orderconfiemationmain> {
//   @override
//   Widget build(BuildContext context) {
//     final size = MediaQuery.of(context).size;
//     final baseTextSize = getResponsiveSize(context);
//     final itemSpacing = size.height * 0.02;
//     final isLandscape = size.width > size.height;

//     // Add responsive container sizing
//     final cookingImageSize =
//         isLandscape ? size.height * 0.30 : size.width * 0.40;
//     final driverContainerWidth =
//         isLandscape ? size.width * 0.2 : size.width * 0.45;
//     final progressBarWidth =
//         isLandscape ? size.width * 0.08 : size.width * 0.12;
//           Map<String, dynamic>? _selectedOrder;
//     final List<Map<String, dynamic>> orders = [
//       {
//         'chefName': 'Chef Vishwanathan C.',
//         'avatar': 'assets/images/chef_10.png',
//         'itemCount': 5,
//         'hours': 'Open 9AM-8PM',
//         'days': 'M, W, F, S, S',
//         'status': 'Preparing',
//       },
//       {
//         'chefName': 'Chef Hiroshi S.',
//         'avatar': 'assets/images/chef_4.png',
//         'itemCount': 5,
//         'hours': 'Open 9AM-8PM',
//         'days': 'M, W, F, S, S',
//         'status': 'Picking Up',
//       },
//       {
//         'chefName': 'Chef Jennifer W.',
//         'avatar': 'assets/images/chef_5.png',
//         'itemCount': 5,
//         'hours': 'Open 9AM-8PM',
//         'days': 'M, W, F, S, S',
//         'status': 'Delivering',
//       },
//     ];
//     return Scaffold(
//       backgroundColor: CustomColor.primaryColor,
//       body: Padding(
//         padding: EdgeInsets.symmetric(
//           horizontal: size.width * 0.05,
//           vertical: size.height * 0.02,
//         ),
//         child: 
//         Column(

//         )),
//     );
//   }

//   double getResponsiveSize(BuildContext context,
//       {double small = 12,
//       double medium = 16,
//       double large = 20,
//       double xlarge = 24}) {
//     final width = MediaQuery.of(context).size.width;
//     if (width < 360) return small;
//     if (width < 600) return medium;
//     if (width < 900) return large;
//     return xlarge;
//   }

//   Widget _buildExpandedOrderView(Map<String, dynamic> order) {
//     final size = MediaQuery.of(context).size;
//     final baseTextSize = getResponsiveSize(context);
//     final itemSpacing = size.height * 0.02;
//     final isLandscape = size.width > size.height;

//     // Add responsive container sizing
//     final cookingImageSize =
//         isLandscape ? size.height * 0.30 : size.width * 0.40;
//     final driverContainerWidth =
//         isLandscape ? size.width * 0.2 : size.width * 0.45;
//     final progressBarWidth =
//         isLandscape ? size.width * 0.08 : size.width * 0.12;

//     return Container(
//       // Add this container
//       color: Color(0xFFF6F3EC), // Match scaffold background color
//       child: Column(
//         children: [
//           Padding(
//             padding: EdgeInsets.symmetric(
//               horizontal: size.width * 0.04,
//               vertical: size.height * 0.02,
//             ),
//             child: GestureDetector(
//               onTap: () => setState(() => _selectedOrder = null),
//               child: Row(
//                 children: [
//                   Icon(
//                     Icons.chevron_left,
//                     size: isLandscape ? size.height * 0.04 : baseTextSize * 1.5,
//                     color: Color(0xFF1F2122),
//                   ),
//                   const SizedBox(width: 8),
//                   Text(
//                     'Back',
//                     style: TextStyle(
//                       fontSize:
//                           isLandscape ? size.height * 0.03 : baseTextSize * 1.2,
//                       fontWeight: FontWeight.w600,
//                       fontFamily: 'Inter',
//                       decoration: TextDecoration.underline,
//                       decorationThickness: 1.5,
//                       color: Color(0xFF1F2122),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//           // SizedBox(height: itemSpacing),
//           Expanded(
//             child: SingleChildScrollView(
//               child: Column(
//                 children: [
//                   Padding(
//                     padding: EdgeInsets.all(size.width * 0.04),
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Container(
//                           padding: EdgeInsets.all(size.width * 0.05),
//                           decoration: BoxDecoration(
//                             color: Colors.white,
//                             borderRadius: BorderRadius.circular(12),
//                             boxShadow: [
//                               BoxShadow(
//                                 color: Colors.black.withOpacity(0.05),
//                                 blurRadius: 4,
//                                 offset: Offset(0, 2),
//                               ),
//                             ],
//                           ),
//                           child: Column(
//                             crossAxisAlignment: CrossAxisAlignment.center,
//                             children: [
//                               Image.asset(
//                                 'assets/images/cooking.png',
//                                 height: cookingImageSize,
//                               ),
//                               SizedBox(height: itemSpacing),

//                               Text(
//                                 '${order['chefName']} is\npreparing your order',
//                                 style: TextStyle(
//                                   fontSize: isLandscape
//                                       ? size.height * 0.04
//                                       : baseTextSize * 1.5,
//                                   fontWeight: FontWeight.w600,
//                                   color: Colors.black,
//                                   fontFamily: 'Inter',
//                                 ),
//                                 textAlign: TextAlign.center,
//                               ),
//                               SizedBox(height: itemSpacing * 0.5),
//                               Text(
//                                 'Estimated arrival 8:30AM',
//                                 style: TextStyle(
//                                   fontSize: baseTextSize,
//                                   fontWeight: FontWeight.w400,
//                                   fontFamily: 'Inter',
//                                   color: Color(0xFF1F2122),
//                                 ),
//                               ),

//                               // Progress indicator
//                               SizedBox(height: itemSpacing),
//                               Row(
//                                 mainAxisAlignment: MainAxisAlignment.center,
//                                 children: List.generate(5, (index) {
//                                   return Container(
//                                     width: progressBarWidth,
//                                     height: size.height * 0.01,
//                                     margin: EdgeInsets.symmetric(
//                                         horizontal: size.width * 0.019),
//                                     decoration: BoxDecoration(
//                                       color: index < 3
//                                           ? Colors.black
//                                           : Color(0xFFD9D9D9),
//                                       borderRadius: BorderRadius.circular(
//                                           size.width * 0.02),
//                                     ),
//                                   );
//                                 }),
//                               ),
//                               SizedBox(height: itemSpacing),

//                               // Delivery address
//                               Text(
//                                 'Your ordered will be delivered to 3800 N Central Expy at 8:30AM',
//                                 style: TextStyle(
//                                   fontSize: baseTextSize * 0.9,
//                                   fontWeight: FontWeight.w400,
//                                   fontFamily: 'Inter',
//                                   color: Color(0xFF1F2122),
//                                 ),
//                                 textAlign: TextAlign.center,
//                               ),

//                               SizedBox(height: itemSpacing),

//                               // Driver information
//                               Container(
//                                 width: driverContainerWidth,
//                                 padding: EdgeInsets.all(size.width * 0.035),
//                                 decoration: BoxDecoration(
//                                   color: Color(0xFFF1F2F3),
//                                   borderRadius: BorderRadius.circular(10),
//                                 ),
//                                 child: Column(
//                                   children: [
//                                     Text(
//                                       'Your Driver',
//                                       style: TextStyle(
//                                         fontSize: baseTextSize * 0.9,
//                                         fontWeight: FontWeight.w400,
//                                         fontFamily: 'Inter',
//                                         color: Color(0xFF414346),
//                                       ),
//                                     ),
//                                     SizedBox(height: itemSpacing * 0.5),
//                                     Row(
//                                       mainAxisAlignment:
//                                           MainAxisAlignment.center,
//                                       children: [
//                                         CircleAvatar(
//                                           radius: isLandscape
//                                               ? size.height * 0.025
//                                               : baseTextSize,
//                                           backgroundImage: AssetImage(
//                                               'assets/images/driver.png'),
//                                         ),
//                                         SizedBox(width: size.width * 0.02),
//                                         Text(
//                                           'John Hancock',
//                                           style: TextStyle(
//                                             fontSize: baseTextSize,
//                                             fontWeight: FontWeight.w600,
//                                             fontFamily: 'Inter',
//                                             color: Color(0xFF1F2122),
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                     SizedBox(height: itemSpacing * 0.4),
//                                     Text(
//                                       '(910) 799-0420',
//                                       style: TextStyle(
//                                         fontSize: baseTextSize,
//                                         fontWeight: FontWeight.w400,
//                                         fontFamily: 'Inter',
//                                         color: Color(0xFF414346),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               ),

//                               // Support text
//                               SizedBox(height: 14),
//                               Row(
//                                 mainAxisAlignment: MainAxisAlignment.center,
//                                 children: [
//                                   Text(
//                                     'Need help? ',
//                                     style: TextStyle(
//                                       fontSize: 12,
//                                       fontFamily: 'Inter',
//                                       color: Color(0xFF1F2122),
//                                     ),
//                                   ),
//                                   Text(
//                                     'Contact support.',
//                                     style: TextStyle(
//                                       fontSize: 12,
//                                       fontFamily: 'Inter',
//                                       fontWeight: FontWeight.w700,
//                                       color: Color(0xFF1F2122),
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ],
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),

//                   // Order Summary card
//                   Padding(
//                     padding: EdgeInsets.all(size.width * 0.04),
//                     child: Container(
//                       width: double.infinity,
//                       padding: EdgeInsets.all(size.width * 0.05),
//                       decoration: BoxDecoration(
//                         color: Colors.white,
//                         borderRadius: BorderRadius.circular(12),
//                         boxShadow: [
//                           BoxShadow(
//                             color: Colors.black.withOpacity(0.05),
//                             blurRadius: 4,
//                             offset: Offset(0, 2),
//                           ),
//                         ],
//                       ),
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             'Order Summary',
//                             style: TextStyle(
//                               fontSize: isLandscape
//                                   ? size.height * 0.04
//                                   : baseTextSize * 1.5,
//                               fontWeight: FontWeight.w600,
//                               fontFamily: 'Inter',
//                               color: Color(0xFF000000),
//                             ),
//                           ),
//                           DottedDivider(),
//                           SizedBox(height: itemSpacing * 0.3),

//                           // Ordered From section
//                           Text(
//                             'Ordered From',
//                             style: TextStyle(
//                               fontSize: baseTextSize * 1.1,
//                               // fontWeight: FontWeight.w500,
//                               fontFamily: 'Inter-medium',
//                               color: Color(0xFF1F2122),
//                             ),
//                           ),
//                           SizedBox(height: itemSpacing * 0.5),
//                           Row(
//                             children: [
//                               CircleAvatar(
//                                 radius: isLandscape
//                                     ? size.height * 0.015
//                                     : baseTextSize * 0.8,
//                                 backgroundImage: AssetImage(order['avatar']),
//                               ),
//                               SizedBox(width: size.width * 0.02),
//                               Text(
//                                 order['chefName'],
//                                 style: TextStyle(
//                                   fontSize: baseTextSize,
//                                   fontWeight: FontWeight.w600,
//                                   color: Color(0xFF1F2122),
//                                 ),
//                               ),
//                             ],
//                           ),
//                           SizedBox(height: itemSpacing * 0.5),
//                           DottedDivider(),
//                           SizedBox(height: itemSpacing * 0.3),

//                           // Order Details section
//                           Text(
//                             'Order Details (5 Items)',
//                             style: TextStyle(
//                               fontSize: baseTextSize * 1.1,
//                               // fontWeight: FontWeight.w500,
//                               fontFamily: 'Inter-medium',
//                               color: Color(0xFF1F2122),
//                             ),
//                           ),
//                           SizedBox(height: itemSpacing * 0.8),
//                           _buildOrderItem('2x', 'Dish Title', '\$15.00'),
//                           SizedBox(height: itemSpacing * 0.5),
//                           _buildOrderItem('2x', 'Dish Title', '\$15.00'),
//                           SizedBox(height: itemSpacing * 0.5),
//                           DottedDivider(),
//                           SizedBox(height: itemSpacing * 0.3),

//                           // Promotions section
//                           Text(
//                             'Promotions',
//                             style: TextStyle(
//                               fontSize: baseTextSize * 1.1,
//                               // fontWeight: FontWeight.w500,
//                               fontFamily: 'Inter-medium',
//                               color: Color(0xFF1F2122),
//                             ),
//                           ),
//                           SizedBox(height: itemSpacing * 0.5),
//                           Text(
//                             'DBEATS50',
//                             style: TextStyle(
//                               fontSize: baseTextSize,
//                               fontWeight: FontWeight.w400,
//                               color: Color(0xFF1F2122),
//                             ),
//                           ),
//                           DottedDivider(),
//                           SizedBox(height: itemSpacing * 0.3),

//                           // Order Total section
//                           Text(
//                             'Order Total',
//                             style: TextStyle(
//                               fontSize: baseTextSize * 1.1,
//                               // fontWeight: FontWeight.w500,
//                               fontFamily: 'Inter-medium',
//                               color: Color(0xFF1F2122),
//                             ),
//                           ),
//                           SizedBox(height: itemSpacing * 0.8),
//                           Column(
//                             children: [
//                               _buildTotalItem('Subtotal', '\$70.00'),
//                               SizedBox(height: itemSpacing * 0.4),
//                               _buildTotalItem('Delivery fee', '\$5.00'),
//                               SizedBox(height: itemSpacing * 0.4),
//                               _buildTotalItem('Discounts', '-\$2.00'),
//                               SizedBox(height: itemSpacing * 0.4),
//                               _buildTotalItem('DB Wallet Credits', '-\$3.50'),
//                               SizedBox(height: itemSpacing * 0.4),
//                               _buildTotalItem('Taxes & Fees', '\$5.00'),
//                               DottedDivider(),
//                               _buildTotalItem('Total', '\$70.00',
//                                   isTotal: true),
//                             ],
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),

//                   // Delivery Details card
//                   Padding(
//                     padding: EdgeInsets.all(size.width * 0.04),
//                     child: Container(
//                       width: double.infinity,
//                       padding: EdgeInsets.all(size.width * 0.05),
//                       decoration: BoxDecoration(
//                         color: Colors.white,
//                         borderRadius: BorderRadius.circular(12),
//                         boxShadow: [
//                           BoxShadow(
//                             color: Colors.black.withOpacity(0.05),
//                             blurRadius: 4,
//                             offset: Offset(0, 2),
//                           ),
//                         ],
//                       ),
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             'Delivery Details',
//                             style: TextStyle(
//                               fontSize: isLandscape
//                                   ? size.height * 0.04
//                                   : baseTextSize * 1.5,
//                               fontWeight: FontWeight.w600,
//                               fontFamily: 'Inter',
//                               color: Color(0xFF000000),
//                             ),
//                           ),
//                           SizedBox(height: itemSpacing),

//                           // Address section
//                           Text(
//                             'Address',
//                             style: TextStyle(
//                               fontSize: baseTextSize,
//                               // fontWeight: FontWeight.w500,
//                               fontFamily: 'Inter-medium',
//                               color: Color(0xFF1F2122),
//                             ),
//                           ),
//                           SizedBox(height: itemSpacing * 0.5),
//                           Row(
//                             children: [
//                               Icon(Icons.location_on_outlined,
//                                   size: baseTextSize * 1.2,
//                                   color: Color(0xFF414346)),
//                               SizedBox(width: size.width * 0.01),
//                               Text(
//                                 '3800 N Central Expy',
//                                 style: TextStyle(
//                                   fontSize: baseTextSize,
//                                   fontWeight: FontWeight.w500,
//                                   fontFamily: 'Inter',
//                                   color: Color(0xFF1F2122),
//                                 ),
//                               ),
//                             ],
//                           ),
//                           SizedBox(height: itemSpacing),

//                           // Delivery Time section
//                           Text(
//                             'Delivery',
//                             style: TextStyle(
//                               fontSize: baseTextSize,
//                               // fontWeight: FontWeight.w500,
//                               fontFamily: 'Inter-medium',
//                               color: Color(0xFF1F2122),
//                             ),
//                           ),
//                           SizedBox(height: itemSpacing * 0.5),
//                           Row(
//                             children: [
//                               Icon(Icons.access_time,
//                                   size: baseTextSize * 1.0,
//                                   color: Color(0xFF414346)),
//                               SizedBox(width: size.width * 0.01),
//                               Text(
//                                 'Today, 8:30AM',
//                                 style: TextStyle(
//                                   fontSize: baseTextSize,
//                                   color: Color(0xFF1F2122),
//                                 ),
//                               ),
//                             ],
//                           ),
//                           SizedBox(height: itemSpacing),
//                           Divider(
//                             height: 0,
//                             thickness: 1,
//                             color: Color(0xFFE1E3E6),
//                           ),
//                           SizedBox(height: itemSpacing),

//                           // Drop-Off Options section
//                           Text(
//                             'Drop-Off Options',
//                             style: TextStyle(
//                               fontSize: baseTextSize * 1.1,
//                               fontFamily: 'Inter-medium',
//                               color: Color(0xFF1F2122),
//                             ),
//                           ),
//                           SizedBox(height: itemSpacing * 0.5),
//                           Text(
//                             'Meet at my door',
//                             style: TextStyle(
//                               fontSize: baseTextSize,
//                               fontFamily: 'Inter',
//                               color: Color(0xFF1F2122),
//                             ),
//                           ),
//                           SizedBox(height: itemSpacing),

//                           // Drop-Off Instructions
//                           Text(
//                             'Drop-Off Instructions',
//                             style: TextStyle(
//                               fontSize: baseTextSize * 1.1,
//                               fontFamily: 'Inter-medium',
//                               color: Color(0xFF1F2122),
//                             ),
//                           ),
//                           SizedBox(height: itemSpacing * 0.5),
//                           Text(
//                             'Doorbell is broken, please knock',
//                             style: TextStyle(
//                               fontSize: baseTextSize,
//                               fontFamily: 'Inter',
//                               color: Color(0xFF1F2122),
//                             ),
//                           ),
//                           SizedBox(height: itemSpacing),
//                           Divider(
//                             height: 0,
//                             thickness: 1,
//                             color: Color(0xFFE1E3E6),
//                           ),
//                           SizedBox(height: itemSpacing),
//                           // Delivery Time row
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               Text(
//                                 'Delivery Time',
//                                 style: TextStyle(
//                                   fontSize: baseTextSize * 1.1,
//                                   fontFamily: 'Inter-medium',
//                                   // fontWeight: FontWeight.w500,
//                                   color: Color(0xFF1F2122),
//                                 ),
//                               ),
//                               Text(
//                                 '45-50 Minutes',
//                                 style: TextStyle(
//                                   fontSize: baseTextSize,
//                                   fontWeight: FontWeight.w500,
//                                   color: Color(0xFF1F2122),
//                                 ),
//                               ),
//                             ],
//                           ),
//                           SizedBox(height: itemSpacing),

//                           // Priority Delivery row
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               Row(
//                                 children: [
//                                   Image.asset(
//                                     'assets/icons/star_2.png',
//                                     width: baseTextSize * 1.0,
//                                     height: baseTextSize * 1.0,
//                                     color: Color(0xFF414346),
//                                   ),
//                                   SizedBox(width: size.width * 0.02),
//                                   Text(
//                                     'Priority',
//                                     style: TextStyle(
//                                       fontSize: baseTextSize,
//                                       fontWeight: FontWeight.w500,
//                                       color: Color(0xFF1F2122),
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                               Text(
//                                 '+\$5.00',
//                                 style: TextStyle(
//                                   fontSize: baseTextSize,
//                                   fontWeight: FontWeight.w500,
//                                   color: Color(0xFF1F2122),
//                                 ),
//                               ),
//                             ],
//                           ),
//                           SizedBox(height: itemSpacing * 0.5),
//                           Text(
//                             'XX -XX min delivery time',
//                             style: TextStyle(
//                               fontSize: baseTextSize * 0.8,
//                               fontFamily: 'Inter',
//                               fontWeight: FontWeight.w400,
//                               color: Color(0xFF414346),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildTotalItem(String label, String amount, {bool isTotal = false}) {
//     final size = MediaQuery.of(context).size;
//     final baseTextSize = getResponsiveSize(context);
//     final isLandscape = size.width > size.height;
//     final isDiscount = label == 'Discounts' || label == 'DB Wallet Credits';

//     return Padding(
//       padding: EdgeInsets.only(
//         bottom: isTotal ? 0 : size.height * 0.01,
//         left: size.width * 0.01,
//         right: size.width * 0.01,
//       ),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Text(
//             label,
//             style: TextStyle(
//               fontSize: isTotal
//                   ? (isLandscape ? size.height * 0.03 : baseTextSize * 1.2)
//                   : (isLandscape ? size.height * 0.025 : baseTextSize),
//               fontFamily: isTotal ? 'Inter-Semibold' : 'Inter',
//               color: Color(0xFF1F2122),
//               fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
//             ),
//           ),
//           Text(
//             amount,
//             style: TextStyle(
//               fontSize: isTotal
//                   ? (isLandscape ? size.height * 0.03 : baseTextSize * 1.2)
//                   : (isLandscape ? size.height * 0.022 : baseTextSize * 0.9),
//               fontFamily: isTotal ? 'Inter-Semibold' : 'Inter',
//               color: isDiscount ? Color(0xFFD31510) : Color(0xFF414346),
//               fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//    Widget _buildOrderItem(String quantity, String title, String price) {
//     final size = MediaQuery.of(context).size;
//     final baseTextSize = getResponsiveSize(context);
//     final isLandscape = size.width > size.height;

//     return Row(
//       children: [
//         Container(
//           padding: EdgeInsets.symmetric(
//             horizontal: size.width * 0.02,
//             vertical: size.height * 0.005,
//           ),
//           decoration: BoxDecoration(
//             color: Color(0xFFE1E3E6),
//             borderRadius: BorderRadius.circular(size.width * 0.04),
//           ),
//           child: Text(
//             quantity,
//             style: TextStyle(
//               fontSize: isLandscape ? size.height * 0.022 : baseTextSize * 0.9,
//               // fontWeight: FontWeight.w500,
//               fontFamily: 'Inter-medium',
//               color: Color(0xFF1F2122),
//             ),
//           ),
//         ),
//         SizedBox(width: size.width * 0.03),
//         Expanded(
//           child: Text(
//             title,
//             style: TextStyle(
//               fontSize: isLandscape ? size.height * 0.025 : baseTextSize * 1.1,
//               // fontWeight: FontWeight.w600,
//               fontFamily: 'Inter-Semibold',
//               color: Color(0xFF1F2122),
//             ),
//           ),
//         ),
//         Text(
//           price,
//           style: TextStyle(
//             fontSize: isLandscape ? size.height * 0.022 : baseTextSize * 0.9,
//             fontWeight: FontWeight.w400,
//             fontFamily: 'Inter',
//             color: Color(0xFF414346),
//           ),
//         ),
//       ],
//     );
//   }

// }
