import 'package:flutter/material.dart';
import 'package:db_eats/data/models/meal_plan/cuisinelistingmodel.dart';

class HierarchicalCuisineDropdown extends StatefulWidget {
  final List<Cuisines> cuisines;
  final String label;
  final Function(Map<String, List<int>>) onSelectionChanged;
  final Map<String, List<int>>? initialSelection;

  const HierarchicalCuisineDropdown({
    super.key,
    required this.cuisines,
    required this.label,
    required this.onSelectionChanged,
    this.initialSelection,
  });

  @override
  State<HierarchicalCuisineDropdown> createState() =>
      HierarchicalCuisineDropdownState();
}

class HierarchicalCuisineDropdownState
    extends State<HierarchicalCuisineDropdown> {
  late FocusNode _dropdownFocusNode;

  bool isExpanded = false;
  final Map<int, bool> _expandedCuisines = {};
  final Map<int, bool> _expandedSubCuisines = {};

  // Selection tracking
  final Set<int> _selectedCuisines = {};
  final Set<int> _selectedSubCuisines = {};
  final Set<int> _selectedLocalCuisines = {};

  @override
  void initState() {
    super.initState();
    _dropdownFocusNode = FocusNode();
    _dropdownFocusNode.addListener(() {
      if (!_dropdownFocusNode.hasFocus) {
        setState(() {
          isExpanded = false;
        });
      }
    });
    _initializeSelection();
  }

  // Public method to close dropdown from parent
  void closeDropdown() {
    setState(() {
      isExpanded = false;
    });
    _dropdownFocusNode.unfocus();
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final screenWidth = MediaQuery.of(context).size.width;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  void dispose() {
    _dropdownFocusNode.dispose();
    super.dispose();
  }

  void _initializeSelection() {
    if (widget.initialSelection != null) {
      _selectedCuisines.addAll(widget.initialSelection!['cuisine_ids'] ?? []);
      _selectedSubCuisines
          .addAll(widget.initialSelection!['sub_cuisine_ids'] ?? []);
      _selectedLocalCuisines
          .addAll(widget.initialSelection!['local_cuisine_ids'] ?? []);
    }
  }

  void _notifySelectionChanged() {
    widget.onSelectionChanged({
      'cuisine_ids': _selectedCuisines.toList(),
      'sub_cuisine_ids': _selectedSubCuisines.toList(),
      'local_cuisine_ids': _selectedLocalCuisines.toList(),
    });
  }

  void _toggleCuisineSelection(Cuisines cuisine) {
    setState(() {
      if (_selectedCuisines.contains(cuisine.id)) {
        _selectedCuisines.remove(cuisine.id);
        // Remove all sub-cuisines and local cuisines
        if (cuisine.subCuisines != null) {
          for (var subCuisine in cuisine.subCuisines!) {
            _selectedSubCuisines.remove(subCuisine.id);
            if (subCuisine.localCuisines != null) {
              for (var localCuisine in subCuisine.localCuisines!) {
                _selectedLocalCuisines.remove(localCuisine.id);
              }
            }
          }
        }
      } else {
        _selectedCuisines.add(cuisine.id!);
        // Select all sub-cuisines and their local cuisines
        if (cuisine.subCuisines != null) {
          for (var subCuisine in cuisine.subCuisines!) {
            _selectedSubCuisines.add(subCuisine.id!);
            if (subCuisine.localCuisines != null) {
              for (var localCuisine in subCuisine.localCuisines!) {
                _selectedLocalCuisines.add(localCuisine.id!);
              }
            }
          }
        }
      }
      _notifySelectionChanged();
    });
  }

  void _toggleSubCuisineSelection(SubCuisines subCuisine) {
    setState(() {
      if (_selectedSubCuisines.contains(subCuisine.id)) {
        _selectedSubCuisines.remove(subCuisine.id);
        // Remove all local cuisines
        if (subCuisine.localCuisines != null) {
          for (var localCuisine in subCuisine.localCuisines!) {
            _selectedLocalCuisines.remove(localCuisine.id);
          }
        }
      } else {
        _selectedSubCuisines.add(subCuisine.id!);
        // Select all local cuisines
        if (subCuisine.localCuisines != null) {
          for (var localCuisine in subCuisine.localCuisines!) {
            _selectedLocalCuisines.add(localCuisine.id!);
          }
        }
        // Ensure parent cuisine is selected
        final parentCuisine = widget.cuisines.firstWhere(
          (cuisine) =>
              cuisine.subCuisines?.any((sc) => sc.id == subCuisine.id) ?? false,
          orElse: () => Cuisines(id: null, name: null),
        );
        if (parentCuisine.id != null) {
          _selectedCuisines.add(parentCuisine.id!);
        }
      }
      _notifySelectionChanged();
    });
  }

  void _toggleLocalCuisineSelection(LocalCuisines localCuisine) {
    setState(() {
      if (_selectedLocalCuisines.contains(localCuisine.id)) {
        _selectedLocalCuisines.remove(localCuisine.id);
      } else {
        _selectedLocalCuisines.add(localCuisine.id!);
        // Ensure parent sub-cuisine and cuisine are selected
        for (var cuisine in widget.cuisines) {
          if (cuisine.subCuisines != null) {
            for (var subCuisine in cuisine.subCuisines!) {
              if (subCuisine.localCuisines
                      ?.any((lc) => lc.id == localCuisine.id) ??
                  false) {
                _selectedSubCuisines.add(subCuisine.id!);
                _selectedCuisines.add(cuisine.id!);
                break;
              }
            }
          }
        }
      }
      _notifySelectionChanged();
    });
  }

  String _getSelectionText() {
    int totalSelected = _selectedCuisines.length +
        _selectedSubCuisines.length +
        _selectedLocalCuisines.length;

    if (totalSelected == 0) {
      return 'Select cuisines';
    } else if (totalSelected == 1) {
      // Show the single selected item name
      if (_selectedCuisines.isNotEmpty) {
        final cuisine =
            widget.cuisines.firstWhere((c) => c.id == _selectedCuisines.first);
        return cuisine.name ?? '';
      } else if (_selectedSubCuisines.isNotEmpty) {
        for (var cuisine in widget.cuisines) {
          if (cuisine.subCuisines != null) {
            final subCuisine = cuisine.subCuisines!
                .where((sc) => sc.id == _selectedSubCuisines.first);
            if (subCuisine.isNotEmpty) {
              return subCuisine.first.name ?? '';
            }
          }
        }
      } else if (_selectedLocalCuisines.isNotEmpty) {
        for (var cuisine in widget.cuisines) {
          if (cuisine.subCuisines != null) {
            for (var subCuisine in cuisine.subCuisines!) {
              if (subCuisine.localCuisines != null) {
                final localCuisine = subCuisine.localCuisines!
                    .where((lc) => lc.id == _selectedLocalCuisines.first);
                if (localCuisine.isNotEmpty) {
                  return localCuisine.first.name ?? '';
                }
              }
            }
          }
        }
      }
    }
    return '$totalSelected selected';
  }

  Widget _buildCheckbox(bool isSelected) {
    return Container(
      width: eighteen,
      height: eighteen,
      decoration: BoxDecoration(
        border: Border.all(
          color: isSelected ? const Color(0xFF1F2122) : const Color(0xFFB9B6AD),
          width: 1.5,
        ),
        borderRadius: BorderRadius.circular(3),
        color: isSelected ? const Color(0xFF1F2122) : Colors.transparent,
      ),
      child: isSelected
          ?  Icon(
              Icons.check,
              size: twelve,
              color: Colors.white,
            )
          : null,
    );
  }

  Widget _buildCuisineItem(Cuisines cuisine) {
    final isSelected = _selectedCuisines.contains(cuisine.id);
    final isExpanded = _expandedCuisines[cuisine.id] ?? false;
    final hasSubCuisines = cuisine.subCuisines?.isNotEmpty ?? false;

    return Column(
      children: [
        Container(
          padding:  EdgeInsets.symmetric(horizontal: sixteen, vertical: twelve),
          child: Row(
            children: [
              GestureDetector(
                onTap: () => _toggleCuisineSelection(cuisine),
                child: _buildCheckbox(isSelected),
              ),
               SizedBox(width: twelve),
              Expanded(
                child: Text(
                  cuisine.name ?? '',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    fontSize: forteen,
                    color: const Color(0xFF1F2122),
                  ),
                ),
              ),
              if (hasSubCuisines)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _expandedCuisines[cuisine.id!] = !isExpanded;
                    });
                  },
                  child: Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: const Color(0xFF666666),
                    size: twenty,
                  ),
                ),
            ],
          ),
        ),
        if (isExpanded && hasSubCuisines)
          ...cuisine.subCuisines!
              .map((subCuisine) => _buildSubCuisineItem(subCuisine)),
      ],
    );
  }

  Widget _buildSubCuisineItem(SubCuisines subCuisine) {
    final isSelected = _selectedSubCuisines.contains(subCuisine.id);
    final isExpanded = _expandedSubCuisines[subCuisine.id] ?? false;
    final hasLocalCuisines = subCuisine.localCuisines?.isNotEmpty ?? false;

    return Column(
      children: [
        Container(
          padding:
               EdgeInsets.only(left: ten*4.4, right: sixteen, top: sixteen/2, bottom: sixteen/2),
          child: Row(
            children: [
              GestureDetector(
                onTap: () => _toggleSubCuisineSelection(subCuisine),
                child: _buildCheckbox(isSelected),
              ),
               SizedBox(width: twelve),
              Expanded(
                child: Text(
                  subCuisine.name ?? '',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    fontSize: forteen,
                    color: const Color(0xFF1F2122),
                  ),
                ),
              ),
              if (hasLocalCuisines)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _expandedSubCuisines[subCuisine.id!] = !isExpanded;
                    });
                  },
                  child: Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: const Color(0xFF666666),
                    size: eighteen,
                  ),
                ),
            ],
          ),
        ),
        if (isExpanded && hasLocalCuisines)
          ...subCuisine.localCuisines!
              .map((localCuisine) => _buildLocalCuisineItem(localCuisine)),
      ],
    );
  }

  Widget _buildLocalCuisineItem(LocalCuisines localCuisine) {
    final isSelected = _selectedLocalCuisines.contains(localCuisine.id);

    return Container(
      padding:  EdgeInsets.only(left: ten*7.2, right: sixteen, top: twelve/2, bottom: twelve/2),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => _toggleLocalCuisineSelection(localCuisine),
            child: _buildCheckbox(isSelected),
          ),
           SizedBox(width: twelve),
          Expanded(
            child: Text(
              localCuisine.name ?? '',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                fontSize: twelve,
                color: const Color(0xFF1F2122),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: forteen,
            color: Color(0xFF1F2122),
          ),
        ),
         SizedBox(height: twelve),
        Focus(
          focusNode: _dropdownFocusNode,
          child: GestureDetector(
            onTap: () {
              setState(() {
                isExpanded = !isExpanded;
              });
              if (isExpanded && !_dropdownFocusNode.hasFocus) {
                _dropdownFocusNode.requestFocus();
              }
            },
            child: Container(
              height: ten*4,
              padding:  EdgeInsets.symmetric(horizontal: eighteen),
              decoration: BoxDecoration(
                color: const Color(0xFFFFFFFF),
                border: Border.all(color: const Color(0xFFE1E3E6)),
                borderRadius: BorderRadius.circular(35),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      _getSelectionText(),
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: sixteen,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: const Color(0xFF1F2122),
                    size: twenty,
                  ),
                ],
              ),
            ),
          ),
        ),
        if (isExpanded)
          Container(
            margin:  EdgeInsets.only(top: sixteen/2),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: const Color(0xFFE1E3E6)),
              borderRadius: BorderRadius.circular(sixteen/2),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: sixteen/2,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            constraints: const BoxConstraints(maxHeight: 300),
            child: SingleChildScrollView(
              child: Column(
                children: widget.cuisines
                    .map((cuisine) => _buildCuisineItem(cuisine))
                    .toList(),
              ),
            ),
          ),
      ],
    );
  }
}