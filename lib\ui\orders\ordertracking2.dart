import 'package:db_eats/bloc/order_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/orders/confirmed_orders_view.dart';
import 'package:db_eats/ui/orders/ordertracking1.dart';
import 'package:db_eats/ui/orders/ordertracking3.dart';
import 'package:db_eats/ui/support/customer_support.dart';
import 'package:db_eats/utils/dotteddivider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:lottie/lottie.dart';

class OrderTrackingView2 extends StatefulWidget {
  final int orderId;

  const OrderTrackingView2({super.key, required this.orderId});

  @override
  _OrderTrackingView2State createState() => _OrderTrackingView2State();
}

class _OrderTrackingView2State extends State<OrderTrackingView2> {
  // Helper method to format time
  String _formatTimeOnly(String deliveryTime) {
    try {
      final dateTime = DateTime.parse(deliveryTime);
      return "${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}";
    } catch (e) {
      return deliveryTime;
    }
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  String _formatTimeToAMPM(String time) {
    try {
      if (time.contains('AM') || time.contains('PM')) {
        return time;
      }

      if (time.contains(':')) {
        final parts = time.split(':');
        int hour = int.parse(parts[0]);
        final minute = parts[1];

        final period = hour >= 12 ? 'PM' : 'AM';
        hour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

        return '$hour:$minute $period';
      }

      return time;
    } catch (e) {
      return time;
    }
  }

  double getResponsiveSize(BuildContext context,
      {double small = 12,
      double medium = 16,
      double large = 20,
      double xlarge = 24}) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return small;
    if (width < 600) return medium;
    if (width < 900) return large;
    return xlarge;
  }

  Widget _buildOrderItem(String quantity, String title, String price) {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final isLandscape = size.width > size.height;

    return Row(
      children: [
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: size.width * 0.02,
            vertical: size.height * 0.005,
          ),
          decoration: BoxDecoration(
            color: Color(0xFFE1E3E6),
            borderRadius: BorderRadius.circular(size.width * 0.04),
          ),
          child: Text(
            quantity,
            style: TextStyle(
              fontSize: isLandscape ? size.height * 0.022 : forteen,
              // fontWeight: FontWeight.w500,
              fontFamily: 'Inter-medium',
              color: Color(0xFF1F2122),
            ),
          ),
        ),
        SizedBox(width: size.width * 0.03),
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              fontSize: isLandscape ? size.height * 0.025 : forteen,
              // fontWeight: FontWeight.w600,
              fontFamily: 'Inter-Semibold',
              color: Color(0xFF1F2122),
            ),
          ),
        ),
        Text(
          price,
          style: TextStyle(
            fontSize: isLandscape ? size.height * 0.022 : forteen,
            fontWeight: FontWeight.w400,
            fontFamily: 'Inter',
            color: Color(0xFF414346),
          ),
        ),
      ],
    );
  }

  // Helper method for total items
  Widget _buildTotalItem(String label, String amount, {bool isTotal = false}) {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final isLandscape = size.width > size.height;
    final isDiscount = label == 'Discounts' || label == 'DB Wallet Credits';

    return Padding(
      padding: EdgeInsets.only(
        bottom: isTotal ? 0 : size.height * 0.01,
        left: size.width * 0.01,
        right: size.width * 0.01,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal
                  ? (isLandscape ? size.height * 0.03 : sixteen)
                  : (isLandscape ? size.height * 0.025 : forteen),
              fontFamily: isTotal ? 'Inter-Semibold' : 'Inter',
              color: Color(0xFF1F2122),
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: isTotal
                  ? (isLandscape ? size.height * 0.03 : sixteen)
                  : (isLandscape ? size.height * 0.022 : forteen),
              fontFamily: isTotal ? 'Inter-Semibold' : 'Inter',
              color: isDiscount ? Color(0xFFD31510) : Color(0xFF414346),
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final itemSpacing = size.height * 0.02;
    final isLandscape = size.width > size.height;

    // Add responsive container sizing
    final cookingImageSize =
        isLandscape ? size.height * 0.30 : size.width * 0.40;
    final driverContainerWidth =
        isLandscape ? size.width * 0.2 : size.width * 0.45;
    final progressBarWidth =
        isLandscape ? size.width * 0.08 : size.width * 0.12;

    context.read<OrderBloc>().add(ViewOrederdetailsEvent(widget.orderId));

    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC),
      body: SafeArea(
        child: BlocConsumer<OrderBloc, OrderState>(
          listener: (context, state) {
            if (state is ViewOrederdetailsFailed) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.message)),
              );
            }
          },
          buildWhen: (previous, current) {
            return current is ViewOrederdetailsLoading ||
                current is ViewOrederdetailsSuccess ||
                current is ViewOrederdetailsFailed;
          },
          builder: (context, state) {
            if (state is ViewOrederdetailsLoading) {
              return const Center(child: CupertinoActivityIndicator());
            }

            if (state is ViewOrederdetailsFailed ||
                Initializer.viewOrderDetailsModel.data == null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: size.width * 0.25,
                      child: Lottie.asset(
                        'assets/noorderes.json',
                        fit: BoxFit.contain,
                      ),
                    ),
                    Text(
                      "Failed to Load Order Details",
                      style: TextStyle(
                        fontSize: getResponsiveSize(context,
                            small: 16, medium: 18, large: 22, xlarge: 26),
                        fontWeight: FontWeight.w700,
                        color: const Color(0xFF1F2122),
                        fontFamily: 'Inter',
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: size.height * 0.01),
                    Text(
                      state is ViewOrederdetailsFailed
                          ? state.message
                          : "No order details available",
                      style: TextStyle(
                        fontSize: getResponsiveSize(context,
                            small: 12, medium: 14, large: 16, xlarge: 18),
                        color: const Color(0xFF66696D),
                        fontFamily: 'Inter',
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }

            final orderData = Initializer.viewOrderDetailsModel.data!;
            final orderNumber = orderData.orderNumber ?? 'Unknown';
            final chefName = orderData.chef?.name ?? 'Unknown Chef';
            final chefAvatar = orderData.chef?.photo != null
                ? ServerHelper.imageUrl + orderData.chef!.photo!
                : 'assets/images/chef_placeholder.png';
            final itemsCount = orderData.items?.length ?? 0;
            final deliveryAddress =
                orderData.address?.addressText ?? 'Unknown Address';

            // Updated to use the new delivery time structure
            final deliveryTimeDescription =
                orderData.deliveryTimes?.description ?? 'Unknown Time';
            final deliveryTime = orderData.deliveryTime ?? '';

            // Format delivery time for display (time only)
            String formattedDeliveryTime = '';
            if (deliveryTime.isNotEmpty) {
              formattedDeliveryTime = _formatTimeOnly(deliveryTime);
            } else {
              formattedDeliveryTime = deliveryTimeDescription;
            }

            final subtotal = orderData.subtotal?.toStringAsFixed(2) ?? '0.00';
            final deliveryFee =
                orderData.deliveryFee?.toStringAsFixed(2) ?? '0.00';
            final discount = orderData.discount?.toStringAsFixed(2) ?? '0.00';
            final walletCredits =
                orderData.walletCredits?.toStringAsFixed(2) ?? '0.00';
            final taxesAndFees =
                orderData.taxesAndFees?.toStringAsFixed(2) ?? '0.00';
            final total = orderData.total?.toStringAsFixed(2) ?? '0.00';
            final status = orderData.status ?? 'Unknown';

            return Container(
              color: const Color(0xFFF6F3EC),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: size.width * 0.04,
                      vertical: size.height * 0.02,
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.all(size.width * 0.04),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  constraints: BoxConstraints(
                                    maxWidth: double.infinity,
                                    maxHeight: 400,
                                  ),
                                  width: double.infinity,
                                  height: 400,
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(
                                        12), // optional rounded corners
                                    child: GoogleMap(
                                      initialCameraPosition: CameraPosition(
                                        target: LatLng(37.7749,
                                            -122.4194), // Replace with user's location
                                        zoom: 14,
                                      ),
                                      markers: {
                                        // Marker(
                                        //   markerId:
                                        //       MarkerId('userLocation'),
                                        //   position: LatLng(37.7749,
                                        //       -122.4194), // same as above
                                        // ),
                                      },
                                      zoomControlsEnabled: false,
                                      myLocationEnabled: true,
                                      myLocationButtonEnabled: true,
                                    ),
                                  ),
                                ),
                                SizedBox(height: itemSpacing * 0.5),
                                Container(
                                  padding: EdgeInsets.all(size.width * 0.05),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      // Image.asset(
                                      //   'assets/images/confirmed_order.png',
                                      //   height: cookingImageSize,
                                      // ),
                                      // SizedBox(height: itemSpacing * 0.5),

                                      Text(
                                        // 'Order $orderNumber   ${status.toLowerCase()}!',
                                        'Driver is now delivering your order',
                                        style: TextStyle(
                                          fontSize: isLandscape
                                              ? size.height * 0.04
                                              : twenty, // ≈ 20,

                                          fontWeight: FontWeight.w600,
                                          color: Colors.black,
                                          fontFamily: 'Inter',
                                          height: 1.24,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      SizedBox(height: itemSpacing * 0.5),
                                      Text.rich(
                                        TextSpan(
                                          text:
                                              'We have received your order and Chef ',
                                          style: TextStyle(
                                            fontSize: forteen,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Inter',
                                            color: const Color(0xFF1F2122),
                                          ),
                                          children: [
                                            TextSpan(
                                              text: chefName,
                                              style: TextStyle(
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            TextSpan(
                                              text: ' is on it!',
                                            ),
                                          ],
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      SizedBox(height: itemSpacing),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: List.generate(5, (index) {
                                          return Container(
                                            width: progressBarWidth,
                                            height: size.height * 0.01,
                                            margin: EdgeInsets.symmetric(
                                                horizontal: size.width * 0.019),
                                            decoration: BoxDecoration(
                                              color: index < 3
                                                  ? Colors.black
                                                  : const Color(0xFFD9D9D9),
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      size.width * 0.02),
                                            ),
                                          );
                                        }),
                                      ),
                                      SizedBox(height: itemSpacing),
                                      Text(
                                        'Your order will be delivered to $deliveryAddress at ${_formatTimeToAMPM(formattedDeliveryTime)}',
                                        style: TextStyle(
                                          fontSize: twelve, // ≈ 12
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Inter',
                                          color: const Color(0xFF1F2122),
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      SizedBox(height: itemSpacing),
                                      Container(
                                        width: driverContainerWidth,
                                        padding:
                                            EdgeInsets.all(size.width * 0.02),
                                        decoration: BoxDecoration(
                                          color: const Color(0xFFF1F2F3),
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                        child: Column(
                                          children: [
                                            Text(
                                              'Your Driver',
                                              style: TextStyle(
                                                fontSize: forteen,
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Inter',
                                                color: const Color(0xFF414346),
                                              ),
                                            ),
                                            SizedBox(height: itemSpacing * 0.2),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CircleAvatar(
                                                  radius: isLandscape
                                                      ? size.height * 0.025
                                                      : baseTextSize,
                                                  backgroundImage: const AssetImage(
                                                      'assets/images/driver.png'),
                                                ),
                                                SizedBox(
                                                    width: size.width * 0.01),
                                                Text(
                                                  'John Hancock', // This could be dynamic if driver data is available
                                                  style: TextStyle(
                                                    fontSize: forteen,
                                                    fontWeight: FontWeight.w600,
                                                    fontFamily: 'Inter',
                                                    color:
                                                        const Color(0xFF1F2122),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: itemSpacing * 0.2),
                                            Text(
                                              '(910) 799-0420', // This could be dynamic if driver data is available
                                              style: TextStyle(
                                                fontSize: forteen,
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Inter',
                                                color: const Color(0xFF414346),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          // Previous Step Button
                                          ElevatedButton(
                                            onPressed: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      OrderTrackingView1(
                                                    orderId: widget.orderId,
                                                  ), // attach your previous step page here
                                                ),
                                              );
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.grey[300],
                                              foregroundColor: Colors.black,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                            ),
                                            child: Text('Prev Step'),
                                          ),

                                          // Next Step Button
                                          ElevatedButton(
                                            onPressed: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      OrderTrackingView3(
                                                    orderId: widget.orderId,
                                                  ), // attach your next step page here
                                                ),
                                              );
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.blue,
                                              foregroundColor: Colors.white,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                            ),
                                            child: Text('Next Step'),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: forteen),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            'Need help? ',
                                            style: TextStyle(
                                              fontSize: twelve,
                                              fontFamily: 'Inter',
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                          GestureDetector(
                                            onTap: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      CustomerSupport(
                                                    orderId: Initializer
                                                            .viewOrderDetailsModel
                                                            .data
                                                            ?.id ??
                                                        0,
                                                  ), // Navigate to CustomerSupport
                                                ),
                                              );
                                            },
                                            child: Text(
                                              'Contact support.',
                                              style: TextStyle(
                                                fontSize: twelve,
                                                fontFamily: 'Inter',
                                                fontWeight: FontWeight.w700,
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Order Summary card
                          Padding(
                            padding: EdgeInsets.all(size.width * 0.04),
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(size.width * 0.05),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Order Summary',
                                    style: TextStyle(
                                      fontSize: isLandscape
                                          ? size.height * 0.04
                                          : eighteen, // ≈ 18
                                      fontWeight: FontWeight.w600,
                                      fontFamily: 'Inter',
                                      color: const Color(0xFF000000),
                                    ),
                                  ),
                                  DottedDivider(),
                                  SizedBox(height: itemSpacing * 0.3),
                                  Text(
                                    'Ordered From',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Row(
                                    children: [
                                      CircleAvatar(
                                        radius: isLandscape
                                            ? size.height * 0.015
                                            : twelve,
                                        backgroundImage:
                                            NetworkImage(chefAvatar),
                                      ),
                                      SizedBox(width: size.width * 0.02),
                                      Text(
                                        chefName,
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w600,
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  DottedDivider(),
                                  SizedBox(height: itemSpacing * 0.3),
                                  Text(
                                    'Order Details ($itemsCount Items)',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.8),
                                  // Display order items dynamically
                                  if (orderData.items != null)
                                    ...orderData.items!.map((item) {
                                      return Column(
                                        children: [
                                          _buildOrderItem(
                                            '${item.quantity}x',
                                            item.dish?.name ?? 'Unknown Dish',
                                            '\$${item.totalPrice?.toStringAsFixed(2) ?? '0.00'}',
                                          ),
                                          SizedBox(height: itemSpacing * 0.5),
                                        ],
                                      );
                                    }),
                                  DottedDivider(),
                                  SizedBox(height: itemSpacing * 0.3),
                                  Text(
                                    'Promotions',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Text(
                                    'DBEATS50', // This could be dynamic if promotion data is available
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  DottedDivider(),
                                  SizedBox(height: itemSpacing * 0.3),
                                  Text(
                                    'Order Total',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.8),
                                  Column(
                                    children: [
                                      _buildTotalItem(
                                          'Subtotal', '\$$subtotal'),
                                      // SizedBox(height: itemSpacing * 0.1),
                                      _buildTotalItem(
                                          'Delivery fee', '\$$deliveryFee'),
                                      //   SizedBox(height: itemSpacing * 0.4),
                                      _buildTotalItem(
                                          'Discounts', '-\$$discount'),
                                      //   SizedBox(height: itemSpacing * 0.4),
                                      _buildTotalItem('DB Wallet Credits',
                                          '-\$$walletCredits'),
                                      //     SizedBox(height: itemSpacing * 0.4),
                                      _buildTotalItem(
                                          'Taxes & Fees', '\$$taxesAndFees'),
                                      DottedDivider(),
                                      _buildTotalItem('Total', '\$$total',
                                          isTotal: true),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Delivery Details card
                          Padding(
                            padding: EdgeInsets.all(size.width * 0.04),
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(size.width * 0.05),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Delivery Details',
                                    style: TextStyle(
                                      fontSize: isLandscape
                                          ? size.height * 0.04
                                          : eighteen,
                                      fontWeight: FontWeight.w600,
                                      fontFamily: 'Inter',
                                      color: const Color(0xFF000000),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Text(
                                    'Address',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Row(
                                    children: [
                                      Icon(Icons.location_on_outlined,
                                          size: baseTextSize * 1.2,
                                          color: const Color(0xFF414346)),
                                      SizedBox(width: size.width * 0.01),
                                    Text(
                                        deliveryAddress.split(' ').length > 5
                                            ? '${deliveryAddress.split(' ').take(5).join(' ')}...'
                                            : deliveryAddress,
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Inter-medium',
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Text(
                                    'Delivery',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Row(
                                    children: [
                                      Icon(Icons.access_time,
                                          size: baseTextSize * 1.0,
                                          color: const Color(0xFF414346)),
                                      SizedBox(width: size.width * 0.01),
                                      Text(
                                        formattedDeliveryTime,
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w500,
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Divider(
                                    height: 0,
                                    thickness: 1,
                                    color: const Color(0xFFE1E3E6),
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Text(
                                    'Drop-Off Options',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontFamily: 'Inter-medium',
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Text(
                                    orderData.dropOffOption?.name ?? 'Unknown',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Inter',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Text(
                                    'Drop-Off Instructions',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontFamily: 'Inter-medium',
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Text(
                                    orderData.dropOffInstructions ?? 'None',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Divider(
                                    height: 0,
                                    thickness: 1,
                                    color: const Color(0xFFE1E3E6),
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Delivery Time',
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Inter-medium',
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                      Text(
                                        (orderData.deliveryTimes?.description ??
                                                '30-60 Minutes')
                                            .replaceFirst(
                                                'Delivery between ', ''),
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w500,
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Image.asset(
                                            'assets/icons/star_2.png',
                                            width: baseTextSize * 1.0,
                                            height: baseTextSize * 1.0,
                                            color: const Color(0xFF414346),
                                          ),
                                          SizedBox(width: size.width * 0.02),
                                          Text(
                                            orderData.deliveryTimes?.name ??
                                                'Priority',
                                            style: TextStyle(
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w500,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ],
                                      ),
                                      Text(
                                        '+\$${orderData.deliveryTimes?.cost ?? '0.00'}',
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w600,
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Text(
                                    ("     ${orderData.deliveryTimes?.description}" ??
                                            '30-60 Minutes')
                                        .replaceFirst('Delivery between ', ''),
                                    style: TextStyle(
                                      fontSize: twelve,
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF414346),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ), // Payment card
                          Padding(
                            padding: EdgeInsets.all(size.width * 0.04),
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(size.width * 0.04),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Payment',
                                        style: TextStyle(
                                          fontSize: forteen * 1.0,
                                          fontWeight: FontWeight.w500,
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: const Color(0xFFE8F5E9),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Image.asset(
                                              'assets/icons/tick.png',
                                              width:
                                                  12, // You can adjust width and height as needed
                                              height: 12,
                                            ),
                                            SizedBox(width: 4),
                                            Text(
                                              'Paid',
                                              style: TextStyle(
                                                fontSize: twelve,
                                                color: const Color(0xFF2E7D32),
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: size.width * 0.03),
                                  Row(
                                    children: [
                                      Image.asset(
                                        'assets/icons/Visa.png',
                                        height: baseTextSize * 1.2,
                                        fit: BoxFit.contain,
                                      ),
                                      SizedBox(width: size.width * 0.03),
                                      Expanded(
                                        child: Text(
                                          'Paid through card ending with 0001',
                                          style: TextStyle(
                                            fontSize: forteen,
                                            fontWeight: FontWeight.w400,
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: size.width * 0.01),
                                  Padding(
                                    padding: EdgeInsets.only(
                                        left: size.width * 0.02),
                                    child: Text(
                                      '      Expires 03/2028',
                                      style: TextStyle(
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w400,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Email notification
                          Padding(
                            padding: EdgeInsets.all(size.width * 0.04),
                            child: Container(
                                width: double.infinity,
                                padding: EdgeInsets.all(size.width * 0.02),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFE1DDD5),
                                  borderRadius: BorderRadius.circular(6),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.05),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Align(
                                      alignment: Alignment.topCenter,
                                      child: Icon(
                                        Icons.mail_outline,
                                        size: baseTextSize * 1.25,
                                        color: const Color(0xFF414346),
                                      ),
                                    ),
                                    SizedBox(width: size.width * 0.02),
                                    Expanded(
                                      child: Text(
                                        'A copy of your invoice is on its way to your inbox. Please check your email.',
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontFamily: 'Inter',
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ),
                                  ],
                                )),
                          ),
                          SizedBox(
                            height: 40,
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
