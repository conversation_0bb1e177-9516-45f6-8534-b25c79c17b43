class FilterdataModel {
  bool? status;
  String? message;
  int? statusCode;
  FilterData? data;

  FilterdataModel({this.status, this.message, this.statusCode, this.data});

  FilterdataModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? FilterData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['status_code'] = statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class FilterData {
  List<Cuisines>? cuisines;
  List<Dietaries>? dietaries;
  List<DishTypes>? dishTypes;
  List<SpiceLevels>? spiceLevels;
  List<TimeSlots>? timeSlots;

  FilterData({
    this.cuisines,
    this.dietaries,
    this.dishTypes,
    this.spiceLevels,
    this.timeSlots,
  });

  FilterData.fromJson(Map<String, dynamic> json) {
    if (json['cuisines'] != null) {
      cuisines = <Cuisines>[];
      json['cuisines'].forEach((v) {
        cuisines!.add(Cuisines.fromJson(v));
      });
    }
    if (json['dietaries'] != null) {
      dietaries = <Dietaries>[];
      json['dietaries'].forEach((v) {
        dietaries!.add(Dietaries.fromJson(v));
      });
    }
    if (json['dish_types'] != null) {
      dishTypes = <DishTypes>[];
      json['dish_types'].forEach((v) {
        dishTypes!.add(DishTypes.fromJson(v));
      });
    }
    if (json['spice_levels'] != null) {
      spiceLevels = <SpiceLevels>[];
      json['spice_levels'].forEach((v) {
        spiceLevels!.add(SpiceLevels.fromJson(v));
      });
    }
    if (json['time_slots'] != null) {
      timeSlots = <TimeSlots>[];
      json['time_slots'].forEach((v) {
        timeSlots!.add(TimeSlots.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (cuisines != null) {
      data['cuisines'] = cuisines!.map((v) => v.toJson()).toList();
    }
    if (dietaries != null) {
      data['dietaries'] = dietaries!.map((v) => v.toJson()).toList();
    }
    if (dishTypes != null) {
      data['dish_types'] = dishTypes!.map((v) => v.toJson()).toList();
    }
    if (spiceLevels != null) {
      data['spice_levels'] = spiceLevels!.map((v) => v.toJson()).toList();
    }
    if (timeSlots != null) {
      data['time_slots'] = timeSlots!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Cuisines {
  int? id;
  String? name;
  List<SubCuisines>? subCuisines;

  Cuisines({this.id, this.name, this.subCuisines});

  Cuisines.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    if (json['sub_cuisines'] != null) {
      subCuisines = <SubCuisines>[];
      json['sub_cuisines'].forEach((v) {
        subCuisines!.add(SubCuisines.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (subCuisines != null) {
      data['sub_cuisines'] = subCuisines!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SubCuisines {
  int? id;
  String? name;
  List<LocalCuisines>? localCuisines;

  SubCuisines({this.id, this.name, this.localCuisines});

  SubCuisines.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    if (json['local_cuisines'] != null) {
      localCuisines = <LocalCuisines>[];
      json['local_cuisines'].forEach((v) {
        localCuisines!.add(LocalCuisines.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (localCuisines != null) {
      data['local_cuisines'] = localCuisines!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class LocalCuisines {
  int? id;
  String? name;

  LocalCuisines({this.id, this.name});

  LocalCuisines.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class Dietaries {
  int? id;
  String? name;

  Dietaries({this.id, this.name});

  Dietaries.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class DishTypes {
  int? id;
  String? name;

  DishTypes({this.id, this.name});

  DishTypes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class SpiceLevels {
  int? id;
  String? name;

  SpiceLevels({this.id, this.name});

  SpiceLevels.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class TimeSlots {
  int? id;
  String? startTime;
  String? endTime;

  TimeSlots({this.id, this.startTime, this.endTime});

  TimeSlots.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    return data;
  }
}
