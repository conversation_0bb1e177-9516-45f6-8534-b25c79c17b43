class ViewCateringRequestModel {
  final bool? status;
  final ViewCateringRequestData? data;
  final int? statusCode;

  const ViewCateringRequestModel({
    this.status,
    this.data,
    this.statusCode,
  });

  factory ViewCateringRequestModel.fromJson(Map<String, dynamic> json) {
    return ViewCateringRequestModel(
      status: json['status'],
      data: json['data'] != null
          ? ViewCateringRequestData.fromJson(json['data'])
          : null,
      statusCode: json['status_code'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      if (data != null) 'data': data!.toJson(),
      'status_code': statusCode,
    };
  }
}

class ViewCateringRequestData {
  final Catering? catering;
  final List<Items>? items;
  final String? totalPrice;

  const ViewCateringRequestData({
    this.catering,
    this.items,
    this.totalPrice,
  });

  factory ViewCateringRequestData.fromJson(Map<String, dynamic> json) {
    return ViewCateringRequestData(
      catering:
          json['catering'] != null ? Catering.fromJson(json['catering']) : null,
      items: json['items'] != null
          ? (json['items'] as List).map((v) => Items.fromJson(v)).toList()
          : null,
      totalPrice: json['total_price'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (catering != null) 'catering': catering!.toJson(),
      if (items != null) 'items': items!.map((v) => v.toJson()).toList(),
      'total_price': totalPrice,
    };
  }
}

class Catering {
  final int? id;
  final int? customerId;
  final int? chefId;
  final int? peopleCount;
  final String? date;
  final int? timeSlotId;
  final String? address;
  final String? state;
  final String? city;
  final String? zipCode;
  final int? packagingTypeId;
  final int? dietaryPreferenceId;
  final int? spiceLevelId;
  final int? dropOffOptionId;
  final String? dropOffInstructions;
  final int? deliveryTimeId;
  final String? allergyPreferenceText; // Fixed typo
  final String? deliveryFee;
  final String? discount;
  final String? walletCredits;
  final String? taxesAndFees;
  final String? total;
  final bool? isEdited; // Renamed from idEdited for clarity
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<CateringCuisines>? cateringCuisines;
  final List<CateringSubCuisines>? cateringSubCuisines;
  final List<CateringLocalCuisines>? cateringLocalCuisines;
  final Chef? chef;
  final PackagingType? packagingType;
  final TimeSlot? timeSlot;

  const Catering({
    this.id,
    this.customerId,
    this.chefId,
    this.peopleCount,
    this.date,
    this.timeSlotId,
    this.address,
    this.state,
    this.city,
    this.zipCode,
    this.packagingTypeId,
    this.dietaryPreferenceId,
    this.spiceLevelId,
    this.dropOffOptionId,
    this.dropOffInstructions,
    this.deliveryTimeId,
    this.allergyPreferenceText,
    this.deliveryFee,
    this.discount,
    this.walletCredits,
    this.taxesAndFees,
    this.total,
    this.isEdited,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.cateringCuisines,
    this.cateringSubCuisines,
    this.cateringLocalCuisines,
    this.chef,
    this.packagingType,
    this.timeSlot,
  });

  factory Catering.fromJson(Map<String, dynamic> json) {
    return Catering(
      id: json['id'],
      customerId: json['customer_id'],
      chefId: json['chef_id'],
      peopleCount: json['people_count'],
      date: json['date'],
      timeSlotId: json['time_slot_id'],
      address: json['address'],
      state: json['state'],
      city: json['city'],
      zipCode: json['zip_code'],
      packagingTypeId: json['packaging_type_id'],
      dietaryPreferenceId: json['dietary_preference_id'],
      spiceLevelId: json['spice_level_id'],
      dropOffOptionId: json['drop_off_option_id'],
      dropOffInstructions: json['drop_off_instructions'],
      deliveryTimeId: json['delivery_time_id'],
      allergyPreferenceText:
          json['allergy_prference_text'], // Keep original key
      deliveryFee: json['delivery_fee'],
      discount: json['discount'],
      walletCredits: json['wallet_credits'],
      taxesAndFees: json['taxes_and_fees'],
      total: json['total'],
      isEdited: json['id_edited'],
      status: json['status'],
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
      cateringCuisines: json['catering_cuisines'] != null
          ? (json['catering_cuisines'] as List)
              .map((v) => CateringCuisines.fromJson(v))
              .toList()
          : null,
      cateringSubCuisines: json['catering_sub_cuisines'] != null
          ? (json['catering_sub_cuisines'] as List)
              .map((v) => CateringSubCuisines.fromJson(v))
              .toList()
          : null,
      cateringLocalCuisines: json['catering_local_cuisines'] != null
          ? (json['catering_local_cuisines'] as List)
              .map((v) => CateringLocalCuisines.fromJson(v))
              .toList()
          : null,
      chef: json['chef'] != null ? Chef.fromJson(json['chef']) : null,
      packagingType: json['packagingType'] != null
          ? PackagingType.fromJson(json['packagingType'])
          : null,
      timeSlot:
          json['timeSlot'] != null ? TimeSlot.fromJson(json['timeSlot']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'chef_id': chefId,
      'people_count': peopleCount,
      'date': date,
      'time_slot_id': timeSlotId,
      'address': address,
      'state': state,
      'city': city,
      'zip_code': zipCode,
      'packaging_type_id': packagingTypeId,
      'dietary_preference_id': dietaryPreferenceId,
      'spice_level_id': spiceLevelId,
      'drop_off_option_id': dropOffOptionId,
      'drop_off_instructions': dropOffInstructions,
      'delivery_time_id': deliveryTimeId,
      'allergy_prference_text': allergyPreferenceText,
      'delivery_fee': deliveryFee,
      'discount': discount,
      'wallet_credits': walletCredits,
      'taxes_and_fees': taxesAndFees,
      'total': total,
      'id_edited': isEdited,
      'status': status,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      if (cateringCuisines != null)
        'catering_cuisines': cateringCuisines!.map((v) => v.toJson()).toList(),
      if (cateringSubCuisines != null)
        'catering_sub_cuisines':
            cateringSubCuisines!.map((v) => v.toJson()).toList(),
      if (cateringLocalCuisines != null)
        'catering_local_cuisines':
            cateringLocalCuisines!.map((v) => v.toJson()).toList(),
      if (chef != null) 'chef': chef!.toJson(),
      if (packagingType != null) 'packagingType': packagingType!.toJson(),
      if (timeSlot != null) 'timeSlot': timeSlot!.toJson(),
    };
  }

  // Helper methods
  String get fullAddress => [address, city, state, zipCode]
      .where((s) => s != null && s.trim().isNotEmpty)
      .join(', ');

  String get chefFullName => chef != null
      ? '${chef!.firstName ?? ''} ${chef!.lastName ?? ''}'.trim()
      : '';

  List<String> get allCuisineNames {
    final cuisines = <String>[];

    if (cateringCuisines != null) {
      cuisines.addAll(cateringCuisines!
          .where((c) => c.cuisine?.name != null)
          .map((c) => c.cuisine!.name!));
    }

    if (cateringSubCuisines != null) {
      cuisines.addAll(cateringSubCuisines!
          .where((c) => c.subcuisine?.name != null)
          .map((c) => c.subcuisine!.name!));
    }

    if (cateringLocalCuisines != null) {
      cuisines.addAll(cateringLocalCuisines!
          .where((c) => c.localCuisine?.name != null)
          .map((c) => c.localCuisine!.name!));
    }

    return cuisines.toSet().toList(); // Remove duplicates
  }
}

// Remaining classes with similar improvements
class CateringCuisines {
  final int? id;
  final int? cateringId;
  final int? cuisineId;
  final Cuisine? cuisine;

  const CateringCuisines(
      {this.id, this.cateringId, this.cuisineId, this.cuisine});

  factory CateringCuisines.fromJson(Map<String, dynamic> json) {
    return CateringCuisines(
      id: json['id'],
      cateringId: json['catering_id'],
      cuisineId: json['cuisine_id'],
      cuisine:
          json['cuisine'] != null ? Cuisine.fromJson(json['cuisine']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'catering_id': cateringId,
      'cuisine_id': cuisineId,
      if (cuisine != null) 'cuisine': cuisine!.toJson(),
    };
  }
}

class Cuisine {
  final int? id;
  final String? name;

  const Cuisine({this.id, this.name});

  factory Cuisine.fromJson(Map<String, dynamic> json) {
    return Cuisine(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}

class CateringSubCuisines {
  final int? id;
  final int? cateringId;
  final int? subcuisineId;
  final Cuisine? subcuisine;

  const CateringSubCuisines(
      {this.id, this.cateringId, this.subcuisineId, this.subcuisine});

  factory CateringSubCuisines.fromJson(Map<String, dynamic> json) {
    return CateringSubCuisines(
      id: json['id'],
      cateringId: json['catering_id'],
      subcuisineId: json['subcuisine_id'],
      subcuisine: json['subcuisine'] != null
          ? Cuisine.fromJson(json['subcuisine'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'catering_id': cateringId,
      'subcuisine_id': subcuisineId,
      if (subcuisine != null) 'subcuisine': subcuisine!.toJson(),
    };
  }
}

class CateringLocalCuisines {
  final int? id;
  final int? cateringId;
  final int? localCuisineId;
  final Cuisine? localCuisine;

  const CateringLocalCuisines(
      {this.id, this.cateringId, this.localCuisineId, this.localCuisine});

  factory CateringLocalCuisines.fromJson(Map<String, dynamic> json) {
    return CateringLocalCuisines(
      id: json['id'],
      cateringId: json['catering_id'],
      localCuisineId: json['local_cuisine_id'],
      localCuisine: json['local_cuisine'] != null
          ? Cuisine.fromJson(json['local_cuisine'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'catering_id': cateringId,
      'local_cuisine_id': localCuisineId,
      if (localCuisine != null) 'local_cuisine': localCuisine!.toJson(),
    };
  }
}

class Chef {
  final int? id;
  final String? firstName;
  final String? lastName;

  const Chef({this.id, this.firstName, this.lastName});

  factory Chef.fromJson(Map<String, dynamic> json) {
    return Chef(
      id: json['id'],
      firstName: json['first_name'],
      lastName: json['last_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'last_name': lastName,
    };
  }

  String get fullName => '${firstName ?? ''} ${lastName ?? ''}'.trim();
}

class PackagingType {
  final int? id;
  final String? name;
  final bool? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const PackagingType(
      {this.id, this.name, this.status, this.createdAt, this.updatedAt});

  factory PackagingType.fromJson(Map<String, dynamic> json) {
    return PackagingType(
      id: json['id'],
      name: json['name'],
      status: json['status'],
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'status': status,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

class TimeSlot {
  final int? id;
  final String? startTime;
  final String? endTime;
  final bool? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const TimeSlot(
      {this.id,
      this.startTime,
      this.endTime,
      this.status,
      this.createdAt,
      this.updatedAt});

  factory TimeSlot.fromJson(Map<String, dynamic> json) {
    return TimeSlot(
      id: json['id'],
      startTime: json['start_time'],
      endTime: json['end_time'],
      status: json['status'],
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'start_time': startTime,
      'end_time': endTime,
      'status': status,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  String get timeRange =>
      startTime != null && endTime != null ? '$startTime - $endTime' : '';
}

class Items {
  final int? id;
  final int? cateringId;
  final int? cateringItemId;
  final int? quantity;
  final int? sumPrice;
  final int? servingSize;
  final String? price;
  final String? photo;
  final String? title;

  const Items({
    this.id,
    this.cateringId,
    this.cateringItemId,
    this.quantity,
    this.sumPrice,
    this.servingSize,
    this.price,
    this.photo,
    this.title,
  });

  factory Items.fromJson(Map<String, dynamic> json) {
    return Items(
      id: json['id'],
      cateringId: json['catering_id'],
      cateringItemId: json['catering_item_id'],
      quantity: json['quantity'],
      sumPrice: json['sum_price'] is int
          ? json['sum_price']
          : (json['sum_price'] is double
              ? (json['sum_price'] as double).toInt()
              : null),
      servingSize: json['serving_size'],
      price: json['price'],
      photo: json['photo'],
      title: json['title'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'catering_id': cateringId,
      'catering_item_id': cateringItemId,
      'quantity': quantity,
      'sum_price': sumPrice,
      'serving_size': servingSize,
      'price': price,
      'photo': photo,
      'title': title,
    };
  }

  // Helper method to get total servings
  int get totalServings => (quantity ?? 0) * (servingSize ?? 0);
}
