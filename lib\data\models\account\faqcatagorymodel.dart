class FaqcategoryModel {
  bool? status;
  String? message;
  int? statusCode;
  FaqByCatagoryData? data;

  FaqcategoryModel({this.status, this.message, this.statusCode, this.data});

  FaqcategoryModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data =
        json['data'] != null
            ? new FaqByCatagoryData.fromJson(json['data'])
            : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class FaqByCatagoryData {
  int? total;
  int? page;
  int? limit;
  List<Faqs>? faqs;

  FaqByCatagoryData({this.total, this.page, this.limit, this.faqs});

  FaqByCatagoryData.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    page = json['page'];
    limit = json['limit'];
    if (json['faqs'] != null) {
      faqs = <Faqs>[];
      json['faqs'].forEach((v) {
        faqs!.add(new Faqs.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['total'] = this.total;
    data['page'] = this.page;
    data['limit'] = this.limit;
    if (this.faqs != null) {
      data['faqs'] = this.faqs!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Faqs {
  int? id;
  String? question;
  String? answer;
  int? categoryId;
  FaqCategory? faqCategory;

  Faqs({
    this.id,
    this.question,
    this.answer,
    this.categoryId,
    this.faqCategory,
  });

  Faqs.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    question = json['question'];
    answer = json['answer'];
    categoryId = json['category_id'];
    faqCategory =
        json['faqCategory'] != null
            ? new FaqCategory.fromJson(json['faqCategory'])
            : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['question'] = this.question;
    data['answer'] = this.answer;
    data['category_id'] = this.categoryId;
    if (this.faqCategory != null) {
      data['faqCategory'] = this.faqCategory!.toJson();
    }
    return data;
  }
}

class FaqCategory {
  int? id;
  String? title;

  FaqCategory({this.id, this.title});

  FaqCategory.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    return data;
  }
}
