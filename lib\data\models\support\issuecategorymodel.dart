class IssueCatogoryModel {
  bool? status;
  int? statusCode;
  String? message;
  IssueCategoryData? data;

  IssueCatogoryModel({this.status, this.statusCode, this.message, this.data});

  IssueCatogoryModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    statusCode = json['status_code'];
    message = json['message'];
    data =
        json['data'] != null ? IssueCategoryData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['status_code'] = statusCode;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class IssueCategoryData {
  List<IssueCategory>? data;

  IssueCategoryData({this.data});

  IssueCategoryData.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <IssueCategory>[];
      json['data'].forEach((v) {
        data!.add(IssueCategory.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class IssueCategory {
  int? id;
  String? name;
  String? role;
  bool? status;
  String? createdAt;
  String? updatedAt;

  IssueCategory({
    this.id,
    this.name,
    this.role,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  IssueCategory.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    role = json['role'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['role'] = role;
    data['status'] = status;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
