import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import '../models/meal_plan/meal_plan_db_model.dart';

class MealPlanDbHelper {
  static final MealPlanDbHelper instance = MealPlanDbHelper._init();
  static Database? _database;

  MealPlanDbHelper._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('meal_plan.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);

    return await openDatabase(
      path,
      version: 2,
      onCreate: _createDB,
    );
  }

  Future<void> _createDB(Database db, int version) async {
    // Create working days table with day columns
    await db.execute('''
      CREATE TABLE working_days(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        meal_plan_id INTEGER NOT NULL UNIQUE,
        day1_date TEXT,
        day1_completed INTEGER DEFAULT 0,
        day2_date TEXT,
        day2_completed INTEGER DEFAULT 0,
        day3_date TEXT,
        day3_completed INTEGER DEFAULT 0,
        day4_date TEXT,
        day4_completed INTEGER DEFAULT 0,
        day5_date TEXT,
        day5_completed INTEGER DEFAULT 0
      )
    ''');

    // Create meal plans table with corrected column name
    await db.execute('''
      CREATE TABLE meal_plans(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        meal_plan_id INTEGER NOT NULL,
        date TEXT NOT NULL,
        chef_id INTEGER NOT NULL,
        menu_item_id INTEGER NOT NULL,
        chef_name TEXT NOT NULL,
        chef_image TEXT NOT NULL,
        item_name TEXT NOT NULL,
        item_image TEXT NOT NULL,
        price REAL NOT NULL,
        servings TEXT NOT NULL,
        FOREIGN KEY (meal_plan_id, date) REFERENCES working_days (meal_plan_id, date)
      )
    ''');
  }

  Future<void> insertMealPlanItem(MealPlanDbModel item) async {
    final db = await database;
    await db.insert('meal_plans', item.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<Map<String, dynamic>> getFormattedMealPlanData(int mealPlanId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'meal_plans',
      where: 'meal_plan_id = ?',
      whereArgs: [mealPlanId],
    );

    // Group by date and chef_id
    Map<String, Map<String, dynamic>> groupedData = {};
    for (var item in maps) {
      String date = item['date'];
      if (!groupedData.containsKey(date)) {
        groupedData[date] = {
          'date': date,
          'chef_id': item['chef_id'],
          'items': <Map<String, dynamic>>[],
        };
      }
      groupedData[date]?['items']
          .add({'chef_menu_item_id': item['menu_item_id']});
    }

    return {
      'meal_plan_id': mealPlanId,
      'days': groupedData.values.toList(),
    };
  }

  Future<void> clearMealPlan(int mealPlanId) async {
    final db = await database;
    await db.delete(
      'meal_plans',
      where: 'meal_plan_id = ?',
      whereArgs: [mealPlanId],
    );
  }

  Future<void> initializeWorkingDays(
      int mealPlanId, String startDate, int planDuration) async {
    final db = await database;

    final List<Map<String, dynamic>> existingData = await db.query(
      'working_days',
      where: 'meal_plan_id = ?',
      whereArgs: [mealPlanId],
    );

    Map<String, int> completionStatus = {};
    if (existingData.isNotEmpty) {
      for (int i = 1; i <= 5; i++) {
        completionStatus['day${i}_completed'] =
            existingData.first['day${i}_completed'] as int? ?? 0;
      }
    }

    List<String> workingDays = [];
    DateTime currentDate = DateTime.parse(startDate);

    // Generate working days up to planDuration (capped at 5 for current schema)
    int maxDays = planDuration.clamp(1, 5); // Cap at 5 due to schema
    while (workingDays.length < maxDays) {
      if (currentDate.weekday != DateTime.saturday &&
          currentDate.weekday != DateTime.sunday) {
        workingDays.add(currentDate.toString().split(' ')[0]);
      }
      currentDate = currentDate.add(const Duration(days: 1));
    }

    // Create map for database insert, preserving completion status
    final Map<String, dynamic> daysData = {
      'meal_plan_id': mealPlanId,
      'day1_date': workingDays.length > 0 ? workingDays[0] : null,
      'day1_completed': completionStatus['day1_completed'] ?? 0,
      'day2_date': workingDays.length > 1 ? workingDays[1] : null,
      'day2_completed': completionStatus['day2_completed'] ?? 0,
      'day3_date': workingDays.length > 2 ? workingDays[2] : null,
      'day3_completed': completionStatus['day3_completed'] ?? 0,
      'day4_date': workingDays.length > 3 ? workingDays[3] : null,
      'day4_completed': completionStatus['day4_completed'] ?? 0,
      'day5_date': workingDays.length > 4 ? workingDays[4] : null,
      'day5_completed': completionStatus['day5_completed'] ?? 0,
    };

    // Insert or update working days
    await db.insert(
      'working_days',
      daysData,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<String>> getWorkingDays(int mealPlanId,
      {int planDuration = 5}) async {
    final db = await database;
    final List<Map<String, dynamic>> result = await db.query(
      'working_days',
      where: 'meal_plan_id = ?',
      whereArgs: [mealPlanId],
    );

    if (result.isEmpty) return [];

    List<String> workingDays = [];
    for (int i = 1; i <= planDuration.clamp(1, 5); i++) {
      final date = result.first['day${i}_date'] as String?;
      if (date != null && date.isNotEmpty) {
        workingDays.add(date);
      }
    }
    return workingDays;
  }

  Future<bool> isDayCompleted(int mealPlanId, int dayNumber) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'working_days',
      columns: ['day${dayNumber}_completed'],
      where: 'meal_plan_id = ?',
      whereArgs: [mealPlanId],
    );

    if (maps.isEmpty) return false;
    return maps.first['day${dayNumber}_completed'] == 1;
  }

  Future<void> markDayAsCompleted(int mealPlanId, String date) async {
    final db = await database;

    // Find which day (1-5) corresponds to this date
    for (int i = 1; i <= 5; i++) {
      final List<Map<String, dynamic>> result = await db.query(
        'working_days',
        columns: ['day${i}_date'],
        where: 'meal_plan_id = ? AND day${i}_date = ?',
        whereArgs: [mealPlanId, date],
      );

      if (result.isNotEmpty) {
        // Found the matching day, now mark it as completed
        await db.update(
          'working_days',
          {'day${i}_completed': 1},
          where: 'meal_plan_id = ?',
          whereArgs: [mealPlanId],
        );
        break;
      }
    }
  }

  Future<String?> getDay1Date(int mealPlanId) async {
    final db = await database;
    final List<Map<String, dynamic>> result = await db.query(
      'working_days',
      columns: ['day1_date'],
      where: 'meal_plan_id = ?',
      whereArgs: [mealPlanId],
    );

    if (result.isEmpty) return null;
    return result.first['day1_date'] as String;
  }

  Future<String?> getNextIncompleteDay(int mealPlanId) async {
    final db = await database;
    for (int i = 1; i <= 5; i++) {
      final List<Map<String, dynamic>> result = await db.query(
        'working_days',
        columns: ['day${i}_date', 'day${i}_completed'],
        where: 'meal_plan_id = ?',
        whereArgs: [mealPlanId],
      );

      if (result.isNotEmpty && result.first['day${i}_completed'] == 0) {
        return result.first['day${i}_date'] as String?;
      }
    }
    return null;
  }

  Future<List<MealPlanDbModel>> getAllMealPlans(int mealPlanId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'meal_plans',
      where: 'meal_plan_id = ?',
      whereArgs: [mealPlanId],
    );

    return List.generate(maps.length, (i) => MealPlanDbModel.fromMap(maps[i]));
  }

  Future<List<MealPlanDbModel>> getMealPlansByDate(
      int mealPlanId, String date) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'meal_plans',
      where: 'meal_plan_id = ? AND date = ?',
      whereArgs: [mealPlanId, date],
    );

    return List.generate(maps.length, (i) => MealPlanDbModel.fromMap(maps[i]));
  }

  Future<Map<String, List<MealPlanDbModel>>> getMealPlansByDay(
      int mealPlanId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'meal_plans',
      where: 'meal_plan_id = ?',
      whereArgs: [mealPlanId],
    );

    Map<String, List<MealPlanDbModel>> mealsByDay = {};

    for (var map in maps) {
      final meal = MealPlanDbModel.fromMap(map);
      if (!mealsByDay.containsKey(meal.date)) {
        mealsByDay[meal.date] = [];
      }
      mealsByDay[meal.date]!.add(meal);
    }

    return mealsByDay;
  }
}
