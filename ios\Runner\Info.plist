<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Eatro</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>db_eats</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app requires access to the photo library to update profile picture.</string>
	<key>NSCameraUsageDescription</key>
	<string>This app requires access to the camera to take profile picture.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs access to location to find nearby chefs.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app needs access to location to find nearby chefs.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need to access the device's location to determine the user's location.</string>
	<!-- Needed for Google Sign-In -->
	<key>CFBundleURLTypes</key>
	<array>
	  <dict>
	    <key>CFBundleTypeRole</key>
	    <string>Editor</string>
	    <key>CFBundleURLSchemes</key>
	    <array>
	      <string>com.googleusercontent.apps.627897874109-r5giqqba4du9mcn39ikco7mf9ejic3ag</string>
	    </array>
	  </dict>
	</array>

	<!-- Needed for Apple Sign-In (no extra keys required, but privacy description is recommended) -->
	<key>NSFaceIDUsageDescription</key>
	<string>This app uses Face ID to sign in with Apple.</string>

	<!-- Optional: Privacy descriptions for best practice -->
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera access for profile photos.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs photo library access for profile photos.</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>NSUserTrackingUsageDescription</key>
	<string>This identifier will be used to deliver personalized ads to you.</string>
	<key>NSPushNotificationUsageDescription</key>
	<string>This app uses notifications to keep you updated.</string>
</dict>
</plist>
