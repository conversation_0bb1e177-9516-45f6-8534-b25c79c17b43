{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98be046eac8ac584e3f4ab7dbe6f678205", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9853badd0937e18db622376423cbd4fbbf", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98331015dfab415315798cba9c4d6f85bc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e721827aa9a20099db9a2ac0960eb190", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98331015dfab415315798cba9c4d6f85bc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987c408b73a0f41bfb04677bec4019a3cc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989e1a61816f7e222f0f680ce3f2f93371", "guid": "bfdfe7dc352907fc980b868725387e98e57cdcb21e0d5df55c528a36c540fa5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d88997b2ed1f1a0b258f358bb986e15", "guid": "bfdfe7dc352907fc980b868725387e982f7e314fe18366204d629bbb9a8dcf81", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca743436507cdb3610386c0539daf7fc", "guid": "bfdfe7dc352907fc980b868725387e98cd2cf17cadf687804b1fa6480b5b1033", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b02bca023189cf1a436b9cd3afcd1b32", "guid": "bfdfe7dc352907fc980b868725387e9863c9819e1b37c6bde732fa2026a74ce8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f0cdfee7f4b78807dbc1b10da128530", "guid": "bfdfe7dc352907fc980b868725387e9869b83b4d1759e41eed07535730045200", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98484459a5638c38323361506a7fd31a4b", "guid": "bfdfe7dc352907fc980b868725387e987588441431289e761da15069c1ad4eda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858a3df5f71c786291bbcf130ae5507ca", "guid": "bfdfe7dc352907fc980b868725387e987d7e37e31c326e7dda17170fbe37ce00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b29bef3f09e92f54096cbe6b907307c2", "guid": "bfdfe7dc352907fc980b868725387e986db5c4a5ef012650ad68ac4f79fbffc2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98118d06765900992de9a2b971146dda17", "guid": "bfdfe7dc352907fc980b868725387e98fb7abcd4d8a4c35dfa16925f35179fb3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98724eea490f956290ab5a9c32892cff8c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98da8474bf0d14482e17922f533786d224", "guid": "bfdfe7dc352907fc980b868725387e98386bb272feed98f03c57e99e6cf4b725"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981901e468d5386d79b99342b3ef23e82b", "guid": "bfdfe7dc352907fc980b868725387e98e54fc242b4a4044226145bab88e73d0d"}], "guid": "bfdfe7dc352907fc980b868725387e987e2ac16a4ffed19acfef2fde142cfd2b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98ccfaf487abd877f8aec2fbc0ec756d1e"}], "guid": "bfdfe7dc352907fc980b868725387e9802bba2943b5377e50c8b581885cc07a9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98de2af249effc3c16a45ec13b2777acb5", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e984fc018f2d1ab04ff0af92b4b5ef9709e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}