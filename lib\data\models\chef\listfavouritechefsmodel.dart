class ListFavouriteChefsModel {
  bool? status;
  List<FavouriteChefData>? data;
  int? statusCode;

  ListFavouriteChefsModel({this.status, this.data, this.statusCode});

  ListFavouriteChefsModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    if (json['data'] != null) {
      data = <FavouriteChefData>[];
      json['data'].forEach((v) {
        data!.add(FavouriteChefData.fromJson(v));
      });
    }
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['status_code'] = statusCode;
    return data;
  }
}

class FavouriteChefData {
  int? chefId;
  String? chefName;
  String? chefStatus;
  String? chefPhoto;
  List<String>? chefSearchTags;
  String? dishPhoto;
  double? distanceKm;
  List<String>? operationDays;
  double? rating;
  int? preparationTime;
  int? chefTotalRatings; // Added field
  double? chefRatingPercentage; // Added field

  FavouriteChefData(
      {this.chefId,
      this.chefName,
      this.chefStatus,
      this.chefPhoto,
      this.chefSearchTags,
      this.dishPhoto,
      this.distanceKm,
      this.operationDays,
      this.rating,
      this.preparationTime,
      this.chefTotalRatings, // Added to constructor
      this.chefRatingPercentage // Added to constructor
      });

  FavouriteChefData.fromJson(Map<String, dynamic> json) {
    chefId = json['chef_id'];
    chefName = json['chef_name'];
    chefStatus = json['chef_status'];
    chefPhoto = json['chef_photo'];
    chefSearchTags = json['chef_search_tags'] != null
        ? List<String>.from(json['chef_search_tags'])
        : [];
    dishPhoto = json['dish_photo'];
    distanceKm = json['distance_km']?.toDouble();
    operationDays = json['operation_days'] != null
        ? List<String>.from(json['operation_days'])
        : [];
    rating = json['rating']?.toDouble() ?? 4.5; // Default rating
    preparationTime = json['preparation_time'] ?? 35; // Default prep time
    chefTotalRatings = json['chef_total_ratings'] ?? 0;
    chefRatingPercentage = json['chef_rating_percentage']?.toDouble() ?? 0.0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['chef_id'] = chefId;
    data['chef_name'] = chefName;
    data['chef_status'] = chefStatus;
    data['chef_photo'] = chefPhoto;
    data['chef_search_tags'] = chefSearchTags;
    data['dish_photo'] = dishPhoto;
    data['distance_km'] = distanceKm;
    data['operation_days'] = operationDays;
    data['rating'] = rating;
    data['preparation_time'] = preparationTime;
    data['chef_total_ratings'] = chefTotalRatings;
    data['chef_rating_percentage'] = chefRatingPercentage;
    return data;
  }
}
