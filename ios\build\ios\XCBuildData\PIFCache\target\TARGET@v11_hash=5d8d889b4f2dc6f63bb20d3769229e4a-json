{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d74ba8931f5d89d9924043ff9b70e42b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808e4208c569eaf04f46cc5c9ce6ea817", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba6b38401a0b68d7b07af48e31c997d0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c527835389939adc38ada85f3587ac01", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba6b38401a0b68d7b07af48e31c997d0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bf33a552e2b5879b4b4ffc4bddbf75bb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e6aec76afd744bd86845d94957b973c3", "guid": "bfdfe7dc352907fc980b868725387e98280bea49bb56eacfe70c78bec7e1ea49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8ad9b63470665d4d26165ae8a1586ba", "guid": "bfdfe7dc352907fc980b868725387e984a85d68769f76f1310222c97c78895d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890819be665b360376136a5ededac5cdb", "guid": "bfdfe7dc352907fc980b868725387e986a43bd5e78ee51d0f64df08ec8c067fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c7e89d744cb810271c452e974570e7b", "guid": "bfdfe7dc352907fc980b868725387e98213e2c4b8dd267ca29db30ea4eb92884"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1020be6243d341514cbc3423ead1509", "guid": "bfdfe7dc352907fc980b868725387e98a61c9e52b175f49de2ca157dd8dc4f7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7f4ade5da68b70ef623de6e4273789c", "guid": "bfdfe7dc352907fc980b868725387e98fd89f4fcdf6eaccf7ba42230427d4cdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981527b40baedf31ca4fb84f2248907208", "guid": "bfdfe7dc352907fc980b868725387e98cb3f2d04080747470d28bc4812fcf64d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb01076903df0ab347b1fc5a262ff094", "guid": "bfdfe7dc352907fc980b868725387e986479660da6f423da2263f6a62de3a791", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801972aa1950026827f701b313d0301c1", "guid": "bfdfe7dc352907fc980b868725387e98305814b3e1ba33c4a6639f24d4a181a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e5b04e4239b7f3cc4f6598eae5368ca", "guid": "bfdfe7dc352907fc980b868725387e984762fa3c5006e1c0c1b588ae7272fb55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a65f772745a396b62451ab5ee61dc9a", "guid": "bfdfe7dc352907fc980b868725387e987389133567d43d3125b6d1dd60628179", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98824796121fd6e19b42b7b3b0725bd24b", "guid": "bfdfe7dc352907fc980b868725387e98a7095831f9f0345a6dad4b8c6f5fddcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880009684a79d2ee214b40679dbccce0e", "guid": "bfdfe7dc352907fc980b868725387e989513238f0a1dd6c292bb3ab2226405cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98630d3f422bf8b7c8fd7554e0926b444b", "guid": "bfdfe7dc352907fc980b868725387e984fd164cdcb9769e74bb5d17c2166adec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a10330611d8bafd5d3539c4aaa8764f", "guid": "bfdfe7dc352907fc980b868725387e98be2a5a37b4d8ae7dc23884a25a261d1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9ed37915a027632c6f949a0e9f84b20", "guid": "bfdfe7dc352907fc980b868725387e98c66f2361aaf39751d30a95fa13383e12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98626fb38b758510dc840b4bbc058831a6", "guid": "bfdfe7dc352907fc980b868725387e987c5d41923f04d19599035df564f8011f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da0815f3d65743f68da6f733ba96b5d8", "guid": "bfdfe7dc352907fc980b868725387e9860a2e85642de52c741423e718e67e598"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6f3568528dc17b30b6809fd1a771db2", "guid": "bfdfe7dc352907fc980b868725387e980b5c8f8481445988bbc886e52448e665"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f90df4d54d8b2ee5c777d1a4601b060f", "guid": "bfdfe7dc352907fc980b868725387e98d077aa34a00b55c74dce7f9e4e6e2313"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb689ef734c9099f3ec465fdf808e575", "guid": "bfdfe7dc352907fc980b868725387e98dab3c6373b2ac950c405e6a3750bb0e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad19f2d22d56e02f6d608675511c640f", "guid": "bfdfe7dc352907fc980b868725387e986c1ff756569ef14d8d7f80fba81d66e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2968dc8344979cd99fdcd8e24ffb6eb", "guid": "bfdfe7dc352907fc980b868725387e985963368d702b5b62ca8352ad1dbd20a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f74a971033a88927ae2520caaf032a51", "guid": "bfdfe7dc352907fc980b868725387e98a5d0ad7f0ed5eb322b1da87be6304f51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983568aa48abca9cbf283e582aeedcc38e", "guid": "bfdfe7dc352907fc980b868725387e98352265c5d84cc577db62a703faf0b7d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c9c0e2eecb925e78fca446b3dcb5f49", "guid": "bfdfe7dc352907fc980b868725387e988ece77cc4d940b25a8e8d6dab85794cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828001f3b89646b42a7f4f8fc5c489fa0", "guid": "bfdfe7dc352907fc980b868725387e983c76486dc88d92ba46af8f939b57e22a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848e6cffc9f74824c8783265cf16e0016", "guid": "bfdfe7dc352907fc980b868725387e9885ab8f04cf11f5de0401dba95bf638a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843fd1bb786355b1e8567de9463ad0ac8", "guid": "bfdfe7dc352907fc980b868725387e98b87a2c649a445e12805961f90abefef0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6cb0625b3947553e1cf92f24c6b1f41", "guid": "bfdfe7dc352907fc980b868725387e98a47b2662040d1f45aa2707366a381003"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cd3c1c35c4fa78118d5e6f4b80052c2", "guid": "bfdfe7dc352907fc980b868725387e98aabfc4e94ff95e88344e5dd4cfa16609"}], "guid": "bfdfe7dc352907fc980b868725387e98791467009c0eee4cb63d8e952cdc0842", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9804922949d9c15ffe87e26e8bdfa33b8e", "guid": "bfdfe7dc352907fc980b868725387e984c571e90d58693e693311239e729c865"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d82a48cec496270711e172d30991419", "guid": "bfdfe7dc352907fc980b868725387e98b6747bdd32925098d99a71996e8fd6f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838ae0760b41a19e8d79f68e7dbd749bc", "guid": "bfdfe7dc352907fc980b868725387e98ed39fbcae07dbc82da96f84d25938c69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c55b8ef6c24a95d359c4a3bf1b7ac6f3", "guid": "bfdfe7dc352907fc980b868725387e98cab6decea93ef10d29b3858016d1f4db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98252eca19aaf0c027a4bbd796bd163adf", "guid": "bfdfe7dc352907fc980b868725387e987745464c5a025dab3be64c76a5d0c4d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd913a5ad4f7cb2ee634e0bfec7df513", "guid": "bfdfe7dc352907fc980b868725387e988465a235beb7ce93ecb9f9b272949502"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98013fac81ea9fe09d0e1d3f9e6759821e", "guid": "bfdfe7dc352907fc980b868725387e9851295a520a7d183f6f85cf6777d8268f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987436a31600871d6d0d2df500c393d0a0", "guid": "bfdfe7dc352907fc980b868725387e98d834b826011eb7e7c7832216a3e0ff73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e96bd542226712f160c17a720fe2f52b", "guid": "bfdfe7dc352907fc980b868725387e9817a8d9fb643a3513972eb567c235bd7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fcc96728bb523cfae3415bc2fad0983", "guid": "bfdfe7dc352907fc980b868725387e9813156ec461fdefe73d84480ba7c75352"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2870f3210d52a2562b9eb031c56c432", "guid": "bfdfe7dc352907fc980b868725387e9840736c0c49833cdacc5d5aa0633714d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c37e1ba0c0e1c74038595dadf6047096", "guid": "bfdfe7dc352907fc980b868725387e98e56b3bb1f79d2aa866f6ee0e3b772f21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b41bc948f141e812cf96e6f90fc3fd25", "guid": "bfdfe7dc352907fc980b868725387e98204e0cf7a1fbc932ba89f4e16b2a016d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982283570fbea81a151db8dce81c1411c7", "guid": "bfdfe7dc352907fc980b868725387e98ef02ca1bfc5bfefcf4775b2a3e461f2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828147f94fe871d8fde1101d1bf1a5c93", "guid": "bfdfe7dc352907fc980b868725387e98fbdb31f9d6540dc97b2003edfd4040c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dcd312ab62316c7407f5179a09fd49a", "guid": "bfdfe7dc352907fc980b868725387e98754d2b74de2d2f08633955b1a78882c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819f4ad264a014af1bc920eb4de110448", "guid": "bfdfe7dc352907fc980b868725387e98b4a70dfa7418436937edaeb3f04e069b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805b87140b4c0febe694a0600e945a333", "guid": "bfdfe7dc352907fc980b868725387e9880cc601cfcc1b7ecba6ac3fe64b024a3"}], "guid": "bfdfe7dc352907fc980b868725387e98f0eae4c78e2a0b39fe3291d206be72b5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e986942bf72373bb1245c41306148af8d87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e98a6bcacaece07228e6f339cf29f5e7e19"}], "guid": "bfdfe7dc352907fc980b868725387e9840610e2b45c20927c9ab2830580d24a2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980d8379f056d8a9d204c8e402ff37a1f7", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e986d328765c74371f70bbd38b8dffe1876", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}