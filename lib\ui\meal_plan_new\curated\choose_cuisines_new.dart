import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/bloc/newmealplan_bloc.dart';
import 'package:db_eats/data/models/meal_plan/cuisinelistingmodel.dart';
import 'package:db_eats/ui/meal_plan_new/curated/choose_dietry_new.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChooseCuisinesNew extends StatefulWidget {
  final int mealPlanId;
  const ChooseCuisinesNew({super.key, required this.mealPlanId});

  @override
  State<ChooseCuisinesNew> createState() => _ChooseCuisinesNewState();
}

class _ChooseCuisinesNewState extends State<ChooseCuisinesNew> {
  final Set<int> _selectedCuisineIds = {};
  final Set<int> _selectedSubCuisineIds = {};
  final Set<int> _selectedLocalCuisineIds = {};

  final Map<int, bool> _expandedCuisines = {};
  final Map<int, bool> _expandedSubCuisines = {};
  bool _selectAll = false;
  late final MealplanBloc _mealPlanBloc;
  List<Cuisines> cuisines = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _mealPlanBloc = BlocProvider.of<MealplanBloc>(context);
    _mealPlanBloc.add(ListCuisineEvent());
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    return MultiBlocListener(
      listeners: [
        BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is ListCuisineSuccess) {
              final cuisineData = state.data as CuisinesListModel;
              setState(() {
                cuisines = cuisineData.data?.cuisines ?? [];
              });
            }
          },
        ),
        BlocListener<NewmealplanBloc, NewMealPlanState>(
          listener: (context, state) {
            if (state is NewMealPlanStep2StateLoading) {
              setState(() {
                _isLoading = true;
              });
            } else if (state is NewMealPlanStep2StateSuccess) {
              setState(() {
                _isLoading = false;
              });
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) =>
                      ChooseDietryNew(mealPlanId: widget.mealPlanId),
                ),
              );
            } else if (state is NewMealPlanStep2StateLoadFailed) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.message)),
              );
            }
          },
        ),
      ],
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF6F3EC),
          elevation: 0,
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.transparent,
          automaticallyImplyLeading: false,
          centerTitle: true,
          title: Image.asset(
            'assets/logo.png',
            width: 112,
            height: 29,
            fit: BoxFit.contain,
          ),
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(screenHeight * 0.002),
            child: Divider(
              color: Colors.grey[300],
              height: screenHeight * 0.002,
            ),
          ),
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Close button row
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () => Navigator.of(context).pop(),
                    child: const Icon(Icons.close, size: 22),
                  ),
                ],
              ),
            ),
            // Progress bar section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Single continuous progress bar
                  Stack(
                    children: [
                      // Background bar (unfilled portion)
                      Container(
                        height: 7,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: const Color(0xFFE1DDD5),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      FractionallySizedBox(
                        widthFactor: 0.5,
                        child: Container(
                          height: 7,
                          decoration: BoxDecoration(
                            color: const Color(0xFF007A4D),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 18),
                  // Step indicator text
                  Text(
                    '2 of 4',
                    style: TextStyle(
                      fontSize: forteen,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: Color(0xFF414346),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                "Choose cuisines",
                style: TextStyle(
                  fontSize: twentyFour,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Inter',
                  color: Color(0xFF1F2122),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 7.0),
              child: Row(
                children: [
                  Checkbox(
                    value: _selectAll,
                    onChanged: (bool? value) {
                      setState(() {
                        _selectAll = value ?? false;
                        if (_selectAll) {
                          _selectedCuisineIds.addAll(cuisines
                              .map((c) => c.id ?? 0)
                              .where((id) => id != 0));

                          // Select all sub cuisines
                          for (var cuisine in cuisines) {
                            if (cuisine.subCuisines != null) {
                              _selectedSubCuisineIds.addAll(cuisine.subCuisines!
                                  .map((sc) => sc.id ?? 0)
                                  .where((id) => id != 0));

                              // Select all local cuisines
                              for (var subCuisine in cuisine.subCuisines!) {
                                if (subCuisine.localCuisines != null) {
                                  _selectedLocalCuisineIds.addAll(subCuisine
                                      .localCuisines!
                                      .map((lc) => lc.id ?? 0)
                                      .where((id) => id != 0));
                                }
                              }
                            }
                          }
                        } else {
                          // Clear all selections
                          _selectedCuisineIds.clear();
                          _selectedSubCuisineIds.clear();
                          _selectedLocalCuisineIds.clear();
                        }
                      });
                    },
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    side: const BorderSide(color: Color(0xFF1F2122)),
                    activeColor: const Color(0xFF1F2122),
                  ),
                  Text(
                    "Select All",
                    style: TextStyle(
                      fontSize: forteen,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: const Color(0xFF1F2122),
                      height: 24 / 16,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            // Divider below Select All
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Container(
                height: 1,
                color: const Color(0xFFE1DDD5),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    children: [
                      _buildTwoColumnCuisineList(),
                      const SizedBox(
                          height: 100), // Bottom padding for navigation buttons
                    ],
                  ),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: const Color(0xFFF6F3EC),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Color(0xFF1F2122)),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(28),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 19),
                    ),
                    child: const Text(
                      'Back',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _onNextPressed,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(28),
                      ),
                      padding: EdgeInsets.symmetric(
                          horizontal: _isLoading ? 32 : 20, vertical: 19),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Text(
                            'Next',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w400,
                              fontSize: 16,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // New method to build two-column cuisine list
  Widget _buildTwoColumnCuisineList() {
    List<Widget> cuisineBlocks = [];

    if (cuisines.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 32.0),
          child: CircularProgressIndicator(color: Color(0xFF007A4D)),
        ),
      );
    }

    for (int i = 0; i < cuisines.length; i += 2) {
      List<Widget> rowItems = [];

      rowItems.add(
        Expanded(
          child: _buildExpandableCuisineColumn(cuisines[i]),
        ),
      );

      if (i + 1 < cuisines.length) {
        rowItems.add(const SizedBox(width: 8)); // Add spacing between columns
        rowItems.add(
          Expanded(
            child: _buildExpandableCuisineColumn(cuisines[i + 1]),
          ),
        );
      } else {
        // If odd number of cuisines, add an empty space for the second column
        rowItems.add(const SizedBox(width: 10));
        rowItems.add(Expanded(child: Container()));
      }

      // Add the row to our list of widgets
      cuisineBlocks.add(
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: rowItems,
        ),
      );
    }

    return Column(
      children: cuisineBlocks
          .map((block) => Padding(
                padding: const EdgeInsets.only(bottom: 0),
                child: block,
              ))
          .toList(),
    );
  }

  // Build a cuisine with its expandable sub-cuisines
  Widget _buildExpandableCuisineColumn(Cuisines cuisine) {
    final hasSubCuisines = cuisine.subCuisines?.isNotEmpty == true;
    final isExpanded = _expandedCuisines[cuisine.id ?? 0] ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main cuisine item with checkbox and expansion capability
        _buildSimpleCuisineItem(cuisine),

        // Show sub-cuisines if expanded
        if (isExpanded && hasSubCuisines)
          Padding(
            padding: const EdgeInsets.only(left: 28.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: cuisine.subCuisines!.map((subCuisine) {
                final hasLocalCuisines =
                    subCuisine.localCuisines?.isNotEmpty == true;
                final isSubExpanded =
                    _expandedSubCuisines[subCuisine.id ?? 0] ?? false;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSubCuisineItem(
                        subCuisine, hasLocalCuisines, isSubExpanded, cuisine),

                    // Show local cuisines if sub-cuisine is expanded
                    if (isSubExpanded && hasLocalCuisines)
                      Padding(
                        padding: const EdgeInsets.only(left: 28.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: subCuisine.localCuisines!
                              .map((local) => _buildLocalCuisineItem(
                                  local, subCuisine, cuisine))
                              .toList(),
                        ),
                      ),
                  ],
                );
              }).toList(),
            ),
          ),
      ],
    );
  }

  // Simple cuisine item for the two-column layout
  Widget _buildSimpleCuisineItem(Cuisines cuisine) {
    final hasSubCuisines = cuisine.subCuisines?.isNotEmpty == true;
    final isExpanded = _expandedCuisines[cuisine.id ?? 0] ?? false;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1.0),
      child: Row(
        children: [
          Checkbox(
            value: _selectedCuisineIds.contains(cuisine.id),
            onChanged: (bool? value) {
              _handleParentCuisineSelection(cuisine, value);
            },
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
            side: const BorderSide(color: Color(0xFF1F2122)),
            activeColor: const Color(0xFF1F2122),
          ),
          Expanded(
            child: Text(
              cuisine.name ?? '',
              style: TextStyle(
                fontSize: forteen,
                fontWeight: FontWeight.w400,
                color: Color(0xFF1F2122),
              ),
            ),
          ),
          // Only show expansion icon if there are sub-cuisines
          if (hasSubCuisines)
            InkWell(
              onTap: () {
                setState(() {
                  _expandedCuisines[cuisine.id ?? 0] = !isExpanded;
                });
              },
              child: Icon(
                isExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: const Color(0xFF1F2122),
                size: 20,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSubCuisineItem(SubCuisines subCuisine, bool hasLocalCuisines,
      bool isSubExpanded, Cuisines parentCuisine) {
    return InkWell(
      onTap: hasLocalCuisines
          ? () {
              setState(() {
                _expandedSubCuisines[subCuisine.id ?? 0] = !isSubExpanded;
              });
            }
          : null,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 1.0),
        child: Row(
          children: [
            Checkbox(
              value: _selectedSubCuisineIds.contains(subCuisine.id),
              onChanged: (bool? value) =>
                  _handleSubCuisineSelection(subCuisine, parentCuisine, value),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              side: const BorderSide(color: Color(0xFF1F2122)),
              activeColor: const Color(0xFF1F2122),
            ),
            Expanded(
              child: Text(
                subCuisine.name ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF1F2122),
                ),
              ),
            ),
            if (hasLocalCuisines)
              Icon(
                isSubExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: const Color(0xFF1F2122),
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocalCuisineItem(LocalCuisines localCuisine,
      SubCuisines parentSubCuisine, Cuisines parentCuisine) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1.0),
      child: Row(
        children: [
          Checkbox(
            value: _selectedLocalCuisineIds.contains(localCuisine.id),
            onChanged: (value) => _handleLocalCuisineSelection(
                localCuisine, parentSubCuisine, parentCuisine, value),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
            side: const BorderSide(color: Color(0xFF1F2122)),
            activeColor: const Color(0xFF1F2122),
          ),
          Expanded(
            child: Text(
              localCuisine.name ?? '',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Color(0xFF1F2122),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _onNextPressed() {
    if (_selectedCuisineIds.isEmpty &&
        _selectedSubCuisineIds.isEmpty &&
        _selectedLocalCuisineIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one cuisine'),
          backgroundColor: Color(0xFFE11900),
        ),
      );
      return;
    }

    final cuisineData = {
      "meal_plan_id": widget.mealPlanId,
      "cuisine_ids": _selectedCuisineIds.toList(),
      "subcuisine_ids": _selectedSubCuisineIds.toList(),
      "localcuisine_ids": _selectedLocalCuisineIds.toList(),
    };

    context.read<NewmealplanBloc>().add(NewStep2MEalPlanEvent(cuisineData));
  }

  void _handleLocalCuisineSelection(LocalCuisines localCuisine,
      SubCuisines parentSubCuisine, Cuisines parentCuisine, bool? selected) {
    setState(() {
      if (selected ?? false) {
        // Select local cuisine
        _selectedLocalCuisineIds.add(localCuisine.id ?? 0);
        // Select parent sub cuisine
        _selectedSubCuisineIds.add(parentSubCuisine.id ?? 0);
        // Select parent cuisine
        _selectedCuisineIds.add(parentCuisine.id ?? 0);
      } else {
        // Unselect local cuisine
        _selectedLocalCuisineIds.remove(localCuisine.id);
        // Unselect parent sub cuisine if no local cuisines are selected
        if (!parentSubCuisine.localCuisines!
            .any((lc) => _selectedLocalCuisineIds.contains(lc.id))) {
          _selectedSubCuisineIds.remove(parentSubCuisine.id);
        }
        // Unselect parent cuisine if no sub cuisines are selected
        if (!parentCuisine.subCuisines!
            .any((sc) => _selectedSubCuisineIds.contains(sc.id))) {
          _selectedCuisineIds.remove(parentCuisine.id);
        }
      }
    });
  }

  void _handleSubCuisineSelection(
      SubCuisines subCuisine, Cuisines parentCuisine, bool? selected) {
    setState(() {
      if (selected ?? false) {
        // Select sub cuisine
        _selectedSubCuisineIds.add(subCuisine.id ?? 0);
        // Select parent cuisine
        _selectedCuisineIds.add(parentCuisine.id ?? 0);
        // Select all local cuisines
        if (subCuisine.localCuisines != null) {
          _selectedLocalCuisineIds.addAll(subCuisine.localCuisines!
              .map((lc) => lc.id ?? 0)
              .where((id) => id != 0));
        }
      } else {
        // Unselect sub cuisine
        _selectedSubCuisineIds.remove(subCuisine.id);
        // Unselect all local cuisines
        if (subCuisine.localCuisines != null) {
          for (var lc in subCuisine.localCuisines!) {
            _selectedLocalCuisineIds.remove(lc.id);
          }
        }
        // Unselect parent cuisine if no sub cuisines are selected
        if (!parentCuisine.subCuisines!
            .any((sc) => _selectedSubCuisineIds.contains(sc.id))) {
          _selectedCuisineIds.remove(parentCuisine.id);
        }
      }
    });
  }

  void _handleParentCuisineSelection(Cuisines parentCuisine, bool? selected) {
    setState(() {
      if (selected ?? false) {
        // Select parent cuisine
        _selectedCuisineIds.add(parentCuisine.id ?? 0);

        // Select all sub cuisines
        if (parentCuisine.subCuisines != null) {
          for (var subCuisine in parentCuisine.subCuisines!) {
            _selectedSubCuisineIds.add(subCuisine.id ?? 0);

            // Select all local cuisines under each sub cuisine
            if (subCuisine.localCuisines != null) {
              _selectedLocalCuisineIds.addAll(subCuisine.localCuisines!
                  .map((lc) => lc.id ?? 0)
                  .where((id) => id != 0));
            }
          }
        }
      } else {
        // Unselect parent cuisine
        _selectedCuisineIds.remove(parentCuisine.id);

        // Unselect all sub cuisines
        if (parentCuisine.subCuisines != null) {
          for (var subCuisine in parentCuisine.subCuisines!) {
            _selectedSubCuisineIds.remove(subCuisine.id);

            // Unselect all local cuisines under each sub cuisine
            if (subCuisine.localCuisines != null) {
              for (var localCuisine in subCuisine.localCuisines!) {
                _selectedLocalCuisineIds.remove(localCuisine.id);
              }
            }
          }
        }
      }
    });
  }
}
