import 'package:flutter/material.dart';

class DottedDivider extends StatelessWidget {
  final double height;
  final Color color;

  const DottedDivider({
    this.height = 18,
    this.color = const Color(0xFFE1E3E6),
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      child: Center(
        child: LayoutBuilder(
          builder: (context, constraints) {
            final boxWidth = constraints.constrainWidth();
            const dashWidth = 2.0;
            const dashSpace = 2.5;
            final dashCount = (boxWidth / (dashWidth + dashSpace)).floor();

            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: List.generate(dashCount, (_) {
                return Container(
                  width: dashWidth,
                  height: 1,
                  color: color,
                );
              }),
            );
          },
        ),
      ),
    );
  }
}
