{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989aa3dcc9a94d398ffed6609389331bad", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c5a8c72169b97027ba1a9c8a02f61207", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c5a8c72169b97027ba1a9c8a02f61207", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a058479175326ca7d96f851084bbf05a", "guid": "bfdfe7dc352907fc980b868725387e98e37370358cdd8bd852c6ecd29d1c6699", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1424dd542052f1c98470af18da445cf", "guid": "bfdfe7dc352907fc980b868725387e98c65ba688da72266cc96899ded9209799", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9b46d632b0c7c92723f4bc50b4b7939", "guid": "bfdfe7dc352907fc980b868725387e980b056ed7b6b99f8533fec1d37701b716", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982724bdde9f0391f5cdb14c99e458cfbc", "guid": "bfdfe7dc352907fc980b868725387e9815ab9e1123f6342cc131b55219cae95e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a5844866082f1bfd59acd9c126a0c5a", "guid": "bfdfe7dc352907fc980b868725387e98728fa087c1df983a5776c90c2ad32f0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab2c418dc0d2c310992b4a1acd79a104", "guid": "bfdfe7dc352907fc980b868725387e9839a0717184882b751f041be102c19634", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d3456a850ddf79ca3eb5e8cee80eb9a", "guid": "bfdfe7dc352907fc980b868725387e9890e24b099a4a172ddf354c0bc757e22b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896f12cb7e79e7e7effbbd5952ac3119e", "guid": "bfdfe7dc352907fc980b868725387e98ff295f0cbbbfff612a1e6f0e7954818d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6f37f341432d3610c8182e24d2a5fb4", "guid": "bfdfe7dc352907fc980b868725387e98072a14fe78636dcbdda404cfbb314ea4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871e559d47112dd65e509d470066f1ee5", "guid": "bfdfe7dc352907fc980b868725387e98a482eaf54f776bc628053d383c33a7a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989659a6ab4c7e878e4d28fbf37c0f9047", "guid": "bfdfe7dc352907fc980b868725387e98a9f7fdd85aca408013c7e62caebfdfa4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4f570eca00a0fdff14c57dfb8458b09", "guid": "bfdfe7dc352907fc980b868725387e9814deb33597fcd96bb77bf5ee3918a74b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98022cfdbe20adee5e1453e2caeac379b6", "guid": "bfdfe7dc352907fc980b868725387e98cb363a67bc779b208d6c566751fa38c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf8498f18c1adfb87bd1646ef6ef4ed5", "guid": "bfdfe7dc352907fc980b868725387e98716042837ddd330b76e4d1536dc5312e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98568b07d8fad137f0cb6af6527309f3df", "guid": "bfdfe7dc352907fc980b868725387e982e54e546bc4a81e9db10cc68946fbe94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbb051c6137e6e54bde43099c1e3b61e", "guid": "bfdfe7dc352907fc980b868725387e98c20b87d9fb4d46d9a2b74b6dbe0cac0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbc3db2886e34edaedfbe46c7d75d097", "guid": "bfdfe7dc352907fc980b868725387e98ff08a61b058b5ec2e421ebefc03205cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f2878a5314b193b2b8fc13af49de24c", "guid": "bfdfe7dc352907fc980b868725387e98243dc5e59d20b619b46287f6653881ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98630afc214cce6dd34cadb87e2b641741", "guid": "bfdfe7dc352907fc980b868725387e98b7419b4b5b3e5c0c3fdd93fb14972121", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af8618c282ef37bf4f47d8b776688e41", "guid": "bfdfe7dc352907fc980b868725387e9851d632d6a2d05235d081558e2f536733", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98725cfcd39e3f5b51c9112c65ea9e91d7", "guid": "bfdfe7dc352907fc980b868725387e984d34f4c346c4f1da8e7fc2e72b19d4f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c27617e71b39b851b65ae5f3bc266ce8", "guid": "bfdfe7dc352907fc980b868725387e9896fae0012b96ffd3543779cd02b0050b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5362ee055fc0b906181edca9984991c", "guid": "bfdfe7dc352907fc980b868725387e988cb4dcd87b2f0b7364b4d4163de5a067"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804035fc54a204ca6a3940f5ccca49d8b", "guid": "bfdfe7dc352907fc980b868725387e98f3dcb2fcce36b41792f1f08053ca2c08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987619bfc57f99b2639612b57e3c651a0f", "guid": "bfdfe7dc352907fc980b868725387e98e8c897a93b6a47264dd51ac9e2c6a04d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5f49e6ddee9c5744fed9d4a505bc577", "guid": "bfdfe7dc352907fc980b868725387e985f1e7ae11f39dd25ad8b1164f365222e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868bca498da25c8cb505e186782172232", "guid": "bfdfe7dc352907fc980b868725387e98a11730584e196fde0a5136c7d4362936"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984516457acaa153979efcd26d4b5bc9de", "guid": "bfdfe7dc352907fc980b868725387e9881c667a42d9ccb55da19e7d57ed6785c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da9874182b6d0e3a038e9ca83d4bc613", "guid": "bfdfe7dc352907fc980b868725387e9849d41e022dfad9f58c9ba2e8695065dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98113c470ff435818426a52745f0b14c4b", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804f3ca27bdab1c6597384b882f64da01", "guid": "bfdfe7dc352907fc980b868725387e98048d6aa51e941f690dd97c41690fe291"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3c8c9996c7435756ef32e815e1a40c6", "guid": "bfdfe7dc352907fc980b868725387e9889e5900cd748b720870db624f23b8de5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaad6c4eaaff5475fb58a67154fddb5d", "guid": "bfdfe7dc352907fc980b868725387e9825f7bcc81a598ac43b43d6acd38fb779"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e43a95c9bf60659724bb8f3230921443", "guid": "bfdfe7dc352907fc980b868725387e98bc4c66aa1954c19129acd8de26620a19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ad1664ababc512f3faf6637fa898d7f", "guid": "bfdfe7dc352907fc980b868725387e9805765735996844fda4b63fcc155f1c21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e538cf1c22b56b4801894b93d2004e57", "guid": "bfdfe7dc352907fc980b868725387e9832bea6933864bce1eeb84097f0ba450c"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}