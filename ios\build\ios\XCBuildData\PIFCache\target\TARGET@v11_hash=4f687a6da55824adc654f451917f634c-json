{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c9612d2c7815b0bbe2aab76138d9f793", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e2277be28a487919925c019eadbc3d4d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9881accfd4ad5cbff8194f719ede8fa9ee", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9837d6095d2edb1b19ee11d5bb60efcadb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9881accfd4ad5cbff8194f719ede8fa9ee", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98266c1528ba49ac2bac63a560ee690878", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9880590530b548015c140ee055074d271b", "guid": "bfdfe7dc352907fc980b868725387e98e266072598b9c98e52aea7bbded35295", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9800d50e50f4ef8e57b903ef311be596b5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9863a032c2d711ec6a15f4ba749870aabf", "guid": "bfdfe7dc352907fc980b868725387e985e71767b2cf6bcbe4aa7e814e13620dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98492410ad979f8abcddb774642fe5d06b", "guid": "bfdfe7dc352907fc980b868725387e9831525010bfb1b1e0c5d26fea61e477e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ae819149e1922648110534a40168b66", "guid": "bfdfe7dc352907fc980b868725387e98b57b5816d65fc28a088d58c468741c6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5af6c3b5a5dacda2ab35fda5f4a4e7e", "guid": "bfdfe7dc352907fc980b868725387e988211440440856f50cbf26c7badc1b866"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980acb86bd3de7001aba9e8ea02df32b62", "guid": "bfdfe7dc352907fc980b868725387e9835e53fb2e560775f470c652d9b579a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b26dec866cf4127ba813fe38daacd5d1", "guid": "bfdfe7dc352907fc980b868725387e986794b6c0939151035c2ab274f1711125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0b3c71bc33fb34bbfeae2be3b12051f", "guid": "bfdfe7dc352907fc980b868725387e98f34a32fc4451160f848d719fc5f81f12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f7d84f1da628a04dea417efb17d522b", "guid": "bfdfe7dc352907fc980b868725387e988e64943a42cf12277e5c7e4f72f2bf24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849c9a1734832c83f651869c15519451f", "guid": "bfdfe7dc352907fc980b868725387e98a7f262082f40c1e9a308fa29c64e0cfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982630734b5eeed35466f8fd4bd36afe1b", "guid": "bfdfe7dc352907fc980b868725387e98e83c626b280a1984826e0a8fec0e8118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8abeabbb4692442e72f5a1ca1486cf3", "guid": "bfdfe7dc352907fc980b868725387e98d22b444d6d153e8f3e7e449aefa370eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cac67481407c70f1512ff92682b7cd4", "guid": "bfdfe7dc352907fc980b868725387e98089f83e5952fce913475cd95c38bd99c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfa7d274983a7f204f74244dc44c1f09", "guid": "bfdfe7dc352907fc980b868725387e9890506ea18e7f21cc71daca82d4d212cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e0f91c8bcc990f4fe35ec315c0d8970", "guid": "bfdfe7dc352907fc980b868725387e98dea4509592aa0c6fabb9766f37c3e38e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b76d83116ec6181fc56c13eaba5b8533", "guid": "bfdfe7dc352907fc980b868725387e987c4c617ae775f307055be664aec1c2ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cc23406a9a7caaeaa5ea9b6b1b7ad25", "guid": "bfdfe7dc352907fc980b868725387e984dc26de453d9cfa4e5eadaa36416751b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870d657f3970d564eb5b9c8e88ca86062", "guid": "bfdfe7dc352907fc980b868725387e98ab2ce06f23ea3006eb332485697eb9c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1743be139070cb270aa2d77cff9848a", "guid": "bfdfe7dc352907fc980b868725387e9845d840f476efcee1af7c183ac5d9af8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c32cbd6c52a5c1226eda5d4ec789f31", "guid": "bfdfe7dc352907fc980b868725387e98dce3ee724f424304a4d660f38a6944da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b0b7e55e34107b3693819ab4050caa1", "guid": "bfdfe7dc352907fc980b868725387e98a73c04e13e52b41ee070d2a467c1e6f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810bc5011dd08e68b009f8670c1c1c01c", "guid": "bfdfe7dc352907fc980b868725387e9816762b1be109f65eabe0994f9ca493b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864a936a8ccd2a345c4b24028a8acab04", "guid": "bfdfe7dc352907fc980b868725387e98e8bd2430c067efd14e4ffb7200b3f64b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afa170b162d52067077082853b305f56", "guid": "bfdfe7dc352907fc980b868725387e98718446df57e9895d602c6fcd3e141240"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a1ecc245604772fb35b9509593936eb", "guid": "bfdfe7dc352907fc980b868725387e98131857d7b303a6374fbc9fd721e9cfba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98243c0a8b66b67e706f680cbea047b408", "guid": "bfdfe7dc352907fc980b868725387e987dd8c291d9a9e17029c957748eafbb17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b2b6bad5721fc8a98c6878c758976ab", "guid": "bfdfe7dc352907fc980b868725387e98f3e08e667b79ef42c07eddba7c2a976b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811379d7a0278b77eecdd80f7527f2114", "guid": "bfdfe7dc352907fc980b868725387e98fcb311f8096cd0a656a7902a194258f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e539238ed94ead82b284e2622ad419b0", "guid": "bfdfe7dc352907fc980b868725387e9801de043157615b1222c171fbba127ae8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98555ef5f7672e2f29b01bc0462942853a", "guid": "bfdfe7dc352907fc980b868725387e98b729a65c86fa445602b60f2c0498ba91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba7cc074695c18481cde8c274165c500", "guid": "bfdfe7dc352907fc980b868725387e989622e82a87fdc3d8adc1917f6d0cfad9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874c1acd88fb5d64fb26065e8be9d43a0", "guid": "bfdfe7dc352907fc980b868725387e9811424d6f02786bae164d9359f3e22d68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852badb78725e59fa8ccc722c7033affa", "guid": "bfdfe7dc352907fc980b868725387e9871026481ccbfab3f1beb1d91210e6617"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f8e0aceb8ccedfba9bb474b5e870ef2", "guid": "bfdfe7dc352907fc980b868725387e98280087e742826f7b7aa41c9c25d2a697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98864c3f3f4e72c7b5e435fb0d69b75155", "guid": "bfdfe7dc352907fc980b868725387e9839d75e19d3f22529f017625e9384a1a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5b4923881596f3f02bed3b438c7248c", "guid": "bfdfe7dc352907fc980b868725387e98a1d55e0acf299eded1f99b334bf3e857"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980382b8b9a12abb9be5d544896c148ff5", "guid": "bfdfe7dc352907fc980b868725387e98e2d4a1e3d1aa90bcc74e288af5e4ebb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e0f2f07d39c7cc282a851365eaee57a", "guid": "bfdfe7dc352907fc980b868725387e98ab91b99606cee030e5afce8505e821a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98425732d08e4e11d45ed0191910f139fc", "guid": "bfdfe7dc352907fc980b868725387e98910412d9f5b863c249eefa42c85fd50b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edb7436f83bd2ff4b91ddc4f6eda4bc7", "guid": "bfdfe7dc352907fc980b868725387e98467532b9e6540a51e9c961700d776523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804b614f21f63513a847e16f33544e515", "guid": "bfdfe7dc352907fc980b868725387e9851245b6b9117d368fcfabcace1603a5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cd5fc778cad3e045bb3d8ba2934ae42", "guid": "bfdfe7dc352907fc980b868725387e983c69a79206e5b87de2802ec2a2391d53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98090b71144f8b0a65811d9547f1ff2218", "guid": "bfdfe7dc352907fc980b868725387e98187490859cb24e2d0e1921e651444b9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d8376aef1c2a239d5892f15212d26f7", "guid": "bfdfe7dc352907fc980b868725387e98c915c74d9f0a28227f77a2c0186bc091"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ade9ba3992764b71a0ad5002bcb7fd9", "guid": "bfdfe7dc352907fc980b868725387e98d646c134a27df478370327c27db28a44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff1281608bc993516489ab97a8ca258", "guid": "bfdfe7dc352907fc980b868725387e98940ad400b820ce8802bee9b0489197e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d84d66230c41e84c96401b3ac7707d24", "guid": "bfdfe7dc352907fc980b868725387e98c407e37b00ccd454776e0c1afc59af12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98701a6d9ba6d8cde5850871c32043b2cf", "guid": "bfdfe7dc352907fc980b868725387e9868344afce35baf05114ccfd51ee8f325"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b236f8e3b7b3183325e7de7c425169", "guid": "bfdfe7dc352907fc980b868725387e98bed9c58ac8bd697d31385454670d3f23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3321cf5469435986850e2889b30ba85", "guid": "bfdfe7dc352907fc980b868725387e98a8d3ed72e68273aa9b45af1482d0ab81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d79844bf10d294c3452d27a872c85507", "guid": "bfdfe7dc352907fc980b868725387e9811c557859e61ed80dc9c2a1d4eba9969"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98206ca19bb911b028e54fedd7c18c52cc", "guid": "bfdfe7dc352907fc980b868725387e982650e6f6a724d30b892cf5ae66b63637"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981214ce9ba5e52ee186fe4f3df168ae92", "guid": "bfdfe7dc352907fc980b868725387e988a819307a82baa0e057f01e2c5c8fda4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce0439a72ff544dc635eae21ce88e379", "guid": "bfdfe7dc352907fc980b868725387e98d1b72562bc1b600823e710486bfd29b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4533df12e6379af292798c0c753e12c", "guid": "bfdfe7dc352907fc980b868725387e9819f99e8789ad2b451e63645768cb993b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98763429f7604f1c04008948acac7ce25f", "guid": "bfdfe7dc352907fc980b868725387e98cbd4ed178572905e5f79227502c26d9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988105bd9f5156ec4689342fe167449814", "guid": "bfdfe7dc352907fc980b868725387e98300021b9b9a4010277e262c7b1bdd899"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d5181210a61c3940afb2dd301b71d80", "guid": "bfdfe7dc352907fc980b868725387e9840d414d6d90bca8c0a826d3e7f08c4b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff53243e78a544cf7b35269a0162785e", "guid": "bfdfe7dc352907fc980b868725387e9878d11d2e119bfc334ced087f3d45b874"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b32f52647a27a286ad51928c7ac2eb70", "guid": "bfdfe7dc352907fc980b868725387e98d2c807268d32c38e3aaa6a836f7c4ef7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a13aeb522d50fb57bb421272d53053e", "guid": "bfdfe7dc352907fc980b868725387e98549a86644f77b1feb8b2c94f79d3f194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a6049ae6eedf25b8a2841f3440d3130", "guid": "bfdfe7dc352907fc980b868725387e986f82fe15cd763d022a41430a93da8a2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f5da420a244ba07856476e71178fa0e", "guid": "bfdfe7dc352907fc980b868725387e981130f662e555066c50717a8a8c57c0e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da34d9f3e28d94f3c499787ea222b486", "guid": "bfdfe7dc352907fc980b868725387e9810fbd4abdf5ccfa1e6d9e2f53193bad0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b2c01a29876880e2a69150070333a45", "guid": "bfdfe7dc352907fc980b868725387e98ddb97ae0d0784417cf2d6521ed00a178"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d03823a9c8e635f49b1c40837e5fc358", "guid": "bfdfe7dc352907fc980b868725387e98fe230647cd1411ecd5dc2bf2a9d4e2a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864ccdfcfe74b3e3df4cc75c9c13b0a04", "guid": "bfdfe7dc352907fc980b868725387e984da98442a79ed8a0e1387a0ce5b27b1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830cb3e318805500a90e5397c4638e630", "guid": "bfdfe7dc352907fc980b868725387e98b6d16efc003dad8f3653a0e13ef727ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98942eb980544679c7978ac5dfc7eb7d0b", "guid": "bfdfe7dc352907fc980b868725387e981dc23601da91a33d58485de82cac566e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dcf3fed5ebb7957ee1de17c78887c9d", "guid": "bfdfe7dc352907fc980b868725387e98ff7d8badc1c9d5ee2e1d88269964c805"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853fcea8fc659d40e46b8a52ad34e5c5c", "guid": "bfdfe7dc352907fc980b868725387e98c5f061912801e82f57aa72405a11cc49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984db23cfd0e7b99b463aed58a6b74d3ea", "guid": "bfdfe7dc352907fc980b868725387e98372ebd4fa8fe796651da1765a5891a57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e3efdaf66a353dba78322622d86bab5", "guid": "bfdfe7dc352907fc980b868725387e98eb20c013caa5de6a362fdf6ac6c9943a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98712ed25a693067e768dd136e07bf01e6", "guid": "bfdfe7dc352907fc980b868725387e98c871d1ce93c5ed836a06e670bc9f96c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee194a2941a30da06ff5562f25c150ce", "guid": "bfdfe7dc352907fc980b868725387e981f2144de32952af0363dea8b5ef341d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c2cab4934bd4ef0524c36dc1ce27888", "guid": "bfdfe7dc352907fc980b868725387e986448cc18fccf832b75437980db5a5e21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983024fe48a473ad971d70abf6295d73a2", "guid": "bfdfe7dc352907fc980b868725387e986c25ace66908c54f0b43bf2f789111f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fbb93adaa84f430acc9f1abe7b778de", "guid": "bfdfe7dc352907fc980b868725387e980774b6b4832c0461a0f8629ee85b1feb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8cce133878dbafe3f534d97e8015109", "guid": "bfdfe7dc352907fc980b868725387e984bd29858a3836dbea44f997fbdd4e1b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98755a4d067e0b53cb9ac6f7009ad547a1", "guid": "bfdfe7dc352907fc980b868725387e982e213790a9afe76ce56981f6648c8212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989758eb08b69c28bbf76a41e2ff5fa2b1", "guid": "bfdfe7dc352907fc980b868725387e98a3b60caba9104f2262e0e30bd6e565ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f565071fe41beea690b7c99c2ba996d", "guid": "bfdfe7dc352907fc980b868725387e981fcad40ff117428162e99a7a25ae0c41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984226b31ce9b4c9525ca232bb59ad04ad", "guid": "bfdfe7dc352907fc980b868725387e98ce34395ba64614458fc90b5bd4385cf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c996be83995746251d729170cb845bd9", "guid": "bfdfe7dc352907fc980b868725387e985a90a85f1d2f7d5513c41ebb2947feeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f30219e57913e6765acf583b08968a30", "guid": "bfdfe7dc352907fc980b868725387e985145a37f733cb33167c13152e5b57b13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98100d2512aab9f160838c1560820fd267", "guid": "bfdfe7dc352907fc980b868725387e98367ba72a0bdff4fe465a78e65e2586e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835d512565f3f722558af7c8ce7818a68", "guid": "bfdfe7dc352907fc980b868725387e9892ca5dcf5d43dba7241c5593929aafee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e43312e581d22e4487b7f51d0a0b1b1e", "guid": "bfdfe7dc352907fc980b868725387e98bb52774805f03428eb40a2f9b2147a86"}], "guid": "bfdfe7dc352907fc980b868725387e98997d1f3320b96a489bbfeca42b5f2b52", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9820ad3732878a4721ac229b7e03e0956a"}], "guid": "bfdfe7dc352907fc980b868725387e9814822ff19035a835afeb657bb4dcb35b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c9cbb956b62333c02329180fbe03b825", "targetReference": "bfdfe7dc352907fc980b868725387e9890f568fc9b811cd56b08e401eec0f35e"}], "guid": "bfdfe7dc352907fc980b868725387e98b78f48222917d3611c7c397d7f9ae63e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9890f568fc9b811cd56b08e401eec0f35e", "name": "rive_common-rive_common_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9849c5bdbe31e3b466d37bab9271baf60a", "name": "rive_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9884d4dff26bf6b3e66f738069c7476896", "name": "rive_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}