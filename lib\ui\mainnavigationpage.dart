import 'package:db_eats/ui/catering/cateringrequest.dart';
import 'package:db_eats/ui/chef/chef_info.dart';
import 'package:db_eats/ui/demo_cateringpage.dart';
import 'package:db_eats/ui/home2.dart';
import 'package:db_eats/ui/messages/messages.dart';
import 'package:db_eats/ui/orders/ongoinglist.dart';
import 'package:db_eats/ui/profile/account.dart';
import 'package:db_eats/widgets/navbar.dart';
import 'package:flutter/material.dart';

class MainNavigationScreen extends StatefulWidget {
  final int startIndex;

  const MainNavigationScreen({this.startIndex = 0});

  @override
  _MainNavigationScreenState createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  late int _currentIndex;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.startIndex;
    _pageController = PageController(initialPage: _currentIndex);
  }
  

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    _pageController.jumpToPage(index);
    setState(() => _currentIndex = index);
  }

  void _onPageChanged(int index) {
    setState(() => _currentIndex = index);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: _onPageChanged,
        children: [
          Home2(),
          Ongoinglist(),
          DemoCateringPage(),
          MessagesPage(),
          AccountPage(),
        ],
        physics: NeverScrollableScrollPhysics(), // Disable swipe if you want
      ),
      bottomNavigationBar: CustomBottomNavBar(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
      ),
    );
  }
}


class WithNavBar extends StatelessWidget {
  final Widget child;
  final int currentIndex;

  const WithNavBar({required this.child, required this.currentIndex});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: CustomBottomNavBar(
        currentIndex: currentIndex,
        onTap: (index) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (_) => MainNavigationScreen(startIndex: index)),
            (route) => false,
          );
        },
      ),
    );
  }
}
