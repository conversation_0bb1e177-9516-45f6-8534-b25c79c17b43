import 'dart:io';
import 'package:db_eats/server/serverhelper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:db_eats/bloc/chef_block.dart';

class ChefReviewPage extends StatefulWidget {
  final String chefName;
  final String chefImage;
  final int chefId;
  final int orderId;

  const ChefReviewPage({
    Key? key,
    required this.chefName,
    required this.chefImage,
    required this.chefId,
    required this.orderId,
  }) : super(key: key);

  @override
  State<ChefReviewPage> createState() => _ChefReviewPageState();
}

class _ChefReviewPageState extends State<ChefReviewPage> {
  int _rating = 0;
  List<File> _selectedImages = [];
  bool _isLoading = false;
  final ImagePicker _picker = ImagePicker();
  final List<String> _attributes = [
    'Taste',
    'Fresh',
    'Packaging',
    'Followed Instructions',
    'Preparation Time',
    'Price',
  ];
  final Set<String> _selectedAttributes = {};
  final TextEditingController _commentController = TextEditingController();

  double getResponsiveSize(BuildContext context,
      {double small = 12,
      double medium = 16,
      double large = 20,
      double xlarge = 24}) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return small;
    if (width < 600) return medium;
    if (width < 900) return large;
    return xlarge;
  }

  EdgeInsets getResponsivePadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final isLandscape = width > height;

    if (width < 360) {
      return EdgeInsets.all(width * 0.03);
    } else if (width < 600) {
      return EdgeInsets.all(width * 0.04);
    } else {
      return EdgeInsets.all(isLandscape ? width * 0.03 : width * 0.05);
    }
  }

  Future<void> _pickImage() async {
    try {
      final List<XFile> images = await _picker.pickMultiImage();
      if (images.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(images.map((xFile) => File(xFile.path)));
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to pick images: $e')),
      );
    }
  }

  void _submitReview() async {
    if (_rating == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a rating')),
      );
      return;
    }

    if (_selectedImages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one photo')),
      );
      return;
    }

    try {
      final data = {
        'chef_id': widget.chefId,
        'order_id': widget.orderId,
        'star_rating': _rating,
        'comment': _commentController.text,
        // 'attributes': _selectedAttributes.toList(),
      };

      setState(() => _isLoading = true);
      context.read<ChefBloc>().add(AddChefRatingEvent(data, _selectedImages));
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error submitting review: $e')),
      );
    }
  }

  void _addAttributeToComment(String attribute, bool isSelected) {
    final currentText = _commentController.text;

    if (isSelected) {
      // Remove the attribute and comma if present
      final attributePattern = RegExp(
          r'$attribute,\s*|,\s*$attribute|^$attribute(?=\s|$)'
              .replaceAll(r'$attribute', attribute));
      _commentController.text = currentText
          .replaceAll(attributePattern, '')
          .replaceAll(RegExp(r'^,\s*|,\s*$'), '')
          .replaceAll(RegExp(r'\s*,\s*'), ', ');
    } else {
      // Add new attribute with comma
      if (currentText.isEmpty) {
        _commentController.text = attribute;
      } else {
        _commentController.text =
            currentText.isEmpty ? attribute : '$currentText, $attribute';
      }
    }

    // Set cursor position after the newly added text
    _commentController.selection = TextSelection.fromPosition(
      TextPosition(offset: _commentController.text.length),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;

    final maxContentWidth = size.width > 900
        ? 800.0
        : size.width > 600
            ? 600.0
            : size.width;

    final baseTextSize = getResponsiveSize(context,
        small: 12, medium: 14, large: 16, xlarge: 18);
    final headingTextSize = baseTextSize * 1.4286;
    final subheadingTextSize = baseTextSize * 1.1429;
    final captionTextSize = baseTextSize * 0.8571;

    final contentPadding = getResponsivePadding(context);
    final itemSpacing = size.height * 0.015;

    final avatarRadius = isLandscape
        ? size.height * 0.08
        : size.width * (size.width < 600 ? 0.08 : 0.06);

    return BlocListener<ChefBloc, ChefState>(
      listener: (context, state) {
        if (state is ChefRatingLoading) {
          setState(() => _isLoading = true);
        } else if (state is ChefRatingAdded) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
          Navigator.pop(context);
        } else if (state is ChefRatingError) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        body: SafeArea(
          child: Center(
            child: LayoutBuilder(
              builder: (context, constraints) {
                return SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: EdgeInsets.symmetric(
                    vertical: size.height * 0.02,
                    horizontal: size.width * 0.04,
                  ),
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: maxContentWidth,
                    ),
                    child: Column(
                      children: [
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.1),
                                spreadRadius: 1,
                                blurRadius: 5,
                              ),
                            ],
                          ),
                          padding: contentPadding,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              FittedBox(
                                fit: BoxFit.scaleDown,
                                child: Text(
                                  'How was the food from\n${widget.chefName}?',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: headingTextSize,
                                    fontFamily: 'Inter-Semibold',
                                    color: Color(0xFF000000),height: 1.28
                                  ),
                                ),
                              ),
                              SizedBox(height: itemSpacing),
                              CircleAvatar(
                                radius: avatarRadius,
                                backgroundColor: const Color(0xFFE1E3E6),
                                child: ClipRRect(
                                  borderRadius:
                                      BorderRadius.circular(avatarRadius),
                                  child: Image.network(
                                    ServerHelper.imageUrl + widget.chefImage,
                                    width: avatarRadius * 2,
                                    height: avatarRadius * 2,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Icon(
                                        Icons.person,
                                        size: avatarRadius,
                                        color: Colors.grey[400],
                                      );
                                    },
                                  ),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.7),
                              Text(
                                widget.chefName,
                                style: TextStyle(
                                  fontSize: subheadingTextSize,
                                  fontFamily: 'Inter-Medium',
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing),
                              Wrap(
                                spacing: size.width * 0.02,
                                children: List.generate(5, (index) {
                                  return GestureDetector(
                                    onTap: () =>
                                        setState(() => _rating = index + 1),
                                    child: Icon(
                                      _rating > index
                                          ? Icons.star
                                          : Icons.star_border,
                                      size: isLandscape
                                          ? size.height * 0.07
                                          : size.width *
                                              (size.width < 600 ? 0.13 : 0.06),
                                      color: const Color(0xFFFFBE16),
                                    ),
                                  );
                                }),
                              ),
                              SizedBox(height: itemSpacing * 0.7),
                              Divider(
                                color: Color(0xFFE1E3E6),
                                thickness: 1,
                              ),
                              SizedBox(height: itemSpacing * 0.7),
                              Container(
                                width: double.infinity,
                                alignment: Alignment.center,
                                child: Wrap(
                                  spacing: size.width * 0.02,
                                  runSpacing: itemSpacing,
                                  alignment: WrapAlignment.center,
                                  children: _attributes.map((attribute) {
                                    final isSelected =
                                        _selectedAttributes.contains(attribute);
                                    return GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          final isSelected = _selectedAttributes
                                              .contains(attribute);
                                          if (isSelected) {
                                            _selectedAttributes
                                                .remove(attribute);
                                          } else {
                                            _selectedAttributes.add(attribute);
                                          }
                                          _addAttributeToComment(
                                              attribute, isSelected);
                                        });
                                      },
                                      child: Container(
                                        margin: EdgeInsets.symmetric(
                                          horizontal: size.width * 0.005,
                                          vertical: size.height * 0.002,
                                        ),
                                        padding: EdgeInsets.symmetric(
                                          horizontal: size.width * 0.03,
                                          vertical: size.height * 0.008,
                                        ),
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? Colors.black
                                              : Color(0xFFE1E3E6),
                                          borderRadius:
                                              BorderRadius.circular(16),
                                        ),
                                        child: Text(
                                          attribute,
                                          style: TextStyle(
                                              color: isSelected
                                                  ? Colors.white
                                                  : Color(0xFF1F2122),
                                              fontSize: captionTextSize,
                                              fontFamily: 'Inter-Medium',
                                              fontWeight: FontWeight.w500),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 1.2),
                              AnimatedContainer(
                                duration: Duration(milliseconds: 300),
                                padding: EdgeInsets.symmetric(
                                    horizontal: size.width * 0.03,
                                    vertical: size.height * 0.01),
                                decoration: BoxDecoration(
                                  color: Color(0xFFFFFFFF),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Color(0xFFE1E3E6),
                                    width: 1,
                                  ),
                                ),
                                child: TextField(
                                  controller: _commentController,
                                  maxLines: isKeyboardVisible ? 3 : 4,
                                  style: TextStyle(
                                    fontSize: baseTextSize,
                                    fontFamily: 'Inter',
                                  ),
                                  decoration: InputDecoration(
                                    hintText: 'Leave a comment',
                                    hintStyle: TextStyle(
                                      color: const Color(0xFF66696D),
                                      fontSize: baseTextSize,
                                      fontFamily: 'Inter',
                                    ),
                                    border: InputBorder.none,
                                    contentPadding: EdgeInsets.zero,
                                  ),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 1.2),
                              Container(
                                width: double.infinity,
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  'Upload a photo of your meal',
                                  style: TextStyle(
                                    fontSize: subheadingTextSize,
                                    fontFamily: 'Inter-Semibold',
                                    color: Color(0xFF000000),
                                  ),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.8),
                              SizedBox(
                                width: double.infinity,
                                height: isLandscape
                                    ? size.height * 0.08
                                    : size.height * 0.06,
                                child: OutlinedButton(
                                  onPressed: _pickImage,
                                  style: OutlinedButton.styleFrom(
                                    side: BorderSide(color: Colors.black),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                          size.width > 600 ? 28 : 24),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        _selectedImages.isNotEmpty
                                            ? Icons.check_circle
                                            : Icons.camera_alt_outlined,
                                        color: Colors.black,
                                        size: baseTextSize * 1.2,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        _selectedImages.isNotEmpty
                                            ? 'Photos Added (${_selectedImages.length})'
                                            : 'Add Photos',
                                        style: TextStyle(
                                          fontSize: baseTextSize,
                                          fontFamily: 'Inter-Medium',
                                          color: Colors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              if (_selectedImages.isNotEmpty) ...[
                                SizedBox(height: itemSpacing),
                                SizedBox(
                                  height: isLandscape
                                      ? size.height * 0.15
                                      : size.height * 0.1,
                                  child: ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    itemCount: _selectedImages.length,
                                    itemBuilder: (context, index) {
                                      return Padding(
                                        padding: EdgeInsets.only(
                                            right: size.width * 0.02),
                                        child: Stack(
                                          children: [
                                            Image.file(
                                              _selectedImages[index],
                                              width: size.width * 0.2,
                                              height: size.height * 0.1,
                                              fit: BoxFit.cover,
                                            ),
                                            Positioned(
                                              top: 0,
                                              right: 0,
                                              child: GestureDetector(
                                                onTap: () {
                                                  setState(() {
                                                    _selectedImages
                                                        .removeAt(index);
                                                  });
                                                },
                                                child: Container(
                                                  color: Colors.black54,
                                                  child: Icon(
                                                    Icons.close,
                                                    color: Colors.white,
                                                    size: baseTextSize,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                              SizedBox(height: itemSpacing),
                              Divider(
                                color: Color(0xFFE1E3E6),
                                thickness: 1,
                              ),
                              SizedBox(height: itemSpacing),
                              SizedBox(
                                width: double.infinity,
                                height: isLandscape
                                    ? size.height * 0.08
                                    : size.height * 0.06,
                                child: ElevatedButton(
                                  onPressed: _isLoading ? null : _submitReview,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.black,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                          size.width > 600 ? 28 : 24),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: _isLoading
                                      ? SizedBox(
                                          height: baseTextSize,
                                          width: baseTextSize,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                    Colors.white),
                                          ),
                                        )
                                      : Text(
                                          'Submit',
                                          style: TextStyle(
                                            fontSize: baseTextSize,
                                            fontFamily: 'Inter-Medium',
                                            color: Colors.white,
                                          ),
                                        ),
                                ),
                              ),
                              SizedBox(height: itemSpacing),
                              SizedBox(
                                width: double.infinity,
                                height: isLandscape
                                    ? size.height * 0.08
                                    : size.height * 0.06,
                                child: OutlinedButton(
                                  onPressed: () => Navigator.pop(context),
                                  style: OutlinedButton.styleFrom(
                                    side: BorderSide(color: Colors.black),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                          size.width > 600 ? 28 : 24),
                                    ),
                                  ),
                                  child: Text(
                                    'Rate Later',
                                    style: TextStyle(
                                      fontSize: baseTextSize,
                                      fontFamily: 'Inter-Medium',
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }
}
