class SearchDishesModel {
  bool? status;
  String? message;
  int? statusCode;
  SearchDishesData? data;

  SearchDishesModel({this.status, this.message, this.statusCode, this.data});

  SearchDishesModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null
        ? new SearchDishesData.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class SearchDishesData {
  int? total;
  int? page;
  int? limit;
  List<Dishes>? dishes;

  SearchDishesData({this.total, this.page, this.limit, this.dishes});

  SearchDishesData.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    page = json['page'];
    limit = json['limit'];
    if (json['dishes'] != null) {
      dishes = <Dishes>[];
      json['dishes'].forEach((v) {
        dishes!.add(new Dishes.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['total'] = this.total;
    data['page'] = this.page;
    data['limit'] = this.limit;
    if (this.dishes != null) {
      data['dishes'] = this.dishes!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Dishes {
  int? id;
  String? name;
  String? photo;
  List<ServingSizePrices>? servingSizePrices;

  Dishes({this.id, this.name, this.photo, this.servingSizePrices});

  Dishes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
    if (json['serving_size_prices'] != null) {
      servingSizePrices = <ServingSizePrices>[];
      json['serving_size_prices'].forEach((v) {
        servingSizePrices!.add(new ServingSizePrices.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['photo'] = this.photo;
    if (this.servingSizePrices != null) {
      data['serving_size_prices'] =
          this.servingSizePrices!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ServingSizePrices {
  int? id;
  int? servingSizeId;
  String? price;
  ServingSize? servingSize;

  ServingSizePrices(
      {this.id, this.servingSizeId, this.price, this.servingSize});

  ServingSizePrices.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    servingSizeId = json['serving_size_id'];
    price = json['price'];
    servingSize = json['serving_size'] != null
        ? new ServingSize.fromJson(json['serving_size'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['serving_size_id'] = this.servingSizeId;
    data['price'] = this.price;
    if (this.servingSize != null) {
      data['serving_size'] = this.servingSize!.toJson();
    }
    return data;
  }
}

class ServingSize {
  int? id;
  String? title;
  int? serves;

  ServingSize({this.id, this.title, this.serves});

  ServingSize.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    serves = json['serves'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['serves'] = this.serves;
    return data;
  }
}
