import 'dart:async';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/bloc/main_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/deals/dealslistmodel.dart';
import 'package:db_eats/data/models/guesthome/promocodesmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:lottie/lottie.dart';

class DealsOfTheDay extends StatefulWidget {
  const DealsOfTheDay({super.key});

  @override
  State<DealsOfTheDay> createState() => _DealsOfTheDayState();
}

class _DealsOfTheDayState extends State<DealsOfTheDay> {
  final ScrollController _scrollController = ScrollController();
  final Color _backgroundColor = const Color(0xFFF6F3EC);
  final Color _cardColor = Colors.white;
  final Color _primaryTextColor = const Color(0xFF1F2122);
  final Color _secondaryTextColor = const Color(0xFF414346);
  final Color _accentColor = const Color(0xFF2E7D32);
  final int _currentPage = 1;
  double? _currentLatitude;
  double? _currentLongitude;
  bool _isFetchingLocation = true;

  @override
  void initState() {
    super.initState();
    _getLocationAndLoadChefs();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _getLocationAndLoadChefs() async {
    try {
      // Check location permissions using Initializer
      bool isPermissionGranted =
          await Initializer.checkLocationPermission(context);
      if (!isPermissionGranted) {
        setState(() {
          _isFetchingLocation = false;
        });
        return;
      }

      // Get latitude and longitude from Initializer
      _currentLatitude = Initializer().getLatitude;
      _currentLongitude = Initializer().getLongitude;

      // Check if coordinates are available
      if (_currentLatitude != null && _currentLongitude != null) {
        context.read<HomeBloc>().add(
              GetPromoCodesEvent(
                data: {
                  'page': _currentPage,
                  'limit': 10,
                  'latitude': _currentLatitude!,
                  'longitude': _currentLongitude!,
                },
              ),
            );
      } else {
        final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
        );
        _currentLatitude = position.latitude;
        _currentLongitude = position.longitude;
        // Update Initializer with new coordinates
        Initializer().setCoordinates(_currentLatitude!, _currentLongitude!);

        context.read<MainBloc>().add(
              DealsListEvent(
                page: _currentPage,
                limit: 10,
                lat: _currentLatitude!,
                lon: _currentLongitude!,
              ),
            );
      }
    } catch (e) {
      print('Error fetching location: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to get location: $e')),
      );
    } finally {
      setState(() {
        _isFetchingLocation = false;
      });
    }
  }

  Future<void> _navigateBack() async {
    final savedFilters = await Initializer.getAppliedFilters();

    // Create request data with coordinates regardless of filter status
    final requestData = <String, dynamic>{
      'latitude': Initializer.latitude,
      'longitude': Initializer.longitude,
    };

    // Add filter data if it exists
    if (savedFilters != null) {
      requestData.addAll(savedFilters);
    }

    // Call the event with either just coordinates or coordinates + filters
    context.read<HomeBloc>().add(GetHomeDataEvent(data: requestData));
    Navigator.pop(context);
  }

  // Helper method to get responsive sizes based on screen width
  double _getResponsiveSize(BuildContext context, double factor) {
    final size = MediaQuery.of(context).size;
    return size.width * factor; // Use percentage of screen width
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final baseTextSize =
        _getResponsiveSize(context, 0.04); // 4% of screen width
    final padding =
        _getResponsiveSize(context, 0.05); // 5% of screen width for padding

    return WillPopScope(
      onWillPop: () async {
        await _navigateBack();
        return true;
      },
      child: Scaffold(
        backgroundColor: _backgroundColor,
        appBar: AppBar(
          backgroundColor: _backgroundColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: _primaryTextColor,
              size: isLandscape ? size.height * 0.04 : baseTextSize * 1.5,
            ),
            onPressed: _navigateBack,
          ),
          title: Text(
            'Deals',
            style: TextStyle(
              color: _primaryTextColor,
              fontSize: isLandscape ? size.height * 0.04 : baseTextSize * 1.4,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
            ),
          ),
        ),
        body: _isFetchingLocation
            ? Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(_accentColor),
                ),
              )
            : BlocConsumer<HomeBloc, HomeState>(
                listener: (context, state) {
                  if (state is PromoCodesFailed) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Failed to load deals')),
                    );
                  }
                },
                builder: (context, state) {
                  if (state is PromoCodesLoading) {
                    return Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(_accentColor),
                      ),
                    );
                  } else if (state is PromoCodesSuccess) {
                    final promoCodes =
                        Initializer.promoCodeModel.data?.promoCodes ?? [];
                    if (promoCodes.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: size.width * 0.3,
                              height: size.width * 0.3,
                              child: Lottie.asset(
                                'assets/noorderes.json',
                                fit: BoxFit.contain,
                              ),
                            ),
                            SizedBox(height: padding * 0.5),
                            Text(
                              "No Hot Deals!",
                              style: TextStyle(
                                fontSize: baseTextSize * 1.2,
                                fontWeight: FontWeight.w700,
                                color: _primaryTextColor,
                                fontFamily: 'Inter',
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: padding * 0.25),
                            Text(
                              "There are currently no hot deals available.\nPlease check back later!",
                              style: TextStyle(
                                fontSize: baseTextSize * 0.9,
                                color: _secondaryTextColor,
                                fontFamily: 'Inter',
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      );
                    }
                    return _buildPromoCodesList(
                        promoCodes, size.width, size.height);
                  } else {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'No deals loaded',
                            style: TextStyle(
                              fontSize: baseTextSize * 1.2,
                              color: _secondaryTextColor,
                            ),
                          ),
                          SizedBox(height: padding),
                          ElevatedButton(
                            onPressed: () {
                              if (_currentLatitude != null &&
                                  _currentLongitude != null) {
                                context.read<HomeBloc>().add(
                                      GetPromoCodesEvent(
                                        data: {
                                          'latitude': _currentLatitude!,
                                          'longitude': _currentLongitude!,
                                        },
                                      ),
                                    );
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _accentColor,
                              padding: EdgeInsets.symmetric(
                                horizontal: padding * 1.5,
                                vertical: padding * 0.75,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(padding * 0.5),
                              ),
                            ),
                            child: Text(
                              'Refresh Deals',
                              style: TextStyle(
                                fontSize: baseTextSize,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                },
              ),
      ),
    );
  }

  Widget _buildPromoCodesList(
      List<PromoCodes> promoCodes, double width, double height) {
    final padding = _getResponsiveSize(context, 0.05);
    final baseTextSize = _getResponsiveSize(context, 0.04);

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.all(padding),
      itemCount: promoCodes.length,
      itemBuilder: (context, index) {
        final promo = promoCodes[index];
        return Card(
          color: _cardColor,
          margin: EdgeInsets.only(bottom: padding),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(padding * 0.5),
          ),
          child: Padding(
            padding: EdgeInsets.all(padding * 0.7),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  promo.discountName ?? '',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: baseTextSize * 1.1,
                    color: _primaryTextColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: padding * 0.3),
                Text(
                  promo.description ?? '',
                  style: TextStyle(
                    fontSize: baseTextSize * 0.95,
                    color: _secondaryTextColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: padding * 0.3),
                Text(
                  'Code: ${promo.couponCode ?? ''}',
                  style: TextStyle(
                    fontSize: baseTextSize * 0.95,
                    color: _accentColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
