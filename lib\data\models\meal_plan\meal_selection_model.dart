class MealSelectionModel {
  final int day;
  final String date;
  final int chefId;
  final List<MealItem> selectedMeals;

  MealSelectionModel({
    required this.day,
    required this.date,
    required this.chefId,
    required this.selectedMeals,
  });

  Map<String, dynamic> toJson() => {
        'day': day,
        'date': date,
        'chef_id': chefId,
        'meals': selectedMeals.map((meal) => meal.toJson()).toList(),
      };
}

class MealItem {
  final int mealId;
  final String name;
  final String photo;
  final double price;
  final String servingSize;

  MealItem({
    required this.mealId,
    required this.name,
    required this.photo,
    required this.price,
    required this.servingSize,
  });

  Map<String, dynamic> toJson() => {
        'meal_id': mealId,
        'name': name,
        'photo': photo,
        'price': price,
        'serving_size': servingSize,
      };
}
