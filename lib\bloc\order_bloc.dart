import 'dart:async';
import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/booking/bookinglistmodel.dart';
import 'package:db_eats/data/models/booking/listdishtypemodel.dart';
import 'package:db_eats/data/models/booking/mealplandetailsmodel.dart';
import 'package:db_eats/data/models/booking/mealplanlistmodel.dart';
import 'package:db_eats/data/models/booking/vieworderdetailsmodel.dart';
import 'package:db_eats/data/models/cart/getordersummarymodel.dart';
import 'package:db_eats/data/models/dish/dishdetailmodel.dart';
import 'package:db_eats/data/models/verifyrefreshtokenmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/storage/localstorage.dart';

class OrderBloc extends Bloc<OrderEvent, OrderState> {
  OrderBloc() : super(OrderInitial()) {
    on<CheckoutOrderEvent>(_checkout);
    on<GetOrgerSummaryEvent>(_getOrderSummary);
    on<BookingListEvent>(_fetchBookingList);
    on<ListDishTypeEvent>(_listDishType);
    on<ListDishDetails>(_listDishDetails);
    on<MealPlanListEvent>(_listMealPlan);
    on<ViewOrederdetailsEvent>(_viewOrderDetails);
    on<ViewOrederdetailsEvent2>(_viewOrderDetails2);
    on<ViewMealPlanDetailsEvent>(_viewMealPlanDetails);
    on<SubmitDriverRatingEvent>(_submitDriverRating);
    on<RefreshTokenEvent>(_refreshToken);
    // on<GetCartCountEvent>(_getCartCount);
  }

  Future<void> _checkout(
      CheckoutOrderEvent event, Emitter<OrderState> emit) async {
    emit(CheckoutOrderLoading());
    try {
      final response =
          await ServerHelper.post1('/v1/customer/orders/checkout', event.data);
      log('Checkout Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));

        // emit(CheckoutOrderFailed('Session expired. Refreshing token...'));
        // return;
      }

      if (response['status'] == true) {
        emit(CheckoutOrderSuccess(
          response['data']?['total'] ?? 0,
          response['message'] ?? 'Order placed successfully',
          response['data']?['payment']?['checkout_url'] ?? '',
          response['data']?['payment']?['id'] ?? '',
        ));
      } else {
        emit(CheckoutOrderFailed(
            response['message'] ?? 'Failed to place order'));
      }
    } catch (e) {
      log('Error during checkout: $e');
      emit(CheckoutOrderFailed('Error occurred while placing order'));
    }
  }

  Future<void> _getOrderSummary(
      GetOrgerSummaryEvent event, Emitter<OrderState> emit) async {
    emit(GetOrgerSummaryLoading());
    try {
      final response =
          await ServerHelper.post1('/v1/customer/orders/summary', event.data);
      log('Get Order Summary Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));

        // emit(GetOrgerSummaryFailed('Session expired. Refreshing token...'));
        // return;
      }

      if (response['status'] == true) {
        GetOrderSummaryModel getOrderSummaryModel =
            GetOrderSummaryModel.fromJson(response);
        emit(GetOrgerSummarySuccess(getOrderSummaryModel.data));
      } else {
        emit(GetOrgerSummaryFailed(
            response['message'] ?? 'Failed to get order summary'));
      }
    } catch (e) {
      log('Error getting order summary: $e');
      emit(GetOrgerSummaryFailed('Error occurred while getting order summary'));
    }
  }

  FutureOr<void> _fetchBookingList(
      BookingListEvent event, Emitter<OrderState> emit) async {
    emit(BookingListLoading());
    try {
      final bookingResponse = await ServerHelper.get1(
          '/v1/customer/orders/list?filter=${event.status}&page=${event.page}');
      log('Booking List Response: $bookingResponse');
      if (bookingResponse['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));

        // emit(BookingListFailed('Session expired. Refreshing token...'));
        // return;
      }
      Initializer.ongoingBookinglistmodel =
          OngoingBookinglistModel.fromJson(bookingResponse);
      // log('Bookings data: ${Initializer.ongoingBookinglistmodel.data?.bookings?.map((b) => b.toJson()).toList()}');

      if (Initializer.ongoingBookinglistmodel.status!) {
        emit(BookingListSuccess(
            Initializer.ongoingBookinglistmodel.data?.bookings ?? []));
      } else {
        emit(BookingListFailed('Failed to fetch booking list'));
      }
    } catch (e) {
      log('Error fetching booking list: $e');
      emit(BookingListFailed('Error occurred while fetching booking list'));
    }
  }

  Future<void> _listDishType(
      ListDishTypeEvent event, Emitter<OrderState> emit) async {
    emit(ListDishTypeLoading());
    try {
      final response = await ServerHelper.get1('/v1/common/dish-type/list');
      log('Dish Type Listing Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));

        // emit(ListDishTypeFailed('Session expired. Refreshing token...'));
        // return;
      }

      Initializer.listDishTypeModel = Listdishtypemodel.fromJson(response);

      if (Initializer.listDishTypeModel.status == true &&
          Initializer.listDishTypeModel.data != null) {
        emit(ListDishTypeSuccess(Initializer.listDishTypeModel.data!));
      } else {
        emit(ListDishTypeFailed(
            response['message'] ?? 'Failed to load dish types'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(ListDishTypeFailed('Error occurred while loading dish types'));
    }
  }

  Future<void> _listDishDetails(
      ListDishDetails event, Emitter<OrderState> emit) async {
    emit(ListDishDetailsLoading());
    try {
      final response = await ServerHelper.get1(
          '/v1/customer/dish/get?dish_id=${event.dishId}');
      log('Dish Details Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));

        // emit(ListDishDetailsFailed('Session expired. Refreshing token...'));
        // return;
      }

      Initializer.dishDetailModel = DishDetailModel.fromJson(response);

      if (Initializer.dishDetailModel.status == true) {
        emit(ListDishDetailsSuccess(Initializer.dishDetailModel));
      } else {
        emit(ListDishDetailsFailed(
            response['message'] ?? 'Failed to load dish details'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(ListDishDetailsFailed('Error occurred while loading dish details'));
    }
  }

  Future<void> _listMealPlan(
      MealPlanListEvent event, Emitter<OrderState> emit) async {
    emit(MealPlanListLoading());
    try {
      final response = await ServerHelper.get1('/v1/customer/meal-plan/list');
      log('Meal Plan Listing Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));

        // emit(MealPlanListFailed('Session expired. Refreshing token...'));
        // return;
      }

      Initializer.mealPlanListModel = MealPlanListModel.fromJson(response);

      if (Initializer.mealPlanListModel.status == true) {
        emit(MealPlanListSuccess(Initializer.mealPlanListModel.data));
      } else {
        emit(MealPlanListFailed(
            response['message'] ?? 'Failed to load meal plans'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(MealPlanListFailed('Error occurred while loading meal plans'));
    }
  }

  Future<void> _viewOrderDetails(
      ViewOrederdetailsEvent event, Emitter<OrderState> emit) async {
    emit(ViewOrederdetailsLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/orders/view', {'id': event.bookingId}); //booking_id
      log('View Order Details Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));

        // emit(ViewOrederdetailsFailed('Session expired. Refreshing token...'));
        // return;
      }

      Initializer.viewOrderDetailsModel =
          ViewOrderDetailsModel.fromJson(response);

      if (Initializer.viewOrderDetailsModel.status == true) {
        emit(ViewOrederdetailsSuccess(Initializer.viewOrderDetailsModel.data));
      } else {
        emit(ViewOrederdetailsFailed(
            response['message'] ?? 'Failed to view order details'));
      }
    } catch (e) {
      log('Error viewing order details: $e');
      emit(ViewOrederdetailsFailed(
          'Error occurred while viewing order details'));
    }
  }

  Future<void> _viewOrderDetails2(
      ViewOrederdetailsEvent2 event, Emitter<OrderState> emit) async {
    emit(ViewOrederdetailsLoading2());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/orders/view', {'id': event.bookingId}); //booking_id
      log('View Order Details Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));

        // emit(ViewOrederdetailsFailed('Session expired. Refreshing token...'));
        // return;
      }

      Initializer.viewOrderDetailsModel =
          ViewOrderDetailsModel.fromJson(response);

      if (Initializer.viewOrderDetailsModel.status == true) {
        emit(ViewOrederdetailsSuccess2(Initializer.viewOrderDetailsModel.data));
      } else {
        emit(ViewOrederdetailsFailed2(
            response['message'] ?? 'Failed to view order details'));
      }
    } catch (e) {
      log('Error viewing order details: $e');
      emit(ViewOrederdetailsFailed2(
          'Error occurred while viewing order details'));
    }
  }

  Future<void> _viewMealPlanDetails(
      ViewMealPlanDetailsEvent event, Emitter<OrderState> emit) async {
    emit(ViewMealPlanDetailsLoading());
    try {
      // Clear previous data
      Initializer.mealPlanDetailsModel = MealPlanDetailsModel();

      final response = await ServerHelper.get1(
          '/v1/customer/meal-plan/get?meal_plan_id=${event.mealPlanId}');
      log('View Meal Plan Details Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));

        // emit(ViewMealPlanDetailsFailed('Session expired. Refreshing token...'));
        // return;
      }

      Initializer.mealPlanDetailsModel =
          MealPlanDetailsModel.fromJson(response);

      if (Initializer.mealPlanDetailsModel.status == true) {
        emit(ViewMealPlanDetailsSuccess(Initializer.mealPlanDetailsModel.data));
      } else {
        emit(ViewMealPlanDetailsFailed(
            response['message'] ?? 'Failed to view meal plan details'));
      }
    } catch (e) {
      log('Error viewing meal plan details: $e');
      emit(ViewMealPlanDetailsFailed(
          'Error occurred while viewing meal plan details'));
    }
  }

  Future<void> _submitDriverRating(
      SubmitDriverRatingEvent event, Emitter<OrderState> emit) async {
    emit(SendDriverRatingLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/driver_rating/add', event.data);
      log('Submit Driver Rating Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));

        // emit(DriverRatingFailed('Session expired. Refreshing token...'));
        // return;
      }

      if (response['status'] == true) {
        emit(DriverRatingSubmitted(response['message'] ?? 'Rating submitted'));
      } else {
        emit(DriverRatingFailed(
            response['message'] ?? 'Failed to submit driver rating'));
      }
    } catch (e) {
      log('Error submitting driver rating: $e');
      emit(DriverRatingFailed('Error occurred while submitting driver rating'));
    }
  }

  Future<void> _refreshToken(
      RefreshTokenEvent event, Emitter<OrderState> emit) async {
    emit(RefreshTokenLoading());
    try {
      Initializer.verifyRefreshTokenModel = VerifyRefreshTokenModel.fromJson(
          await ServerHelper.getrefresh(
              '/v1/customer/auth/verify-refresh-token'));
      if (Initializer.verifyRefreshTokenModel.status == true) {
        await LocalStorage.setAccessToken(
            Initializer.verifyRefreshTokenModel.data?.accessToken ?? "");
        emit(RefreshTokenSuccess());
        if (event.nextEvent != null) {
          add(event.nextEvent!);
        }
      } else if (Initializer.verifyRefreshTokenModel.statusCode == 401) {
        emit(RefreshTokenFailed());
        // await LocalStorage.setAccessToken('');
        // await LocalStorage.setRefreshToken('');
        // Navigator.of(context).pushAndRemoveUntil(
        //   MaterialPageRoute(builder: (context) => const Home()),
        //   (route) => false,
        // );
      }
    } catch (e) {
      emit(RefreshTokenFailed());
    }
  }

  // Future<void> _getCartCount(
  //     GetCartCountEvent event, Emitter<OrderState> emit) async {
  //   emit(GetCartCountLoading());
  //   try {
  //     final response = await ServerHelper.get1('/v1/customer/cart/count');
  //     log('Get Cart Count Response: $response');

  //     if (response['status'] == true) {
  //       final count = response['data']['cart_count'] ?? 0;
  //       Initializer.cartCount = count;
  //       emit(GetCartCountSuccess(count));
  //     } else {
  //       emit(GetCartCountFailed(
  //           response['message'] ?? 'Failed to get cart count'));
  //     }
  //   } catch (e) {
  //     log('Error getting cart count: $e');
  //     emit(GetCartCountFailed('Error occurred while getting cart count'));
  //   }
  // }
}

// Events
abstract class OrderEvent {}

class CheckoutOrderEvent extends OrderEvent {
  final Map<String, dynamic> data;
  CheckoutOrderEvent(this.data);
}

class GetOrgerSummaryEvent extends OrderEvent {
  final Map<String, dynamic> data;
  GetOrgerSummaryEvent(this.data);
}

class RefreshTokenEvent extends OrderEvent {
  final String refreshToken;
  final OrderEvent? nextEvent;
  RefreshTokenEvent({required this.refreshToken, required this.nextEvent});
}

// States
abstract class OrderState {}

class OrderInitial extends OrderState {}

class CheckoutOrderLoading extends OrderState {}

class CheckoutOrderSuccess extends OrderState {
  final num total;
  final String message;
  final String? orderId; // Optional order ID if needed
  final String? paymenturl; // Optional order ID if needed
  CheckoutOrderSuccess(this.total, this.message, this.paymenturl, this.orderId);
}

class CheckoutOrderFailed extends OrderState {
  final String message;
  CheckoutOrderFailed(this.message);
}

class GetOrgerSummaryLoading extends OrderState {}

class GetOrgerSummarySuccess extends OrderState {
  final dynamic data;
  GetOrgerSummarySuccess(this.data);
}

class GetOrgerSummaryFailed extends OrderState {
  final String message;
  GetOrgerSummaryFailed(this.message);
}

// booking list classes
class BookingListEvent extends OrderEvent {
  final String status;
  final int page;
  BookingListEvent(this.status, {this.page = 1});
}

class MealPlanListEvent extends OrderEvent {}

class ListDishTypeEvent extends OrderEvent {}

class ViewOrederdetailsEvent extends OrderEvent {
  final int bookingId;
  ViewOrederdetailsEvent(this.bookingId);
}

class ViewOrederdetailsEvent2 extends OrderEvent {
  final int bookingId;
  ViewOrederdetailsEvent2(this.bookingId);
}

class ViewMealPlanDetailsEvent extends OrderEvent {
  final int mealPlanId;
  ViewMealPlanDetailsEvent(this.mealPlanId);
}

class SubmitDriverRatingEvent extends OrderEvent {
  final Map<String, dynamic> data;
  SubmitDriverRatingEvent(this.data);
}

// class GetCartCountEvent extends OrderEvent {}

class ListDishDetails extends OrderEvent {
  final int dishId;
  ListDishDetails(this.dishId);
}

class BookingListLoading extends OrderState {}

class BookingListSuccess extends OrderState {
  final List<dynamic> bookings; // Replace with actual booking model
  BookingListSuccess(this.bookings);
}

class BookingListFailed extends OrderState {
  final String message;
  BookingListFailed(this.message);
}

class ListDishTypeLoading extends OrderState {}

class ListDishTypeSuccess extends OrderState {
  final DishTypeData data;
  ListDishTypeSuccess(this.data);
}

class ListDishTypeFailed extends OrderState {
  final String message;
  ListDishTypeFailed(this.message);
}

class ListDishDetailsLoading extends OrderState {}

class ListDishDetailsSuccess extends OrderState {
  final DishDetailModel data;
  ListDishDetailsSuccess(this.data);
}

class ListDishDetailsFailed extends OrderState {
  final String message;
  ListDishDetailsFailed(this.message);
}

class MealPlanListLoading extends OrderState {}

class MealPlanListSuccess extends OrderState {
  final dynamic data;
  MealPlanListSuccess(this.data);
}

class MealPlanListFailed extends OrderState {
  final String message;
  MealPlanListFailed(this.message);
}

class ViewOrederdetailsLoading extends OrderState {}

class ViewOrederdetailsSuccess extends OrderState {
  final dynamic data;
  ViewOrederdetailsSuccess(this.data);
}

class ViewOrederdetailsFailed extends OrderState {
  final String message;
  ViewOrederdetailsFailed(this.message);
}

class ViewMealPlanDetailsLoading extends OrderState {}

class ViewMealPlanDetailsSuccess extends OrderState {
  final dynamic data;
  ViewMealPlanDetailsSuccess(this.data);
}

class ViewMealPlanDetailsFailed extends OrderState {
  final String message;
  ViewMealPlanDetailsFailed(this.message);
}

class GetCartCountLoading extends OrderState {}

class GetCartCountSuccess extends OrderState {
  final int count;
  GetCartCountSuccess(this.count);
}

class GetCartCountFailed extends OrderState {
  final String message;
  GetCartCountFailed(this.message);
}

class RefreshTokenLoading extends OrderState {}

class RefreshTokenSuccess extends OrderState {}

class RefreshTokenFailed extends OrderState {}

class ViewOrederdetailsLoading2 extends OrderState {}

class ViewOrederdetailsSuccess2 extends OrderState {
  final dynamic data;
  ViewOrederdetailsSuccess2(this.data);
}

class ViewOrederdetailsFailed2 extends OrderState {
  final String message;
  ViewOrederdetailsFailed2(this.message);
}

class SendDriverRatingLoading extends OrderState {}

class DriverRatingSubmitted extends OrderState {
  final String message;
  DriverRatingSubmitted(this.message);
}

class DriverRatingFailed extends OrderState {
  final String message;
  DriverRatingFailed(this.message);
}
