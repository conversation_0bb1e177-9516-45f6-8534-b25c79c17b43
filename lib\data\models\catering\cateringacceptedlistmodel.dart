class CateringAcceptedListModel {
  bool? status;
  List<CateringAcceptedListData>? data;
  int? statusCode;

  CateringAcceptedListModel({this.status, this.data, this.statusCode});

  CateringAcceptedListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    if (json['data'] != null) {
      data = <CateringAcceptedListData>[];
      json['data'].forEach((v) {
        data!.add(CateringAcceptedListData.fromJson(v));
      });
    }
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['status_code'] = statusCode;
    return data;
  }
}

class CateringAcceptedListData {
  int? id;
  Chef? chef;
  CateringType? cateringType;
  int? peopleCount;
  String? date;
  TimeSlot? timeSlot;
  int? deliveryTimeId;
  String? address;
  String? state;
  String? city;
  String? zipCode;
  int? packagingTypeId;
  int? dietaryPreferenceId;
  int? spiceLevelId;
  String? allergyPreferenceText;
  String? status;
  String? createdAt;
  String? updatedAt;
  List<CateringCuisines>? cateringCuisines;
  List<CateringSubCuisines>? cateringSubCuisines;
  List<CateringLocalCuisines>? cateringLocalCuisines;

  CateringAcceptedListData({
    this.id,
    this.chef,
    this.peopleCount,
    this.date,
    this.timeSlot,
    this.cateringType,
    this.deliveryTimeId,
    this.address,
    this.state,
    this.city,
    this.zipCode,
    this.packagingTypeId,
    this.dietaryPreferenceId,
    this.spiceLevelId,
    this.allergyPreferenceText,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.cateringCuisines,
    this.cateringSubCuisines,
    this.cateringLocalCuisines,
  });

  CateringAcceptedListData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    chef = json['chef'] != null ? Chef.fromJson(json['chef']) : null;
    peopleCount = json['people_count'];
    date = json['date'];
    timeSlot =
        json['time_slot'] != null ? TimeSlot.fromJson(json['time_slot']) : null;
    cateringType = json['catering_type'] != null
        ? CateringType.fromJson(json['catering_type'])
        : null;
    deliveryTimeId = json['delivery_time_id'];
    address = json['address'];
    state = json['state'];
    city = json['city'];
    zipCode = json['zip_code'];
    packagingTypeId = json['packaging_type_id'];
    dietaryPreferenceId = json['dietary_preference_id'];
    spiceLevelId = json['spice_level_id'];
    allergyPreferenceText = json['allergy_prference_text'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    if (json['catering_cuisines'] != null) {
      cateringCuisines = <CateringCuisines>[];
      json['catering_cuisines'].forEach((v) {
        cateringCuisines!.add(CateringCuisines.fromJson(v));
      });
    }
    if (json['catering_sub_cuisines'] != null) {
      cateringSubCuisines = <CateringSubCuisines>[];
      json['catering_sub_cuisines'].forEach((v) {
        cateringSubCuisines!.add(CateringSubCuisines.fromJson(v));
      });
    }
    if (json['catering_local_cuisines'] != null) {
      cateringLocalCuisines = <CateringLocalCuisines>[];
      json['catering_local_cuisines'].forEach((v) {
        cateringLocalCuisines!.add(CateringLocalCuisines.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    if (chef != null) {
      data['chef'] = chef!.toJson();
    }
    if (cateringType != null) {
      data['catering_type'] = cateringType!.toJson();
    }
    data['people_count'] = peopleCount;
    data['date'] = date;
    if (timeSlot != null) {
      data['time_slot'] = timeSlot!.toJson();
    }
    data['delivery_time_id'] = deliveryTimeId;
    data['address'] = address;
    data['state'] = state;
    data['city'] = city;
    data['zip_code'] = zipCode;
    data['packaging_type_id'] = packagingTypeId;
    data['dietary_preference_id'] = dietaryPreferenceId;
    data['spice_level_id'] = spiceLevelId;
    data['allergy_prference_text'] = allergyPreferenceText;
    data['status'] = status;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    if (cateringCuisines != null) {
      data['catering_cuisines'] =
          cateringCuisines!.map((v) => v.toJson()).toList();
    }
    if (cateringSubCuisines != null) {
      data['catering_sub_cuisines'] =
          cateringSubCuisines!.map((v) => v.toJson()).toList();
    }
    if (cateringLocalCuisines != null) {
      data['catering_local_cuisines'] =
          cateringLocalCuisines!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Chef {
  int? id;
  String? name;
  String? photo;
  List<String>? searchTags;
  String? rating;
  num? ratingPercentage;
  num? noOfRatings;

  Chef({
    this.id,
    this.name,
    this.photo,
    this.searchTags,
    this.rating,
    this.ratingPercentage,
    this.noOfRatings,
  });

  Chef.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
    if (json['search_tags'] != null) {
      searchTags = json['search_tags'].cast<String>();
    }
    rating = json['rating'];
    ratingPercentage = json['rating_percentage'];
    noOfRatings = json['no_of_ratings'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['photo'] = photo;
    data['search_tags'] = searchTags;
    data['rating'] = rating;
    data['rating_percentage'] = ratingPercentage;
    data['no_of_ratings'] = noOfRatings;
    return data;
  }
}

class CateringType {
  int? id;
  String? name;

  CateringType({this.id, this.name});

  CateringType.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class TimeSlot {
  int? id;
  String? startTime;
  String? endTime;

  TimeSlot({this.id, this.startTime, this.endTime});

  TimeSlot.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    return data;
  }
}

class CateringCuisines {
  int? id;
  String? name;

  CateringCuisines({this.id, this.name});

  CateringCuisines.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class CateringSubCuisines {
  int? id;
  String? name;

  CateringSubCuisines({this.id, this.name});

  CateringSubCuisines.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class CateringLocalCuisines {
  int? id;
  String? name;

  CateringLocalCuisines({this.id, this.name});

  CateringLocalCuisines.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}
