class ListAddressModel {
  bool? status;
  int? statusCode;
  List<AddressData>? data;

  ListAddressModel({this.status, this.statusCode, this.data});

  ListAddressModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    statusCode = json['status_code'];
    if (json['data'] != null) {
      data = <AddressData>[];
      json['data'].forEach((v) {
        data!.add(AddressData.fromJson(v));
      });
    }
  }
}

class AddressData {
  int? id;
  int? customerId;
  Location? location;
  String? addressText;
  String? buildingType;
  String? houseNumber;
  String? landmark;
  bool? isCurrent;
  String? createdAt;
  String? updatedAt;

  AddressData(
      {this.id,
      this.customerId,
      this.location,
      this.addressText,
      this.buildingType,
      this.houseNumber,
      this.landmark,
      this.isCurrent,
      this.createdAt,
      this.updatedAt});

  AddressData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    customerId = json['customer_id'];
    location =
        json['location'] != null ? Location.fromJson(json['location']) : null;
    addressText = json['address_text'];
    buildingType = json['building_type'];
    houseNumber = json['house_number'];
    landmark = json['landmark'];
    isCurrent = json['is_current'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}

class Location {
  Crs? crs;
  String? type;
  List<double>? coordinates;

  Location({this.crs, this.type, this.coordinates});

  Location.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? Crs.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates']?.cast<double>();
  }
}

class Crs {
  String? type;
  Properties? properties;

  Crs({this.type, this.properties});

  Crs.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'] != null
        ? Properties.fromJson(json['properties'])
        : null;
  }
}

class Properties {
  String? name;

  Properties({this.name});

  Properties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }
}
