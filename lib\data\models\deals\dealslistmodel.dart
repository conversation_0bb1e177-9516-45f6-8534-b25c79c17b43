class DealslistModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  DealslistModel({this.status, this.message, this.statusCode, this.data});

  DealslistModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['status_code'] = statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  int? total;
  int? page;
  int? limit;
  List<Deals>? deals;

  Data({this.total, this.page, this.limit, this.deals});

  Data.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    page = json['page'];
    limit = json['limit'];
    if (json['deals'] != null) {
      deals = <Deals>[];
      json['deals'].forEach((v) {
        deals!.add(Deals.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total'] = total;
    data['page'] = page;
    data['limit'] = limit;
    if (deals != null) {
      data['deals'] = deals!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Deals {
  int? id;
  int? dealType;
  String? title;
  String? description;
  String? discountPercentage;
  String? discountAmount;
  String? minimumSpendAmount;
  String? startDate;
  String? endDate;
  String? status;
  Chef? chef;

  Deals(
      {this.id,
      this.dealType,
      this.title,
      this.description,
      this.discountPercentage,
      this.discountAmount,
      this.minimumSpendAmount,
      this.startDate,
      this.endDate,
      this.status,
      this.chef});

  Deals.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    dealType = json['deal_type'];
    title = json['title'];
    description = json['description'];
    discountPercentage = json['discount_percentage'];
    discountAmount = json['discount_amount'];
    minimumSpendAmount = json['minimum_spend_amount'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    status = json['status'];
    chef = json['chef'] != null ? Chef.fromJson(json['chef']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['deal_type'] = dealType;
    data['title'] = title;
    data['description'] = description;
    data['discount_percentage'] = discountPercentage;
    data['discount_amount'] = discountAmount;
    data['minimum_spend_amount'] = minimumSpendAmount;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['status'] = status;
    if (chef != null) {
      data['chef'] = chef!.toJson();
    }
    return data;
  }
}

class Chef {
  String? firstName;
  String? lastName;
  Profile? profile;

  Chef({this.firstName, this.lastName, this.profile});

  Chef.fromJson(Map<String, dynamic> json) {
    firstName = json['first_name'];
    lastName = json['last_name'];
    profile =
        json['profile'] != null ? Profile.fromJson(json['profile']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    if (profile != null) {
      data['profile'] = profile!.toJson();
    }
    return data;
  }
}

class Profile {
  String? profilePhoto;
  String? coverPhoto;

  Profile({this.profilePhoto, this.coverPhoto});

  Profile.fromJson(Map<String, dynamic> json) {
    profilePhoto = json['profile_photo'];
    coverPhoto = json['cover_photo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['profile_photo'] = profilePhoto;
    data['cover_photo'] = coverPhoto;
    return data;
  }
}
