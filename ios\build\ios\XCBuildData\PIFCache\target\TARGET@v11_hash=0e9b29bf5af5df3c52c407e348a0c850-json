{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98707f1476040ce6c57fb829e89141f1f1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d045548770b7f036852a1e6ab0fc2447", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862074a87fd0758e7fbd5b2fcf45e61b4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c1c643d8a1222ee9767ec750af73456e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862074a87fd0758e7fbd5b2fcf45e61b4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ffb69227a3f2326643cbef8851cc73ea", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f6bdc7f524b792647f51b3c28405201b", "guid": "bfdfe7dc352907fc980b868725387e98c0125f80551b8718b5403b2790ea4360"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae85709c15f5c504d2a9c8fbebbb1284", "guid": "bfdfe7dc352907fc980b868725387e989768e9079eea8ca914cfe840cfe08724"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e71578f3b1c705be6aa5928b5916831", "guid": "bfdfe7dc352907fc980b868725387e98552a8fae4d5f9f87498bd78011b00b58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e0649f08d8a0b5a201c0cba54bd0cfb", "guid": "bfdfe7dc352907fc980b868725387e985b31cc4120adc7d492a9b30a8ccabe58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acd987f79699326213fded35491bd4b3", "guid": "bfdfe7dc352907fc980b868725387e98784383f5e8bc8a3fb39f758d4c9ef5df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866a8cca069eeecd737df31fa7f37bc80", "guid": "bfdfe7dc352907fc980b868725387e980a0ac2c55016050bc34c402851deb2df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6de99029fa0d0f799bc68a520c035b4", "guid": "bfdfe7dc352907fc980b868725387e9877c785ca8698a6a8782ca0fb11a9737c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ed7f912fa15eed7abac969bedce7ec2", "guid": "bfdfe7dc352907fc980b868725387e981b7f33cde462ab0f3ff33230bcf275b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850ec5611f960d734cf4c63405b463f3a", "guid": "bfdfe7dc352907fc980b868725387e9880bd1c03530868e23ae02df02a011595"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d740c86cb130e6089b6575c596d9f7eb", "guid": "bfdfe7dc352907fc980b868725387e9866903d3e097500752f6e8b1581e11fc6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d58fae14f8563edd3297a754ca62be64", "guid": "bfdfe7dc352907fc980b868725387e98ab78933ade1bebef2bc7ce611221d64f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d21f9b5b548a48c708697531a034e6e", "guid": "bfdfe7dc352907fc980b868725387e98e9bee5a6f93f101c922aad370dd0519b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98885f9d0d26baf75dc6dc4c53de293504", "guid": "bfdfe7dc352907fc980b868725387e98be841aa942efa47cf02473ddfeb9316b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f2bdcd178bfa82bc8a29bf115580dd7", "guid": "bfdfe7dc352907fc980b868725387e98183cbc6d45d458c1c94952dd5c0cec3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5df3910b3c43d44f0030ccbbb9629e1", "guid": "bfdfe7dc352907fc980b868725387e986f8b15e504f43770e07a289b4cd7f745"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986551bf36b71a7a15103570113c8885b0", "guid": "bfdfe7dc352907fc980b868725387e98585d7a5793fd5535937fc309c35ce0d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835eb35a8243cdea992c91ee3b703e63c", "guid": "bfdfe7dc352907fc980b868725387e98cb829ebfddfdb5a3d47c09398ceb45e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c313c7fcc9daa55deba29e14b7c446b7", "guid": "bfdfe7dc352907fc980b868725387e98eb407a7bec0d5a6971681cda4159ed67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ac197fa38fdc01ab5c31a3906e5139", "guid": "bfdfe7dc352907fc980b868725387e986e48384ada2a694dcad72f92a9b663f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98325892cced2817c95622998d12b649b7", "guid": "bfdfe7dc352907fc980b868725387e9847836d9cdf0ff2cb11938709d7d11b70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed38e7869de5022b34dbf5231d034e57", "guid": "bfdfe7dc352907fc980b868725387e983e78481004e471fe404c1ce0a34b812d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2fd07d787fa7515bf2ede3e35872f31", "guid": "bfdfe7dc352907fc980b868725387e98edeec6f23a284c23337757ab809a9571"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886a32d5fb9feab2ea5218264bd7012d9", "guid": "bfdfe7dc352907fc980b868725387e984b816d75e4239de90299a4068fb3cf4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98318c77de13cbc016761f462c8602cab0", "guid": "bfdfe7dc352907fc980b868725387e98082d53e8990959d67a7e149b440d0996"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882310159bfe8be59a87155519b95f49d", "guid": "bfdfe7dc352907fc980b868725387e98c740f9dbe8a8ac7612ec9e028edb7ef8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d082f69ca50eec2c413a32ca2b03834", "guid": "bfdfe7dc352907fc980b868725387e98f71960d4a46d63b6ee7c2a6a42c13e93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c0750af2f2a7b5e99e6682e048571ab", "guid": "bfdfe7dc352907fc980b868725387e989b48f3eea943c24d332d86b3b1e1f2da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5f1ff6fecdef3b6e5278f314fbf60cf", "guid": "bfdfe7dc352907fc980b868725387e98c232a3bc81beddb35d88793756eab1a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdab23381c78aa6ba6d722efc15a83a9", "guid": "bfdfe7dc352907fc980b868725387e98fa0250dc15605bcf75f466b9c6a93556"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c3e0e2035bfca6e18e68705951e643a", "guid": "bfdfe7dc352907fc980b868725387e984b6940113110cc650cfa40697b0fbd04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7a2665dfc2a787c84049ff9c5d1f585", "guid": "bfdfe7dc352907fc980b868725387e983b877c2b2790bcd4da521fbd622bb00b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819d3f16269a5c15e09bfcc16e34e444c", "guid": "bfdfe7dc352907fc980b868725387e98756a43d32573f1544f5b8093550a1783", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882b53230b8a7ab963b93890eea00ae3f", "guid": "bfdfe7dc352907fc980b868725387e987b5a0fc20c83c80691cbd683003b76df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988993215557b51fa861aa0babdaa58a0c", "guid": "bfdfe7dc352907fc980b868725387e98536af0169ea81afc4b7095228f823d77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851f8905f8ac80493f551a76edfb072f1", "guid": "bfdfe7dc352907fc980b868725387e98fb8e44b8d1da4e66143a0246f2777742"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef5fb78a500784fb07ca790c549cc557", "guid": "bfdfe7dc352907fc980b868725387e98b3d6538e51de04859a42a250c64b3908"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eda6df19090fb11c9e983bee47340040", "guid": "bfdfe7dc352907fc980b868725387e98a0d5195992c9b9ac8ab3b77083fdc889"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eef794aa6a5ad43f3a2f6df5f7d1c6c5", "guid": "bfdfe7dc352907fc980b868725387e98a34e23dbd9e821cd3af1801efe056cb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840d4c88e57e961a8f8203136868369de", "guid": "bfdfe7dc352907fc980b868725387e9870ab813d40269aefe7a17d9d884a1fc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d21476a5555ab9cec670fb6cce5582fa", "guid": "bfdfe7dc352907fc980b868725387e98c54d1e63a750ccd4d74d4078efd53aa2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98440ee27b1957d9cc76024af915d54cb0", "guid": "bfdfe7dc352907fc980b868725387e98cafcae9785816656208a0202b68ed4df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823a99f805ed0a8e1d961a0addb549498", "guid": "bfdfe7dc352907fc980b868725387e98788ee005b9e55d57a123337e4b476f10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8b8d193dfdb95504ca233ec9187dce3", "guid": "bfdfe7dc352907fc980b868725387e98074aeb0d39196d7e8696e779222f1962"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841ca9fce5bbb3316ccdf83c217544b25", "guid": "bfdfe7dc352907fc980b868725387e987e03ab9234c6e7c9e9933f4092b752f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9a9c4c628675be22a68466b220f69c3", "guid": "bfdfe7dc352907fc980b868725387e98f3d067331e83107346ed0ce7452f7bc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824428241e3439d9668bbc2f82ca32b0e", "guid": "bfdfe7dc352907fc980b868725387e988dfdc174e1bea52f9bc9d8818181c9f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d66a23995cc6e2d61eeb3a8daac220e8", "guid": "bfdfe7dc352907fc980b868725387e9844698481df6d97fd6645706e46c04891"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1405ccfe7c95f8f5eb002c6be0188e9", "guid": "bfdfe7dc352907fc980b868725387e9852e8616bc4d75f260f8cf45318427062"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98041158f415525c4aed3213f8151d2cf8", "guid": "bfdfe7dc352907fc980b868725387e98b5e2948bb52aebe37891ca95a2c409c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98762cb2172ce331141275fbf10f0bcb5a", "guid": "bfdfe7dc352907fc980b868725387e987041cb5724f9bd70485a43eb0c5e6a4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825da8ce99594a238f6929d90895b3b07", "guid": "bfdfe7dc352907fc980b868725387e98e4883cb7ff407656ad6f381b8ccb28d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866bbdc9a57bc083545baee7eac53ce99", "guid": "bfdfe7dc352907fc980b868725387e98a904a44d45430dd916c94d300c4d312d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987654fde657d67ecbfe053d985914e328", "guid": "bfdfe7dc352907fc980b868725387e98a587cf7df1123bdddf975e581648d6c1"}], "guid": "bfdfe7dc352907fc980b868725387e989923941512891438930916798546b857", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac4823b08309625c8ef8098ebf4fd0c6", "guid": "bfdfe7dc352907fc980b868725387e98dd41bfa7bb4914ac0d80290204232192"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b46067814d02b02fe4ecf45d691a54cb", "guid": "bfdfe7dc352907fc980b868725387e98138fdd6e00ee3ba234dacb54a10469b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816a6c6ccefb90cde6241db54cc82b926", "guid": "bfdfe7dc352907fc980b868725387e984116dd1dd0e1adb3cec1b224153a964f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abf7a2b33a457636880c41f2ffaa90a9", "guid": "bfdfe7dc352907fc980b868725387e98369f3e7e95528c4b90e950eb45344f44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843e03d306f30e687356d70b0c3cadb55", "guid": "bfdfe7dc352907fc980b868725387e9833494a20e7a700131de953832141c908"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986be35ca5c9710efba82ed75e60538625", "guid": "bfdfe7dc352907fc980b868725387e989fc6fb3a47306c26fc6374efff268632"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdbb16e0b9b98256f442f27dead2a487", "guid": "bfdfe7dc352907fc980b868725387e983f553868b6ae178da6e8e600838b8e17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a1ecec8f9f57c4d0fc43954963cfdee", "guid": "bfdfe7dc352907fc980b868725387e98d4abd9d3eb23e0d67cb45fb4680b2128"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848d0f1b8d943c2c33328b5ec2bc1b1b9", "guid": "bfdfe7dc352907fc980b868725387e9801cc9cbdce94e4bb3bf531444c7e42f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833f955a6c9f702098b7ae5f1af17855b", "guid": "bfdfe7dc352907fc980b868725387e9899ae8dcbe14188d1afd353b422d42908"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982941a1dc5ce1f32e62fde5b1ea2f1afa", "guid": "bfdfe7dc352907fc980b868725387e9812036a56fa60134f6eaf305c37debf0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b51d9b8751bedb901c2ef7ecb55df40e", "guid": "bfdfe7dc352907fc980b868725387e98564d4b3fee2a707ceb7c5fc6efd4f6e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dadadb11542036a3bac004c39290023e", "guid": "bfdfe7dc352907fc980b868725387e9804a0d02851df0c76f34a53d5d2f6e6c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6ef1344afaa48979b2c3b0652bd44b8", "guid": "bfdfe7dc352907fc980b868725387e98265da1d5b1aa2462ad77159c204a2575"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984759061d3d44f9e5e7fadd284d512c3d", "guid": "bfdfe7dc352907fc980b868725387e98442a7d52206baa7d1170a5b60b5df744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817df6c277af2214ad81a1042b0346779", "guid": "bfdfe7dc352907fc980b868725387e98b9e0fa6c7dfe2c5e3bc461f47b47ebd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f03f110b20ece608de77701cbd6c2da1", "guid": "bfdfe7dc352907fc980b868725387e983d1be9a245f6c52778088179068e6511"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877428e1235368e44573b0f262581d77c", "guid": "bfdfe7dc352907fc980b868725387e98d73a1871568957e3f44609786f0ac40e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827c8862a70f768b0f03cb1e17e356c0e", "guid": "bfdfe7dc352907fc980b868725387e9851ecf17050944d6195e64787cbaa3183"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9f92ef442ee44575f1ef5a1f54ea63c", "guid": "bfdfe7dc352907fc980b868725387e984f94699f588d19270901ec5716c15a81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876931f14abdbffe50bbdc144dd568d2b", "guid": "bfdfe7dc352907fc980b868725387e98302d7994d12779fc4cacb51c5b2b0936"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7dfcd638af0b638cb4b270bd1d5e440", "guid": "bfdfe7dc352907fc980b868725387e9859ae9a4b9b4fb6a81d0e95537b642af0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e307044edf816ae7306b262c2fa50eca", "guid": "bfdfe7dc352907fc980b868725387e98984696c543ccfcd9f98d323984571697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98349664ab7d2eafd934cdca12ffc65402", "guid": "bfdfe7dc352907fc980b868725387e988c22c0a83a39e324600ffdf91fac4feb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986549072956f609ad2efad12ae3eb9ed4", "guid": "bfdfe7dc352907fc980b868725387e98d3ecc032e72b552e1c2775d1daa5c189"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a57185068fba979255ca4e409290e7fe", "guid": "bfdfe7dc352907fc980b868725387e987912e41c81b233423e92c79c94d1da17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae8a776e6847342b65bcce74461e9a90", "guid": "bfdfe7dc352907fc980b868725387e9853f177689474ee2e037464fc9cab5d54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981042f46f5fa31e86e7eb39bda01775f2", "guid": "bfdfe7dc352907fc980b868725387e9817b9c6d496ba2f6d356b103161f70714"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98036bab9b4472abb8f1cd946979c61497", "guid": "bfdfe7dc352907fc980b868725387e9857e06e3846d0aaaa5394b1de049c3a0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c82be0ecfef44df8b703289ce13f4c56", "guid": "bfdfe7dc352907fc980b868725387e982c6b2bf2f36b7060479bd60232afab3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ae6467934b0bb991ff5d8886fdba813", "guid": "bfdfe7dc352907fc980b868725387e98b6ff59987a49f8570307acd544aeb771"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98667e9b548f4f01273474553c26de40aa", "guid": "bfdfe7dc352907fc980b868725387e985d9e7adffbdb5c0c19eb47e6211ddb6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884f0bf86f3a17480ac866f366299343e", "guid": "bfdfe7dc352907fc980b868725387e984d5edadde502f2fbe64b5ced910f8aa2"}], "guid": "bfdfe7dc352907fc980b868725387e9867fee882e8aeb96eb62de900af13622b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e983daeca68412df0c1397d13b09f1efe6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e28c510bcdc3834514b09ab79a067e08", "guid": "bfdfe7dc352907fc980b868725387e982148686faf947a5c94dcddd230639f02"}], "guid": "bfdfe7dc352907fc980b868725387e98e381b178891ac59b257b96cb90532af7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989bb19850900ae68befa2899f8463312c", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98e1e6837be8f2a2fdccc9aaedc23a2b62", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}