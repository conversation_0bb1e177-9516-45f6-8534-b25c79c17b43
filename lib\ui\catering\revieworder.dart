import 'package:db_eats/bloc/catering_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/catering/cateringcheckout.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ReviewOrderPage extends StatefulWidget {
  final int cateringId;

  const ReviewOrderPage({super.key, required this.cateringId});

  @override
  State<ReviewOrderPage> createState() => _ReviewOrderPageState();
}

class _ReviewOrderPageState extends State<ReviewOrderPage> {
  late Map<int, int> _quantities;
  final Map<String, bool> _loadingButtons = {};
  final Map<int, int> _previousQuantities = {};
  final Set<int> _removedDishIds = {};
  int? _currentlyRemovingDishId;
  bool _hasQuantityChanges = false;

  @override
  void initState() {
    super.initState();
    _quantities = {};
    context.read<CateringBloc>().add(ViewCateringRequest(widget.cateringId));
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  bool isButtonLoading(int itemId, bool isIncrement) {
    return _loadingButtons['${itemId}_${isIncrement ? 'inc' : 'dec'}'] ?? false;
  }

  void _incrementQuantity(int cateringItemId) {
    final previousQuantity = _quantities[cateringItemId] ?? 0;
    setState(() {
      _previousQuantities[cateringItemId] = previousQuantity;
      _quantities[cateringItemId] = previousQuantity + 1;
      _loadingButtons['${cateringItemId}_inc'] = true;
      _hasQuantityChanges = true;
    });

    context.read<CateringBloc>().add(UpdateDishQuantity({
          "catering_id": widget.cateringId,
          "dish_id": cateringItemId,
          "quantity": _quantities[cateringItemId]!,
        }));
  }

  void _decrementQuantity(int cateringItemId) {
    final previousQuantity = _quantities[cateringItemId] ?? 0;
    if (previousQuantity > 0) {
      setState(() {
        _previousQuantities[cateringItemId] = previousQuantity;
        _quantities[cateringItemId] = previousQuantity - 1;
        _loadingButtons['${cateringItemId}_dec'] = true;
        _hasQuantityChanges = true;
      });

      context.read<CateringBloc>().add(UpdateDishQuantity({
            "catering_id": widget.cateringId,
            "dish_id": cateringItemId,
            "quantity": _quantities[cateringItemId]!,
          }));
    }
  }

  double get _subtotal {
    double total = 0;
    if (Initializer.viewCateringRequestModel.data?.items != null) {
      for (var item in Initializer.viewCateringRequestModel.data!.items!) {
        if (_removedDishIds.contains(item.cateringItemId)) continue;

        final price = double.tryParse(item.price ?? '0') ?? 0;
        total +=
            price * (_quantities[item.cateringItemId!] ?? item.quantity ?? 0);
      }
    }
    return total;
  }

  Widget _buildQuantityButton(
      IconData icon, Function() onPressed, bool isLoading) {
    return Container(
      width: ten * 3,
      height: ten * 3,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(sixteen),
        border: Border.all(
          color: Color(0xFFD2D4D7),
          width: twelve / 12,
        ),
      ),
      child: isLoading
          ? Center(
              child: SizedBox(
                width: sixteen,
                height: sixteen,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1F2122)),
                ),
              ),
            )
          : IconButton(
              padding: EdgeInsets.zero,
              icon: Icon(icon, size: sixteen),
              onPressed: onPressed,
            ),
    );
  }

  void _removeDish(int cateringItemId) async {
    // Show confirmation dialog
    final bool? shouldRemove = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Remove Item',
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w600,
              fontSize: eighteen,
            ),
          ),
          content: Text(
            'Are you sure you want to remove this item from your cart?',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: forteen,
              color: Color(0xFF414346),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'Cancel',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: forteen,
                  color: Color(0xFF414346),
                ),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                'Remove',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: forteen,
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );

    // Only proceed with removal if user confirmed
    if (shouldRemove == true) {
      setState(() {
        _loadingButtons['${cateringItemId}_remove'] = true;
        _currentlyRemovingDishId = cateringItemId;
      });

      context.read<CateringBloc>().add(RemoveDishFromRequest({
            "catering_id": widget.cateringId,
            "dish_id": cateringItemId,
          }));
    }
  }

  void _handleBackNavigation() {
    final hasQuantityChanges = _hasQuantityChanges;
    final hasRemovedItems = _removedDishIds.isNotEmpty;

    Navigator.of(context).pop({
      'updated': hasQuantityChanges || hasRemovedItems,
      'hasRemovedItems': hasRemovedItems,
    });
  }

  Widget _buildQuantityDisplay(int cateringItemId, int quantity) {
    if (quantity == 0) {
      return GestureDetector(
        onTap: () => _removeDish(cateringItemId),
        child: Container(
          width: ten * 3,
          height: ten * 3,
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(sixteen),
            border: Border.all(
              color: Colors.red,
              width: twelve / 12,
            ),
          ),
          child: _loadingButtons['${cateringItemId}_remove'] == true
              ? Center(
                  child: SizedBox(
                    width: sixteen,
                    height: sixteen,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                    ),
                  ),
                )
              : Icon(
                  Icons.delete,
                  size: sixteen,
                  color: Colors.red,
                ),
        ),
      );
    } else {
      return Text(
        '$quantity',
        style: TextStyle(
          fontFamily: 'Inter',
          fontWeight: FontWeight.w600,
          fontSize: forteen,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CateringBloc, CateringState>(
      listener: (context, state) {
        if (state is UpdateDishQuantitySuccess) {
          setState(() {
            _loadingButtons.clear();
            _previousQuantities.clear();
          });
        }
        if (state is UpdateDishQuantityFailed) {
          setState(() {
            _loadingButtons.clear();
            _previousQuantities.forEach((itemId, previousQuantity) {
              _quantities[itemId] = previousQuantity;
            });
            _previousQuantities.clear();
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 2),
            ),
          );
        }
        if (state is RemoveDishFromRequestSuccess) {
          setState(() {
            _loadingButtons.clear();
            _previousQuantities.clear();
            if (_currentlyRemovingDishId != null) {
              _removedDishIds.add(_currentlyRemovingDishId!);
              _quantities.remove(_currentlyRemovingDishId);
              _currentlyRemovingDishId = null;
            }
          });
          // ScaffoldMessenger.of(context).showSnackBar(
          //   SnackBar(
          //     content: Text('Dish removed successfully'),
          //     backgroundColor: Colors.green,
          //     duration: const Duration(seconds: 2),
          //   ),
          // );
        }
        if (state is RemoveDishFromRequestFailed) {
          setState(() {
            _loadingButtons.clear();
            _currentlyRemovingDishId = null;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      },
      child: BlocBuilder<CateringBloc, CateringState>(
        buildWhen: (previous, current) =>
            current is ViewCateringRequestLoading ||
            current is ViewCateringRequestSuccess ||
            current is ViewCateringRequestFailed,
        builder: (context, state) {
          if (state is ViewCateringRequestLoading) {
            return Scaffold(
              body: Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1F2122)),
                ),
              ),
            );
          } else if (state is ViewCateringRequestFailed) {
            return Scaffold(
              body: Center(child: Text('Error: ${state.message}')),
            );
          } else if (state is ViewCateringRequestSuccess) {
            final currentItems =
                Initializer.viewCateringRequestModel.data?.items ?? [];

            for (var item in currentItems) {
              if (!_quantities.containsKey(item.cateringItemId!) &&
                  !_removedDishIds.contains(item.cateringItemId!)) {
                _quantities[item.cateringItemId!] = item.quantity ?? 0;
              }
            }

            final serverItemIds =
                currentItems.map((item) => item.cateringItemId!).toSet();
            _quantities
                .removeWhere((key, value) => !serverItemIds.contains(key));
            final allItems =
                Initializer.viewCateringRequestModel.data?.items ?? [];
            final items = allItems
                .where((item) => !_removedDishIds.contains(item.cateringItemId))
                .toList();

            final hasZeroQuantityItems = items
                .any((item) => (_quantities[item.cateringItemId!] ?? 0) == 0);
            final shouldDisableCheckout = items.isEmpty || hasZeroQuantityItems;
            return WillPopScope(
                onWillPop: () async {
                  _handleBackNavigation();
                  return false;
                },
                child: Scaffold(
                  backgroundColor: Colors.white,
                  appBar: AppBar(
                    backgroundColor: Colors.white,
                    elevation: 0,
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.black),
                      onPressed: () {
                        _handleBackNavigation();
                      },
                    ),
                  ),
                  body: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                            left: sixteen, right: sixteen, top: 0),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Catering Dishes',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w600,
                              fontSize: eighteen,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: twelve),
                      Expanded(
                        child: items.isEmpty
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Image.asset(
                                      'assets/icons/empty_cart.gif',
                                      width: ten * 5,
                                      height: ten * 5,
                                    ),
                                    SizedBox(height: sixteen),
                                    Text(
                                      'No items added to cart',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: sixteen,
                                        color: Color(0xFF414346),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : Column(
                                children: [
                                  Expanded(
                                    child: ListView.builder(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: sixteen),
                                      itemCount: items.length + 1,
                                      itemBuilder: (context, index) {
                                        if (index == items.length) {
                                          return Container(
                                            padding: EdgeInsets.symmetric(
                                                vertical: eighteen),
                                            decoration: const BoxDecoration(),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  'Subtotal',
                                                  style: TextStyle(
                                                    fontFamily: 'Inter',
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: forteen,
                                                  ),
                                                ),
                                                Text(
                                                  '\$${_subtotal.toStringAsFixed(2)}',
                                                  style: TextStyle(
                                                    fontFamily: 'Inter',
                                                    fontWeight: FontWeight.w600,
                                                    fontSize: forteen,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        } // Regular item row
                                        final item = items[index];

                                        return Padding(
                                            padding: EdgeInsets.only(
                                                bottom: sixteen / 2),
                                            child: Container(
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                  color:
                                                      const Color(0xFFE1E3E6),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                color: Colors.white,
                                              ),
                                              child: Padding(
                                                padding: EdgeInsets.only(
                                                    left: twelve,
                                                    right: twelve,
                                                    top: ten,
                                                    bottom: ten),
                                                child: Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    // First column - Image
                                                    Expanded(
                                                      flex: 3,
                                                      child: ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8),
                                                        child: item.photo !=
                                                                    null &&
                                                                item.photo!
                                                                    .isNotEmpty
                                                            ? Image.network(
                                                                ServerHelper
                                                                        .imageUrl +
                                                                    item.photo!,
                                                                width: 6 * ten +
                                                                    sixteen,
                                                                height:
                                                                    6 * ten +
                                                                        sixteen,
                                                                fit: BoxFit
                                                                    .cover,
                                                                errorBuilder:
                                                                    (context,
                                                                        error,
                                                                        stackTrace) {
                                                                  return Container(
                                                                    width: 6 *
                                                                            ten +
                                                                        sixteen,
                                                                    height: 6 *
                                                                            ten +
                                                                        sixteen,
                                                                    color: Colors
                                                                        .grey
                                                                        .shade300,
                                                                    child: Icon(
                                                                        Icons
                                                                            .restaurant,
                                                                        size: ten *
                                                                            4),
                                                                  );
                                                                },
                                                              )
                                                            : Container(
                                                                width: 6 * ten +
                                                                    sixteen,
                                                                height:
                                                                    6 * ten +
                                                                        sixteen,
                                                                color: Colors
                                                                    .grey
                                                                    .shade300,
                                                                child: Icon(
                                                                    Icons
                                                                        .restaurant,
                                                                    size: ten *
                                                                        4),
                                                              ),
                                                      ),
                                                    ),

                                                    // Second column - Title, Price, Serving Size
                                                    Expanded(
                                                      flex: 5,
                                                      child: Padding(
                                                        padding:
                                                            EdgeInsets.only(
                                                                left: sixteen),
                                                        child: Column(
                                                          mainAxisSize:
                                                              MainAxisSize.max,
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .spaceAround,
                                                          children: [
                                                            Text(
                                                              item.title ??
                                                                  'Unknown Dish',
                                                              style: TextStyle(
                                                                fontFamily:
                                                                    'Inter',
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                                fontSize:
                                                                    forteen,
                                                                color: Color(
                                                                    0xFF1F2122),
                                                              ),
                                                            ),
                                                            SizedBox(
                                                                height:
                                                                    sixteen /
                                                                        2),
                                                            Text(
                                                              '\$${double.tryParse(item.price ?? '0')?.toStringAsFixed(2) ?? '0.00'}',
                                                              style: TextStyle(
                                                                fontFamily:
                                                                    'Inter',
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                fontSize:
                                                                    forteen,
                                                              ),
                                                            ),
                                                            SizedBox(
                                                                height:
                                                                    sixteen /
                                                                        2),
                                                            Container(
                                                              padding: EdgeInsets
                                                                  .symmetric(
                                                                      horizontal:
                                                                          sixteen /
                                                                              2,
                                                                      vertical:
                                                                          sixteen /
                                                                              4),
                                                              decoration:
                                                                  BoxDecoration(
                                                                color: const Color(
                                                                    0xFFE1E3E6),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            20),
                                                              ),
                                                              child: Text(
                                                                '${item.servingSize ?? 0} Servings',
                                                                style:
                                                                    TextStyle(
                                                                  fontSize:
                                                                      twelve,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                  fontFamily:
                                                                      'Inter',
                                                                  color: Color(
                                                                      0xFF1F2122),
                                                                  height: 1.0,
                                                                  letterSpacing:
                                                                      0.24,
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),

                                                    // Third column - Quantity controls
                                                    Expanded(
                                                      flex: 5,
                                                      child: Column(
                                                        children: [
                                                          Row(
                                                            children: [
                                                              Expanded(
                                                                flex: 2,
                                                                child: Row(
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .end,
                                                                  children: [
                                                                    if ((_quantities[item.cateringItemId!] ??
                                                                            0) >
                                                                        0)
                                                                      _buildQuantityButton(
                                                                        Icons
                                                                            .remove,
                                                                        () => _decrementQuantity(
                                                                            item.cateringItemId!),
                                                                        isButtonLoading(
                                                                            item.cateringItemId!,
                                                                            false),
                                                                      ),
                                                                    SizedBox(
                                                                        width:
                                                                            sixteen),
                                                                    _buildQuantityDisplay(
                                                                      item.cateringItemId!,
                                                                      _quantities[
                                                                              item.cateringItemId!] ??
                                                                          0,
                                                                    ),
                                                                    SizedBox(
                                                                        width:
                                                                            sixteen),
                                                                    _buildQuantityButton(
                                                                      Icons.add,
                                                                      () => _incrementQuantity(
                                                                          item.cateringItemId!),
                                                                      isButtonLoading(
                                                                          item.cateringItemId!,
                                                                          true),
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ));
                                      },
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ],
                  ),
                  bottomNavigationBar: Container(
                    padding: EdgeInsets.all(sixteen),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, -5),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          "Total bill includes Aenean sit amet sagittis odio. Aliquam sed nisl eu lacus aliquam efficitur a laoreet lacus.",
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            fontSize: forteen,
                            color: Color(0xFF414346),
                          ),
                        ),
                        SizedBox(height: sixteen),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: shouldDisableCheckout
                                ? null
                                : () async {
                                    final result = await Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            CateringCheckoutPage(
                                          cateringId: widget.cateringId,
                                        ),
                                      ),
                                    );
                                    if (result != null) {}
                                  },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: shouldDisableCheckout
                                  ? Colors.grey.shade300
                                  : const Color(0xFF1F2122),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(ten * 3),
                              ),
                              padding: EdgeInsets.symmetric(
                                  horizontal: twentyFour, vertical: twenty),
                            ),
                            child: Text(
                              'Continue to Checkout',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: sixteen,
                                fontWeight: FontWeight.w400,
                                letterSpacing: 0.32,
                                color: shouldDisableCheckout
                                    ? Colors.grey.shade600
                                    : Colors.white,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: ten),
                      ],
                    ),
                  ),
                ));
          }
          return Scaffold(
            body: Center(child: Text('Initializing...')),
          );
        },
      ),
    );
  }
}
