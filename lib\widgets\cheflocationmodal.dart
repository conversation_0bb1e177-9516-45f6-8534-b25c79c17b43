import 'dart:convert';

import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/storage/localstorage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;

class ChefLocationModal extends StatefulWidget {
  final String type;

  const ChefLocationModal({super.key, this.type = 'home'});

  @override
  _ChefLocationModalState createState() => _ChefLocationModalState();
}

class _ChefLocationModalState extends State<ChefLocationModal> {
  final TextEditingController _addressController = TextEditingController();
  List<Prediction> _searchResults = [];
  bool _showPredictions = false;
  bool _isLoading = false;
  String kGoogleApiKey = "AIzaSyCpAdQaZ3fPe5H0wfkI0NqMXcT8J7AW9uY";
  Position? _lastPosition;

  @override
  void initState() {
    super.initState();
    // If Initializer has latitude and longitude, reverse geocode and show in text field
    if (Initializer.latitude != null &&
        Initializer.longitude != null &&
        Initializer.latitude != '0.0' &&
        Initializer.longitude != '0.0') {
      try {
        _setAddressFromLatLng(
          double.parse(Initializer.latitude!),
          double.parse(Initializer.longitude!),
        );
      } catch (e) {
        print('Error parsing coordinates: $e');
      }
    }
  }

  Future<void> _setAddressFromLatLng(double lat, double lng) async {
    setState(() => _isLoading = true);
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(lat, lng);
      String address = '';
      if (placemarks.isNotEmpty) {
        final place = placemarks[0];
        if (place.street != null && place.street!.isNotEmpty) {
          address += place.street!;
        }
        if (place.locality != null && place.locality!.isNotEmpty) {
          if (address.isNotEmpty) address += ', ';
          address += place.locality!;
        }
        if (place.administrativeArea != null &&
            place.administrativeArea!.isNotEmpty) {
          if (address.isNotEmpty) address += ', ';
          address += place.administrativeArea!;
        }
      }
      if (address.isEmpty) {
        // fallback to Google Geocode API
        final response = await http.get(Uri.parse(
            'https://maps.googleapis.com/maps/api/geocode/json?latlng=$lat,$lng&key=$kGoogleApiKey'));
        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          if (data['status'] == 'OK' && data['results'].isNotEmpty) {
            address = data['results'][0]['formatted_address'];
          }
        }
      }
      setState(() {
        _addressController.text = address.isNotEmpty ? address : "$lat, $lng";
        _lastPosition = Position(
          latitude: lat,
          longitude: lng,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        );
        _isLoading = false;
        _showPredictions = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  Future<Position?> _getCurrentLocation() async {
    setState(() => _isLoading = true);

    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showSnackbar('Location permissions are denied');
          setState(() => _isLoading = false);
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showSnackbar(
            'Location permissions are permanently denied, please enable in settings');
        setState(() => _isLoading = false);
        return null;
      }

      Position position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);

      List<Placemark> placemarks =
          await placemarkFromCoordinates(position.latitude, position.longitude);

      if (placemarks.isNotEmpty) {
        final place = placemarks[0];
        String address = '';

        if (place.street != null && place.street!.isNotEmpty) {
          address += place.street!;
        }
        if (place.locality != null && place.locality!.isNotEmpty) {
          if (address.isNotEmpty) address += ', ';
          address += place.locality!;
        }
        if (place.administrativeArea != null &&
            place.administrativeArea!.isNotEmpty) {
          if (address.isNotEmpty) address += ', ';
          address += place.administrativeArea!;
        }

        setState(() {
          _addressController.text = address;
          _isLoading = false;
          _showPredictions = false;
          _lastPosition = position;
        });
      } else {
        final response = await http.get(Uri.parse(
            'https://maps.googleapis.com/maps/api/geocode/json?latlng=${position.latitude},${position.longitude}&key=$kGoogleApiKey'));

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          if (data['status'] == 'OK' && data['results'].isNotEmpty) {
            final address = data['results'][0]['formatted_address'];
            setState(() {
              _addressController.text = address;
              _isLoading = false;
              _showPredictions = false;
              _lastPosition = position;
            });
          }
        } else {
          setState(() {
            _addressController.text =
                "${position.latitude}, ${position.longitude}";
            _isLoading = false;
            _lastPosition = position;
          });
        }
      }

      return position;
    } catch (e) {
      _showSnackbar('Error getting location: $e');
      setState(() => _isLoading = false);
      return null;
    }
  }

  Future<void> searchPlaces(String query) async {
    if (query.isEmpty) {
      setState(() {
        _showPredictions = false;
        _searchResults = [];
      });
      return;
    }

    try {
      final response = await http.get(Uri.parse(
          'https://maps.googleapis.com/maps/api/place/autocomplete/json?input=$query&types=address&key=$kGoogleApiKey'));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'OK') {
          final results = (data['predictions'] as List)
              .map((p) => Prediction(
                    p['description'],
                    p['place_id'],
                    p['structured_formatting']?['main_text'],
                  ))
              .toList();

          setState(() {
            _searchResults = results;
            _showPredictions = results.isNotEmpty;
          });
        }
      }
    } catch (e) {
      print('Error searching places: $e');
    }
  }

  Future<void> selectPlace(Prediction prediction) async {
    try {
      final response = await http.get(Uri.parse(
          'https://maps.googleapis.com/maps/api/place/details/json?place_id=${prediction.placeId}&fields=formatted_address,geometry&key=$kGoogleApiKey'));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'OK') {
          final formattedAddress = data['result']['formatted_address'];
          final location = data['result']['geometry']['location'];

          setState(() {
            _addressController.text = formattedAddress;
            _showPredictions = false;
            _lastPosition = Position(
              latitude: location['lat'],
              longitude: location['lng'],
              timestamp: DateTime.now(),
              accuracy: 0,
              altitude: 0,
              heading: 0,
              speed: 0,
              speedAccuracy: 0,
              altitudeAccuracy: 0,
              headingAccuracy: 0,
            );
          });
        }
      }
    } catch (e) {
      print('Error getting place details: $e');
    }
  }

  void _handleDeliverHere() async {
    if (_addressController.text.isEmpty) {
      _showSnackbar('Please enter your address or use locate me');
      return;
    }

    if (_lastPosition == null) {
      final position = await _getCurrentLocation();
      if (position == null) return;
    }

    // Save address to Initializer
    await Initializer.saveAddress(_addressController.text);

    final accessToken = await LocalStorage.getAccessToken();

    if (accessToken == null || accessToken.isEmpty) {
      // Guest
      context.read<HomeBloc>().add(
            GetGuestHomeDataEvent(
              latitude: _lastPosition!.latitude,
              longitude: _lastPosition!.longitude,
            ),
          );
      Navigator.pop(context, true);
    } else {
      // Logged in
      context.read<AccountBloc>().add(
            AddSearchAddressEvent({
              'address_text': _addressController.text,
              'latitude': _lastPosition!.latitude,
              'longitude': _lastPosition!.longitude,
            }),
          );
    }
  }

  void _showSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 16),
      backgroundColor: Colors.white,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 24, 20, 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: const BoxDecoration(
                    color: Color(0xFFFFBE16),
                    shape: BoxShape.circle,
                  ),
                  child:
                      const Icon(Icons.search, color: Colors.black, size: 24),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Find chefs in your area',
                  style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter'),
                ),
                const SizedBox(height: 8),
                const Text(
                  'We would like to use your location to show\nall the amazing options in your area',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16, height: 1.5),
                ),
                const SizedBox(height: 18),
                _buildSearchInput(),
                const SizedBox(height: 13),
                BlocListener<AccountBloc, AccountState>(
                  listener: (context, state) {
                    if (state is AddSearchAddressSuccess) {
                      // First update home data
                      context.read<HomeBloc>().add(
                            GetHomeDataEvent(
                              data: {
                                'latitude': _lastPosition!.latitude,
                                'longitude': _lastPosition!.longitude,
                              },
                            ),
                          );

                      // Then refresh the addresses list
                      context
                          .read<AccountBloc>()
                          .add(ListSearchAddressesEvent());

                      // Finally close the modal
                      // Navigator.pop(context);
                    } else if (state is AddSearchAddressFailed) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                            content: Text(
                                'Failed to add address: ${state.message}')),
                      );
                    }
                  },
                  child: SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: BlocBuilder<AccountBloc, AccountState>(
                      builder: (context, state) {
                        final isLoading = state is AddSearchAddressLoading;

                        return ElevatedButton(
                          onPressed: isLoading ? null : _handleDeliverHere,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF151515),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(24),
                            ),
                          ),
                          child: isLoading
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white)),
                                )
                              : const Text(
                                  'Deliver Here',
                                  style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white),
                                ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_showPredictions && _searchResults.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(bottom: 8),
            constraints: const BoxConstraints(maxHeight: 200),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2))
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: ListView.separated(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: _searchResults.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final prediction = _searchResults[index];
                  return ListTile(
                    dense: true,
                    title: Text(prediction.description ?? '',
                        style: const TextStyle(fontSize: 14)),
                    onTap: () => selectPlace(prediction),
                  );
                },
              ),
            ),
          ),
        Container(
          height: 40,
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFE1E3E6)),
            borderRadius: BorderRadius.circular(24),
            color: Colors.white,
          ),
          child: Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: TextField(
                    controller: _addressController,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      hintText: 'Search for an address',
                      isDense: true,
                    ),
                    style: const TextStyle(fontSize: 14),
                    onChanged: searchPlaces,
                  ),
                ),
              ),
              TextButton.icon(
                onPressed: _getCurrentLocation,
                icon: _isLoading
                    ? const SizedBox(
                        width: 21,
                        height: 21,
                        child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.black54)),
                      )
                    : Image.asset(
                        'assets/icons/my_location.png',
                        width: 21,
                        height: 21,
                        color: const Color(0xFF414346),
                      ),
                label: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Locate me',
                      style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.w600,
                        fontSize: 13,
                        letterSpacing: 0.5,
                        height: 0.9,
                      ),
                    ),
                    const SizedBox(height: 1.0),
                    Container(height: 1.5, width: 58, color: Colors.black54),
                  ],
                ),
                style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12)),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class Prediction {
  final String? description;
  final String? placeId;
  final String? mainText;

  Prediction(this.description, this.placeId, this.mainText);
}
