import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/bloc/newmealplan_bloc.dart';
import 'package:db_eats/data/models/meal_plan/spicelevellistmodel.dart';
import 'package:db_eats/ui/meal_plan_new/curated/curated_chef_lists.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChooseSpiceLevelNew extends StatefulWidget {
  final int mealPlanId;
  const ChooseSpiceLevelNew({super.key, required this.mealPlanId});

  @override
  State<ChooseSpiceLevelNew> createState() => _ChooseSpiceLevelNewState();
}

class _ChooseSpiceLevelNewState extends State<ChooseSpiceLevelNew> {
  String _selectedChooseSpiceLevelNew = 'No spice';
  String _selectedSpiceLevel = '';
  int? _selectedSpiceLevelId;
  bool _isLoading = false;
  List<SpiceLevels> _spiceLevels = [];
  late final MealplanBloc _mealPlanBloc;

  @override
  void initState() {
    super.initState();
    _mealPlanBloc = BlocProvider.of<MealplanBloc>(context);
    _mealPlanBloc.add(ListSpiceLevelEvent());
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600; // Define tablet breakpoint
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return MultiBlocListener(
      listeners: [
        BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is ListSpiceLevelSuccess) {
              final spiceLevelData = state.data as SpiceLevelListModel;
              setState(() {
                _spiceLevels = spiceLevelData.data?.spiceLevels ?? [];
              });
            } else if (state is ListSpiceLevelFailed) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.message)),
              );
            }
          },
        ),
        BlocListener<NewmealplanBloc, NewMealPlanState>(
          listener: (context, state) {
            if (state is NewMealPlanStep4StateLoading) {
              setState(() {
                _isLoading = true;
              });
            } else if (state is NewMealPlanStep4StateSuccess) {
              setState(() {
                _isLoading = false;
              });
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) =>
                      CuratedChefLists(mealPlanId: widget.mealPlanId),
                ),
              );
            } else if (state is NewMealPlanStep4StateLoadFailed) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.error)),
              );
            }
          },
        ),
      ],
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF6F3EC),
          elevation: 0,
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.transparent,
          automaticallyImplyLeading: false,
          centerTitle: true,
          title: Image.asset(
            'assets/logo.png',
            width: 112,
            height: 29,
            fit: BoxFit.contain,
          ),
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(screenHeight * 0.002),
            child: Divider(
              color: Colors.grey[300],
              height: screenHeight * 0.002,
            ),
          ),
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: size.height * 0.02),
            // Close button row
            Padding(
              padding: EdgeInsets.fromLTRB(
                size.width * 0.04,
                0,
                size.width * 0.04,
                size.height * 0.03,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () => Navigator.of(context).pop(),
                    child: Icon(
                      Icons.close,
                      size: size.width * 0.06,
                    ),
                  ),
                ],
              ),
            ),
            // Progress bar
            Padding(
              padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
              child: Stack(
                children: [
                  Container(
                    height: size.height * 0.01,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE1DDD5),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  FractionallySizedBox(
                    widthFactor: 1.0, // Fully filled for step 4
                    child: Container(
                      height: size.height * 0.01,
                      decoration: BoxDecoration(
                        color: const Color(0xFF007A4D),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: size.height * 0.02),
            // Step indicator
            Padding(
              padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
              child: Text(
                '4 of 4',
                style: TextStyle(
                  fontSize: forteen,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                  fontFamily: 'Inter',
                ),
              ),
            ),
            SizedBox(height: size.height * 0.02),
            // Title
            Padding(
              padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
              child: Text(
                'Select your spice level',
                style: TextStyle(
                  fontSize: isTablet ? size.width * 0.05 : twentyFour,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[900],
                  fontFamily: 'Inter',
                ),
              ),
            ),
            SizedBox(height: size.height * 0.03),
            // Spice level options
            Flexible(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight:
                      size.height * 0.4, // or any other max height you want
                ),
                child: _spiceLevels.isEmpty
                    ? const Center(
                        child: CircularProgressIndicator(
                          color: Color(0xFF007A4D),
                        ),
                      )
                    : isTablet
                        ? _buildGridView(size, isLandscape)
                        : _buildListView(size),
              ),
            ),
            // Navigation buttons
            Padding(
              padding: EdgeInsets.all(size.width * 0.04),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Color(0xFF1F2122)),
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.07),
                        ),
                        padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.04,
                            vertical: screenHeight * 0.02),
                      ),
                      child: Text(
                        'Back',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          fontSize: size.width * 0.04,
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: size.width * 0.02),
                  Flexible(
                    child: ElevatedButton(
                      onPressed: _isLoading
                          ? null
                          : () {
                              if (_selectedSpiceLevelId == null) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content:
                                        Text('Please select a spice level'),
                                    backgroundColor: Color(0xFFE11900),
                                  ),
                                );
                                return;
                              }

                              final spiceLevelData = {
                                "meal_plan_id": widget.mealPlanId,
                                "spice_level_id": _selectedSpiceLevelId,
                              };

                              context
                                  .read<NewmealplanBloc>()
                                  .add(NewStep4MEalPlanEvent(spiceLevelData));
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.black,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.07),
                        ),
                        padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.04,
                            vertical: screenHeight * 0.02),
                      ),
                      child: _isLoading
                          ? SizedBox(
                              width: size.width * 0.05,
                              height: size.width * 0.05,
                              child: const CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              'Next',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w400,
                                fontSize: size.width * 0.04,
                                color: Colors.white,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListView(Size size) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
      itemCount: _spiceLevels.length,
      itemBuilder: (context, index) {
        final spiceLevel = _spiceLevels[index];
        final isSelected = _selectedSpiceLevelId == spiceLevel.id;
        final isNoSpice = spiceLevel.name?.toLowerCase() == 'no spice';

        return Container(
          margin: EdgeInsets.symmetric(vertical: size.height * 0.01),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? Colors.black : const Color(0xFFB9B6AD),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(size.width * 0.02),
            color:
                isSelected ? const Color(0xFFE1DDD5) : const Color(0xFFF6F3EC),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(size.width * 0.02),
              onTap: () {
                setState(() {
                  _selectedSpiceLevelId = spiceLevel.id;
                  _selectedSpiceLevel = spiceLevel.name ?? '';
                });
              },
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: size.width * 0.04,
                  vertical: size.height * 0.02,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        spiceLevel.name ?? '',
                        style: TextStyle(
                          fontSize: forteen,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Inter',
                          height: 1.4,
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                    ),
                    if (isNoSpice)
                      Center(
                        child: Image.asset(
                          'assets/icons/close_2.png',
                          width: size.width * 0.06,
                          height: size.width * 0.06,
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                    _buildSpiceIcons(index, size),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGridView(Size size, bool isLandscape) {
    return GridView.builder(
      padding: EdgeInsets.symmetric(horizontal: size.width * 0.04),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount:
            isLandscape ? 3 : 2, // 3 columns in landscape, 2 in portrait
        crossAxisSpacing: size.width * 0.02,
        mainAxisSpacing: size.height * 0.015,
        childAspectRatio: 3.0, // Adjust for content
      ),
      itemCount: _spiceLevels.length,
      itemBuilder: (context, index) {
        final spiceLevel = _spiceLevels[index];
        final isSelected = _selectedSpiceLevelId == spiceLevel.id;
        final isNoSpice = spiceLevel.name?.toLowerCase() == 'no spice';

        return Container(
          margin: EdgeInsets.symmetric(vertical: size.height * 0.01),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? Colors.black : const Color(0xFFB9B6AD),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(size.width * 0.02),
            color:
                isSelected ? const Color(0xFFE1DDD5) : const Color(0xFFF6F3EC),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(size.width * 0.02),
              onTap: () {
                setState(() {
                  _selectedSpiceLevelId = spiceLevel.id;
                  _selectedSpiceLevel = spiceLevel.name ?? '';
                });
              },
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: size.width * 0.04,
                  vertical: size.height * 0.02,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        spiceLevel.name ?? '',
                        style: TextStyle(
                          fontSize: size.width * 0.035,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Inter',
                          height: 1.4,
                          color: const Color(0xFF1F2122),
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (isNoSpice)
                      Container(
                        width: size.width * 0.05,
                        height: size.width * 0.05,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: const Color(0xFF1F2122),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.close,
                            size: size.width * 0.035,
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                      )
                    else
                      _buildSpiceIcons(index, size),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSpiceIcons(int count, Size size) {
    return Row(
      children: List.generate(
        count,
        (index) => Padding(
          padding: EdgeInsets.only(right: size.width * 0.01),
          child: Image.asset(
            'assets/icons/spice.png',
            width: size.width * 0.06,
            height: size.width * 0.06,
            color: Colors.black,
            errorBuilder: (context, error, stackTrace) => Icon(
              Icons.error,
              size: size.width * 0.06,
              color: Colors.red,
            ),
          ),
        ),
      ),
    );
  }
}
