import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/ui/common/uihelper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../bloc/account_bloc.dart';

class FAQDetailPage extends StatelessWidget {
  Widget _FaqDetailShimmer(BuildContext context) {
    return Padding(
      padding: ResponsiveUtils.pAll(context, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            height: ResponsiveUtils.h(context, 24),
            margin: EdgeInsets.only(bottom: ResponsiveUtils.h(context, 12)),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          Container(
            width: double.infinity,
            height: ResponsiveUtils.h(context, 16),
            margin: EdgeInsets.only(bottom: ResponsiveUtils.h(context, 8)),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          Container(
            width: double.infinity,
            height: ResponsiveUtils.h(context, 16),
            margin: EdgeInsets.only(bottom: ResponsiveUtils.h(context, 8)),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          Container(
            width: double.infinity,
            height: ResponsiveUtils.h(context, 16),
            margin: EdgeInsets.only(bottom: ResponsiveUtils.h(context, 8)),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(6),
            ),
          ),
        ],
      ),
    );
  }

  final int faqId;
  const FAQDetailPage({Key? key, required this.faqId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: BlocProvider.of<AccountBloc>(context)
        ..add(GetFAQData(data: {'id': faqId})),
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF6F3EC),
          elevation: 0,
          iconTheme: const IconThemeData(color: Colors.black87),
          title: Text(
            '',
            style: TextStyle(
              fontFamily: 'suisse-intl',
              fontSize: ResponsiveUtils.f(context, 18),
              color: Colors.black87,
            ),
          ),
        ),
        body: Padding(
          padding: ResponsiveUtils.pAll(context, 12.0),
          child: BlocBuilder<AccountBloc, AccountState>(
            builder: (context, state) {
              if (state is GetFAQDataLodaing) {
                return _FaqDetailShimmer(context);
              }

              if (state is GetFAQDataFailed) {
                return Center(child: Text(state.message));
              }
              final faq = Initializer.getFaqModel.data?.faq;
              if (faq == null) {
                return const Center(child: Text('No FAQ data found.'));
              }
              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: ResponsiveUtils.pAll(context, 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            faq.question ?? '',
                            style: TextStyle(
                              fontFamily: 'suisse-intl',
                              fontSize: ResponsiveUtils.f(context, 16),
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          SizedBox(height: ResponsiveUtils.h(context, 8)),
                          Text(
                            faq.answer ?? '',
                            style: TextStyle(
                              fontSize: ResponsiveUtils.f(context, 14),
                              color: Colors.black87,
                              height: 1.5,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
