{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983c3e5578711d093a72fe4c7cfad8da03", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fcc40c4f099d332d8947ac34579bb54a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ec1824b8d0b7f574e38897e61b0d0413", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9814db34663fb9acf87ec46d6e62843458", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ec1824b8d0b7f574e38897e61b0d0413", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f8cdb29f58954e23b21cc37b0a2be3d9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98371a757a792d97226b1e11d24f15e6a5", "guid": "bfdfe7dc352907fc980b868725387e98aa3def0405a4fc4088222e9c42f64213"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaba791926045b31827f6d7ee5fad796", "guid": "bfdfe7dc352907fc980b868725387e98c05046b1ce6364e7a094ba1ef43ea166", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b0676f60f21322639e5f09885e83fc3", "guid": "bfdfe7dc352907fc980b868725387e98a6bdcec24b92284056934ad24a7d7da3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e194e289b81d1732ac4be81e815361b6", "guid": "bfdfe7dc352907fc980b868725387e989103fa34b1af0b07aac53cf28378f98e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811a671f668cc3eef1546223b0a8e91fe", "guid": "bfdfe7dc352907fc980b868725387e985f0ab3803ae5a354b8e6723daf1c2f68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885e598943a1d3a0cf89b7fbaf653a665", "guid": "bfdfe7dc352907fc980b868725387e9893caf7882adb6f3bf92ec2117417c533"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b35f5ad791f4d046e7b7472c1191473", "guid": "bfdfe7dc352907fc980b868725387e9804bb9f3a1a03fb017c8fac15e6509263"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be80e8e0ddbcae10a79304535af6ebbb", "guid": "bfdfe7dc352907fc980b868725387e98d6e43d2df8ed48eebe96730edf8081a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857f654b327f6c9b521b607d0d82c77c4", "guid": "bfdfe7dc352907fc980b868725387e98ffd2b220f5068b8c09adc3422c429ce2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895c8b1dad835ebe84739a5a1d05b7f99", "guid": "bfdfe7dc352907fc980b868725387e98395efeddad1bc3610ed81737b145cb6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982db55048cce73705ee868c5360fd31c4", "guid": "bfdfe7dc352907fc980b868725387e9876f895af9a64fb4abf7b4cb34334e55a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882a4224cd2734906e77743237e0745f7", "guid": "bfdfe7dc352907fc980b868725387e98038878fc9d11e072d9d583c2e70b77b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d40834350096a5dd473fbd2793ed9595", "guid": "bfdfe7dc352907fc980b868725387e9823e97f9b79ea58d67a77a78f56382141"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e942bb7108a9376577837086dcd0069", "guid": "bfdfe7dc352907fc980b868725387e9894f8b174d5f762ee2834c5300a7a72ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5c56134cdedc28b9dca43c28611661d", "guid": "bfdfe7dc352907fc980b868725387e98ea363a4c58856e2e8dfb56db492abb0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811d2597acdb5546be311377dcb10a83c", "guid": "bfdfe7dc352907fc980b868725387e988b8a328d0c8a222ffbee91280b0eb5d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3c895d33195686cf7607fa0a004b40e", "guid": "bfdfe7dc352907fc980b868725387e98b99d501b574ed41bf5883e03768e588b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835fe3cde8635f8dd7e5517b3ce509588", "guid": "bfdfe7dc352907fc980b868725387e98ef69af06cac7aaf256ff85853eec9101", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ed5a659ff0893355d05ea0c0335fe93", "guid": "bfdfe7dc352907fc980b868725387e98c2ebc65324de047d55bcca61b14d9a39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a550ad345f2f3c84add9f9192d21b19a", "guid": "bfdfe7dc352907fc980b868725387e986542cb1fa4f2f3d2d43486948e8dc70c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13c6f06fb0ee8488acb875080775fae", "guid": "bfdfe7dc352907fc980b868725387e986ce934f558440c9f30258b82fa960de9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fb6f2f6ad7c37ed10c017de234bbcbb", "guid": "bfdfe7dc352907fc980b868725387e9866ef86456169bdd774a2e5e490929230"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cbd0e2d5c742fa2917b6f43e5174df2", "guid": "bfdfe7dc352907fc980b868725387e982807ee08708459ebff62778fe1c8e178", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bd6a8e7bcaabda538492994ce28dc8b3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989fef3281f2f5fa6e9a1b0e8dcaee237a", "guid": "bfdfe7dc352907fc980b868725387e98a7689e4e14af6feae59c133df92eb1ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98884d6b37a7391d3e33799dab27c93f0c", "guid": "bfdfe7dc352907fc980b868725387e98dcdc7ffbe20b1e3483312bd576180412"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3703997aa6d0572d9478e1edfa9e866", "guid": "bfdfe7dc352907fc980b868725387e98292d44c49ab44fc656ed30796aa19c94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985603157ff20296b93f47030cd0c38055", "guid": "bfdfe7dc352907fc980b868725387e987ced25c7b62ac7123cb1e9108d61d581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0ab0ccd30d6c560916f59611565f9b2", "guid": "bfdfe7dc352907fc980b868725387e986b0c709d4305a4f71bbe55db20d381d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956e5d00f408f13895cee5ae79cc9fc0", "guid": "bfdfe7dc352907fc980b868725387e9816bee31dbfbb2b7d2a1e68b39d0eb119"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e725dc70f3115126d4dc46b421fcade5", "guid": "bfdfe7dc352907fc980b868725387e98f96967604c47ed47896875643d98981a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d681f708a8508ac3a36d639d2f6751f", "guid": "bfdfe7dc352907fc980b868725387e985860be3da70a7aefdcd1576951cb68f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847f19e4424c9ef968d6d6b33ca965dd7", "guid": "bfdfe7dc352907fc980b868725387e98841d5bc2408221fdf870fbbb164bcbd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98888a6965fe80f3cf0e0312a72841dcee", "guid": "bfdfe7dc352907fc980b868725387e98235ae6b210794815aab7d2a965ed3d6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98818555ba34e81aada26bae6141e88360", "guid": "bfdfe7dc352907fc980b868725387e98ccdb331b788b617daac913554394f0b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae03b736cf780a0769d264514bf3f30b", "guid": "bfdfe7dc352907fc980b868725387e985f502a583673b5a20b7c86d3a25fb166"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98affff77b59640c787ad989e2c2006f17", "guid": "bfdfe7dc352907fc980b868725387e98bf9d62072e51046bcf28355e2e3567e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3b4c74929390a4977401362ece2d468", "guid": "bfdfe7dc352907fc980b868725387e98979ac897ec4d2589a6f01293bd90e3e5"}], "guid": "bfdfe7dc352907fc980b868725387e98f23e7d97ce6629dd2dc092d2879f6b65", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e988b281581a0fd33da6107498e8254806e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b194461b3d65889636e9bae473ca683", "guid": "bfdfe7dc352907fc980b868725387e988d0f93901d5dd43077d803e7eb444c14"}], "guid": "bfdfe7dc352907fc980b868725387e98526a5c9babf258c4fb5b0ae22acccf7f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d1f25b2947252e778845b1b5fb380d37", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98f6b8350dcc488aabc2942c9f6f538dd5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}