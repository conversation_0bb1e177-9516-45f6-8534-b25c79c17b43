class SettingsChangeModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  SettingsChangeModel({this.status, this.message, this.statusCode, this.data});

  SettingsChangeModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['status_code'] = statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  int? id;
  int? customerId;
  bool? pushNotification;
  bool? smsOffers;
  bool? emailOffers;
  String? createdAt;
  String? updatedAt;

  Data(
      {this.id,
      this.customerId,
      this.pushNotification,
      this.smsOffers,
      this.emailOffers,
      this.createdAt,
      this.updatedAt});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    customerId = json['customer_id'];
    pushNotification = json['push_notification'];
    smsOffers = json['sms_offers'];
    emailOffers = json['email_offers'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['customer_id'] = customerId;
    data['push_notification'] = pushNotification;
    data['sms_offers'] = smsOffers;
    data['email_offers'] = emailOffers;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
