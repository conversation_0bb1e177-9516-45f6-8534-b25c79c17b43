import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';

class LocationPrediction {
  final String? description;
  final String? placeId;
  LocationPrediction({this.description, this.placeId});
}

class LocationSearchField extends StatefulWidget {
  final String googleApiKey;
  final void Function(String address, double lat, double lng) onLocationSelected;
  final void Function()? onLocateMe; // Optional callback for "Locate me"

  const LocationSearchField({
    Key? key,
    required this.googleApiKey,
    required this.onLocationSelected,
    this.onLocateMe,
  }) : super(key: key);

  @override
  State<LocationSearchField> createState() => _LocationSearchFieldState();
}

class _LocationSearchFieldState extends State<LocationSearchField> {
  final TextEditingController _controller = TextEditingController();
  List<LocationPrediction> _predictions = [];
  bool _isLoading = false;
  bool _isLocating = false;
  Timer? _debounce;

  void _onChanged(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    if (value.trim().isEmpty) {
      setState(() => _predictions = []);
      return;
    }
    _debounce = Timer(const Duration(milliseconds: 300), () => _search(value));
  }

  Future<void> _search(String query) async {
    setState(() => _isLoading = true);
    try {
      final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${Uri.encodeComponent(query)}&key=${widget.googleApiKey}',
      );
      final response = await http.get(url);
      final json = jsonDecode(response.body);
      if (json['status'] == 'OK') {
        setState(() {
          _predictions = (json['predictions'] as List)
              .map((p) => LocationPrediction(
                    description: p['description'],
                    placeId: p['place_id'],
                  ))
              .toList();
        });
      } else {
        setState(() => _predictions = []);
      }
    } catch (_) {
      setState(() => _predictions = []);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _selectPrediction(LocationPrediction prediction) async {
    setState(() => _isLoading = true);
    try {
      final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/place/details/json?place_id=${prediction.placeId}&fields=geometry&key=${widget.googleApiKey}',
      );
      final response = await http.get(url);
      final json = jsonDecode(response.body);
      if (json['status'] == 'OK') {
        final location = json['result']['geometry']['location'];
        final lat = location['lat'] as double;
        final lng = location['lng'] as double;
        widget.onLocationSelected(prediction.description ?? '', lat, lng);
        _controller.text = prediction.description ?? '';
        setState(() => _predictions = []);
      }
    } catch (_) {
      // Handle error
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() => _isLocating = true);
    
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _showLocationError('Location services are disabled. Please enable location services.');
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showLocationError('Location permissions are denied');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showLocationError('Location permissions are permanently denied. Please enable them in settings.');
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Reverse geocode to get address
      await _reverseGeocode(position.latitude, position.longitude);
      
      // Call the optional callback if provided
      if (widget.onLocateMe != null) {
        widget.onLocateMe!();
      }
      
    } catch (e) {
      _showLocationError('Failed to get current location: ${e.toString()}');
    } finally {
      setState(() => _isLocating = false);
    }
  }

  Future<void> _reverseGeocode(double lat, double lng) async {
    try {
      final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/geocode/json?latlng=$lat,$lng&key=${widget.googleApiKey}',
      );
      final response = await http.get(url);
      final json = jsonDecode(response.body);
      
      if (json['status'] == 'OK' && json['results'].isNotEmpty) {
        final address = json['results'][0]['formatted_address'] as String;
        
        // Update the text field with the found address
        _controller.text = address;
        
        // Call the onLocationSelected callback
        widget.onLocationSelected(address, lat, lng);
        
        // Clear predictions
        setState(() => _predictions = []);
      } else {
        // If reverse geocoding fails, still provide the coordinates with a generic address
        final address = 'Current Location ($lat, $lng)';
        _controller.text = address;
        widget.onLocationSelected(address, lat, lng);
        setState(() => _predictions = []);
      }
    } catch (e) {
      // If reverse geocoding fails, still provide the coordinates
      final address = 'Current Location ($lat, $lng)';
      _controller.text = address;
      widget.onLocationSelected(address, lat, lng);
      setState(() => _predictions = []);
    }
  }

  void _showLocationError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFE1E3E6), width: 1),
            borderRadius: BorderRadius.circular(screenWidth * 0.09),
            color: Colors.white,
          ),
          padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.045),
          height: screenHeight * 0.055,
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _controller,
                  onChanged: _onChanged,
                  style: TextStyle(
                    fontSize: screenWidth * 0.035,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Inter',
                    color: const Color(0xFF1F2122),
                  ),
                  decoration: InputDecoration(
                    hintText: 'Street, Postal code',
                    hintStyle: TextStyle(
                      color: const Color(0xFF66696D),
                      fontSize: screenWidth * 0.035,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                  ),
                ),
              ),
              TextButton.icon(
                onPressed: _isLocating ? null : _getCurrentLocation,
                icon: _isLocating
                    ? SizedBox(
                        width: screenWidth * 0.04,
                        height: screenWidth * 0.04,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: const Color(0xFF1F2122),
                        ),
                      )
                    : Icon(Icons.my_location,
                        size: screenWidth * 0.05, color: const Color(0xFF1F2122)),
                label: Text(
                  _isLocating ? 'Locating...' : 'Locate me',
                  style: TextStyle(
                    fontSize: screenWidth * 0.030,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Inter',
                    color: _isLocating ? const Color(0xFF66696D) : const Color(0xFF1F2122),
                    decoration: TextDecoration.underline,
                  ),
                ),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.zero,
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
              if (_isLoading && !_isLocating)
                Padding(
                  padding: EdgeInsets.only(left: 8),
                  child: SizedBox(
                    width: 18,
                    height: 18,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
            ],
          ),
        ),
        if (_predictions.isNotEmpty)
          Container(
            margin: EdgeInsets.only(top: screenHeight * 0.01),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth * 0.03),
              border: Border.all(color: const Color(0xFFE1E3E6), width: 1),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.15),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            constraints: BoxConstraints(maxHeight: screenHeight * 0.2),
            child: ListView.separated(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: _predictions.length,
              separatorBuilder: (_, __) => const Divider(
                height: 1,
                color: Color(0xFFE1E3E6),
              ),
              itemBuilder: (context, i) {
                final p = _predictions[i];
                return ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: screenWidth * 0.04,
                    vertical: screenHeight * 0.005,
                  ),
                  title: Text(
                    p.description ?? '',
                    style: TextStyle(
                      fontSize: screenWidth * 0.035,
                      fontFamily: 'Inter',
                    ),
                  ),
                  onTap: () => _selectPrediction(p),
                );
              },
            ),
          ),
      ],
    );
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _controller.dispose();
    super.dispose();
  }
}