class DeliveryTimeModel {
  bool? status;
  int? statusCode;
  String? message;
  DeliveryTimeData? data;

  DeliveryTimeModel({this.status, this.statusCode, this.message, this.data});

  DeliveryTimeModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    statusCode = json['status_code'];
    message = json['message'];
    data = json['data'] != null ? DeliveryTimeData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['status_code'] = this.statusCode;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DeliveryTimeData {
  List<DeliveryTimeItem>? data;

  DeliveryTimeData({this.data});

  DeliveryTimeData.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <DeliveryTimeItem>[];
      json['data'].forEach((v) {
        data!.add(DeliveryTimeItem.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DeliveryTimeItem {
  int? id;
  String? name;
  String? description;
  bool? status;
  String? cost;
  String? createdAt;
  String? updatedAt;

  DeliveryTimeItem(
      {this.id,
      this.name,
      this.description,
      this.status,
      this.cost,
      this.createdAt,
      this.updatedAt});

  DeliveryTimeItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    status = json['status'];
    cost = json['cost'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['description'] = this.description;
    data['status'] = this.status;
    data['cost'] = this.cost;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}