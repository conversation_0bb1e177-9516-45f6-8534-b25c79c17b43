class ListFavouriteDishesModel {
  bool? status;
  List<FavouriteDishData>? data;
  int? statusCode;

  ListFavouriteDishesModel({this.status, this.data, this.statusCode});

  ListFavouriteDishesModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    if (json['data'] != null) {
      data = <FavouriteDishData>[];
      json['data'].forEach((v) {
        data!.add(FavouriteDishData.fromJson(v));
      });
    }
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['status_code'] = statusCode;
    return data;
  }
}

class FavouriteDishData {
  int? favouriteId;
  int? dishId;
  String? dishName;
  String? dishPhoto;
  int? chefId;
  String? chefName;
  String? chefPhoto;
  List<ServingSizePrice>? servingSizePrices;

  FavouriteDishData({
    this.favouriteId,
    this.dishId,
    this.dishName,
    this.dishPhoto,
    this.chefId,
    this.chefName,
    this.chefPhoto,
    this.servingSizePrices,
  });

  FavouriteDishData.fromJson(Map<String, dynamic> json) {
    favouriteId = json['favourite_id'];
    dishId = json['dish_id'];
    dishName = json['dish_name'];
    dishPhoto = json['dish_photo'];
    chefId = json['chef_id'];
    chefName = json['chef_name'];
    chefPhoto = json['chef_photo'];
    if (json['serving_size_prices'] != null) {
      servingSizePrices = <ServingSizePrice>[];
      json['serving_size_prices'].forEach((v) {
        servingSizePrices!.add(ServingSizePrice.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['favourite_id'] = favouriteId;
    data['dish_id'] = dishId;
    data['dish_name'] = dishName;
    data['dish_photo'] = dishPhoto;
    data['chef_id'] = chefId;
    data['chef_name'] = chefName;
    data['chef_photo'] = chefPhoto;
    if (servingSizePrices != null) {
      data['serving_size_prices'] =
          servingSizePrices!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ServingSizePrice {
  int? id;
  int? servingSizeId;
  String? price;
  ServingSize? servingSize;

  ServingSizePrice({
    this.id,
    this.servingSizeId,
    this.price,
    this.servingSize,
  });

  ServingSizePrice.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    servingSizeId = json['serving_size_id'];
    price = json['price'];
    servingSize = json['serving_size'] != null
        ? ServingSize.fromJson(json['serving_size'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['serving_size_id'] = servingSizeId;
    data['price'] = price;
    if (servingSize != null) {
      data['serving_size'] = servingSize!.toJson();
    }
    return data;
  }
}

class ServingSize {
  int? id;
  String? title;
  int? serves;

  ServingSize({
    this.id,
    this.title,
    this.serves,
  });

  ServingSize.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    serves = json['serves'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['serves'] = serves;
    return data;
  }
}
