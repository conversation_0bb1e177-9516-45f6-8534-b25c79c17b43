class CateringTypeModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  CateringTypeModel({this.status, this.message, this.statusCode, this.data});

  CateringTypeModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<CateringTypes>? cateringTypes;

  Data({this.cateringTypes});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['catering_types'] != null) {
      cateringTypes = <CateringTypes>[];
      json['catering_types'].forEach((v) {
        cateringTypes!.add(new CateringTypes.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.cateringTypes != null) {
      data['catering_types'] =
          this.cateringTypes!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class CateringTypes {
  int? id;
  String? name;

  CateringTypes({this.id, this.name});

  CateringTypes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}
