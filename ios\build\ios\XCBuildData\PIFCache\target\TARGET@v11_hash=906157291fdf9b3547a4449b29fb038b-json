{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9852cdca8607df70d71e2f9846f4dd05a8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e2277be28a487919925c019eadbc3d4d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a6d0556f3dfcb1598bed8b7cc41d267c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9837d6095d2edb1b19ee11d5bb60efcadb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a6d0556f3dfcb1598bed8b7cc41d267c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98266c1528ba49ac2bac63a560ee690878", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b36a410eb73f442d842fb8f6e985182b", "guid": "bfdfe7dc352907fc980b868725387e98e266072598b9c98e52aea7bbded35295", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9800d50e50f4ef8e57b903ef311be596b5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9887ab155b7114d3ed7f75d40b4d2b6ef5", "guid": "bfdfe7dc352907fc980b868725387e985e71767b2cf6bcbe4aa7e814e13620dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843285d09d5f36b58a4098a66c18285e2", "guid": "bfdfe7dc352907fc980b868725387e9831525010bfb1b1e0c5d26fea61e477e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834e71158ae8c105620c565824e02759d", "guid": "bfdfe7dc352907fc980b868725387e98b57b5816d65fc28a088d58c468741c6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98558c1caa9b18a43682c678813ec55faa", "guid": "bfdfe7dc352907fc980b868725387e988211440440856f50cbf26c7badc1b866"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984140047c2de74b85b4b83a10e9ae1725", "guid": "bfdfe7dc352907fc980b868725387e9835e53fb2e560775f470c652d9b579a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98804ae179af5bd207284746aaba4c5893", "guid": "bfdfe7dc352907fc980b868725387e986794b6c0939151035c2ab274f1711125"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98712e6f26360e389022a84753bde09aee", "guid": "bfdfe7dc352907fc980b868725387e98f34a32fc4451160f848d719fc5f81f12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841007498486abd884ababbe447ae5da2", "guid": "bfdfe7dc352907fc980b868725387e988e64943a42cf12277e5c7e4f72f2bf24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba2ac5325e34785027ac913e6dc95fac", "guid": "bfdfe7dc352907fc980b868725387e98a7f262082f40c1e9a308fa29c64e0cfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98302e0ab338f6757d10d4d443407039b8", "guid": "bfdfe7dc352907fc980b868725387e98e83c626b280a1984826e0a8fec0e8118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9734fe5c12d78df16e259ec7adc17da", "guid": "bfdfe7dc352907fc980b868725387e98d22b444d6d153e8f3e7e449aefa370eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d5b75d9c30c18a042f60d3ae21e4a39", "guid": "bfdfe7dc352907fc980b868725387e98089f83e5952fce913475cd95c38bd99c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c7be4fd411927c1262dfc56add7d439", "guid": "bfdfe7dc352907fc980b868725387e9890506ea18e7f21cc71daca82d4d212cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a373045f2f791381a2cc1347eac70be", "guid": "bfdfe7dc352907fc980b868725387e98dea4509592aa0c6fabb9766f37c3e38e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98717820adb27031aef085dc6a7e425fbf", "guid": "bfdfe7dc352907fc980b868725387e987c4c617ae775f307055be664aec1c2ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849a31958e479fcdca9c69da25219e515", "guid": "bfdfe7dc352907fc980b868725387e984dc26de453d9cfa4e5eadaa36416751b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d335bbdc2bb180cf4faec7b73b5e7f4", "guid": "bfdfe7dc352907fc980b868725387e98ab2ce06f23ea3006eb332485697eb9c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4e8c13245aa02c85ee5d58b8146cf57", "guid": "bfdfe7dc352907fc980b868725387e9845d840f476efcee1af7c183ac5d9af8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d43d4e8b75b14f3fa2ac231f7ac5bd88", "guid": "bfdfe7dc352907fc980b868725387e98dce3ee724f424304a4d660f38a6944da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e325f6fda656d6923eda750931c1c8ba", "guid": "bfdfe7dc352907fc980b868725387e98a73c04e13e52b41ee070d2a467c1e6f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ced6d2c289297b7248cae5b9a2e14622", "guid": "bfdfe7dc352907fc980b868725387e9816762b1be109f65eabe0994f9ca493b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858604760e7b7899ecfece08858628b64", "guid": "bfdfe7dc352907fc980b868725387e98e8bd2430c067efd14e4ffb7200b3f64b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98860faef35af310c496977b5940fbfa01", "guid": "bfdfe7dc352907fc980b868725387e98718446df57e9895d602c6fcd3e141240"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffef61de713e8e5211535de56e2723b4", "guid": "bfdfe7dc352907fc980b868725387e98131857d7b303a6374fbc9fd721e9cfba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989df1a4cc131bcb0ec87918eecdfc2a6b", "guid": "bfdfe7dc352907fc980b868725387e987dd8c291d9a9e17029c957748eafbb17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b906e0675952e83449607be95fed0e97", "guid": "bfdfe7dc352907fc980b868725387e98f3e08e667b79ef42c07eddba7c2a976b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb9a5399db0f2876a141168163c551da", "guid": "bfdfe7dc352907fc980b868725387e98fcb311f8096cd0a656a7902a194258f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc3917ee0e99cbad176dac3901aad050", "guid": "bfdfe7dc352907fc980b868725387e9801de043157615b1222c171fbba127ae8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ab65da8ef9a4c9c396d39ca0a4d53af", "guid": "bfdfe7dc352907fc980b868725387e98b729a65c86fa445602b60f2c0498ba91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987113215a051e6abacd7e8853e5b2d980", "guid": "bfdfe7dc352907fc980b868725387e989622e82a87fdc3d8adc1917f6d0cfad9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e360694dc02217a6c48c832eeac36599", "guid": "bfdfe7dc352907fc980b868725387e9811424d6f02786bae164d9359f3e22d68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cf15ae5bf7f24871e1dcd681b23c558", "guid": "bfdfe7dc352907fc980b868725387e9871026481ccbfab3f1beb1d91210e6617"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986de07e08b06bc40c9a7454845c65691d", "guid": "bfdfe7dc352907fc980b868725387e98280087e742826f7b7aa41c9c25d2a697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860361ebfb6aa9ae1185c191a95f49e47", "guid": "bfdfe7dc352907fc980b868725387e9839d75e19d3f22529f017625e9384a1a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851d02c9d4e46678c3ddfac183723bcac", "guid": "bfdfe7dc352907fc980b868725387e98a1d55e0acf299eded1f99b334bf3e857"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e37a279f8efda4e1f904530842eb3f56", "guid": "bfdfe7dc352907fc980b868725387e98e2d4a1e3d1aa90bcc74e288af5e4ebb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c69aab01230f467fb098028f3be346a", "guid": "bfdfe7dc352907fc980b868725387e98ab91b99606cee030e5afce8505e821a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b3fce9e811abb3179519a67318af0a", "guid": "bfdfe7dc352907fc980b868725387e98910412d9f5b863c249eefa42c85fd50b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce2b63aa1fe2c901b44a7d15e31016ca", "guid": "bfdfe7dc352907fc980b868725387e98467532b9e6540a51e9c961700d776523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a39b5ebe684a6eef6a8249704054e24", "guid": "bfdfe7dc352907fc980b868725387e9851245b6b9117d368fcfabcace1603a5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98318105dc2fbacbc9c52a449e3836130a", "guid": "bfdfe7dc352907fc980b868725387e983c69a79206e5b87de2802ec2a2391d53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eed447ce410fac10568ab5262f660fa7", "guid": "bfdfe7dc352907fc980b868725387e98187490859cb24e2d0e1921e651444b9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eec55c44ea1fcb1ddd04cbbf6eb874e2", "guid": "bfdfe7dc352907fc980b868725387e98c915c74d9f0a28227f77a2c0186bc091"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d1a5959ad7ca6af63a6a36c31cfa4f3", "guid": "bfdfe7dc352907fc980b868725387e98d646c134a27df478370327c27db28a44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b958eb5cfeee07f45cf88c65177ccd57", "guid": "bfdfe7dc352907fc980b868725387e98940ad400b820ce8802bee9b0489197e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982175f723c83c54c970ceacb1d3e4d608", "guid": "bfdfe7dc352907fc980b868725387e98c407e37b00ccd454776e0c1afc59af12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98128df19163df227f9adb8ac9c9d328b0", "guid": "bfdfe7dc352907fc980b868725387e9868344afce35baf05114ccfd51ee8f325"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae2303608a552a3203b454fe59d4f4c9", "guid": "bfdfe7dc352907fc980b868725387e98bed9c58ac8bd697d31385454670d3f23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1151cab2641bedc0d1f723f326972ba", "guid": "bfdfe7dc352907fc980b868725387e98a8d3ed72e68273aa9b45af1482d0ab81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffd3dd1bd06b2095e1a623ab7c37e6eb", "guid": "bfdfe7dc352907fc980b868725387e9811c557859e61ed80dc9c2a1d4eba9969"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98122c4b142da2a1138227f752a74d20c3", "guid": "bfdfe7dc352907fc980b868725387e982650e6f6a724d30b892cf5ae66b63637"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98777fcda95571f9d794926c30d2726af5", "guid": "bfdfe7dc352907fc980b868725387e988a819307a82baa0e057f01e2c5c8fda4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983194a72cb061428bbb10b8c9cca6d7d5", "guid": "bfdfe7dc352907fc980b868725387e98d1b72562bc1b600823e710486bfd29b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4e363a50353fe90d86c5e960c2e54ca", "guid": "bfdfe7dc352907fc980b868725387e9819f99e8789ad2b451e63645768cb993b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982db2009fd12a3bf5def48a2723b5247c", "guid": "bfdfe7dc352907fc980b868725387e98cbd4ed178572905e5f79227502c26d9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98482080c6c33270f8ee833ea7aa85012a", "guid": "bfdfe7dc352907fc980b868725387e98300021b9b9a4010277e262c7b1bdd899"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981693c85367989321154e60932b992f41", "guid": "bfdfe7dc352907fc980b868725387e9840d414d6d90bca8c0a826d3e7f08c4b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892c58ab1ee6411740b55681b94b320e5", "guid": "bfdfe7dc352907fc980b868725387e9878d11d2e119bfc334ced087f3d45b874"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad9a0e8ab2f8780c3be89fcf1f3bf275", "guid": "bfdfe7dc352907fc980b868725387e98d2c807268d32c38e3aaa6a836f7c4ef7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888860085924ca802a6e4effe417c8fb6", "guid": "bfdfe7dc352907fc980b868725387e98549a86644f77b1feb8b2c94f79d3f194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98328bb03a1d5b7ad17d63d887b1a55987", "guid": "bfdfe7dc352907fc980b868725387e986f82fe15cd763d022a41430a93da8a2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b457cf95c1f84b0a3c8d73498e355467", "guid": "bfdfe7dc352907fc980b868725387e981130f662e555066c50717a8a8c57c0e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a7745cd194308f9e0a31862068d6dbc", "guid": "bfdfe7dc352907fc980b868725387e9810fbd4abdf5ccfa1e6d9e2f53193bad0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fea7088618b06994720ad760077762c5", "guid": "bfdfe7dc352907fc980b868725387e98ddb97ae0d0784417cf2d6521ed00a178"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801368ed716ce36f64d35dac16033647f", "guid": "bfdfe7dc352907fc980b868725387e98fe230647cd1411ecd5dc2bf2a9d4e2a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bfb509b03adbd570f626a2b1faf5cd8", "guid": "bfdfe7dc352907fc980b868725387e984da98442a79ed8a0e1387a0ce5b27b1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bba54d2410dbe2181f0a2c0288405f58", "guid": "bfdfe7dc352907fc980b868725387e98b6d16efc003dad8f3653a0e13ef727ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3e34209cb9db2b1b568e068076741cf", "guid": "bfdfe7dc352907fc980b868725387e981dc23601da91a33d58485de82cac566e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daa622b90ace630cc19f1fa6da6d9105", "guid": "bfdfe7dc352907fc980b868725387e98ff7d8badc1c9d5ee2e1d88269964c805"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0bf5b0c012fa69d75fbb4d93a050930", "guid": "bfdfe7dc352907fc980b868725387e98c5f061912801e82f57aa72405a11cc49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803b3890fe8b0eae0003caa3994b4aaea", "guid": "bfdfe7dc352907fc980b868725387e98372ebd4fa8fe796651da1765a5891a57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836353514c3a6ddd1ad72d9cd8cd453db", "guid": "bfdfe7dc352907fc980b868725387e98eb20c013caa5de6a362fdf6ac6c9943a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6edb7e196636d95ec1b39b2967462be", "guid": "bfdfe7dc352907fc980b868725387e98c871d1ce93c5ed836a06e670bc9f96c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd288a3f983a00a5c2bcf3d5e93a1499", "guid": "bfdfe7dc352907fc980b868725387e981f2144de32952af0363dea8b5ef341d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fccd15abdb33e872efb0d1510db997aa", "guid": "bfdfe7dc352907fc980b868725387e986448cc18fccf832b75437980db5a5e21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccb31fd221bd58077da04ec1702b9f0c", "guid": "bfdfe7dc352907fc980b868725387e986c25ace66908c54f0b43bf2f789111f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e100097dbce9a4b80bb754d4a33401dc", "guid": "bfdfe7dc352907fc980b868725387e980774b6b4832c0461a0f8629ee85b1feb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98354c00ed75b0846143e23980c4828f41", "guid": "bfdfe7dc352907fc980b868725387e984bd29858a3836dbea44f997fbdd4e1b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d5387bf8c90317bd9fda9dd8dd68b26", "guid": "bfdfe7dc352907fc980b868725387e982e213790a9afe76ce56981f6648c8212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cfaded99d56086494d592af22de727a", "guid": "bfdfe7dc352907fc980b868725387e98a3b60caba9104f2262e0e30bd6e565ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988259e229b337678a42461ff32ba17b7b", "guid": "bfdfe7dc352907fc980b868725387e981fcad40ff117428162e99a7a25ae0c41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98501aeae882980cafe3675b868830dd98", "guid": "bfdfe7dc352907fc980b868725387e98ce34395ba64614458fc90b5bd4385cf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98628e0e9cae2a68a420f8c08b39578fcd", "guid": "bfdfe7dc352907fc980b868725387e985a90a85f1d2f7d5513c41ebb2947feeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98629cd2510fadd0c510e05cd677b16ead", "guid": "bfdfe7dc352907fc980b868725387e985145a37f733cb33167c13152e5b57b13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865a55e68fd06d4cbbcd084402b6d21f8", "guid": "bfdfe7dc352907fc980b868725387e98367ba72a0bdff4fe465a78e65e2586e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceca360182186130f4078f61e0d5e556", "guid": "bfdfe7dc352907fc980b868725387e9892ca5dcf5d43dba7241c5593929aafee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f8f0f2a248502574949f1f3ce17c7de", "guid": "bfdfe7dc352907fc980b868725387e98bb52774805f03428eb40a2f9b2147a86"}], "guid": "bfdfe7dc352907fc980b868725387e98997d1f3320b96a489bbfeca42b5f2b52", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9820ad3732878a4721ac229b7e03e0956a"}], "guid": "bfdfe7dc352907fc980b868725387e9814822ff19035a835afeb657bb4dcb35b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c9cbb956b62333c02329180fbe03b825", "targetReference": "bfdfe7dc352907fc980b868725387e9890f568fc9b811cd56b08e401eec0f35e"}], "guid": "bfdfe7dc352907fc980b868725387e98b78f48222917d3611c7c397d7f9ae63e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9890f568fc9b811cd56b08e401eec0f35e", "name": "rive_common-rive_common_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9849c5bdbe31e3b466d37bab9271baf60a", "name": "rive_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9884d4dff26bf6b3e66f738069c7476896", "name": "rive_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}