class DropoffOptionsModel {
  bool? status;
  int? statusCode;
  String? message;
  DropoffOptionsData? data;

  DropoffOptionsModel({this.status, this.statusCode, this.message, this.data});

  DropoffOptionsModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    statusCode = json['status_code'];
    message = json['message'];
    data =
        json['data'] != null ? DropoffOptionsData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['status_code'] = this.statusCode;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DropoffOptionsData {
  List<DropoffOptionItem>? data;

  DropoffOptionsData({this.data});

  DropoffOptionsData.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <DropoffOptionItem>[];
      json['data'].forEach((v) {
        data!.add(DropoffOptionItem.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DropoffOptionItem {
  int? id;
  String? name;
  bool? status;
  String? createdAt;
  String? updatedAt;

  DropoffOptionItem({
    this.id,
    this.name,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  DropoffOptionItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}
