import 'dart:developer';

import 'package:db_eats/bloc/main_bloc.dart';
import 'package:db_eats/ui/auth/forgotpass.dart';
import 'package:db_eats/ui/auth/verifysignup.dart';
import 'package:db_eats/ui/home.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleLogIn = GoogleSignIn();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _showPassword = false;
  bool _emailError = false;
  bool _passwordError = false;
  Future<void> _signInWithGoogle(BuildContext context) async {
    try {
      await _googleLogIn.signOut();
      final GoogleSignInAccount? googleUser = await _googleLogIn.signIn();

      if (googleUser == null) {
        // The user canceled the sign-in
        return;
      }
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      final AuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      log(" Token: ${credential.token}");
      credential.token;
      log("Access Token: ${googleAuth.accessToken}");
      log("ID Token: ${googleAuth.idToken}");

      final UserCredential userCredential =
          await _auth.signInWithCredential(credential);

      // It's possible that the UserCredential object doesn't have the tokens directly
      // You need to get the idToken from the user object instead
      final User? user = userCredential.user;
      if (user != null) {
        final idToken = await user.getIdToken();
        log("ID Token from User: $idToken");

        context.read<MainBloc>().add(GoogleLoginEvenet(
              signmethod: 'GOOGLE',
              idToken: idToken.toString(),
            ));
      }
    } catch (e) {
      print(e);
    }
  }

  Future<UserCredential?> signInWithApple(BuildContext context) async {
    try {
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      final UserCredential userCredential =
          await _auth.signInWithCredential(oauthCredential);
      final User? user = userCredential.user;
      if (user != null) {
        final idToken = await user.getIdToken();
        context.read<MainBloc>().add(GoogleLoginEvenet(
              signmethod: 'APPLE',
              idToken: idToken.toString(),
            ));
        return userCredential;
      }

      return userCredential;
    } catch (e) {
      log('Apple sign-in error: $e');
      return null;
    }
  }

  Future<UserCredential?> signInWithAppleAndroid(BuildContext context) async {
    await FirebaseAuth.instance.signOut();

    AppleAuthProvider appleProvider = AppleAuthProvider();
    appleProvider = appleProvider.addScope('email');
    appleProvider = appleProvider.addScope('name');

    try {
      final UserCredential userCredential =
          await FirebaseAuth.instance.signInWithProvider(appleProvider);
      final User? user = userCredential.user;

      if (!mounted) return null;

      // Extract first and last name from displayName if available
      String firstName = '';
      String lastName = '';
      if (user?.displayName != null) {
        final names = user!.displayName!.split(' ');
        firstName = names.isNotEmpty ? names.first : '';
        lastName = names.length > 1 ? names.sublist(1).join(' ') : '';
      }

      if (user != null) {
        final idToken = await user.getIdToken();
        context.read<MainBloc>().add(GoogleLoginEvenet(
              signmethod: 'APPLE',
              idToken: idToken.toString(),
            ));
        return userCredential;
      }
      return null;
    } catch (e) {
      log('Apple sign-in error: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final orientation = MediaQuery.of(context).orientation;
    final isLandscape = orientation == Orientation.landscape;

    final horizontalPadding = isLandscape
        ? ResponsiveSizes.wp(context, 15)
        : size.width < 600
            ? ResponsiveSizes.wp(context, 5)
            : ResponsiveSizes.wp(context, 10);

    final buttonHeight = isLandscape
        ? ResponsiveSizes.hp(context, 12)
        : ResponsiveSizes.hp(context, 7);

    final double scaleFactor = size.width < 600 ? 1.0 : 0.8;

    final TextStyle labelStyle = TextStyle(
      fontSize: ResponsiveSizes.wp(context, 3.5 * scaleFactor),
      fontWeight: FontWeight.w500,
      fontFamily: 'Inter',
      color: Color(0xFF1F2122),
    );

    final TextStyle inputStyle = TextStyle(
      fontSize: ResponsiveSizes.wp(context, 4 * scaleFactor),
      fontWeight: FontWeight.w400,
      fontFamily: 'Inter',
      height: 1.0,
    );

    final TextStyle buttonTextStyle = TextStyle(
      fontSize: ResponsiveSizes.wp(context, 4),
      fontFamily: 'Inter',
      fontWeight: FontWeight.w600,
    );

    return BlocListener<MainBloc, MainState>(
      listener: (context, state) {
        if (state is EmailSignInSuccess) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
                builder: (context) => const MainNavigationScreen()),
            (route) => false,
          );
        } else if (state is EmailSignInVerificationRequired) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => Verifysignup(
                email: state.email,
                phone: state.phone,
                countryCode: state.countryCode,
              ),
            ),
          );
        } else if (state is EmailSignInFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
        if (state is CheckingEmailSuccess) {
          setState(() {
            _showPassword = true;
            _emailError = false;
          });
        } else if (state is CheckingEmailFailed) {
          setState(() {
            _emailError = true;
          });
        }
      },
      child: BlocBuilder<MainBloc, MainState>(
        builder: (context, state) {
          return Stack(
            children: [
              Scaffold(
                backgroundColor: const Color(0xFFF6F3EC),
                body: SafeArea(
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: size.width > 900 ? 600 : size.width,
                    ),
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: horizontalPadding,
                          vertical: isLandscape
                              ? ResponsiveSizes.hp(context, 1)
                              : ResponsiveSizes.hp(context, 2),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Align(
                              alignment: Alignment.topLeft,
                              child: Container(
                                width: ResponsiveSizes.wp(context, 8),
                                height: ResponsiveSizes.wp(context, 8),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.05),
                                      blurRadius: 2,
                                      offset: const Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: IconButton(
                                  icon: Icon(Icons.close,
                                      size: ResponsiveSizes.wp(context, 4),
                                      color: Colors.black),
                                  onPressed: () {
                                    // Navigator.pop(context);
                                    Navigator.of(context).pushAndRemoveUntil(
                                      MaterialPageRoute(
                                          builder: (context) => const Home()),
                                      (route) => false,
                                    );
                                  },
                                  padding: EdgeInsets.all(
                                      ResponsiveSizes.wp(context, 2)),
                                  constraints: BoxConstraints(),
                                  iconSize: 20,
                                ),
                              ),
                            ),

                            SizedBox(
                                height: ResponsiveSizes.hp(
                                    context, 9)), // Reduced from 6

                            // Sign up text
                            Text(
                              'Log in',
                              style: TextStyle(
                                fontSize: ResponsiveSizes.wp(context, 6),
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Inter',
                                color: Colors.black,
                              ),
                            ),

                            SizedBox(height: ResponsiveSizes.hp(context, 2)),

                            // Email label
                            Text('Email Address', style: labelStyle),

                            SizedBox(height: ResponsiveSizes.hp(context, 1.5)),

                            TextField(
                              controller: _emailController,
                              onChanged: (value) {
                                if (_emailError) {
                                  setState(() {
                                    _emailError = false;
                                  });
                                }
                              },
                              decoration: InputDecoration(
                                hintText: 'Enter your email',
                                hintStyle: inputStyle.copyWith(
                                    fontSize: ResponsiveSizes.wp(context, 4),
                                    color: Color(0xFF66696D)),
                                filled: true,
                                fillColor: Colors.white,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(25),
                                  borderSide: _emailError
                                      ? const BorderSide(color: Colors.red)
                                      : BorderSide.none,
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(25),
                                  borderSide: _emailError
                                      ? const BorderSide(color: Colors.red)
                                      : const BorderSide(
                                          color: Color(0xFFE1E3E6)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(25),
                                  borderSide: const BorderSide(
                                      color: Colors.black, width: 1.5),
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: ResponsiveSizes.wp(context, 5),
                                  vertical: ResponsiveSizes.hp(context, 2),
                                ),
                                isDense: true,
                              ),
                              keyboardType: TextInputType.emailAddress,
                              style: inputStyle,
                            ),

                            if (_emailError) ...[
                              Container(
                                margin: EdgeInsets.only(
                                    top: ResponsiveSizes.hp(context, 1.5)),
                                padding: EdgeInsets.symmetric(
                                  horizontal: ResponsiveSizes.wp(context, 3),
                                  vertical: ResponsiveSizes.hp(context, 1),
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFFFEBE7),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: BlocBuilder<MainBloc, MainState>(
                                  builder: (context, state) {
                                    String errorMessage =
                                        'Enter a valid email address';
                                    if (state is CheckingEmailFailed) {
                                      errorMessage = state.message;
                                    }
                                    return Row(
                                      children: [
                                        Icon(Icons.error_outline,
                                            color: Colors.red,
                                            size:
                                                ResponsiveSizes.wp(context, 4)),
                                        SizedBox(
                                            width:
                                                ResponsiveSizes.wp(context, 2)),
                                        Expanded(
                                          child: Text(
                                            errorMessage,
                                            style: TextStyle(
                                              fontSize: ResponsiveSizes.wp(
                                                  context, 3.2),
                                              fontWeight: FontWeight.w500,
                                              fontFamily: 'Inter',
                                              color: Colors.black,
                                            ),
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ),
                            ],

                            SizedBox(height: ResponsiveSizes.hp(context, 1.5)),

                            if (_showPassword) ...[
                              Text('Password', style: labelStyle),
                              SizedBox(
                                  height: ResponsiveSizes.hp(context, 1.5)),
                              TextField(
                                controller: _passwordController,
                                obscureText: true,
                                onChanged: (value) {
                                  if (_passwordError) {
                                    setState(() {
                                      _passwordError = false;
                                    });
                                  }
                                },
                                decoration: InputDecoration(
                                  hintText: 'Enter your password',
                                  hintStyle: inputStyle.copyWith(
                                      fontSize: ResponsiveSizes.wp(context, 4),
                                      color: Color(0xFF66696D)),
                                  filled: true,
                                  fillColor: Colors.white,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(25),
                                    borderSide: _passwordError
                                        ? const BorderSide(color: Colors.red)
                                        : BorderSide.none,
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(25),
                                    borderSide: _passwordError
                                        ? const BorderSide(color: Colors.red)
                                        : const BorderSide(
                                            color: Color(0xFFE1E3E6)),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(25),
                                    borderSide: const BorderSide(
                                        color: Colors.black, width: 1.5),
                                  ),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: ResponsiveSizes.wp(context, 5),
                                    vertical: ResponsiveSizes.hp(context, 2),
                                  ),
                                  isDense: true,
                                ),
                                style: inputStyle,
                              ),
                              if (_passwordError)
                                Container(
                                  margin: EdgeInsets.only(
                                      top: ResponsiveSizes.hp(context, 1.5)),
                                  padding: EdgeInsets.symmetric(
                                    horizontal: ResponsiveSizes.wp(context, 3),
                                    vertical: ResponsiveSizes.hp(context, 1),
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFFFEBE7),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(Icons.error_outline,
                                          color: Colors.red,
                                          size: ResponsiveSizes.wp(context, 4)),
                                      SizedBox(
                                          width:
                                              ResponsiveSizes.wp(context, 2)),
                                      Expanded(
                                        child: Text(
                                          'Enter a valid password',
                                          style: TextStyle(
                                            fontSize: ResponsiveSizes.wp(
                                                context, 3.2),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Inter',
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              Align(
                                alignment: Alignment.centerRight,
                                child: TextButton(
                                  onPressed: () {
                                    String email = _emailController.text;
                                    if (email.isEmpty) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        const SnackBar(
                                            content: Text(
                                                'Please enter your email address')),
                                      );
                                      return;
                                    }
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            Forgotpass(email: email),
                                      ),
                                    );
                                  },
                                  style: TextButton.styleFrom(
                                    padding: EdgeInsets.symmetric(
                                        horizontal:
                                            ResponsiveSizes.wp(context, 2.5),
                                        vertical:
                                            ResponsiveSizes.hp(context, 0.5)),
                                  ),
                                  child: Text(
                                    'Forgot password?',
                                    style: TextStyle(
                                      fontSize:
                                          ResponsiveSizes.wp(context, 3.5),
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter',
                                      color: Colors.black87,
                                    ),
                                  ),
                                ),
                              ),
                            ],

                            // SizedBox(height: ResponsiveSizes.hp(context, 2)),

                            SizedBox(
                              width: double.infinity,
                              height: buttonHeight,
                              child: ElevatedButton(
                                onPressed: () {
                                  String email = _emailController.text;
                                  if (!_showPassword) {
                                    if (email.isEmpty || !email.contains('@')) {
                                      setState(() {
                                        _emailError = true;
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                                'Please enter a valid email address'),
                                          ),
                                        );
                                      });
                                    } else {
                                      context.read<MainBloc>().add(
                                          CheckEmailEvent(
                                              email: email, issignin: true));
                                    }
                                  } else {
                                    setState(() {
                                      _emailError = false;
                                      _passwordError = false;
                                    });

                                    String password = _passwordController.text;
                                    if (password.isEmpty) {
                                      setState(() {
                                        _passwordError = true;
                                      });
                                    } else {
                                      context.read<MainBloc>().add(
                                            EmailSignInEvent(
                                              email: email,
                                              password: password,
                                            ),
                                          );
                                    }
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.black,
                                  foregroundColor: Colors.white,
                                  padding: EdgeInsets.symmetric(
                                      vertical: ResponsiveSizes.hp(context, 1)),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(
                                        ResponsiveSizes.hp(context, 25)),
                                  ),
                                ),
                                child: BlocBuilder<MainBloc, MainState>(
                                  builder: (context, state) {
                                    if (state is CheckingEmail) {
                                      return SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2,
                                        ),
                                      );
                                    }
                                    return FittedBox(
                                      fit: BoxFit.scaleDown,
                                      child: Text(
                                        'Continue',
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          fontSize:
                                              ResponsiveSizes.wp(context, 4),
                                          fontFamily: 'Inter',
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),

                            Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: ResponsiveSizes.hp(context, 2.5)),
                              child: Row(
                                children: [
                                  Expanded(
                                      child: Divider(
                                          color: Color(0xFFD2D4D7),
                                          thickness: 1)),
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal:
                                            ResponsiveSizes.wp(context, 4)),
                                    child: Text(
                                      'or',
                                      style: TextStyle(
                                          fontSize:
                                              ResponsiveSizes.wp(context, 4),
                                          color: Color(0xFF414346),
                                          fontFamily: 'Inter',
                                          fontWeight: FontWeight.w400),
                                    ),
                                  ),
                                  Expanded(
                                      child: Divider(
                                          color: Color(0xFFD2D4D7),
                                          thickness: 1)),
                                ],
                              ),
                            ),

                            Container(
                              margin: EdgeInsets.symmetric(
                                  vertical: ResponsiveSizes.hp(context, 2)),
                              height: buttonHeight,
                              width: double.infinity,
                              child: OutlinedButton.icon(
                                icon: Image.asset(
                                  'assets/icons/apple.png',
                                  width: ResponsiveSizes.wp(context, 5),
                                  height: ResponsiveSizes.wp(context, 5),
                                ),
                                label: Text(
                                  'Continue With Apple',
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: ResponsiveSizes.wp(context, 4),
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                onPressed: () {
                                  if (Theme.of(context).platform ==
                                      TargetPlatform.iOS) {
                                    signInWithApple(context);
                                  } else if (Theme.of(context).platform ==
                                      TargetPlatform.android) {
                                    signInWithAppleAndroid(context);
                                  }
                                },
                                style: OutlinedButton.styleFrom(
                                  side: const BorderSide(
                                      color: Color(0xFF1F2122), width: 1.0),
                                  padding: EdgeInsets.symmetric(
                                      vertical: ResponsiveSizes.hp(context, 2)),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(
                                        ResponsiveSizes.hp(context, 25)),
                                  ),
                                ),
                              ),
                            ),

                            SizedBox(
                              width: double.infinity,
                              height: buttonHeight,
                              child: OutlinedButton.icon(
                                icon: Image.asset(
                                  'assets/icons/google.png',
                                  width: ResponsiveSizes.wp(context, 5),
                                  height: ResponsiveSizes.wp(context, 5),
                                ),
                                label: Text(
                                  'Continue With Google',
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: ResponsiveSizes.wp(context, 4),
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                onPressed: () {
                                  _signInWithGoogle(context);
                                },
                                style: OutlinedButton.styleFrom(
                                  side: const BorderSide(
                                      color: Color(0xFF1F2122), width: 1.0),
                                  padding: EdgeInsets.symmetric(
                                      vertical: ResponsiveSizes.hp(context, 2)),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(
                                        ResponsiveSizes.hp(context, 25)),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              // Loading overlay
              BlocBuilder<MainBloc, MainState>(
                builder: (context, state) {
                  if (state is EmailSigningIn) {
                    return Container(
                      color: Colors.black.withOpacity(0.8),
                      child: const Center(
                        child: SizedBox(
                          width: 40,
                          height: 40,
                          child: CupertinoActivityIndicator(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}

// Add ResponsiveSizes utility class if not already imported
class ResponsiveSizes {
  static double wp(BuildContext context, double percentage) =>
      MediaQuery.of(context).size.width * (percentage / 100);

  static double hp(BuildContext context, double percentage) =>
      MediaQuery.of(context).size.height * (percentage / 100);
}
