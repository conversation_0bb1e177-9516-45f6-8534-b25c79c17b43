class SaversPlanSummaryModel {
  bool? status;
  String? message;
  SaversPlanSummaryData? data;
  int? statusCode;

  SaversPlanSummaryModel({
    this.status,
    this.message,
    this.data,
    this.statusCode,
  });

  SaversPlanSummaryModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null
        ? SaversPlanSummaryData.fromJson(json['data'])
        : null;
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['status_code'] = statusCode;
    return data;
  }
}

class SaversPlanSummaryData {
  PlanSummary? plan;
  double? walletBalance;
  double? subTotal;
  double? walletCredits;
  double? tax;
  double? total;

  SaversPlanSummaryData({
    this.plan,
    this.walletBalance,
    this.subTotal,
    this.walletCredits,
    this.tax,
    this.total,
  });

  SaversPlanSummaryData.fromJson(Map<String, dynamic> json) {
    plan = json['plan'] != null ? PlanSummary.fromJson(json['plan']) : null;
    walletBalance = json['wallet_balance'] != null
        ? double.tryParse(json['wallet_balance'].toString())
        : null;
    subTotal = json['sub_total'] != null
        ? double.tryParse(json['sub_total'].toString())
        : null;
    walletCredits = json['wallet_credits'] != null
        ? double.tryParse(json['wallet_credits'].toString())
        : null;
    tax = json['tax'] != null ? double.tryParse(json['tax'].toString()) : null;
    total = json['total'] != null
        ? double.tryParse(json['total'].toString())
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (plan != null) {
      data['plan'] = plan!.toJson();
    }
    data['wallet_balance'] = walletBalance;
    data['sub_total'] = subTotal;
    data['wallet_credits'] = walletCredits;
    data['tax'] = tax;
    data['total'] = total;
    return data;
  }
}

class PlanSummary {
  int? id;
  String? planName;
  int? planType;
  double? price;

  PlanSummary({
    this.id,
    this.planName,
    this.planType,
    this.price,
  });

  PlanSummary.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    planName = json['plan_name'];
    planType = json['plan_type'];
    price = json['price'] != null
        ? double.tryParse(json['price'].toString())
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['plan_name'] = planName;
    data['plan_type'] = planType;
    data['price'] = price;
    return data;
  }
}
