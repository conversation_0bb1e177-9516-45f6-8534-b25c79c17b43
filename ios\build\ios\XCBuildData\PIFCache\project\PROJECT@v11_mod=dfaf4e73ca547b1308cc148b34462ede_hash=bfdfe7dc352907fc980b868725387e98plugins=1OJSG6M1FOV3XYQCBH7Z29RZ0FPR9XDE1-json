{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c329620c51892527db69ac984ef9321b", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e986eaba3bbf34fffc52894406988f981b0", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9804db47a3ceef83edd118018eb43bf272", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d63c0d418cf844eb1bef3fa722bf957", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources/firebase_auth/firebase_auth_messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980052c209c719a233619dbe3f6d7b3124", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources/firebase_auth/FLTAuthStateChannelStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987be38bdc47d091c79c61057b5cc87102", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources/firebase_auth/FLTFirebaseAuthPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f78ec038df13109f289a62ac02a1c93", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources/firebase_auth/FLTIdTokenChannelStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f865301a95be7c06f549c0b9abaf30da", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources/firebase_auth/FLTPhoneNumberVerificationStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980a17a1d7f334519d0896b29209b921ef", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources/firebase_auth/PigeonParser.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f6a571827b54359556a632ea89bfda4", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources/firebase_auth/include/Private/FLTAuthStateChannelStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985072041f942b7a1ca6edeca21ef5af92", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources/firebase_auth/include/Private/FLTIdTokenChannelStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a08f814362885e8719b5da907dbe392d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources/firebase_auth/include/Private/FLTPhoneNumberVerificationStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b65d8c18fdb034ac4be5b8e84ccedd5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources/firebase_auth/include/Private/PigeonParser.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987c8c4e08f8f32408a13ae866ebed8aef", "name": "Private", "path": "Private", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1d5ff3e8745b06a37b73f068aa13512", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources/firebase_auth/include/Public/CustomPigeonHeader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985730898e18627cd514362b84265db80d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources/firebase_auth/include/Public/firebase_auth_messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4b85011c3fe43c4983794ded8c81812", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources/firebase_auth/include/Public/FLTFirebaseAuthPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bf1a59340dc6d2289750c58ccb5233fc", "name": "Public", "path": "Public", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985316cf6083d22d6ff9c5b73c71c4e996", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b9cb8b037af4a4bac35c176c4ee406f", "name": "firebase_auth", "path": "firebase_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981466710a18bc978f3cd9408aaffd09ea", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8d9bf8394f107f5e4c2e16e54d2513b", "name": "firebase_auth", "path": "firebase_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a34123cdba21444d7e370752fa47f638", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a54f6f337c930a91950afb7e1f3c6275", "name": "firebase_auth", "path": "firebase_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e99dc68a468976504c5654c18b524e2", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e85fc0fa43abc83a7d57bda01fb1fb5", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98711afe8d5e5757cb09dadf467da46487", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ee187285f36d94fc7aaa51a97c78dfb", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6079dad0daf03c1b1fb3252491f4304", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982dc689537fe5a6a4cb1112cc2d1d3128", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984400e20963d81963b92f1ef0a5a3f22a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d685a770fd7a4d22c38bbac599ea008", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4956d875b57af0618ce71688d39fa8d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808390afba7f897ee812f96fd6cae2a1d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f12ce00c0a33e1a81488c775329300c9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b145a4ec0a6ed00770f5c6cce3a0ee02", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98300bd9cd56f0c6db9fb0d033d4845562", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879d1d82e2643e965a10ac1c637ed6dfa", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e986f5732a734bd34a216f09bac572c9f9b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/ios/firebase_auth.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e987ca05b624c415ab91d95cabc455fe546", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982d6306ee7f924d7234371304e301d8fd", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e00cec29fa38454cf445b605df2edfe6", "path": "firebase_auth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987feb133c3aaa7fceef1da7374798568e", "path": "firebase_auth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9828416146e4ac98c3f21f3ec18dfb5156", "path": "firebase_auth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983f637d0392ee154ff394290a0efd4f66", "path": "firebase_auth-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98018e88dc873f9a7cd5fcca835088b291", "path": "firebase_auth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c3a22d08eac1f783557ea21fee302da1", "path": "firebase_auth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980043bbac60025fb5bd25e12239a1132a", "path": "firebase_auth.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9824b8fed5c044f6fb3f739b3f539f8b8d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98186296342b7a12d462ed5c9e05540146", "name": "firebase_auth", "path": "../.symlinks/plugins/firebase_auth/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989ecbb116490711e9003d0c1f2276ead4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.14.0/ios/firebase_core/Sources/firebase_core/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809b4237d7f67a3cdeeae79821e75795a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.14.0/ios/firebase_core/Sources/firebase_core/FLTFirebaseCorePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9830363b7b242c0382c1489bb857bc4add", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.14.0/ios/firebase_core/Sources/firebase_core/FLTFirebasePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9878e7df8c986f06e786921d227a694ace", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.14.0/ios/firebase_core/Sources/firebase_core/FLTFirebasePluginRegistry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac872da469ee775d5ae2a3a55f6feeae", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.14.0/ios/firebase_core/Sources/firebase_core/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b33e5834f802d34f06051824e1ea68db", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.14.0/ios/firebase_core/Sources/firebase_core/include/firebase_core/dummy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f62638fca2c5191e414a37892f67fe42", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.14.0/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebaseCorePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982732449c0e33296b2c37c355d0d19209", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.14.0/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebasePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc7df986ea967a29a3ec1d5595f64642", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.14.0/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebasePluginRegistry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870925f32843cd9344eadeab3a4032fda", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.14.0/ios/firebase_core/Sources/firebase_core/include/firebase_core/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e4c1a7e8d598de9658866d41864c7fd7", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983dbec6de4299f249da8e26e1b791bff2", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98172d4e40d66a6b707f08bb5f6a3769fc", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5f6987f078c9164385f3376ff1acbb9", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987bb0f9ccf2d6ec4dafa0f5dc5270b600", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98631247f7b47890d2acb2fb80044facfe", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9863f0c800b69a529527dc14e46bbb6f39", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e75404276b407947143f9bff521530e1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897f28ccbd55622e1234ec39e9600a19b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eaad262196cea5bfe20aaa884abdc58c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5debb942f04ffdcfa64630590e120a8", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98105453d9670a7a33e7f621ed9c36f890", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f7d2dee423fbe0861b09624ef9e34cf", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bce46361b6e1cb347287a4f8e1db9a77", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852ad07ffd70058f1a2b8efc3f0927c82", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a46c82d82f38a07f69ddbc5dafe2eded", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd65313db44aa7a1388781c35e32dcca", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc99622f5a74afac20316b465899252d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f91a61f1aa854df4e1f002c187e2aacc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dee27a76b978052e7808a635352dbb16", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cf6ed7272b4ddb07121e55675784b8c", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.14.0/ios/firebase_core/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983a09af98cd090b3565d0bb284de0a09b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.14.0/ios/firebase_core.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e980b37e2a6d6d7267c36313c8df19945f7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.14.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984a8b8dc79059a2fbdb99ab39e0db17ef", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f6fae26e58b250bdf3c5ea103616e498", "path": "firebase_core.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9802b650d787cdea7cd79de53f6e0465d5", "path": "firebase_core-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9864ed9604c04f1709f5368bd371fdb835", "path": "firebase_core-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e010903a359e5386dd9290ce527e6ab3", "path": "firebase_core-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a92da916b14680b5cfb5af900120067e", "path": "firebase_core-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98916028c2a9e734856f1f41c6512e6287", "path": "firebase_core.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982665af0808ddf8d882b5c1dac7f0da06", "path": "firebase_core.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987c1a0c1e1bd33c2356c48370f67caef5", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f777378d079fff192cc0c1783b695638", "name": "firebase_core", "path": "../.symlinks/plugins/firebase_core/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a2332b952aa654ee9ec092c741711e8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.7/ios/firebase_messaging/Sources/firebase_messaging/FLTFirebaseMessagingPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872d58e6032efdfddd5e2a8bc4ea0cc14", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.7/ios/firebase_messaging/Sources/firebase_messaging/include/FLTFirebaseMessagingPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c4b862ed9d120feb477ddde400f6c68d", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984dbeb95afa51b1eee4d675eae4d68ea9", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b3452828f710285418fe9b982d3d6ff", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98885cbec0146a922a4ad83707a72bbe09", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3e28141a8012fe2fe2d2941524e47b9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a8a59d6b81764fba571c40efa102656", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987816ec964120ae4418b36d325eba08d8", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989352e8c32c2efcbc1f6adcbda04bbe2e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ab2dc9938b3a23e5d3fa985aa1f7009", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f65a82a5ee2642c7666bec835f2a2a65", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98446a98c2e44f03f5d7712e746542ce8a", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a92d1272f246ded3d86e59b6155c019", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987014617093e5d7def1b7d8a86d8ab411", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981978931392b6cfd401467ac0c2845652", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98def1816914a36085b49ef3e7e16e4f6f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cfd9088f6bf5828db27aa2496b60ef88", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e41c1ea7e8f162c1114b0f6dcb6210ab", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980727c21bc860bdaa8c5a8d1642a846ea", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981cb9e82cf029985f1523c855fe5b8959", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d980406d36ae77abdd55a833c1c8a4f", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.7/ios/firebase_messaging/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98c8f267a62f54656f62d0a1bcbe500fa7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.7/ios/firebase_messaging.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f3a93af36930a38b5d514ddcd0d04ad4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.7/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fc4ef251e43de99651a2b5e49133f51a", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9871f515bd142ea856f53daf7af42769d0", "path": "firebase_messaging.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a1f1392e8ab12687a949a1092c506c0", "path": "firebase_messaging-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986b94a1e3122a73029af7b703985518f6", "path": "firebase_messaging-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9850a3be3b91e0c6a4bf674117d81b34ce", "path": "firebase_messaging-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e496aa917f03116d882365ae5f6938ea", "path": "firebase_messaging-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9801f43d5e5f3e4223b732063beaaf6cdd", "path": "firebase_messaging.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ec94f4813b4d87c26c7141e36f9921c1", "path": "firebase_messaging.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b0a731775bfe2e22fab0d499d1654f05", "path": "ResourceBundle-firebase_messaging_Privacy-firebase_messaging-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f898c68b252022a0fa6c4f0f2460300b", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ba5163dd58314633a59a6a35f30f4be", "name": "firebase_messaging", "path": "../.symlinks/plugins/firebase_messaging/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987d698f059ac9a98aaddb8e2fef8d2560", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98debfa2298176c4f31c01779df1761460", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98296ccfd2c29b79bd33f0e11787852f4c", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980413f95465a758fb3fe4f2fca227f855", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982e0263e8fe52447de4b1f4303940eced", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98799f52789807fd3bb713c90e00a66304", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f309e0b3f54487f37cd4497d0abe551c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Classes/FluttertoastPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9877a983d0507616e76f97c6546391c7c6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Classes/FluttertoastPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b83467874d0500c11ce8e115258edc2a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Classes/UIView+Toast.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986f327fc56d4545d782c780594d14a07f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Classes/UIView+Toast.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ee4627aa49ec5ad7414fdf33927ec2e0", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e989c99c25494ad837b5e8712f375132f00", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f16d1e32f78983ba469adfe50b351ef8", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983fbd48e24fe2a34be65dae129b222aa2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3ed5476fdd07d3dff55a238da133cf5", "name": "fluttertoast", "path": "fluttertoast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98134155b19257e67c1f1a169f60596998", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c01fb93f7eb15a8a07dca673dfea8d4f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9bf713f91e050bdaee3fd1545afacfd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebbf627d7bfad13dcf2e4e376600bf25", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988725ca9977f57c65a356e8a59c72d3b2", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989eae42af519146eb1cdc86830d52d782", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899e5c787c6c19f9d2dffc6ba7b35e99e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cae118e6e147606747c477fc0bf28725", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989aa247253bd61095d96a30dd45a17305", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eaeae71ac650c946a3b001ed13387bf9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800a5f84794a81a27b9def197d761dfe8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987be7f8383f67fb06b1b30b98c0f15989", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e082e606f81de67aaf5915e6ba35cbdb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/ios/fluttertoast.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98727b55d0e1ae091ac61bec500171b32a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987f8898b811536a0006a3630dd5bb0f86", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988b15a25581578a8175f9ac0a269ff01a", "path": "fluttertoast.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98725ca5b8e9214013436be11145dcba36", "path": "fluttertoast-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983f771993c4c46195ea814bfb049b8517", "path": "fluttertoast-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a1a3329f2c40792a7180809bdca7baf", "path": "fluttertoast-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9879b844b9b046922fb4b3223bf617ce89", "path": "fluttertoast-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9832c55cf7e5e1315dfd0d04bfd897379e", "path": "fluttertoast.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98beb3c2e9ad38fcf05792b995ccbb4a7b", "path": "fluttertoast.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9809dcd2a45942692546e92ec99c037f54", "path": "ResourceBundle-fluttertoast_privacy-fluttertoast-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98314feefd3511fd9135d82aeb0376693c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/fluttertoast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ba2f9415688b624e564e06ffa8e21fce", "name": "fluttertoast", "path": "../.symlinks/plugins/fluttertoast/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d912f96d050e3ef3e630e16a3819cd31", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.2/ios/Classes/GeocodingHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98589771762d58b04fc57ea8333d6ce57a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.2/ios/Classes/GeocodingHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b813af3a9826964eaeee682dd2e721c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.2/ios/Classes/GeocodingPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9859886efb6122fd79f324f214fff07728", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.2/ios/Classes/GeocodingPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988ade2f061f5b77d9268d8fe97097f2c8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.2/ios/Classes/Extensions/CLPlacemarkExtensions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983e52fe20f3a9433688e44886a294047f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.2/ios/Classes/Extensions/CLPlacemarkExtensions.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9889eee2329c7c02529f592d27706f94b3", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9823483451e4550ccb5349bfbe4d552973", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98da721e2fb2bd9c9369fc098a381a0bb8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.2/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9864df72d67440deec061650afab370753", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a15bfbeb0c7591f4b4e7751d937feeb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98555bd840ac69a06a8dd7af91f12f65d6", "name": "geocoding_ios", "path": "geocoding_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eacf9f77d0df186541ab9b958e01d10a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986572ff7d3b0f359d903050b27eada0d1", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984af004b839821d3b2e70fe62d39a3c43", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c11eb275873b827d985f99041c1a243", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985969ce66b96e9370402c1de3b2a43726", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9876d0cf11faa69eefad6d6de183450d0b", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9890318265ebad8a01a772aa9fba8d736d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2d559b0fa649f98856162ad2d6e27ad", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a474f71e376999d726fc631f8212156", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98355e62b489c73188c869f87cf69c9eee", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985a14c0d197f2f7f9c796c104d8ad838b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98687ea5bb5a8aec235c05d6ee743e2876", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e981be15a08791811d535e646dc1844299e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.2/ios/geocoding_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e981b2d4dec8d2a08de53c723498070b3fd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9855b3598d663dc48704ac0b8291327bc8", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985fc52fcd1fce131079974f3c78b6e82d", "path": "geocoding_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ef408bd53fa87fa6a34e0866934557fd", "path": "geocoding_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f2edfd165604b17b4ee546166d8d8a1a", "path": "geocoding_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dda59555277de26b2ce9dda163708dda", "path": "geocoding_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc6ce900ff1a12a8c80c62ac4ee53346", "path": "geocoding_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986deb58d80064cef4160eb67628ab9c5b", "path": "geocoding_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ff2f2d40aaa38432723995b8b001406c", "path": "geocoding_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98225c68c42b67ee2e7ced3bdab90c7482", "path": "ResourceBundle-geocoding_ios_privacy-geocoding_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f933a4a0cf9d982e7e7eadfee4ad9362", "name": "Support Files", "path": "../../../../Pods/Target Support Files/geocoding_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857a949b8ce9028a9dab883ce433f04e5", "name": "geocoding_ios", "path": "../.symlinks/plugins/geocoding_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980d5b76c0485c72b2c4da563974a3fc32", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/GeolocatorPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e982d1b6e050270df12d1464e2fb1835946", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98abf21244b62589fdd7c5694a582f5e79", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Constants/ErrorCodes.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981a077127c258676521be3f66b6a63c36", "name": "Constants", "path": "Constants", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af21a699e7cf91efe0137369e306fb8e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/GeolocationHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d23ab725b940ed9fce687e953f4f9373", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/LocationAccuracyHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98297323fbd944c854bfa400b03c7dfa23", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/LocationServiceStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987c64fb0ef588cba2dfc38ae34b03d5e4", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/PermissionHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986cc6b5b2fe0e1d50f60ba477752c2df7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/PositionStreamHandler.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ac7534325c03c78ee85a655d6ec70e34", "name": "Handlers", "path": "Handlers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9857745b24f2f45936184b4cf4ecbc790c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bc740be1db784e3840ab0ec7c446be4b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/GeolocatorPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986c1ffc627ec7a9d7cf45c7a63bffa3a1", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/GeolocatorPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1fbec41a75f24ba02568bf7cb36c0c9", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Constants/ErrorCodes.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98313fffbedbeed895fbe596cff673ac92", "name": "Constants", "path": "Constants", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b267af6420bf04ef2f0e797bf85285b5", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/GeolocationHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98651e7248e992f0d01abbb953465ed451", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/GeolocationHandler_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fdb38301877be0685bc6f3be5f063138", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/LocationAccuracyHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981867b3e037a071d66560821b1be966be", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/LocationServiceStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad3c451424676cf57e2913e2211420c5", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/PermissionHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98344a4380ad1cabda3918e814fa6a4157", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/PositionStreamHandler.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c557b8086f1f1689346eb4bae65e7f24", "name": "Handlers", "path": "Handlers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981759afd9a1b875c74c4bf8bcd6615041", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/ActivityTypeMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98200295998e37337038488ffa9f110cdd", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/AuthorizationStatusMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98520cb7ff827baaf0c247b872f362d990", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/LocationAccuracyMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ed5af2db332c82c1bd83dedfe4fb2f3", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/LocationDistanceMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f74be768cb53c8f682a636bb90957774", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/LocationMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd856a724596b07340e05feeb88d1bb8", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/PermissionUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f95a3995d22992a08d4f2db340b3cda0", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/ServiceStatus.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f881145ee5bd61f519f88492c71a256b", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e97c4f87a694c2bce332ac1102cc8cb0", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982962cce5efd3f644b00741315d112cba", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9899ab2fa32bb9f6e7af8ac0bbb9259ff1", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/ActivityTypeMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fde39d7f1d6af9d9434f7b5a4b15c097", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/AuthorizationStatusMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a2ed4fccf10c92e65dc1f45c50bda0e4", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/LocationAccuracyMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ccd217f28d1a0e4d0fafe009cf56c367", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/LocationDistanceMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a5c29c15fbb7851411ef3740dbbd2a25", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/LocationMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9891b0d77f98a4bf288255398b36543013", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/PermissionUtils.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98397344dabe3faadcd7bf9f30143b9502", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827395f22a5c17407f09d3b7703f35a32", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989066ca8a2ba18409a41564b6d2efd635", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800ab1a87186e897965d992445f6b3186", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e5a1d29593eb0563345349a7f2cb3eb8", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c8ca685d22e2573298e441bf9e73214", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be333b254fd523f1f54df73f4a2ae976", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982290454c8d5ba2f1a343b73d8f31eaef", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9850ee7328d0372ce360693f6d5f30df07", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98821a1b6c6d29051bbaa3d20f6f247f66", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f2eed7195034077a8e3669ad526a176", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc48e4b40bd61321209277f2dbf7ddec", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980cda33eb8c9b248b9bf64d8480cb3562", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801254df5826922f0ed6f7b64e71c0f93", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982070b2c2651bd16e50d805d31b108535", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc369e9bc484992a6f08a8145505572e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a05405e0a2ea31a3ec85631537bb87f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98178c7053470aef4208ae2e616d362f10", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b72535f7473c55cf475b3086bee66438", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c703a8097dd1151972077a87c7a27f9b", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e989eee6a1e346943d541d68e4b8456858f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9869411f7b52a959620f325f5e0f412aa1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/GeolocatorPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e986d4f4cdfb71470b2526b09af74c76aa3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984406f771a873579c773b269ba0629987", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98002c2ab526915a4843ebd7e67830baa3", "path": "geolocator_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9877aaa2224c52c643dfb86f6b658a92b1", "path": "geolocator_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987c59e4b2901366cb9fc6edeae3861fcc", "path": "geolocator_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9845228e8b3d68352395885651b12a451c", "path": "geolocator_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9860d29d7b5d1ca1778d4902c6bc276140", "path": "geolocator_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980ff3f560eb2fb1b764738e76e10679a1", "path": "geolocator_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c1cab46d1d3a79c9276b6407c5bd024e", "path": "ResourceBundle-geolocator_apple_privacy-geolocator_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98705fa338abd454cb79a3ba12fe4e3836", "name": "Support Files", "path": "../../../../Pods/Target Support Files/geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889e15d7cbfbf6e8991618e0f9c19f97f", "name": "geolocator_apple", "path": "../.symlinks/plugins/geolocator_apple/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98785e9a264f9ebde2e8009f5327bcce6c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMCATransactionWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983e20fa1914f5025baa6322abccfa8584", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMCATransactionWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9844cd285448a7385d6648e33b4669516e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMClusterManagersController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98390825ad6d5e19ef4b8c072003fc1efc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMClusterManagersController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a8073a49e97a5a1d34404e2da2d4e535", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMGroundOverlayController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a78d93cdc522f0b0327be160524a8d7a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMGroundOverlayController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce9235f9471b577f8f2d127db562d7a4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMGroundOverlayController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98722e462f183e87a9f1b6d7465452941a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMImageUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815fe1109492a7c89a4e18450a196bde6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMImageUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988bc17a3bba280f4ee45aad0a3e913236", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMMarkerUserData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981505fc1950c5825cff29e69cb131d1dd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMMarkerUserData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b5007b9742661baabc0cad56709f0e82", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapHeatmapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9855b9f688f00896397a0d7a286a36ccec", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapHeatmapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98abefde05b4748025d74463c0e48231cd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapJSONConversions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98da8e2a82c2f9ad6bcd7fb4e1ac79a236", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapJSONConversions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984cafc2d2acce24811162969cbba1ff7d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985eb53f92ee16a630390f3892bd6a699c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bcefaf1ce3a8510dbfcdc1e8f716dc2c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapTileOverlayController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983474aa27b8d37621640bc3d78202957c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapTileOverlayController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f80dd7985126d034236434abc8aff891", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/google_maps_flutter_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98224621af2c30fcfd48aeaf44b2879aa5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapCircleController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aad191772c5add3c46a3ade3d8408bd1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapCircleController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855ccb998c790e3cc8aeeafd28a15123d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9882d088f0186819cd625813d9068ecdb0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bc46265b397560e8a7e52a97f94ad725", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98263db4639836237e06b340778ee42546", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapMarkerController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989faef2ed70f34b4ffc7a7c37cdf5af1f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapMarkerController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987348d69ed4266433dbc5ca6d0ec8f0d1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapMarkerController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ea49cb0e466dab6cffd4817aa01c075", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapPolygonController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b6f2db848fe02accbfb804719dd05f6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapPolygonController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9899ac66b9c459f0ea84f11af8bda3d886", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapPolylineController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9800a0e7083269859f8763dfcf33fe563c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapPolylineController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f5884918b8adb8653e507a8ed9c20174", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapPolylineController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1acc13f7cd5e86648ef305f014a9bee", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f0f75a9c63eee06d9ed26bc40ba01228", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a15fa2d8d724032a91c395ef9cbc2275", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9859366e8c1dd9047690b262feb9a7d81a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98aa21abd35c4ee52149854bac3f62211b", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0591488a97d0a05a4364918eb36c75d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca6dff61c4237c35e7ae4128eff11c36", "name": "google_maps_flutter_ios", "path": "google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820eafd5be28a6933e0c71c14c6f8d569", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989584707c9672ab3573d8f1104113ed22", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a70d2c016e60ad27c5d1f6ef92446e9d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98556f2585ff280179beab75932412d1dd", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987efe847e6df0b1c21e1025fff68978f9", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e0e05f4e1521788d37934c48735d376", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836ea94f593dcc3d05fb4be0f5c5cc5e0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f3531695e867be8ccee0e4d860b041f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9850b83bdf44637e257793c8587d3b199d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986691f3f320e2a0f12a0db8b4395bbe6b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989efef42b1dc9a88b51508adb18c9465c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f840599f649707e8a0933787e2a2470a", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981a2e460510dace614e1201293ad64ac7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983bb7316f8ae75f102e694b98332edefe", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/google_maps_flutter_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98768f451d4c3fb5aa9f2db6e822aff65e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983e4e85258a8a99f6982fc5ada4106fc6", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c53a6eed5ded3248e52144621714407e", "path": "google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9835a1625893a4a5d40cde997e117b16d2", "path": "google_maps_flutter_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980d7052f21439f91d166e0f27ebbac782", "path": "google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883df8f7edd345d8cef20968cbb83343d", "path": "google_maps_flutter_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e225dbd93effac5081b5b44c982839f4", "path": "google_maps_flutter_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988a0c5d3135dcab14b3d150767d7a63d8", "path": "google_maps_flutter_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981728c4b781828e810bcc4691d2962047", "path": "ResourceBundle-google_maps_flutter_ios_privacy-google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9898ccf432b8618e8713b1df8743216561", "name": "Support Files", "path": "../../../../Pods/Target Support Files/google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc4f9011bbef282e51e0287a5503ee70", "name": "google_maps_flutter_ios", "path": "../.symlinks/plugins/google_maps_flutter_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98bb2671b9b67bae46ced3e336a4ceb495", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f274fb795f7599880fad80bd11f57c9d", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989df802228f82a59ebe288f9820f0ce3b", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ea1c8545cc5279ff70a57fb6cfc4992", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5d64cb8c42e0dac8bdb83c1a1aa7632", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98265e6a7d953ccfab286d82d7ea3a04c9", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd64f70ee2b6f3a9a68bf9253dc19fe1", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0629be2b9920dfabc2896a959b0e95a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d64d972a27af73d80c39c48cb132077e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9864ff2bf3851b9110f429ff470b782409", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9354cbfbcb002817e00ff9506b00d0f", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d0f56a0c98eab0258c14eb860196317", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f48b1d4c0bc0f9b3cddde1457269e897", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983775f608551df6a6f62e5e7e3f5c5c98", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987e474a49b76a7560cd8fe77932af4519", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/FLTGoogleSignInPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986c88a3d5bd9278a6a3c93ae2171830b1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98026b30826bfaac7aa6896d11af9f83a5", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883ec25d066f888243c39fe27b1f4f0bd", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios/FLTGoogleSignInPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bc9cde485ac627fc4528fb0f3af2c51e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios/FLTGoogleSignInPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c48cee61bcb3a96fa291b2188b58b824", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986d24fb9c52c95d6a2abdef6789f157e4", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fcdc65c2fb55582cc31022f9c16d4cc2", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f73edb2482b46013f0b5ac9055fe7e4", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889b650eb1fe7a94d6f65709cd0d9a9bd", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98183ea18dfbb8d292234ca596fab9def9", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988444b812b648fbb3657910b8ba8e5816", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d104ab0a75c239bfabe2be00216706b", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98826dbe200463a8bb2378d3e33f905e29", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d227c4db4cc37a9b58188de7758bc323", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab0ba8a18ee3c1e9ec13ca17733b19a0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f49849119f1b8101f97932e0cdbbd97b", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a9970b1aecedf03314c405fc1d590be", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e722f70689bd54ef1851ddbe33135dc3", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a452c290591d5601300fdb88185621b6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d2ea79466fa77bcc8ee99bfd1dccebb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e77927bc7da5de29048d3a884e7e907", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ab84c4c5b8485dc6dba1949ea8e4d49", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c35ccd6b3e4e21fe86e00f19af13189d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2b89a65d54852fe68588a81ae94cdab", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985a874915198fdcdc6a3307f5e4990758", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98616a594f450d5900afab5a5858306fea", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9889c35f6c0ced210674a868f29de01021", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/FLTGoogleSignInPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9854d4dc7fd7087fe022dc979f61a9fae3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98b1311ab1e0dac65b5d0b7056eff916d0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985789da27be9fa1b9d3e5df65d08507fe", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b15b6aa13c0cbd96ab221655ec271e82", "path": "google_sign_in_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981af45d0d407bef875a5baa02cdc8ed1a", "path": "google_sign_in_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9866ca397b688e675b48525a6db54f81ce", "path": "google_sign_in_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9891b57de3f791f9d16bcbf5d953f5a8c6", "path": "google_sign_in_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986f0ceb7b00ae9d81183b5cde42b73095", "path": "google_sign_in_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989a05a0437721d9c3210e600faa30f92b", "path": "google_sign_in_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984623b39d4b007950af59768cd9e5728a", "path": "ResourceBundle-google_sign_in_ios_privacy-google_sign_in_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9873dfc99465ad79ee66d5f442a450cf72", "name": "Support Files", "path": "../../../../Pods/Target Support Files/google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aaef87529bda24adafad5d93e030ce56", "name": "google_sign_in_ios", "path": "../.symlinks/plugins/google_sign_in_ios/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e984dbc08c45b0c954f1b14c415714ba4bb", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf88edae76f7911aa26294a4b672e54e", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eda717df574aeece972e4e7683e06bdd", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98604a5901fb53fbec96b3d38c7bba1d27", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f6587c83f2b1aeaed630c741db47f21", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea66ffd37489bbf36cbbe4b682579da8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c78df0d8b7c168e91490249ebb58636", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c1d5566f3f197772bfe91b27d26d0198", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986316bf0aa49c1a69fbf715d96c546bde", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980cf6feee58719e2c55ed7907f9ced292", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e6b76f32fde87ca3f2bb6984ecee4d1", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982016a960fda8491d7bce8f0a7fdbf7d9", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828a24a456ef60b23635ed696903e6c5f", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed9fe66dc4859f4c46509017c6ebc076", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb754f8f2ad6f863449a2c52c10f106a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981323ddb6253f5ca2d8f39be416affdfe", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e1b14194b609da58dcd3d92b5173cb45", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9894aa21161948dbdcb07ac62ad4e0d47d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f5429f2dad50bcebcb573d0e99b1d599", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981a190c118a42ae6b81c7f2e8259fcb0c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98699336432abdeb069df720989f33ccfc", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9873aaefe4533796f237c35ad2bf68fbab", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981123bbea573de62d87580ee2a77f5de7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e2d1b417e14064e2331902a9992663c9", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b36a18a1240cd90abfc9e81f4abeb7d2", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc9a0e15710543afc24fe4dcb9d73ab0", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988df6f7ac940f7e87276a8a8be986746e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830640025e82707f1a05e91f21c64f703", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9842b874240c787896ebbc3b81e57a3193", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986368d4d62e0300ad58af633b341b2add", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c88b7b8edf1ca034ec60995b0470975a", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98633bc50da27473a2955f4cca65e809bb", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d29bdb77a912c71531eba940fd01dd3", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802faefdcc2db231d4636c613a129fd04", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d51115b69b96443df400a237ed00c3ec", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e31a8b21f138fcbf20b801a8fc91f093", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9b8bda07bbf1ad6d01bd1cbe8eec4c5", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98114663a008ccf5c2b1bd914847e715ed", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98680b64f0ce65ac371ffbcc258d966e19", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7e883a34dbc41459dd65b9796a9c229", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812582ff46c8e32176ff03459dda20af6", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9825a6f32eb140ed9a7a8b57f3dd0d7e23", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb09be4008ef7b38530f9d0b5f3d7788", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5f2d270fe674b9ee8e1bae0cfb4f431", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9813ddafbb772e7a2f592fafd886c7999b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a24cf604f7d75a0dc02a84eb00f1d631", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980307c9971831ba4c1cdc1b4ccc3cc46c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9887dc2cfdfc80b11e50183a96abd174e5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988614cd31861112e77f36c6a6995847d1", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b04ab6010c9af3b7fc88e9a9a546b8d2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98387b00d1db6d7fa51647f95dd845c1ec", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9891eb328c9d012577f5af9632a3cc1587", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9831bdbe17e1d415817742a9e9975aedee", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984766f4a5d1986aed207e73992a0f5f17", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f6d9059a7d08a081b01426f2374259f1", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e6cd0b732bfdd54e96380e69cdd77b8", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c8b22da4080796dd9d8289ccdfcc6aaf", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c69393bace83e7bad1c1b00ded8923c1", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98eee111f5677c620a15319035f7d91d18", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98246ca0dd5400b7d642844dd732f307e4", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981ebffd7d6096733b7de3b00e0d6571cb", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6a0fb04c85fe754950f8d02fb1aaaa1", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf00f4a3e7aa1f4cfe5aa2f6437419ff", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OneSignalPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984a11dc29f668d60c8f9f7fa351a78ea6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OneSignalPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802a31e44ff8a1f4d3b538e2d9e0e95d4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterCategories.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9818ae4f45b41db2cdd7cab97018bf01d1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterCategories.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895c65a6b606d5853ea59ce75a3c131ef", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterDebug.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9817e45555a34124f09fc50fb7840d417f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterDebug.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9800ea5b9ba570b41662199f85b2751620", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterInAppMessages.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f1bfe92028753e1738425ef33d5c406", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterInAppMessages.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9875542b2142a67104bbec31d8cac9ca76", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterLiveActivities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9865f8b98f250199f7baf63db9ab39bf3d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterLiveActivities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98beed101d7f2687da8438f73e017415a2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterLocation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98641930443571db47a6d1a9f168c2e0c0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterLocation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae7632a1efacefbf84d46694afba8981", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterNotifications.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9850ba591dea23b91922501db664e8ea81", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterNotifications.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7f08bcbba286541db2e9ebad3ebab1a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterPushSubscription.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986146afad0c25eae5872ba0e33c457a34", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterPushSubscription.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980de5aa556e3d23f125115b2d5e8f67ab", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c0fd52f96fd889a1498b27edce0a7123", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterSession.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef493e17903ee83be6f99a01c7fff1dd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterUser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ae007ada5b9f81dc5151d592f1cb55f0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/Classes/OSFlutterUser.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987457b44e248ef2d0779cde93658eae83", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a31be4b04167b64478e1e6c58905658a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d66bcf4cf8ed1ea308da80458817f7a8", "name": "onesignal_flutter", "path": "onesignal_flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e3aae4784931dfe409faa6f1f4f04a7", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982eb1e3e2cdcc71a7552fc8bea8d7a4c3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb738ab3602f3973903d773100ca210a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a237c8b54a47d8e9814860bc814211e", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832f16a8d53689ab05276a0c3fd453858", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e16cc99a28d240082fe0d448cef1823a", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98822bc8eb7bf33de0b37b5ed2eedb9d14", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98127073d5973fff77e9243ffdde1bb365", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9825e68883bed8abfd8bc31f739b293d46", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9e6754f0517d4903c54686627c06abb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98229e47e8ea87502749741f4255627c0d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827f57b02cbf8b87d5a013f9098ea2b1c", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e987044d488585fbd783c6d222dac2e429d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b9db3a97b7800394505541c2bb579bd5", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/ios/onesignal_flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ccdcdce51b9cddb430ccd140f7bcf11e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9874c38aaa8c071d756c78f16ff980ca21", "path": "onesignal_flutter.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f97dccb5c6034025c88ea550b70b4fe1", "path": "onesignal_flutter-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98617a8a3afc3dede596724a97ed27e32b", "path": "onesignal_flutter-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea17e9cd4f673a0ba2a1a1bd7e9048d1", "path": "onesignal_flutter-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f814945eb8f1c835e6838c44dca59e93", "path": "onesignal_flutter-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98913c5972c1ce00d68dbcd16b5ed750e1", "path": "onesignal_flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9830f575a70582a8513b0a932fd86eb054", "path": "onesignal_flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9820a45aa2659855ff6d893aefb7f819e4", "name": "Support Files", "path": "../../../../Pods/Target Support Files/onesignal_flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984503f5c5d2ef824e0fb978b4f0f69db0", "name": "onesignal_flutter", "path": "../.symlinks/plugins/onesignal_flutter/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98aca61e659244e1c4aa2dc17c7df7865b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cc53c102c897d84ac7aa1c6a4255d284", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9865ab266f417d39eecac065fbff97adcb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857c9fbf4ad0a9893cecb342edb505bb8", "name": "rive_common", "path": "rive_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ab6fc91b96bd6c68b5fb889869abb4b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98378517d4923e0e5d5dd17d57288489dd", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984dafe1166ad251396af07798d3fbbb15", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b8afcae413e95e1a464ec7f8cf21ee05", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2c685d00549b89479e18b7d1c70761f", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98acd02ab847466c1a8c466353763ad0d5", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844db8426da64084752f99ab0a249b4a3", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e981a60699d145beb3e905c1c10c837e1e9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/common.cpp", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988d077fe14bac4ff7b95d972661d1ca62", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/Classes/RivePlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98801bc54fe5ca544aad587699c15f8bab", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e985a26958da0edd201e2f6cb07ebc2afc6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-aat-layout.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e988ec52ce715a441795ec8f84a8d38cc99", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-aat-map.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98dd5dc949c749fc25731d8c18f97cee97", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-blob.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9896808c52d38d2e325ea21f1b288511cc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-buffer.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98292c1ff3ff18a843daca948a4a339c9a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-buffer-serialize.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9835585d4f79f048be75e0205e57cff047", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-buffer-verify.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9887e1f20d69bcaff7b7b7be773f4da045", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-common.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98a854365526cc88ee68936c140ece3458", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-draw.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98a5c701ff20c8306cc309c66cae1de326", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-face.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98305ccf9a63fe38f03800d0286ae597bf", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-face-builder.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9879744f1fe74fd43ab7e9880038f974bd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-font.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98532e284edd0d89a055c27481b4a9ce18", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-map.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e988eacacc7a89a6b74a2c2a23b2682d336", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-number.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e982fb394a7d2751eeacb8a709b1072d9ab", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-cff1-table.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98323b4eb98ba7f29eb217211d60a6ea01", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-cff2-table.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98d52948097b68a944880ecd5fce6b2fcf", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-color.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e984b6a92c46af4182e6226061a0c2ee264", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-face.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98430c91449b59aff99ce0bc0576f7240e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-font.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98e7ba24c458cab4a5ade0e02035ae62a7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-layout.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e982eeed929142da67b741c51bc10f1e51d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-map.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e981eb59cb4388e14f80a2c82ac047d1ac4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-math.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9813343b5c4f8c23110c2ae7f6f8c981a7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-meta.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98fcccfb779ce0482d7e3c3dc5889c6825", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-metrics.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98b0e7888114caaea2cf8a5e37fa49f63c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-name.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e983a6fc5147c3496ee0d1f41be158ce3f0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shape.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e983a6b1e2081e2d4d96db1c1c340003243", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shape-fallback.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98facffd06b18c9eb259cce2e425be9304", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shape-normalize.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e983b3d90783cd4a71c258fd70364c804d0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-arabic.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e988b047c4a553829f6b4bd1b7ea1042afe", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-default.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9889801f290b9a741a5af8c38d29bd54c9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-hangul.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98a0a20a33d0e9fb52ca31b1efa4c3de73", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-hebrew.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9837e4bd8e8d25288bdd4f8c6a6f3c69d2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-indic.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e982b5c0c09a142f4655b79c411e9bfc11f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-indic-table.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98021b2e1c95c3f66f807324cc25f18d4a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-khmer.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98076569ab6678e130e74949f625f45faf", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-myanmar.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98f59c75f174fb43f26ecd1dbc8119371c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-syllabic.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98437d6a7142c8d8249a4cd87bb80b50ff", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-thai.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9804b1f8866231d0d1ec4ea63dd18830c0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-use.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98b89a0e0a22fdc095b82a8a63e8cebb8c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-shaper-vowel-constraints.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e985c3fea58e984bf4f11b46f9882a4b0f2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-tag.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e986b96e58ad8ed35d9dcbec119bb71fa98", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ot-var.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9854499ad231c8b3d267faea9bf8912fb2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-outline.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e985cf9a6328f572bbb05e76ac095e7759f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-paint.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e981c1d8ad0443b2a11bfa448349f8cd81c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-paint-extents.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e985b7ac6c77bdf7ad2ceab9b82a796b51b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-set.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e981c3b45e64118db6b9f026ac976284799", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-shape.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98721753b2258fe35bad5aab2b7bb495ba", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-shape-plan.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9821d835fe2418f2104f4a225415c35748", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-shaper.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e983222a60741e7f691eefaf3a83c99f35e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-static.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e989670c90bdb5aec4fc3f9b25085531866", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-style.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98b0c2790606583591da66b3021f86e32c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9866b83d3756312d35d6476c1524790dce", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-cff-common.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e983ca11f74bba5b0f638d7629bfb6b9e39", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-cff1.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98b3251cec6fe78d40526508054de0e04a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-cff2.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e986a42679d4912a6becbb98db56b818f1f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-input.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98fc43ba41064f33cc1441025106f68874", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-instancer-solver.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e985725332a29f40151c2981caee7f00f99", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-plan.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98e4d5ca1f86a2de1c5760fe01b13ccf02", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-subset-repacker.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98bef1cd35caa0ab063cf6f6f058dc0542", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-ucd.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e984c10c4e10445fee4d73421f3299d22e1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/hb-unicode.cc", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e983ed401e1c3043094f75ab7c20615250c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/harfbuzz/src/graph/gsubgpos-context.cc", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98286acd701efc5bfeebcea94b9a35f53a", "name": "graph", "path": "graph", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848800ae27a9397cbaca6ecd8b6e93612", "name": "src", "path": "src", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984cfd9e5a840563954b55134e9a4b1e55", "name": "harfbuzz", "path": "harfbuzz", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9845b9efd5e9d16842e61eeee9d1bbb2e7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/renderer.cpp", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9877005d31cbb8cb385aaa0fde73e31484", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/audio/audio_engine.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981e2df5a973e76ebb5a6ff265b45a9084", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/audio/audio_engine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9804be3f9abe8898b51d8f8c276bb087a2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/audio/audio_reader.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98b26b0567bc7c7bbda48b5b74c9e3066b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/audio/audio_sound.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98518f4440b844ec59bdffb57b85dc3705", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/audio/audio_source.cpp", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98628b4cf219feeed2c1a0196abc198be3", "name": "audio", "path": "audio", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98118e412335f3987a9075869c85314696", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/math/mat2d.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98604fe2ddca12b65eb4d0de4523e64c19", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/math/raw_path.cpp", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984aaddd25ef295c8e3a40bb1b71a2025b", "name": "math", "path": "math", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98ea11af9beafb9c663379ae4954553140", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/text/font_hb.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98d6e88325f6f193a5d0b1edc7691d8cb9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive-cpp/src/text/line_breaker.cpp", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980c3df7a3c91341845b5a019550ec3b39", "name": "text", "path": "text", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b681bff331f2cff2714c39f94492c7d9", "name": "src", "path": "src", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a25f9b3f3953eb36654fcc3a27e2927b", "name": "rive-cpp", "path": "rive-cpp", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98948e9bb04304f8632d0aeae0e7d4fc83", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive_text/rive_text.cpp", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dfc7a14053838da9233ada25b79c2a51", "name": "rive_text", "path": "rive_text", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a101f4fba863083d7c9c677b73c0001a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/SheenBidi/Source/SheenBidi.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9893ea57ce9c810d142c88340a146cccf4", "name": "Source", "path": "Source", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b804844a6b7ceb351d3e67f5e50d8904", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9853cddbe29371b607fe1b8a2046d77dae", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/log.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e987dbca31dc2c495234687faae3cbc5347", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/Utils.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9851bd45214eef5ccb961f324702c7a12f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGConfig.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e986a9064aa42e672671abd99837eba93cf", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGEnums.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e987ba5363906c18756b5d19533fded46a3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGLayout.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e982f16463381e806824cf27b2847f6a732", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGNode.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98b73d9f5efe874d18705fadf29d609d4a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGNodePrint.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e98014ff9de9b397f1515c502679fba677a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGStyle.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9885b5a86480a4b97f5a743736df1cf9c6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/YGValue.cpp", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9830ea5d99c0c0c208e108989b62e93e73", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/Yoga.cpp", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e987f4b946dfb6449d906c4d97ea2bfec80", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/yoga/yoga/event/event.cpp", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9838efba36d6e64cb89579428c17751922", "name": "event", "path": "event", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ccc3812a036937829729b5e867737b82", "name": "yoga", "path": "yoga", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9822edfb68338a4c389a9a5325f0e209cf", "name": "yoga", "path": "yoga", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4e20dce5f0c5326fc971b2bf6fe6e10", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862a9bd4857b2396017ccbbb188bf250a", "name": "rive_common", "path": "rive_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6c1ef0a043a97085d46002243f94221", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800a33288322fd38c7a8f2ad1a9041e80", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d88924820b9de85cda95ff6ef70a08de", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9850ec6a9577fa1885613dd330a8bdcbe8", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983c212da2832eafb7430a9daa2b6854b1", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983ae9f45a6bf638202bbd89b39a18f954", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e5d2df4ccc846dedbe1bc012fe31a46", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982aa9a219722edc6c8b847c05c1311237", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f24bcd43c21c53815eb15797acdf71cf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e9d52055fee4434b375c523e201b8be1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fcde343d2c58d53af0c05ce3b77a4056", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98eb223337342bcdf2cad630494f916f75", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b480e34f25f574256ecfd8c8bb0eeb5e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/rive_common-0.4.15/ios/rive_common.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9834d67ab98d4738d2ee88b5b78b2ed13d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b85c98140942faae361beead909324cb", "path": "ResourceBundle-rive_common_privacy-rive_common-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9819f9ed7df54b23ca2289e82b31fa8757", "path": "rive_common.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cc54b19698d694885c3e01f07e2735d3", "path": "rive_common-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988c60e2272921e4b1bf7882201f566402", "path": "rive_common-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a78bf85fe2bd9390d7341083351020aa", "path": "rive_common-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9853c2e4a593db20fd75712709971c7bd6", "path": "rive_common-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983f3900c3837cb0bc1baf282f05e538fa", "path": "rive_common.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98053786dff7b5e2110aaf73472b42aa36", "path": "rive_common.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9850d93a40f9e386146ff20a6ef8396504", "name": "Support Files", "path": "../../../../Pods/Target Support Files/rive_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f84fca22f17e5958423a575a62a5d826", "name": "rive_common", "path": "../.symlinks/plugins/rive_common/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98fb554d335744c762f100918c9ac234ed", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983f8051aa300830092131cb4515815834", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a49ebe11ef8b8a20f2964945e06dc5b2", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dad14c5108b65b91bd847a7f42f7f549", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981546a1e15da95763d1bca9797062a2fe", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ab5cfee385ce2ed8172458c79f948b1", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9805e4079a58dc52edc93a8574aa1650f4", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e837fb2a930a5a4e6679222993d4c1b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989baf9d458ce64ed4dc9995b0de7962bb", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98522ea0df2840554b0b14288f6f008f15", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989349dcc95d973e0fb194627cfa07d02e", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988504b8ae05dc913d8d7b87530ce5f2ec", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ddf969ed9ee022bbcf9b886d9a0c0ebf", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989eb30a70bba85ae13349243ca42cce79", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9809c40847c922c5b375691daa416b1fe8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ceae0152c46cb1bf278ca1fe09fedb97", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9884707787e898668c77561499e4dfee16", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb879d85d4878c58c1b613205316b8b8", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870b924399a48e991c465e88903743516", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d3a269d7a26e4c259c47986e7a94535", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db5450a6cad63fceddc5005e1ad4254c", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f9b1c8b9fa00c7712fa461b687962fa", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868776cc8852ead45b029aefc9cedbdd3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6b0d6e7b1f02857e974dd553e3a96b5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980141f7393dd8ce7307aaf8def846f6d2", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd11d84ab5100409534e33459217bd17", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d5305f08311d78225f0b356deb1f9b1", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf56dc328544a876ad19922b42f3c8ef", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc3dd391be5cc1ad231543bd7722bef9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5ff58c39dd27ab7f107687f99370352", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc8bb9976f26ef1b834aaa1f94525bb5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981361e8a06bc3405518aeff2ca93c68ce", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3cf5348cf994ca55209447a79048fb2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a24a1dc48132d59e1ab429be1565406c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98234f0a8097224f44579c0038aa842f4a", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98afabfa8da550f8cf5871e9b2eb7386c1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9803ff03bfa1b9a5d3016f8145783ca1e6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9802c719cdba02f4f064e6b47ee3ce6406", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9825a10d8e08481cd8339c8ea03f39d4bf", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985debf41d46f7d1f66fbba95507337c2d", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dae8b8c626e8c6d57fddee815ef7cad1", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986a146e35c5576f2a4be885322cd4bb22", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98180efcc812895fdfcb60ad105160dc51", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987d3af59cc332ed16c1abbd7269fba894", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982fd519fa1d75672ec6683e7de4dcc979", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988d14b05418edd273cfde7814fd7a785c", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988c5dc9fb0260f1d4b167560eeeac3b90", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98586819565fdac3f68b39c7e799edacd0", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986581f435e63d81e5d68b7368195c4d72", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithAppleAvailablePlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bffcc1073c5078a546e05f0dff5687b6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithAppleError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98496002aabf89f970232d077fa7293a2b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithApplePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ce900a6b045e3a096d6513fee52a27fe", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithApplePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bee02a0a576a03a3fd5736f3e439d939", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithAppleUnavailablePlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ddd2d916b3ed85ee5b34dc7b50634dd2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SwiftSignInWithApplePlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9869167a57054dd2a0f5f08ddf9001b41f", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872975d2410cfd2843545b412cf1b858d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98daea05a9cf2f481c859b3f84d1c61513", "name": "sign_in_with_apple", "path": "sign_in_with_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcaf8fda944fadb4b8a9f62a96f79780", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985aa5cb485a7355abee4bb5ba30b4bc20", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebb429824dbbd1367ace3f24d243507a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984022599527517c6a4cce6ecaeeae1317", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc322b15fe1b106f58d6282ae374e106", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6ba16be24abbfae05b910ae21ede2de", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989172ff08dfbfd8458d828cd595b27bb7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830f1be6db73268721bb8307835864b50", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5831c247e158a3f6ee3663cdee63fa3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b81da506930a670bedacf2133442d63", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eaa5013992c3545fde1b37253d94ae3f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870c97dcd1fce1f1eb3cc03aaa87800fd", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e982d3cd7528e39f3c6a701da2e5f0bfa8d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d9450c38eb6a8531923acbec4f86e092", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/sign_in_with_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98396e448ac45ee073ee3d852ea440d68a", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98fe46a25715626b5226f7adc8efee0ecb", "path": "sign_in_with_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e396736a8bf252dd49bb66eb43abde09", "path": "sign_in_with_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987655c08e32d196751482e3990a8b122d", "path": "sign_in_with_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982737948c8f8827ade7c9eca0c9a06510", "path": "sign_in_with_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989abd5cf621a50f7cb1823f65d616bed6", "path": "sign_in_with_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f36805e9ed745587596f48ad42bbf84a", "path": "sign_in_with_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988215d6cc25d7a92a2c87cac41a3fd3b2", "path": "sign_in_with_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98801e984f12d887a8534249296f082246", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sign_in_with_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe5c96cf47d700285cb405347fb68ceb", "name": "sign_in_with_apple", "path": "../.symlinks/plugins/sign_in_with_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98df65e5b0cbd69e264ebc7be26c4361d9", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9852d34c464c141d037f1e640448394e5c", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984220a7c187c15f017bb6305bce6e364d", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880cbe5ab2e620e4b2c3169059ba8bed0", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9821f01de28c9f31c05aedee552cc19558", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98594b26174efeee8f9dc13368ca91dc23", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98484f947b5dfed583430f2bc4165174e7", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d501a7e9f85cf060a425faf7a39c6a4d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866827267bce8913c9d177dc8f6b14634", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e7919568747a9743709d9b04b92ad5d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98048e7a2d6d6b1694bef49e4d765dc08b", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6e9700ee4f00ee1986dfced305bb1f3", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a588ef970834cf812eb9230bab4cdc8", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989fd7f20fcbb941e52ed55ee6bb4e7fbc", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d147fa3418a8a6871a127f12855af4ea", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe0f7af844f19dc26f8195a737f98ba7", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986acd46a157528e861d884ca1076c9366", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9821be13f990fea473456074b13eb14700", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3066f7360cbd320e582badd08f98608", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d2842fc6d91885c78a1a755b02beb6ac", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989d29740b83cdce8ceadfba74f3eb21b6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843a149445b64be87fb8b46381a17e923", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987413c3bea9f55052d6779df1898bdd8b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984b2b426cb4a4389a83512db0094dd804", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ed43b56efdfe5545495570775285299", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9806931a374dfcef4dd58f134545fc7c97", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9829a9c9c532931ecf5d93417c85f9e0ec", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981880f8c055e9083f9dee97308033394e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98478a94e923d9235a6cdc1aabf64af5c7", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98958760e828f276feede21d7781dcefb2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bc5925e66a15823ab7083e77ed1b253f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c497238b8f6789541d46033ce2ceb359", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985dab6332d7b0b639be0f97b665f951ae", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984facdaaff01fd7b934869c35b6429d6e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980bd5fe422140faeafc417dd3681fced8", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98133d918d3e0494295931acffbe2d4ec7", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8ac2a88a82dde52488484021a28e751", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b527ad72b1395d4c6c1ddc711cccac8", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4ffbb57b505677b5d85a0200eb96837", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829ce1236702da4f25dcc08d315bc5b2d", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800c5d0a6b03843e30d2fd6045bc1bbf8", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9863703f24b431882fa2ff7f5270c20600", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873fdd0f9a24e30bde27ef516e14b334b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a991eded301f83539504f3b60beceae", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98985beb83fc225c6aff8e6ff31eb2e1d7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f12e3e27c3531b068f240720684e2b4e", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98efae3b6a4cf742526b58afcae9ffb8d4", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987774e81377e9adecaa066917123b38bb", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbdb11e1f9287f406d74800e56c64850", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832e2ca9b3a18388f91a13420e364e4be", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a7100618f9730a0f0fb9fbaaab41d672", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9878de12e8d1ca5a81dfdd63811854de96", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a61c5163d9449a9d97dc2f433f01113a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898372d280ddf4d94273f117917c7ac0e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856788d0400f9a39d9945aaade10a11e8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4898052740514c5dd5e3b239e8ffc9a", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985ca4c4662f8e4f7b28d63b389f8c696b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e982995c4fc925cc925026a91105feacee7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98ea667b0607d8098e0d20720b1d72d354", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982535f0a7ced569eae8aa9f4e6621f055", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ea5428b95530321d32724d77eadb9c10", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d1d05bc6ab246d39b6ce922258ba0c30", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9807a93f2d6cb694888271bb29c8d31edd", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f9b492d7023d11bbaf89c537e03668f1", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4943cfc49acae4c4cf2eb04be78c591", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a97d0becbc79c8dbbd6d646a4419f9b7", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9866899c4aeabe4c0e9cc720686b08a5a5", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e391031374e90ad205926972b3b7812f", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9847887120cd52b7377319e6de1271a943", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bab316fa5a078921860a863743211dcc", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98b0cb81e78055efdfc3e52f6c8e147b51", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984c208f30b5bff5dd665bda610e4b4d67", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982aa44ce93fe17318221def1946737846", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b092130f4d24d984292b2466906fc900", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f49ca209a0c51a60e22f22a218a54b79", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872cd026a3cd28f9ed37136275a6c2c81", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ebd193bf97a4ee4f048686890b84706", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f16fd5df3f5917b0fd5a030c99f703f5", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f350ee73d3a8f41c6d7b5893f0699e9f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d0b481de6f934389d0b53c3d48609bff", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896a2827e4ade4575860005edc18d4829", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987544a6bf1650ee25c9ec3041164d773f", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98733a18093879888575f9087bd5aa01a3", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0c0b7247af25d13fca685219ecdcf4f", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989151ca3941ebf2704e95256e87a1970f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/AuthenticationChallengeResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98225d8849183384031f3fd553366383a1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/AuthenticationChallengeResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9832e620814dc2d3c2e76ffdfdbb857f6f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ErrorProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981dffa96b9cefe150749c1ac39f83736f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FlutterAssetManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9874c2cbd3c834314b9597bb66450b87be", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FlutterViewFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b51a91ca2f729fc4f05b6a52a94c2011", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FrameInfoProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c88fd4a02606be6886b09cfffa6c5c23", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/GetTrustResultResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982443f10d78bb6321b5678d9c9a8b01fb", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/GetTrustResultResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9893b62609c6fd3ddcebfe7beedf89b283", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPCookieProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9803712d87174bf64ac509883eb0ae5364", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPCookieStoreProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e368118d1b2939ff6a70dd091230b9af", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPURLResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e052d2390fe57ab0179447ba47f9049e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationActionProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b6c406384748e40396c6c7105dcc0309", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983c44a92052e9e36fab9c4396b00a6a5a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98785cb41ea4fee0568e7423fca43f856b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NSObjectProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d6a11d2756caaf16ba3feb95d1398fa5", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/PreferencesProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98665ecd67e24dd5d416fdcdf74f114fd9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ProxyAPIRegistrar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98acfcdb50c23b87a23dac78ccd330882c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScriptMessageHandlerProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98858b47e377b4e6a24c4f21c7d73cbbe0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScriptMessageProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c74996ad5e6a96eca7963b2ca9d61c14", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScrollViewDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ce7325fdc02273c926a14ad1b4e5cd8c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScrollViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984eb2e46626151a84eeed7cac4a778b44", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecCertificateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988da82174abc400dd89d2adab2b2a3503", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecTrustProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985040f4711ddbb4125d43717611eb7599", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecurityOriginProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989b9385db1e01666923fa4dbb9499d8cc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecWrappers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a3f55061b1f72b15b3f74e8ed37a1376", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/StructWrappers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fd4e7b0a8979c8734353527f62b52e65", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UIDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98345c98b47bb00c237ae123333ab89a33", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UIViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aa76ce9d8cdb285959fee4036df126e7", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLAuthenticationChallengeProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ba268a1ff13af6da4c27fdae77bfd761", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLCredentialProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982caf6e2338f29e9ea4cf5f40fd760893", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLProtectionSpaceProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980601e494a4cbc41008c24347a24b932c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982deff7e2a00abf753572f2ec4630cb29", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLRequestProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98105ab1e7da9d5c845a85b8a2c96b04c8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UserContentControllerProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9811a1bbd2e9c8d1ee5cb4c5d8ce01b3ef", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UserScriptProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e0c9c766d6fe787a908bed61257f2775", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebKitLibrary.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98177309c3c908dc379b92453ed9a024ac", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebpagePreferencesProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9878e220fe7ffad3e1fe1b33c467d21862", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebsiteDataStoreProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980cecd5253aa17e35b4c17437a0160aa5", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewConfigurationProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987a173e66b351b5fc8d6c92f6cb0680cf", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewFlutterPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983c8bdc4b81b7b639e8d6add9e2fe805c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewFlutterWKWebViewExternalAPI.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a0cdd43942249280f694a8a2c6ddb36b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985fd0aace7010a4f88b51e45d981c2bcf", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8bdddd05d174ac9c169d3434034a89f", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848fcd91655a9b6d09998cd557f610a25", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811cf716f91dab5692779b690e9ae147f", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b8be6482049358b9e566817cbfd0faef", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9863acca6ec5ec49caa4c06d7e0f9aca29", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986837b398d171c0bfee57315e5f00d848", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895139439133986e10dbbb0068e14db5c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98469404963284e765c416c7365edf3d21", "name": "db-eats", "path": "db-eats", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897d0214e7e3221c7a31b642375873dc8", "name": "FLUTTER", "path": "FLUTTER", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856cbdb059d55359efb3dfb99625407ac", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f041e757015822f0b4cc7aa3f8576ce", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98392812ee8698cccf0ba01acc3eab29e8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988768646f34f59a0e7725bc176e7783a4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce39551c815fb970fe73124ab6ea182b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98275afdd50a7d5d528e2e00e8c26cfe44", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bfe678d167416ac6d11dc7ed91d46ed0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870b3dbc59a20949acb7a510efcda7766", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea87d02bbe67668801a76f8d12972fd4", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c36186e3faf9060019ecbb56fa675b60", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9869ab631479d3c67064fd11a89a6924e7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98758a9c57d951e38854ffaebfb7f49952", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c670daea46f16a48517e4652471f77da", "path": "ResourceBundle-webview_flutter_wkwebview_privacy-webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984be39b750fc76d97065c6ca100b10089", "path": "webview_flutter_wkwebview.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fddbb7a8366057fe4db4ecd0ffdc61e8", "path": "webview_flutter_wkwebview-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9840648acf48ba6776e8f7a2797c32ce32", "path": "webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894cdaed461585d20c2484d8718fd5a0d", "path": "webview_flutter_wkwebview-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba916014f738db90e557517e0f7d855b", "path": "webview_flutter_wkwebview-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9835c29fcd64451267a82f462947b16d7f", "path": "webview_flutter_wkwebview.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98997a972174eafcb79cffbcefd67a0cab", "path": "webview_flutter_wkwebview.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9896e8afb3d854aa61a68dcaee8169dfb7", "name": "Support Files", "path": "../../../../Pods/Target Support Files/webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986202576a12a4e81887f68b32372cf726", "name": "webview_flutter_wkwebview", "path": "../.symlinks/plugins/webview_flutter_wkwebview/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9817251d1d6c8a36b8b8fcdf041ac7ae99", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98f682db8b9537d169509768c065a6cc9e", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreGraphics.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98df8ab8f5eb777d3551c3bbc04edf992e", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreTelephony.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98f888991ae5b24f9f379f21bce18f2a85", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreText.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98b13654359f4e9c63153b5fd1f8fd8617", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/LocalAuthentication.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98e9c353e05162843dd115e1ee3b6cfa1c", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/SafariServices.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Security.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98e28c510bcdc3834514b09ab79a067e08", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/SystemConfiguration.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e985b194461b3d65889636e9bae473ca683", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987a819a33b096257e70fa923828343a29", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f57df5597ed36b645cb934c885be56d", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980046e19d98cc36b2b9d350d0b3373849", "path": "Sources/AppAuthCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9811bd59eb4ace90e2e1fafe7dcc095101", "path": "Sources/AppAuthCore/OIDAuthorizationRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98489b72a86d76e1efb5c1d7c7ee173ca3", "path": "Sources/AppAuthCore/OIDAuthorizationRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9839324512ae71bb4a68341659e43a4ea1", "path": "Sources/AppAuthCore/OIDAuthorizationResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b0878d9b8ffc9e2de8ed6040e88fd7d", "path": "Sources/AppAuthCore/OIDAuthorizationResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d06f26229fbeef4c768eab1203d0493c", "path": "Sources/AppAuthCore/OIDAuthorizationService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9807b9143697f4d02244b58fbb13b2b492", "path": "Sources/AppAuthCore/OIDAuthorizationService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a5b88ac8b38cd36cad65a749a6badec", "path": "Sources/AppAuthCore/OIDAuthState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98896e28617d1c358ee27f2e39a4f17b7d", "path": "Sources/AppAuthCore/OIDAuthState.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e23e0c58a66654ea00e72af04bfd540c", "path": "Sources/AppAuthCore/OIDAuthStateChangeDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f733064f3d02fe694ce06bb1af5a2d3b", "path": "Sources/AppAuthCore/OIDAuthStateErrorDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828e357638babaea786b98fec60469dfc", "path": "Sources/AppAuthCore/OIDClientMetadataParameters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9833f7a4d9abb2025adfed39b7b622e8c5", "path": "Sources/AppAuthCore/OIDClientMetadataParameters.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a03a691cd5028211d0a48359b389f024", "path": "Sources/AppAuthCore/OIDDefines.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848fd5f276433d7b53bb978ef861f46e5", "path": "Sources/AppAuthCore/OIDEndSessionRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dee146e60fd489df24bd00e81e33669a", "path": "Sources/AppAuthCore/OIDEndSessionRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98597fe57164256f5db9fa3f700723984f", "path": "Sources/AppAuthCore/OIDEndSessionResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981fd48386f9a3ab795c96baec626f5786", "path": "Sources/AppAuthCore/OIDEndSessionResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821ac00e20d44eb4d4318508e9a970cdb", "path": "Sources/AppAuthCore/OIDError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9829d0e91b04be2addfa884360fbce838c", "path": "Sources/AppAuthCore/OIDError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a34060641e3a44fb6e1d1b313c3b19b3", "path": "Sources/AppAuthCore/OIDErrorUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bd1f671805db28b463f192b0959ed9ba", "path": "Sources/AppAuthCore/OIDErrorUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d0bac3ef6f27eda6c70cb69d2cfc5bf4", "path": "Sources/AppAuthCore/OIDExternalUserAgent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed9d7b9f9896662970643054b11a5e36", "path": "Sources/AppAuthCore/OIDExternalUserAgentRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98160807df11abc826902f74954c6a8cf1", "path": "Sources/AppAuthCore/OIDExternalUserAgentSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981fcfc91686532740e458820134759bc3", "path": "Sources/AppAuthCore/OIDFieldMapping.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989f950fbee0a461bea3a2ca6f3169ee6a", "path": "Sources/AppAuthCore/OIDFieldMapping.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9dd2089047334c0cfab3bf23d281dfe", "path": "Sources/AppAuthCore/OIDGrantTypes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ee5de94f7f3bc72376b468e0c4b013f", "path": "Sources/AppAuthCore/OIDGrantTypes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a75a804bd347d6ae617084c369533843", "path": "Sources/AppAuthCore/OIDIDToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809e7828ab6eb09bdbc1d44a74b15df55", "path": "Sources/AppAuthCore/OIDIDToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd295206031ff9e10c3cd7aed08fc04c", "path": "Sources/AppAuthCore/OIDRegistrationRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98577891e5673bf43d8ef7633126952c5e", "path": "Sources/AppAuthCore/OIDRegistrationRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e4b17296ded2a6e7def506c6e543692", "path": "Sources/AppAuthCore/OIDRegistrationResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9878daa0ec4da8ce2fc043b6e50c2b019a", "path": "Sources/AppAuthCore/OIDRegistrationResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a3a4c862bdebc14324a09d09b171094", "path": "Sources/AppAuthCore/OIDResponseTypes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982384127f13b21b910b1c51b8368e2763", "path": "Sources/AppAuthCore/OIDResponseTypes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c1e80c575ceb9e29ac6bd73324d7d62c", "path": "Sources/AppAuthCore/OIDScopes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a5c0f91df5c3098b0e776d43bc047819", "path": "Sources/AppAuthCore/OIDScopes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c727d2a80f1bf39bb2d377e25069969a", "path": "Sources/AppAuthCore/OIDScopeUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb3815a4c802495cca54d841262b0c4b", "path": "Sources/AppAuthCore/OIDScopeUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9808b6bfef53e91e9300d26a7712fcbe3a", "path": "Sources/AppAuthCore/OIDServiceConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984d02775c04845ea7575418b900567976", "path": "Sources/AppAuthCore/OIDServiceConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9814201fa497177bc26ef58d8982c18368", "path": "Sources/AppAuthCore/OIDServiceDiscovery.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e6f6cdc2354af9ae23ccaee3090c6f36", "path": "Sources/AppAuthCore/OIDServiceDiscovery.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c1f31781fadafc957d7fe45764fc5a86", "path": "Sources/AppAuthCore/OIDTokenRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98932cc1f35e46e3d3aaa4f89257c0c182", "path": "Sources/AppAuthCore/OIDTokenRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b33818482f2123d397b9dabc4d308e3", "path": "Sources/AppAuthCore/OIDTokenResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98869f4acc4b3cb81cd1a3166e4149f267", "path": "Sources/AppAuthCore/OIDTokenResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d8bd9f494c7552514c5878c186f163b", "path": "Sources/AppAuthCore/OIDTokenUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c8c9dda22e2ba44f55ae710e7a34f2af", "path": "Sources/AppAuthCore/OIDTokenUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9eab4b3fd0202b48e18128ad1db7cce", "path": "Sources/AppAuthCore/OIDURLQueryComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9875a0f5ee84f5054424f9cc86eff4eb40", "path": "Sources/AppAuthCore/OIDURLQueryComponent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c43c413cbce3b5f0c3a1c64cc14bba10", "path": "Sources/AppAuthCore/OIDURLSessionProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98349de25a7ad72fba8b40c6e3eee1d053", "path": "Sources/AppAuthCore/OIDURLSessionProvider.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a9ed671a6ad37b18244da8c7fe3f1d48", "path": "Sources/AppAuthCore/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dd4cb02ca3d9c0d14c116e85a43b6fe6", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98faf3a544e50fb4a00d4ebf218051169b", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9877d1706e11c6905fd4164320708779b7", "path": "Sources/AppAuth.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7f2e449447509a4974f292de722e61b", "path": "Sources/AppAuth/iOS/OIDAuthorizationService+IOS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98de48ccc748d5eb4ca671ed8b0aadaceb", "path": "Sources/AppAuth/iOS/OIDAuthorizationService+IOS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce154978a3037e28f7e59bb52c66eaf9", "path": "Sources/AppAuth/iOS/OIDAuthState+IOS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d000ba53e3db3a891e9b9a6fab3407eb", "path": "Sources/AppAuth/iOS/OIDAuthState+IOS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa2bfb7b1ebf085fcbc90e6f7356e0c4", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentCatalyst.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f229930a71eec09f31fa29a330c4dd91", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentCatalyst.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c746374b604e525e6dd9e1aaf981506", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985a6c6976e192376fc668c080830055f8", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c9bcbfbbf644978decd166ccf88c024", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOSCustomBrowser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981844fdd67c8fc8d916bd3ec7a88ef8ff", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOSCustomBrowser.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b09b1ed03b647ca8a4cd3ad82a958883", "name": "ExternalUserAgent", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9879eb56ad448ddf52c6662a8e563c9a53", "path": "AppAuth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9878d2cf3bcfb7bfdfe3d63b9d8a8bd4f4", "path": "AppAuth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98891a739719a75b274acdcb87f4051ac8", "path": "AppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982985cdec864f8ea78cba8fe493fc34dc", "path": "AppAuth-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982e88207ac2c747d20838bc1b624f7542", "path": "AppAuth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9835105608ae29f1c3a3e984d0dda792ee", "path": "AppAuth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b7d248d28a205119daa29547759d865e", "path": "AppAuth.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982fb49be00f17f7056afc8f5edb8809bf", "path": "ResourceBundle-AppAuthCore_Privacy-AppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a53bc333be8d24d32da686bbb627b5cd", "name": "Support Files", "path": "../Target Support Files/AppAuth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d92d27e07d3d9feb2130c19d9a9b3a84", "name": "AppAuth", "path": "AppAuth", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e0811e535f197251618395d7cb9217b", "path": "AppCheckCore/Sources/Public/AppCheckCore/AppCheckCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9854cf5a4ddb2b7ed9ebb8e1c0cf11b312", "path": "AppCheckCore/Sources/AppAttestProvider/DCAppAttestService+GACAppAttestService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985429342f70e6cdbfe194d98f80407025", "path": "AppCheckCore/Sources/AppAttestProvider/DCAppAttestService+GACAppAttestService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6f28b1ef3377950841c32c57e10c976", "path": "AppCheckCore/Sources/DeviceCheckProvider/DCDevice+GACDeviceCheckTokenGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9807cd856cf944d124e634bac8b7f5ecb7", "path": "AppCheckCore/Sources/DeviceCheckProvider/DCDevice+GACDeviceCheckTokenGenerator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cb15bafdfcf1e19420b1f375ea3e1077", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c2fa471da243b0fbe59ccee056d84759", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d52bd543f5932fca2813fd9408e6a00", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestArtifactStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98926b1750d96f1ff154f90d99afeb552c", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestArtifactStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ee01723cc60d8ebcfa33fe221d13561", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAttestationResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98467c3f5ba5359ee56f6f0192c080f24f", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAttestationResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f39a1f0e5a77daef509180f03efa78e", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestKeyIDStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ecd79512b9adf26f16562471b90b4ec", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestKeyIDStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c967f59dbb97ca4d85980e7a9eebe7c", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppAttestProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d3394a3f6cf1315c7edb1878032559b", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f54f9c3d22b79ffa1700032b51c658c4", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestProviderState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fcc68277f8d0aabd1974c37df1945ee9", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestProviderState.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e06b1f671baa362b0ca9a333851fb37", "path": "AppCheckCore/Sources/AppAttestProvider/Errors/GACAppAttestRejectionError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ea14a2917766a44aeb6f4511267ef660", "path": "AppCheckCore/Sources/AppAttestProvider/Errors/GACAppAttestRejectionError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830601cc7e6a33f93c65368368f62e94a", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981795e3539e73c3dd4a613bc20340b88e", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestStoredArtifact.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989e00b86e1a2e2950c3a6c1d2e85d55cb", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestStoredArtifact.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce28d889dcf31653fd754f94e20e1b79", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheck.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9881c3e820fe90fda5e45074d960e7c43d", "path": "AppCheckCore/Sources/Core/GACAppCheck.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982fa3a6bd758096f13aca7e3dba9bdb24", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c339c4499e61d421d724fd304faf2d63", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e6d312e6b8cefc6c26fc42ef8112528", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckAvailability.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ddd70b1bd67dc2f1d759fa31d0377ae", "path": "AppCheckCore/Sources/Core/Backoff/GACAppCheckBackoffWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988c09549f748ac0d5b2b41fc6ac1f8c57", "path": "AppCheckCore/Sources/Core/Backoff/GACAppCheckBackoffWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed05a75b69cb57882b7db47b711e56c7", "path": "AppCheckCore/Sources/Core/Utils/GACAppCheckCryptoUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9874f69ac500a74d1030fb45dd878c3f58", "path": "AppCheckCore/Sources/Core/Utils/GACAppCheckCryptoUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840bad3bffa998a6ab2ce770029cb520b", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckDebugProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987aac5e639d6a0b2579dc8f0f4946cac9", "path": "AppCheckCore/Sources/DebugProvider/GACAppCheckDebugProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a3448f00db94b00e98b6f5007913efe", "path": "AppCheckCore/Sources/DebugProvider/API/GACAppCheckDebugProviderAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d171a6d10d561defd6fb3812c073c816", "path": "AppCheckCore/Sources/DebugProvider/API/GACAppCheckDebugProviderAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0ac302a1ac01a2444e6183eb086a44b", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckErrors.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9869030def4afd6f9bf4a11747b3b10e72", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckErrors.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986bd97c16ffd6c6b81ed3faf7dcbba83c", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckErrorUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c41ba74a65b70ec1565831193f99626", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckErrorUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c73a8af379d4bd5c7d988a755bbb6f0d", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckHTTPError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98793c9d2b5a2bd1f2c0f5e213c77827a3", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckHTTPError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828ff283ff8bc94159d6ade40f3f47ce0", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985270212cbc28ef079a100451d70a6d1d", "path": "AppCheckCore/Sources/Core/GACAppCheckLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985a39a8c404fc533d855796b2e5717cb1", "path": "AppCheckCore/Sources/Core/GACAppCheckLogger+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98333601e60937adb807e238faed747e8d", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98852fc6cb0db5e3e7432ee514f686ff2e", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckSettings.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9835c8183177cce74bc6b11c8d48a7f15a", "path": "AppCheckCore/Sources/Core/GACAppCheckSettings.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f72a1486ef05d58ae80b0c4551ddbad", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98451771cb7f85ccccb7e410ef54f4c7ab", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860b1976b443f07c32f2fa2e388f71e80", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983880d4fdb92aac6571f91e713609eb68", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bc85263ab1696d4d4a7c1adb8e20eac9", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken+GACAppCheckToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989dd0c404fa27c0f97d1286fd53adb48d", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken+GACAppCheckToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d13636de240990561c26262124426da7", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTimer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980d13db17a7e129ef73001bf98853765e", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTimer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dddfc7e78b04db6ea0123e63da153794", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980582f77f4313e8251f42375eaf99d8c9", "path": "AppCheckCore/Sources/Core/GACAppCheckToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c892e5df9575836e9175c4f801d09082", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckToken+APIResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b435084ec5fc715e7c4fb012acb146f5", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckToken+APIResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98174435ac551a78a131623b2c124d7bc3", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckTokenDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b8a82ab643e2e44764af42a5f74f1af1", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefresher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986ca20599780040a8d7e0873c1ed8cca4", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefresher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f32299fa8fa2bbbec3774c32ce8ecb50", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefreshResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cb3fbf95e427ee80a24822bbbd4ad259", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefreshResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b5185fb55ee8cf75c12d064183c50651", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckTokenResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ed1328eb5e04a6838379d31a7f81b80", "path": "AppCheckCore/Sources/Core/GACAppCheckTokenResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989266272b8dcf85e0eb44ad7776aee537", "path": "AppCheckCore/Sources/DeviceCheckProvider/API/GACDeviceCheckAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9818d1eb0b29eb840fe9e2598497c1c924", "path": "AppCheckCore/Sources/DeviceCheckProvider/API/GACDeviceCheckAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980524c0cbeeb478706cda95aae5c0aec5", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACDeviceCheckProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f760e8da9e06dd4d789c91068997eb90", "path": "AppCheckCore/Sources/DeviceCheckProvider/GACDeviceCheckProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ddbba25d1caa31eb423b8ce5bafb824", "path": "AppCheckCore/Sources/DeviceCheckProvider/GACDeviceCheckTokenGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f071e4421bc108f9af709bfc4fa026b5", "path": "AppCheckCore/Sources/Core/APIService/GACURLSessionDataResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981d5737f79d22748b293d8bf4656dcd1c", "path": "AppCheckCore/Sources/Core/APIService/GACURLSessionDataResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980473f04b1f73bbd4f34e42af586516a1", "path": "AppCheckCore/Sources/Core/APIService/NSURLSession+GACPromises.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987992049e9f50052f87bb62d533457c35", "path": "AppCheckCore/Sources/Core/APIService/NSURLSession+GACPromises.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e982e879bc5997d511f1f6a1e227ce33f23", "path": "AppCheckCore.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9825690d898b0a1721c68e9abbc3bb7610", "path": "A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9864fbe63ba0fd7c2714931cf0365bebff", "path": "AppCheckCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ebd701be04c6598e2b6e5c42a22add19", "path": "AppCheckCore-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98af17fa935de605d14865c503f4a13be4", "path": "AppCheckCore.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a039c927cad78ee5205166fd3e738fa0", "path": "AppCheckCore.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981aa849f2180fe041cfa083840c10f3a4", "name": "Support Files", "path": "../Target Support Files/AppCheckCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988552bdbeceacd0ccd120955e22fb4896", "name": "AppCheckCore", "path": "AppCheckCore", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984b22ee89127103dc5303fad44d222ed5", "path": "CoreOnly/Sources/Firebase.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98867af359367d8ee426bbe6917f42ab30", "name": "CoreOnly", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9887b3840b3b0b92e541eade10e8a9faf7", "path": "Firebase.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dd37c8e6d294fc680fc59900ef6cf832", "path": "Firebase.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c93733b92a9e801a4907bd195d0f5ee9", "name": "Support Files", "path": "../Target Support Files/Firebase", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a945c4d1f5882e3cdbbd200b68a58e8a", "name": "Firebase", "path": "Firebase", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b885b9c9d3d38c0c278e0dfebd683d36", "path": "FirebaseAppCheck/Interop/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984f03e861cd7329043262cbb60bcbfaa0", "path": "FirebaseAppCheck/Interop/Public/FirebaseAppCheckInterop/FIRAppCheckInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9839fc9ca5f3669e417e31ba46d0803ddf", "path": "FirebaseAppCheck/Interop/Public/FirebaseAppCheckInterop/FIRAppCheckTokenResultInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f7a69c6c18f38d8c989a2199b5b9a8d2", "path": "FirebaseAppCheck/Interop/Public/FirebaseAppCheckInterop/FirebaseAppCheckInterop.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d640b8dfeca08e2b4ef4a668f9e60be0", "path": "FirebaseAppCheckInterop.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984ef14c744bde764312c50ff182eb86a6", "path": "FirebaseAppCheckInterop-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9850e900e86dea3551fc83640e35f94b71", "path": "FirebaseAppCheckInterop-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872ff893d5e724a17f83b85dd421cfee9", "path": "FirebaseAppCheckInterop-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d4c5e28014049f266fd69e884d3c23cd", "path": "FirebaseAppCheckInterop-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9885270d7a9fe109d7e6f0c7dfeae6f223", "path": "FirebaseAppCheckInterop.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981f37080c7ca47718e3a997ec91c61b99", "path": "FirebaseAppCheckInterop.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989b6f4e6659e907774c827212e425e949", "name": "Support Files", "path": "../Target Support Files/FirebaseAppCheckInterop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980343de705ea26a067d8dd835d06e10c6", "name": "FirebaseAppCheckInterop", "path": "FirebaseAppCheckInterop", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98798540e73afe9aa4eb22ca1b2d455f38", "path": "FirebaseAuth/Sources/Swift/ActionCode/ActionCodeInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982123411ce389e0fe714909938cfb8879", "path": "FirebaseAuth/Sources/Swift/ActionCode/ActionCodeOperation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98df7029882a2c4c601ebbc5ed85f09fa1", "path": "FirebaseAuth/Sources/Swift/ActionCode/ActionCodeSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988dd8a81c5af62f5b06ac5e621603b68c", "path": "FirebaseAuth/Sources/Swift/ActionCode/ActionCodeURL.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981389ca6901a6c20b2499a3fc636119d6", "path": "FirebaseAuth/Sources/Swift/User/AdditionalUserInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f890bd220bd23321b1a3f00fcdd05a85", "path": "FirebaseAuth/Sources/Swift/Auth/Auth.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9806b310068e16413a12a1b127a60d4eb1", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAPNSToken.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9843878f71282f4e1bc234b3007aa3963d", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAPNSTokenManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980f3ea9ae950aae45c5eee079fbb6fee2", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAPNSTokenType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e8c4a966651f624eedff89c934fa0414", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAppCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9857f3345f8f0139717e3aa13815ec1245", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAppCredentialManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980b91b8ec84b1f857a844962b1beedf96", "path": "FirebaseAuth/Sources/Swift/Backend/AuthBackend.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cfa92db3c57cb27fb4456eb98119e03b", "path": "FirebaseAuth/Sources/Swift/Backend/AuthBackendRPCIssuer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988bd6e45588b0e2a4fa7aff137261857d", "path": "FirebaseAuth/Sources/Swift/Auth/AuthComponent.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98acd774b57e57e517ca69d6c9b78761fd", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthCondition.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f720d773c3231977b3a432e5e490e37e", "path": "FirebaseAuth/Sources/Swift/AuthProvider/AuthCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9808344241f5bdaf78189dd0ff5310d022", "path": "FirebaseAuth/Sources/Swift/Auth/AuthDataResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fa1b2f893235edff31e1ca9596255428", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthDefaultUIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d7ef80ac1157c3b47e878769f35555af", "path": "FirebaseAuth/Sources/Swift/Auth/AuthDispatcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980266174a07435effee01cd1a5e33f7c6", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthErrors.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ff452b908ba9645704ce16c0d21a461b", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthErrorUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ccc7ca336a07de58fefa4695059c31d8", "path": "FirebaseAuth/Sources/Swift/Auth/AuthGlobalWorkQueue.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980f0b75836dfcb248e30e7f70c91f99d7", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthInternalErrors.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ccff3e1576e633185ca17a6659a1e760", "path": "FirebaseAuth/Sources/Swift/Storage/AuthKeychainServices.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9835c933d813167c4c53d7ffcd4cbb8415", "path": "FirebaseAuth/Sources/Swift/Storage/AuthKeychainStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ece90d7371e9d6d5264596d331d8e03b", "path": "FirebaseAuth/Sources/Swift/Storage/AuthKeychainStorageReal.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9871ccae1c52d03cdb85d60655f43c5ea1", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthLog.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e11b3387b10e29060f9e7a091fcf1c48", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/AuthMFAResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a33d18f5be92adb0ddecff9a58853117", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthNotificationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9874f97cc35046045ff3bd06f992095902", "path": "FirebaseAuth/Sources/Swift/Auth/AuthOperationType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981886d07a063093417d00a139cf39b1b0", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/AuthProto.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b094c7b8764ca7cacbe231916635ed8e", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoFinalizeMFAPhoneRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9811851aba858ecedbb3f374aa1ca4f9e8", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoFinalizeMFAPhoneResponseInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98568ce506505645534f56d3ef8e0a8a93", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoFinalizeMFATOTPEnrollmentRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cec5858be2e160b072c3a2ce3813b5e2", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoFinalizeMFATOTPEnrollmentResponseInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ef0891cc36acb1f34bf84242757818c7", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoFinalizeMFATOTPSignInRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b7ab67dfde7eb2935dbbd6f817c5dc78", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/AuthProtoMFAEnrollment.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98330440aec6187dcdb811a198eefe3378", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoStartMFAPhoneRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d43ed3cfb18666fe0930e18a387a97c2", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoStartMFAPhoneResponseInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9882f0e489a9f424b6b024980050575f0e", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoStartMFATOTPEnrollmentRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ffbbde6a37edf2d8726210f266d44589", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoStartMFATOTPEnrollmentResponseInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a591d2bed827d340e9854cedd4673c21", "path": "FirebaseAuth/Sources/Swift/AuthProvider/AuthProviderID.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c5234e5414609cb3e70a386b39f23a4a", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthRecaptchaVerifier.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9883b48f3983ceca9715f72ca4d1c78cb2", "path": "FirebaseAuth/Sources/Swift/Backend/AuthRequestConfiguration.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987be5d11b502058bcf337b50f2eafda62", "path": "FirebaseAuth/Sources/Swift/Backend/AuthRPCRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986b8ea50807f7003fa3cbd1ac4f43db66", "path": "FirebaseAuth/Sources/Swift/Backend/AuthRPCResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985916c67f50eabc77cfd691775126bca6", "path": "FirebaseAuth/Sources/Swift/Auth/AuthSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9808127e876595661d360f97cd7f0edff8", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthStoredUserManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985eed239b292c21567b3a6c35a67819e4", "path": "FirebaseAuth/Sources/Swift/Auth/AuthTokenResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987f36da854945075650cca9f2cb1bf714", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthUIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a5ba05bd41c9e80f6f1080396b78ab8a", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthURLPresenter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9823a8ed78f8eba39780c8624e520ef728", "path": "FirebaseAuth/Sources/Swift/Storage/AuthUserDefaults.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9849ee939b74a04b8f4300522938ceef4d", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthWebUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98472fb364639f4793b2b6435f7d0f2c34", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthWebView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aeaee5f2d8c44f6026914fabe0897796", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthWebViewController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f6a19907f337c61ae4e71e9738ac4099", "path": "FirebaseAuth/Sources/Swift/Base64URLEncodedStringExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bb9028d0aee6af2b0b82edd8eedd4275", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/CreateAuthURIRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9812bb500ee4402cfb645357a69cec7c1a", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/CreateAuthURIResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c899634c29d804d09e67a8593f782a02", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/DeleteAccountRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982b408dfa11c0f56a2a1af76faa9ba8c7", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/DeleteAccountResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982d229471f7699b0538ff8c9ec41ec5b4", "path": "FirebaseAuth/Sources/Swift/AuthProvider/EmailAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9882631d7b2b95286adb41024f6fbf31d2", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/EmailLinkSignInRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98db9f56f61269c2142ab236223949dc18", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/EmailLinkSignInResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9878dc6054259c94afb45730cec976af87", "path": "FirebaseAuth/Sources/Swift/AuthProvider/FacebookAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981e687ec8c70ef0efefbbee7437b39e7d", "path": "FirebaseAuth/Sources/Swift/AuthProvider/FederatedAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9893679ff03155e1cd7c45586cfc814c7a", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/FinalizeMFAEnrollmentRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986afe37a38d8651d82965eb36de5e0452", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/FinalizeMFAEnrollmentResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cb23fd4de38d8b30860511a948dadbc7", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/FinalizeMFASignInRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988e2c7d470f849f377ddcf2ad2cb23787", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/FinalizeMFASignInResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f73efa79d0a8ae31917b8709e13d8005", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRAuth.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d35731de43aed8d49ed91c92b43a015e", "path": "FirebaseAuth/Sources/ObjC/FIRAuth.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d97749941cc880bfe5ed6add5076f92e", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRAuthErrors.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983bef5252d0f22057239d1063ba0b11e3", "path": "FirebaseAuth/Sources/ObjC/FIRAuthErrorUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984ff05ebcd971523dc874faee27aba8d2", "path": "FirebaseAuth/Sources/ObjC/FIRAuthProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b4a0b4a4f42a284979a6d493b716f665", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FirebaseAuth.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98127f91322a404e6e939bc660cf6d77f4", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIREmailAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989d253d59631ec80e9962123de77cf550", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRFacebookAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d31968adc5e9e129449ce02420016225", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRFederatedAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980d10a28509e0d3da11aba1e4dbfbfeb3", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRGameCenterAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830a774210bdf587957fe200c3942f11f", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRGitHubAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c45ee3d5a268cb0e22d28d136f82a7a", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRGoogleAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98508e17d191773910029d041415d50264", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRMultiFactor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aa94e1d0283b026a652b5e277c3ee9d5", "path": "FirebaseAuth/Sources/ObjC/FIRMultiFactorConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989276ae8d7a4c270e87fdbb10a08f9b88", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRPhoneAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98550b41a45b411c38860068aaa245c08c", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRTwitterAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982534467405696142d5908f158932dbf3", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRUser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984230478fd996efca4b50e96f9576a83f", "path": "FirebaseAuth/Sources/Swift/AuthProvider/GameCenterAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983d82b57af9e10fa1588b6a178fe13593", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetAccountInfoRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980c8843ceddd6d40c995fa46741f2bd8b", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetAccountInfoResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f7edae272789330700769d0268118003", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetOOBConfirmationCodeRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985cd937c1f9e68b4da13c394347c4b5b5", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetOOBConfirmationCodeResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988155755fa16e7bb8dc0d7a135b2a4ddb", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetProjectConfigRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f8b5198734fd55598038f3d59501aee0", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetProjectConfigResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9860a421cd2956dff7736b2200dbd04d8f", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetRecaptchaConfigRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983e442dc46cdd8ebcdc85e61788b4cb96", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetRecaptchaConfigResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ae20f15428ac9c36d3590edc63e697ea", "path": "FirebaseAuth/Sources/Swift/AuthProvider/GitHubAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d41d176f44800e47149f850037b8d3df", "path": "FirebaseAuth/Sources/Swift/AuthProvider/GoogleAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982cd83d99efff57fdb54e382edc0799a5", "path": "FirebaseAuth/Sources/Swift/Backend/IdentityToolkitRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c378d464d82f72df2d5f8d4a87abd14c", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98712549c35a29eeab9c7e696fb2dd6108", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorAssertion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c82fff012b1a4ba4f3ca80f0adc54258", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982e4c4a7ef4871a3db242fb4bf2916666", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorResolver.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9822d2e7e1c0365947b4fffce4a2d99544", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985dae284c50923340891e5c78c6342c04", "path": "FirebaseAuth/Sources/Swift/AuthProvider/OAuthCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98be927f16f38992062cb5599b8acdc700", "path": "FirebaseAuth/Sources/Swift/AuthProvider/OAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984b4a63d85d88efd97af810a05226a230", "path": "FirebaseAuth/Sources/Swift/AuthProvider/PhoneAuthCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98083a2205e6e943815ff3e94f1d6626fb", "path": "FirebaseAuth/Sources/Swift/AuthProvider/PhoneAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9813fe619ff2b520a0afea410896e743e1", "path": "FirebaseAuth/Sources/Swift/MultiFactor/Phone/PhoneMultiFactorAssertion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987abbcd8e5da6fd7c7912ab8c933f1add", "path": "FirebaseAuth/Sources/Swift/MultiFactor/Phone/PhoneMultiFactorGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988033cadef4fbcc251eed5c6ccc3ad6ba", "path": "FirebaseAuth/Sources/Swift/MultiFactor/Phone/PhoneMultiFactorInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989ef90a0088dbb20bb30b91c1adff3918", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/ResetPasswordRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980c4839632387e7c63aa0c1b46d23d6b2", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/ResetPasswordResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f97b3da33e26e75415463632a2dcad97", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/RevokeTokenRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98358f09251d3a7c5f7ed487de2bf8b9c4", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/RevokeTokenResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aa26025df7d5b2ac7caadde5d43f253a", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SecureTokenRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98625b81e54db54ad79ace3ec73d7ee7aa", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SecureTokenResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ee65a0a746d8aab4ae03ae26f4e27da4", "path": "FirebaseAuth/Sources/Swift/SystemService/SecureTokenService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9816a27efcc8d731d6641f5b7dc9ecc29b", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SendVerificationTokenRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f3a7e46e006db782adeea860de0b6b83", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SendVerificationTokenResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98069d06c0fc2998f544477a435e0f2395", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SetAccountInfoRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a616750ba6ae8a4ebd6be757e58956b4", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SetAccountInfoResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980563334c90196a861b2d3fe571261f32", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SignInWithGameCenterRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98be4a3c9db5262263163f7b925f8aaed5", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SignInWithGameCenterResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a01de41f055e92f0e74167ea2c208fac", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SignUpNewUserRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a9bc1e564882abab5219f4996682cb7b", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SignUpNewUserResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bcb23da2bde7bbd5c06af245061ec171", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/StartMFAEnrollmentRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983ff71180b9546c6e3b8072bc4acb967c", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/StartMFAEnrollmentResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d511cd3c454a86450d488cf4da2ff486", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/StartMFASignInRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986de60854a81d0192efd23e3db5192490", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/StartMFASignInResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b878c04ae06b019191b273d34760a23f", "path": "FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPMultFactorAssertion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d15f4524a2656a5fe942c85bba595186", "path": "FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPMultiFactorGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bac39be465bc7b651e5de14c2fde2b08", "path": "FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPMultiFactorInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ce8c3adc1380f8c9e73a497fbfc48b55", "path": "FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPSecret.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989f7405754506586e150d1c90ab12c6a3", "path": "FirebaseAuth/Sources/Swift/AuthProvider/TwitterAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ef99a50163c097b9ba0bbb6f77841287", "path": "FirebaseAuth/Sources/Swift/User/User.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c916813745ea2e5500e979a9c7760e92", "path": "FirebaseAuth/Sources/Swift/User/UserInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986cdeb9147a5b9cb3e27b5abb5b2445af", "path": "FirebaseAuth/Sources/Swift/User/UserInfoImpl.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987b84376a453cbb5ede443241f4e3b4ff", "path": "FirebaseAuth/Sources/Swift/User/UserMetadata.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b5381b604c1315e5c6ea007eb79a57a0", "path": "FirebaseAuth/Sources/Swift/User/UserProfileChangeRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f5a30ca4bc4e1268df7544f016d914df", "path": "FirebaseAuth/Sources/Swift/User/UserProfileUpdate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f35356f6df88c5bce236a338e2900807", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyAssertionRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ab0a48d624524db26c20c250949fd472", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyAssertionResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983024b500eb3a31694ae7b2508430d20b", "path": "FirebaseAuth/Sources/Swift/Backend/VerifyClientRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dcd340c020ff01d45ffd19ddc346cca6", "path": "FirebaseAuth/Sources/Swift/Backend/VerifyClientResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c3eac9c96b7fd15c543ec634bfdb9864", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyCustomTokenRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9813a47c9bd24024326a51924d2ea51a4f", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyCustomTokenResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fa6136553b21896a8775aa20cba95c03", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPasswordRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984d097cfb9afc54a5c11e07ccc2052e31", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPasswordResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98970af9754c024e11a7ff9810a637b3e9", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPhoneNumberRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9855da9359bc04d6fa9b8cff9092c4b359", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPhoneNumberResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98be30593fbc539c12809f15c28cc155b2", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Unenroll/WithdrawMFARequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b2aa0e3446cf471a75595ced0a30eb67", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Unenroll/WithdrawMFAResponse.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e982d3ee3076a752e056f92a387dbf729fe", "path": "FirebaseAuth/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a0e68ca5a4518723764607674e595afb", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986797bb9de1081daaadf320c5c81f119d", "path": "FirebaseAuth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98179b90302239573c5eb5b8fa46812ea2", "path": "FirebaseAuth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980a7f4df7f3e918d2599a306824318d0b", "path": "FirebaseAuth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b24140a660f48bfc9be46bb0a744ab85", "path": "FirebaseAuth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98172ed27f892b7fe5ab8cf713699b0bd9", "path": "FirebaseAuth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f674375643981d21d3af8cb5bc52fe6e", "path": "FirebaseAuth.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cc062bcf4f5f5abf8fe825d15dfe25fa", "path": "ResourceBundle-FirebaseAuth_Privacy-FirebaseAuth-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b0349a6076996152970e0ff082427282", "name": "Support Files", "path": "../Target Support Files/FirebaseAuth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984df28f5c4c19a10646b6e234925969a1", "name": "FirebaseAuth", "path": "FirebaseAuth", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f74fb4f18d1afbd3149bdfb5ab29701", "path": "FirebaseAuth/Interop/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc1763aa3976ebb5a4129d8f3bfff64c", "path": "FirebaseAuth/Interop/Public/FirebaseAuthInterop/FIRAuthInterop.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98bc43d9636c2035291fa2687fdb20f876", "path": "FirebaseAuthInterop.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f994ecf0b3c5b5c821e136268d1307fa", "path": "FirebaseAuthInterop-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b42be7a19b5b1237ecb736a4d46e995d", "path": "FirebaseAuthInterop-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840f93a7118ac3e2236083e356c79475e", "path": "FirebaseAuthInterop-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c93af56abfb83879856b1fafe7769b5e", "path": "FirebaseAuthInterop-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985344768246a0ad813f0ed60c8126ba96", "path": "FirebaseAuthInterop.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980ea9d81bd85378347dcbf5e2464b9349", "path": "FirebaseAuthInterop.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98febb1774ff445dcfc2661a95dd7b2678", "name": "Support Files", "path": "../Target Support Files/FirebaseAuthInterop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9805108dadc883eea38e1f4dddc374f508", "name": "FirebaseAuthInterop", "path": "FirebaseAuthInterop", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c35be7c6c8e2083be8ea745075cbf13", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9896ca253cfb966693573df1c0d8d841c3", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870cdedbb6f2e5284158be3551e39d352", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRApp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9819c03e0a981100ddc8806afddb30da6e", "path": "FirebaseCore/Sources/FIRApp.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981329831b1a29790e9eb5f82d20904f79", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858c8e5dbf235bd64b101ac0f2820349d", "path": "FirebaseCore/Sources/FIRBundleUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b4052f4d9db16af32bddd2ea037eb56", "path": "FirebaseCore/Sources/FIRBundleUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f43ee1eedeb63d571dfcf470bdf05569", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9831f24ae52833b507d498870f0f08f41f", "path": "FirebaseCore/Sources/FIRComponent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9881d125a7635f05aa39a92c096394ba2c", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98acebf844a521479be77b7053301a8270", "path": "FirebaseCore/Sources/FIRComponentContainer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb40ab29380d9bbdc05d5c9ca76bbde0", "path": "FirebaseCore/Sources/FIRComponentContainerInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cb03b5b16edb9d702639fc546da543ae", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c3df93b7d9a4edaee75a60b04c5274c6", "path": "FirebaseCore/Sources/FIRComponentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9873f789676c9327814ea25c3c556a8fc8", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98099eaf6e3fb8a36f695b771f7d1cbf9c", "path": "FirebaseCore/Sources/FIRConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bce699addc8e5bfdce438b5aa27310b8", "path": "FirebaseCore/Sources/FIRConfigurationInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c006e1b599d34be49a0ead4ace0c6e9", "path": "FirebaseCore/Sources/Public/FirebaseCore/FirebaseCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98422c3776b6d754c443dd0a7ff311630d", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98136bcb9550471ee4f78fb2243fc10084", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b786499bcaba8f65ee03d3baa360741c", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b469b71820171644f1fa71ee3790d595", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9807a9558537b488ecebea34ee358ab1a9", "path": "FirebaseCore/Sources/FIRHeartbeatLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7e5c1f21ef640f596dcd6aa2f89dba3", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9898d2b42608b040550ac70b4e47af07a5", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bb99a24d9e263e567da9b7c9b8d838c2", "path": "FirebaseCore/Sources/FIRLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a0a73f161d4bf3aeba0a504361dad423", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRLoggerLevel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e141557469be245d288ec9a9b4e24be2", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIROptions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f9094605aee4b752d27e94def75df05", "path": "FirebaseCore/Sources/FIROptions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9880adaf4508ea3e67325397bf3de8bf1f", "path": "FirebaseCore/Sources/FIROptionsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98304e2a9e1ad5e60ba52cb64baad142fe", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRTimestamp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98964ce726dfd0d76a234660a197298c23", "path": "FirebaseCore/Sources/FIRTimestamp.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984927632949e4ba84669342b57cd53627", "path": "FirebaseCore/Sources/FIRTimestampInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e0a24cfe66bf3732cd08d4eee5c08fc5", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRVersion.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984f0cf5e067d1d9c84189e087b767d478", "path": "FirebaseCore/Sources/FIRVersion.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9829d80c8c0d7a8ea5952b76df29ea409e", "path": "FirebaseCore/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9868b4a6422f5c649d6631b3cdf3fd845b", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9882dc4043bcf7c16f48030dff383d5e27", "path": "FirebaseCore.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984d92c3a2d5beb2365e9f3d1854115fb4", "path": "FirebaseCore-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9833890f017affa94afa7d12a1fb2a6296", "path": "FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860aed83cddb33454ddb3a4552a81ad22", "path": "FirebaseCore-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988100370e484b3e03b6684d9044d87bdc", "path": "FirebaseCore.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982d9003e093a0109e538a4b771317e145", "path": "FirebaseCore.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d3c6463ff74b2ac2fe157ab435f38831", "path": "ResourceBundle-FirebaseCore_Privacy-FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98239a56c30f193b2e55f1e8b1b55b9957", "name": "Support Files", "path": "../Target Support Files/FirebaseCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897d49506b2ce76e2ed82e970ce03fefc", "name": "FirebaseCore", "path": "FirebaseCore", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c7b5b2d48b007678fafa0dfd810ace5", "path": "FirebaseCore/Extension/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983baeea46e314b16916da345ffd211b04", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98317b2313e8e8f250e705ea856243fc19", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989aa17c8d944365ac53af15cdd38596ac", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98172c34eb1237e15fddf46ed1408b9eab", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981d6aeeeb3d0da2626fa1a294be9dac3b", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9857853d4673ab637ba4dd197ea052caf7", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9835c8b7784520d3a2c475ddba33156917", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98244fc7de6c8e4b7b6cdf1fad7cbdb147", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9876bef69e6900cd1860d04411b406b2f8", "path": "FirebaseCore/Extension/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98381c7172725f7bccbae778d5378b7f01", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d646864dc9d7a131531b435f54c23c33", "path": "FirebaseCoreExtension.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983422c5a6f3f7559029688cb99d9801c5", "path": "FirebaseCoreExtension-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987c19a8d6ee58d89588534bbc09be781e", "path": "FirebaseCoreExtension-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98df66bf2ab6fe1b23a4fd3e5a4c104f41", "path": "FirebaseCoreExtension-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851fec4870172d868599470a3f5cb7de3", "path": "FirebaseCoreExtension-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981522de615a837347d17140f759ea77b2", "path": "FirebaseCoreExtension.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9898ad4c4b5fc88143c5248fb2693cdaea", "path": "FirebaseCoreExtension.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98db06d3185a83af7d2b296fba0756f9d0", "path": "ResourceBundle-FirebaseCoreExtension_Privacy-FirebaseCoreExtension-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9839f97872e04143052cc6664d684b3e48", "name": "Support Files", "path": "../Target Support Files/FirebaseCoreExtension", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2988adfee62f68f85655ae905f0a49f", "name": "FirebaseCoreExtension", "path": "FirebaseCoreExtension", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9848a4f4fe53a5f407fea2ab02e25a58cc", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9800f4f0a330c8856512db67c0afab30fc", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9800e826e6bed45964130400674bb93195", "path": "FirebaseCore/Internal/Sources/Utilities/AtomicBox.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989b893ce1204c2695eb79f1075831f17f", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Heartbeat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9805ea0e957042f7e7a24c48281823d24e", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b5001a0882078c09c9c2c29bc77eb0e8", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatLoggingTestUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981abfaa87a80572a98818b9929e9c18d3", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsBundle.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986cae8b46e5fc3548799fd0060ec603bf", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983c02645cb5a76ae208b345008140f5b9", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980b115e20ef90294e5cb62c2f3ff03673", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/RingBuffer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b6cf198cd381b88f48c986fc70f10a68", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Storage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cec54863aae14381b2e9ae34ea3ec3e1", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/StorageFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b9a6395f99bf1b7c37e75cc9b26f02b9", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/WeakContainer.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e983b9d8ab3441c591515323d63e679c2a3", "path": "FirebaseCore/Internal/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98284bba85a64f11288e6af319a19ebd56", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d1e0c420b0700d39814b6ad5ff2301c7", "path": "FirebaseCoreInternal.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98460b53a8253f028e6442c3bbfc522858", "path": "FirebaseCoreInternal-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d58a399f2830e68d6099cd9d687735f6", "path": "FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984778465ec02aad8dbdf82695918fae7d", "path": "FirebaseCoreInternal-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f64e1e88a1fd156b641daa38c6dd222", "path": "FirebaseCoreInternal-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9805e99277c6affce6464bba15e2d9f734", "path": "FirebaseCoreInternal.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983608760497e4ebe8521ce1c08e9b33dd", "path": "FirebaseCoreInternal.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984cfa75950ab9cfe6a88412178468a12c", "path": "ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9813daf349bb4213486b58b3a0a8c160c9", "name": "Support Files", "path": "../Target Support Files/FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b6fab49c5f0cf5849a6acc74b4a711b", "name": "FirebaseCoreInternal", "path": "FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6aec76afd744bd86845d94957b973c3", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8ad9b63470665d4d26165ae8a1586ba", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9890819be665b360376136a5ededac5cdb", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c7e89d744cb810271c452e974570e7b", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f1020be6243d341514cbc3423ead1509", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804922949d9c15ffe87e26e8bdfa33b8e", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e7f4ade5da68b70ef623de6e4273789c", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981527b40baedf31ca4fb84f2248907208", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FirebaseInstallations.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9801972aa1950026827f701b313d0301c1", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e5b04e4239b7f3cc4f6598eae5368ca", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a65f772745a396b62451ab5ee61dc9a", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallations.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9838ae0760b41a19e8d79f68e7dbd749bc", "path": "FirebaseInstallations/Source/Library/FIRInstallations.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98824796121fd6e19b42b7b3b0725bd24b", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c55b8ef6c24a95d359c4a3bf1b7ac6f3", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9880009684a79d2ee214b40679dbccce0e", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallationsAuthTokenResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98252eca19aaf0c027a4bbd796bd163adf", "path": "FirebaseInstallations/Source/Library/FIRInstallationsAuthTokenResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98630d3f422bf8b7c8fd7554e0926b444b", "path": "FirebaseInstallations/Source/Library/FIRInstallationsAuthTokenResultInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a10330611d8bafd5d3539c4aaa8764f", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsBackoffController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dd913a5ad4f7cb2ee634e0bfec7df513", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsBackoffController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9ed37915a027632c6f949a0e9f84b20", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallationsErrors.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98626fb38b758510dc840b4bbc058831a6", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsErrorUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98013fac81ea9fe09d0e1d3f9e6759821e", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsErrorUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da0815f3d65743f68da6f733ba96b5d8", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsHTTPError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987436a31600871d6d0d2df500c393d0a0", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsHTTPError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6f3568528dc17b30b6809fd1a771db2", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsIDController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e96bd542226712f160c17a720fe2f52b", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsIDController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f90df4d54d8b2ee5c777d1a4601b060f", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984fcc96728bb523cfae3415bc2fad0983", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb689ef734c9099f3ec465fdf808e575", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDTokenStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e2870f3210d52a2562b9eb031c56c432", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDTokenStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad19f2d22d56e02f6d608675511c640f", "path": "FirebaseInstallations/Source/Library/FIRInstallationsItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c37e1ba0c0e1c74038595dadf6047096", "path": "FirebaseInstallations/Source/Library/FIRInstallationsItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f2968dc8344979cd99fdcd8e24ffb6eb", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsItem+RegisterInstallationAPI.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b41bc948f141e812cf96e6f90fc3fd25", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsItem+RegisterInstallationAPI.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f74a971033a88927ae2520caaf032a51", "path": "FirebaseInstallations/Source/Library/FIRInstallationsLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982283570fbea81a151db8dce81c1411c7", "path": "FirebaseInstallations/Source/Library/FIRInstallationsLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983568aa48abca9cbf283e582aeedcc38e", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsSingleOperationPromiseCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9828147f94fe871d8fde1101d1bf1a5c93", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsSingleOperationPromiseCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982c9c0e2eecb925e78fca446b3dcb5f49", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsStatus.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828001f3b89646b42a7f4f8fc5c489fa0", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982dcd312ab62316c7407f5179a09fd49a", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848e6cffc9f74824c8783265cf16e0016", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredAuthToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9819f4ad264a014af1bc920eb4de110448", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredAuthToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9843fd1bb786355b1e8567de9463ad0ac8", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9805b87140b4c0febe694a0600e945a333", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6cb0625b3947553e1cf92f24c6b1f41", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986cd3c1c35c4fa78118d5e6f4b80052c2", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9868dea8a40d43330f6d1885fa169e83f3", "path": "FirebaseInstallations/Source/Library/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ce3325b239f694ab678be6f0b713a694", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98cab7a3f941e2dbdc5aa0be57b4750a12", "path": "FirebaseInstallations.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985d82a48cec496270711e172d30991419", "path": "FirebaseInstallations-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980135f333179c1fd076a5db43a014925f", "path": "FirebaseInstallations-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eb01076903df0ab347b1fc5a262ff094", "path": "FirebaseInstallations-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d74ba8931f5d89d9924043ff9b70e42b", "path": "FirebaseInstallations.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ba6b38401a0b68d7b07af48e31c997d0", "path": "FirebaseInstallations.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d2a3a25cfe721d037684d5d191feeebb", "path": "ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bcb2f9883e915ff961969eab5fe5417c", "name": "Support Files", "path": "../Target Support Files/FirebaseInstallations", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f845feb08949c63f4132dc3246aa222", "name": "FirebaseInstallations", "path": "FirebaseInstallations", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6bdc7f524b792647f51b3c28405201b", "path": "Interop/Analytics/Public/FIRAnalyticsInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae85709c15f5c504d2a9c8fbebbb1284", "path": "Interop/Analytics/Public/FIRAnalyticsInteropListener.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e71578f3b1c705be6aa5928b5916831", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982e0649f08d8a0b5a201c0cba54bd0cfb", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98acd987f79699326213fded35491bd4b3", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9866a8cca069eeecd737df31fa7f37bc80", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6de99029fa0d0f799bc68a520c035b4", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ed7f912fa15eed7abac969bedce7ec2", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9850ec5611f960d734cf4c63405b463f3a", "path": "FirebaseMessaging/Sources/FirebaseMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d740c86cb130e6089b6575c596d9f7eb", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FirebaseMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d21f9b5b548a48c708697531a034e6e", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98885f9d0d26baf75dc6dc4c53de293504", "path": "Interop/Analytics/Public/FIRInteropEventNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985f2bdcd178bfa82bc8a29bf115580dd7", "path": "Interop/Analytics/Public/FIRInteropParameterNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f5df3910b3c43d44f0030ccbbb9629e1", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986551bf36b71a7a15103570113c8885b0", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9835eb35a8243cdea992c91ee3b703e63c", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b46067814d02b02fe4ecf45d691a54cb", "path": "FirebaseMessaging/Sources/FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c313c7fcc9daa55deba29e14b7c446b7", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessaging+ExtensionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9816a6c6ccefb90cde6241db54cc82b926", "path": "FirebaseMessaging/Sources/FIRMessaging+ExtensionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860ac197fa38fdc01ab5c31a3906e5139", "path": "FirebaseMessaging/Sources/FIRMessaging_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98325892cced2817c95622998d12b649b7", "path": "FirebaseMessaging/Sources/FIRMessagingAnalytics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98abf7a2b33a457636880c41f2ffaa90a9", "path": "FirebaseMessaging/Sources/FIRMessagingAnalytics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed38e7869de5022b34dbf5231d034e57", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAPNSInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843e03d306f30e687356d70b0c3cadb55", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAPNSInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c2fd07d787fa7515bf2ede3e35872f31", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthKeychain.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986be35ca5c9710efba82ed75e60538625", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthKeychain.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9886a32d5fb9feab2ea5218264bd7012d9", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cdbb16e0b9b98256f442f27dead2a487", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98318c77de13cbc016761f462c8602cab0", "path": "FirebaseMessaging/Sources/Token/FIRMessagingBackupExcludedPlist.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a1ecec8f9f57c4d0fc43954963cfdee", "path": "FirebaseMessaging/Sources/Token/FIRMessagingBackupExcludedPlist.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882310159bfe8be59a87155519b95f49d", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinPreferences.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9848d0f1b8d943c2c33328b5ec2bc1b1b9", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinPreferences.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989d082f69ca50eec2c413a32ca2b03834", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9833f955a6c9f702098b7ae5f1af17855b", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c0750af2f2a7b5e99e6682e048571ab", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982941a1dc5ce1f32e62fde5b1ea2f1afa", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5f1ff6fecdef3b6e5278f314fbf60cf", "path": "FirebaseMessaging/Sources/FIRMessagingCode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bdab23381c78aa6ba6d722efc15a83a9", "path": "FirebaseMessaging/Sources/FIRMessagingConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b51d9b8751bedb901c2ef7ecb55df40e", "path": "FirebaseMessaging/Sources/FIRMessagingConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987c3e0e2035bfca6e18e68705951e643a", "path": "FirebaseMessaging/Sources/FIRMessagingContextManagerService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dadadb11542036a3bac004c39290023e", "path": "FirebaseMessaging/Sources/FIRMessagingContextManagerService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e7a2665dfc2a787c84049ff9c5d1f585", "path": "FirebaseMessaging/Sources/FIRMessagingDefines.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819d3f16269a5c15e09bfcc16e34e444c", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessagingExtensionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c6ef1344afaa48979b2c3b0652bd44b8", "path": "FirebaseMessaging/Sources/FIRMessagingExtensionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882b53230b8a7ab963b93890eea00ae3f", "path": "FirebaseMessaging/Interop/FIRMessagingInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988993215557b51fa861aa0babdaa58a0c", "path": "FirebaseMessaging/Sources/Token/FIRMessagingKeychain.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984759061d3d44f9e5e7fadd284d512c3d", "path": "FirebaseMessaging/Sources/Token/FIRMessagingKeychain.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851f8905f8ac80493f551a76edfb072f1", "path": "FirebaseMessaging/Sources/FIRMessagingLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9817df6c277af2214ad81a1042b0346779", "path": "FirebaseMessaging/Sources/FIRMessagingLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef5fb78a500784fb07ca790c549cc557", "path": "FirebaseMessaging/Sources/FIRMessagingPendingTopicsList.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f03f110b20ece608de77701cbd6c2da1", "path": "FirebaseMessaging/Sources/FIRMessagingPendingTopicsList.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eda6df19090fb11c9e983bee47340040", "path": "FirebaseMessaging/Sources/FIRMessagingPersistentSyncMessage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9877428e1235368e44573b0f262581d77c", "path": "FirebaseMessaging/Sources/FIRMessagingPersistentSyncMessage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eef794aa6a5ad43f3a2f6df5f7d1c6c5", "path": "FirebaseMessaging/Sources/FIRMessagingPubSub.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9827c8862a70f768b0f03cb1e17e356c0e", "path": "FirebaseMessaging/Sources/FIRMessagingPubSub.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840d4c88e57e961a8f8203136868369de", "path": "FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f9f92ef442ee44575f1ef5a1f54ea63c", "path": "FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d21476a5555ab9cec670fb6cce5582fa", "path": "FirebaseMessaging/Sources/FIRMessagingRmqManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9876931f14abdbffe50bbdc144dd568d2b", "path": "FirebaseMessaging/Sources/FIRMessagingRmqManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98440ee27b1957d9cc76024af915d54cb0", "path": "FirebaseMessaging/Sources/FIRMessagingSyncMessageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c7dfcd638af0b638cb4b270bd1d5e440", "path": "FirebaseMessaging/Sources/FIRMessagingSyncMessageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9823a99f805ed0a8e1d961a0addb549498", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenDeleteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e307044edf816ae7306b262c2fa50eca", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenDeleteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8b8d193dfdb95504ca233ec9187dce3", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenFetchOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98349664ab7d2eafd934cdca12ffc65402", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenFetchOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9841ca9fce5bbb3316ccdf83c217544b25", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986549072956f609ad2efad12ae3eb9ed4", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9a9c4c628675be22a68466b220f69c3", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a57185068fba979255ca4e409290e7fe", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9824428241e3439d9668bbc2f82ca32b0e", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ae8a776e6847342b65bcce74461e9a90", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d66a23995cc6e2d61eeb3a8daac220e8", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981042f46f5fa31e86e7eb39bda01775f2", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1405ccfe7c95f8f5eb002c6be0188e9", "path": "FirebaseMessaging/Sources/FIRMessagingTopicOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98036bab9b4472abb8f1cd946979c61497", "path": "FirebaseMessaging/Sources/FIRMessagingTopicOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98041158f415525c4aed3213f8151d2cf8", "path": "FirebaseMessaging/Sources/FIRMessagingTopicsCommon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98762cb2172ce331141275fbf10f0bcb5a", "path": "FirebaseMessaging/Sources/FIRMessagingUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c82be0ecfef44df8b703289ce13f4c56", "path": "FirebaseMessaging/Sources/FIRMessagingUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981ae6467934b0bb991ff5d8886fdba813", "path": "FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825da8ce99594a238f6929d90895b3b07", "path": "FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9866bbdc9a57bc083545baee7eac53ce99", "path": "FirebaseMessaging/Sources/NSDictionary+FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98667e9b548f4f01273474553c26de40aa", "path": "FirebaseMessaging/Sources/NSDictionary+FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987654fde657d67ecbfe053d985914e328", "path": "FirebaseMessaging/Sources/NSError+FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884f0bf86f3a17480ac866f366299343e", "path": "FirebaseMessaging/Sources/NSError+FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e980cb4df7d7e18506f7895885444e3057b", "path": "FirebaseMessaging/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989821a8972b8e103752e97fefd730bd11", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9899d04d356e5d9d1c4f46d07f3587d56b", "path": "FirebaseMessaging.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac4823b08309625c8ef8098ebf4fd0c6", "path": "FirebaseMessaging-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981d750f7fea9c65a7c84a84df363aa497", "path": "FirebaseMessaging-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d58fae14f8563edd3297a754ca62be64", "path": "FirebaseMessaging-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98707f1476040ce6c57fb829e89141f1f1", "path": "FirebaseMessaging.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9862074a87fd0758e7fbd5b2fcf45e61b4", "path": "FirebaseMessaging.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98830c23c3e725305cb095914c9b51a138", "path": "ResourceBundle-FirebaseMessaging_Privacy-FirebaseMessaging-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988a58b91a6258d34622d414f2fea7d160", "name": "Support Files", "path": "../Target Support Files/FirebaseMessaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98794f2df0d6aafd8f813c45b28d50a59c", "name": "FirebaseMessaging", "path": "FirebaseMessaging", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985c5ee3d9df2240ad4f45306481538e10", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/CartesianPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986c5039d5f8bff60d8b7454792a5b4804", "path": "Sources/GoogleMapsUtils/GeometryUtils/CLLocationCoordinate2D+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98286493b7158b8914017ae84a5e8f1450", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98363975e83956376e19c58b7a3962ecaa", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98899fea99bf8a56387984a53e045a80d4", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPath+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98426943cf526afe3637d7f081ed09a366", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolygon+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987fdcb17c458d321e07610ae60bad0f30", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolyline+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bfc948f68b7604001dac0444e92984a7", "path": "Sources/GoogleMapsUtilsObjC/include/GMUCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865edd012c6840ac77311e90b30907d96", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f60c7cb79db5a2f49c689ce7d15ce6bb", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c778e7eca54392a234db04c94a51c68", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ab955624c48b5fd0c05d554586d4ccd", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989aca6221d2a485e29ce79c6398d1de2c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e3ac0514534c535a54031cbaee6299c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9b5c1f34d70840aa867095b1213fe3a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ed970720ffc73c1126b4fe1c2f4af3b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c7b7c34a25ba8d2699945f523c8bf00", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c314f5429f44ca684fa4fc5231a2e26", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ece928a0c7288c6460ef6cc272ec939b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98844d7c7c24b160b7d35a426abaf95bee", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c03663d67867a53d3a02b783bc62576e", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986273d7e1288d5703f3009346fe994e39", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b839bde0e6c696ee51a169aa2c836606", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98049441b36b2809edfa350bd83936c367", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9841adeece6364f378fc626286991f0698", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9839da0ac4157d436f938de1b52f0d595d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9881162757ddde9002ed131d7406ca9e23", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98031da49db949a058a2a72c489994dec4", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9889bf2307e0545481c603260581ca7beb", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a110dee331143df0071bebb305e5d2d0", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f173da6314617383d7c7205f8d5520d3", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d93a4a0636b3e2ab82aa13ddc76f0c32", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9811ab1fb95030d4a6d80af6254954e0f8", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988216d8d4a6f92db7c6e778da0563572b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985afe67f4c855bb7c57c7e3b136bd0649", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ae388eaa5bcb0a4d63dd8dd5537be348", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4e667ba0592090bb5e353cdf5639b57", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9894363ed0076b8876e8040df894f48c45", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b731431bdc800aa6495197636a5bd06b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987c34c4b29c22ccd00740f715076a0274", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d42c21c78ea349c0fd36a96bf2d51ec", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984a2d3d68658af96b2e50d86776361aa4", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98606591f68399d864ba8299df3e4dd793", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9835a1d8c215a73e1e4c1a2af9149cd17d", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dbd25c07054511f0fb655f09618a4aab", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838accc83bb20a6955b8af7f759cb6cb7", "path": "Sources/GoogleMapsUtilsObjC/include/GMUMarkerClustering.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e0ee4efebfda9159d296d0c19888b2b1", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9870add7361e22e0c6a4990b963bd706a8", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b881cf09b6111b863e03087eab0b8c02", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98811cc642b19327f4fbfc402154eff981", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987b9b41fc9fa8b5c3ce37359f18cffb75", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98695bfdee26f1d24d8397e2c1cdb676b1", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c3c32753bb2bd2dc6a2b196b784b5560", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9822d0d8a2816d353522ed74bdbe1b29b8", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9890a23cc46568fb7384fc7d45299e6ed3", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fc108c982b12e05c9122af2465ac4992", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c5284f8e1f2e97dbe6fd2f41cc1f6ea6", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cdb28fd7fedae2084bc82ac0e75a92ff", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d2f066d5690fe791c7a6fc329a5469f4", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987afcd3df46239cf1991120fe5bc9529e", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c444e1cc5fbf0ea65ac5bfe45c948ba", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9873caf447459992a33c954d2a84ea860b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98704d24173b6e9d3b101f67af16d26e47", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f1f8cdff15a477785608473a3ede0b1", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888f502f9a117985c4575bc397bbb4a42", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986220a8d48c73746fb25321453aa487ce", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9891dc679f161b103227ce7259890640ff", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb7db0f7f7d4db4855896a53fdf9c8c7", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98959ee0542b643ec5c3ecc6dfcdd89132", "path": "Sources/GoogleMapsUtilsObjC/include/GoogleMapsUtils-Bridging-Header.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982c339ba569982cf2d26c87c10c68c684", "path": "Sources/GoogleMapsUtilsObjC/include/GQTBounds.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9803445fe783843c6f01fcad68484b9edc", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98024ff2a558c93715ceeda438202e81df", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9873e323da690271c89cb9b3d22ba7b28c", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980aef5644171d3beb29709a04a94c0729", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f85c55314587387793bf69b2e9969af", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e9e6b0758527582247ad69c4a170846", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98df46362ccb1c7b18b6efb19ddcb99157", "path": "Sources/GoogleMapsUtils/Heatmap/HeatmapInterpolationPoints.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9824f218d1b359bb039c16f26888033d2d", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/LatLngRadians.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a17ff95599feeb10f163c4adc4421824", "path": "Sources/GoogleMapsUtils/GeometryUtils/MapPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985f1bbea0de3d766a5cf5005b9f444a66", "path": "Sources/GoogleMapsUtils/GeometryUtils/Math.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987e9dc55bd027543f23142af0202375b8", "path": "Sources/GoogleMapsUtils/Helper/MockMapView.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b7f2efb5fe8f73fcf1d072da5722d5bc", "path": "Google-Maps-iOS-Utils.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9877ddb2516b6590cf9e12025918cdea7e", "path": "Google-Maps-iOS-Utils-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989565dab9c0f4168d5edfd8e7d6b195a7", "path": "Google-Maps-iOS-Utils-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c68e3eddfd747b21655b060b8599dc9a", "path": "Google-Maps-iOS-Utils-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5f1c40eff793e66ed3d6a13ffba1b0b", "path": "Google-Maps-iOS-Utils-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980e7a18be1578aa94c0e0a123163bf270", "path": "Google-Maps-iOS-Utils.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98be0f5a6c94eb9364ab9d70c139aeee16", "path": "Google-Maps-iOS-Utils.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ad0d06d68849c5fe29ab9ea8ec32bf4e", "name": "Support Files", "path": "../Target Support Files/Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98091b845f82199df7b1285f1a53c19a4b", "name": "Google-Maps-iOS-Utils", "path": "Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a7caca8cbcb52825b05fbbc9a7554a6a", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ef36733f7cd7cf0d995deaef0109bf7", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ac58e4cce3cfe0ef53b34664f8506ee9", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9800e7a87f244c722f350f1e304bb429a6", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e7539d598f54ef8805fc7d215fb71de6", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98163716f3f60eaa9bd00b3b3ceaf108f7", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9878d952632582c30f19029d95be361b07", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dcd970908f8639242d03d8c12483a959", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ad96e1878f9d85fc44ba00455fcd08b4", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c4a62620636e4ec264402816b956dd7", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d921a7e01bc5cfb770f32d64a5ee2f34", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTCompressionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987544b8049dcbcefa14308d6890f82c44", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTCompressionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983eb88b1ea45e34927673430f1ba27ce2", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTNanopbHelpers.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9892b1d4840b19d4e32be20f4d25094207", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTNanopbHelpers.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989cbd7bf2878b6eee20871e0647e85475", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c0a22b5a6524c4958d2e4d13ed7c342", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804034e6956900cfbe03c2aafaed8d4e5", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploadOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9885a30bd2d4421670d74ff5bba8293e9f", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploadOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b53b82832ff1522b3edf13ba07ed8d38", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTURLSessionDataResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e48486c9e717b60b141904c1df52229b", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTURLSessionDataResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9824e9e36a6711a4f42b7f7f7d0d3ad719", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORAssert.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d56c11e1a018109c064b719323141082", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORAssert.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b74f7ce56985e1c42eb816d63f8b019d", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORClock.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9881742d298c5f075242f544073a324ebd", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORClock.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983cd6268e431c1fdadd8ecc6d4cc9f5c9", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORConsoleLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98199987c6b4675dc028c463c89942cff3", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORConsoleLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c7a6486dfc7a4a61088dce3ee6712957", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORDirectorySizeTracker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe3309780fa50de3e85a41a77baf1f29", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORDirectorySizeTracker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3646afd6f65771b0717092da8c0351a", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREndpoints.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e6bd7b63f9b4ea84bfc754482c75cf68", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREndpoints.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bca3783809da8967bf7f0ac0c38173bc", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREndpoints_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98745998fd1ded694b8712869c272abc2e", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREvent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f26d8d1d46a7b4cd9489eecd783ee7c6", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREvent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98710c132d79ede03f93dd56a3b0f81b89", "path": "GoogleDataTransport/GDTCCTLibrary/Public/GDTCOREvent+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981df7cc058982ad693d84dc1e3cc16bd3", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d12744b596e418a4c4b839133a41a697", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCOREvent+GDTMetricsSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cd250bdcceb1f9492b327c7f2c6e40f2", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTMetricsSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985902874431d7da26b80045fa8310b471", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREvent_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98634add95f2f7dc0cbfe3ac444895a34f", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventDataObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef8198239bf466dbbabe3e3c72bf8ca5", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCOREventDropReason.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98203ecdd28d2e9719c6dd5026b9c328e2", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98becb406a142c6913a42beaf3eee5e4ef", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d34074de3c2d38d411e4a3f51c2c5f50", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9815de92523a4a03424b5990cf3f7bae0c", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage+Promises.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca78fded5a6f89a1ece0dac19b037d06", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage+Promises.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9871918eeabe1795390890672039580e1c", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORLifecycle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac56f94d3cf6d2a5bb02c644093bda6a", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLifecycle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980799d8e7fd9e5efa54dca46b9beeb31c", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORLogSourceMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9888fe0a5ad73da4a5ca108bfaed82051e", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLogSourceMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d22aecde212d2bdc1805bbea346b634", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9829173d98ed42f6ad448e8f5b49a4bf29", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98998d6c25b76be2f27673bcdde7d34723", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCORMetrics+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a9ef5d37d0e83f315e33d30209f53a8b", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCORMetrics+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980d895683c1970f680bc2faea27efd91f", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9819a069b44c4c6c701e978be2cf0dbb78", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98df2dfa86f8adebc9736c2be3c06f4655", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORMetricsControllerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987b26ad52ba31b167173e3df8e8a14dd7", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bd36fc5fd384ba9b1482f180c7edcd7a", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9873e86b48245b616b6c30d9bccf5f26d9", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORPlatform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987a62caae3cf38feea74a48264b25d267", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORPlatform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea83f1c7359adc37ec6a0895a4388f3a", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORProductData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983fa4e83cb116423c2f3aa3f1b65220b3", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORProductData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8ba627c741c47de8b259c00ceb8f1e3", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORReachability.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b65844f5cdc6f43dd8bd25d2edcaee7d", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORReachability.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab54ff084bae05b7dcfa4ab45bf44d23", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORReachability_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dea4c3939bae4fc6c859a4dd572ef2cc", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORRegistrar.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c098abd08ea32a66ad0d71eda71ff3fd", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORRegistrar.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987c9a897420388e656a35e7bf8de1746c", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORRegistrar_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e9b06e68afe263f25aad59b41aec5d3", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageEventSelector.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981306c6fa3f6dcde302ad543e1a9b8ed1", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageEventSelector.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98df62cf02b76f6b5ce3330b2ec9145888", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORStorageMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca4ee52d64802af058854c49305becd2", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98173b764cf773afcb1135918da506e04c", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6d4d3b45d11a19b24d0fcd2384f9389", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageSizeBytes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f2872cdbfd674ff694058796922e108c", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTargets.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894e9f5015f0a788e83172bc264f20a61", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d3ac93739b45eaa691e177a930a086b", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b604753c42e5650e6e55cbec3445c186", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98211b323ccbd1b7772c8c91f9ea646f96", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTransport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9826dd7ade2fca5b6defffdd54195b5850", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980e8bb4021a8e85d87ddc826146c9a916", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransport_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d688cfa519d048121f772ed54c2b6746", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadBatch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b5164030d710cef4a2edd9978e68553", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadBatch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981fd81030e60e58339ad7438bf7a306e7", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadCoordinator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98adfa883013a998f0bfc19abfbb17d091", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadCoordinator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98210182087a5e2a7c4f327a00f877ee76", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e733a733bc5e88a86536c2e8397576d", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GoogleDataTransport.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e986b96506cc57400a5456733da5bb6aec8", "path": "GoogleDataTransport/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987280ff51fc1ba4b7ae60742c9e618340", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9897e70946db67b8c31eea2e9a02c2acc5", "path": "GoogleDataTransport.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a15195e5e17109879fec1d40308be6b7", "path": "GoogleDataTransport-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98eb6fdbb14430cfaed634172d936ca17d", "path": "GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851b802206fb0bfd1408b70ea09b1d562", "path": "GoogleDataTransport-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9870b57520237b3c39d060b47105459672", "path": "GoogleDataTransport.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9891261d0455e25a7d216968e9b5ce3403", "path": "GoogleDataTransport.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e64dc4f22a53608315bd503f1ae37404", "path": "ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a85b6a98fe2001f9acdf87fab8bffc74", "name": "Support Files", "path": "../Target Support Files/GoogleDataTransport", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e95ce5633de4d5d313b3ad3600d7aa1", "name": "GoogleDataTransport", "path": "GoogleDataTransport", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed4e88ac5391d5f4b2ce0d64e5d17386", "path": "Base/Sources/GMSEmpty.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98dd80920c5fd59a4d500f5fe7ea50f667", "path": "Base/Frameworks/GoogleMapsBase.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985c75c9752a14e982d86edb5f2cf30baa", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98565c534d09bc68c0e1c7f18f625068b7", "name": "Base", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ab462f60f2fd4168a546a844f556a6c", "path": "Maps/Sources/GMSEmpty.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98598b22927291b706d03521b0b4018eef", "path": "Maps/Frameworks/GoogleMaps.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e988d86ad8f813c05d844250a625b5cbc37", "path": "Maps/Frameworks/GoogleMapsCore.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981e10e829cf8692fa458ca7f6b5099330", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "wrapper.plug-in", "guid": "bfdfe7dc352907fc980b868725387e98e52666c9dd0247f6945b3c0e72c47967", "path": "Maps/Resources/GoogleMapsResources/GoogleMaps.bundle", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fa343b871f703c6389c9d6e219e98a73", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856deb91bedd8b00db24112f854569457", "name": "Maps", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9805ed96771934caf21e971d3f5e06a732", "path": "GoogleMaps-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d1cac31430935dbe6f646b7ff8c600fb", "path": "GoogleMaps.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9833b9d3d22f6427a20b4cb069ff955856", "path": "GoogleMaps.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b1764ff2433decf853e27a46c8090a97", "path": "ResourceBundle-GoogleMapsResources-GoogleMaps-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980b707e7127380f6a00b59a9f037da986", "name": "Support Files", "path": "../Target Support Files/GoogleMaps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a67e8a63a390467180ea64eb1fd09f0", "name": "GoogleMaps", "path": "GoogleMaps", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98253bf8a36280fcd42ab3fb804d5f41e9", "path": "GoogleSignIn/Sources/GIDAppCheck/UI/GIDActivityIndicatorViewController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9822b97696f328c572b1d9bebce6b1cc29", "path": "GoogleSignIn/Sources/GIDAppCheck/UI/GIDActivityIndicatorViewController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fdb43723d1e7b91abcdab869cb34e4d7", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/GIDAppCheck.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a1062767c6d7e36d6819afc37f0937f", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/GIDAppCheck.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988099a2440b83eeba7d12c7db0cb4c815", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDAppCheckError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838d84991e73403d64f553fe6cb23540e", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/Fake/GIDAppCheckProviderFake.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f8fc6958736eb0e82136f5f0d99907bb", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/Fake/GIDAppCheckProviderFake.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98557b1870257f6467fcdf54357ee4e363", "path": "GoogleSignIn/Sources/GIDAuthentication.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982dab61362d83d21c58f4a90bedf22318", "path": "GoogleSignIn/Sources/GIDAuthentication.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9875a9bdaa6266a1d36fa138d98f615d30", "path": "GoogleSignIn/Sources/GIDAuthStateMigration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983c58abcd585b2332bb3ee2dea181c41b", "path": "GoogleSignIn/Sources/GIDAuthStateMigration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98921c2ca1bcb35b0d014bd7ed7db98d41", "path": "GoogleSignIn/Sources/GIDCallbackQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9817418b0392aaf2d6b13fed65b0865f90", "path": "GoogleSignIn/Sources/GIDCallbackQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fcad78a26aa3992cdea0d6728a8e9ddc", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9833111515acde49f26396e971d8a03839", "path": "GoogleSignIn/Sources/GIDConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4eb9c2f09535f51cf322f0ee66feeca", "path": "GoogleSignIn/Sources/GIDEMMErrorHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98096ac6d4d40caafc87f9b92ab03a243a", "path": "GoogleSignIn/Sources/GIDEMMErrorHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a3dd43481cd7a952841bd77e987e1bf6", "path": "GoogleSignIn/Sources/GIDEMMSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9847b16385f94724c2d81058b8eeaf802d", "path": "GoogleSignIn/Sources/GIDEMMSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9815c70cefeb4f9c6ec64a798c0fbc7f8a", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDGoogleUser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fc47295e9fd3897a34c3ce39875a3525", "path": "GoogleSignIn/Sources/GIDGoogleUser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce1834567a0e32fe0f6abc62ea76b11f", "path": "GoogleSignIn/Sources/GIDGoogleUser_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819483c3014c505a8bdb973d973d9de29", "path": "GoogleSignIn/Sources/GIDMDMPasscodeCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98768b31a6e17d13bcfa3fe28bd712e854", "path": "GoogleSignIn/Sources/GIDMDMPasscodeCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d84811568472d27b6728b9887b79cb80", "path": "GoogleSignIn/Sources/GIDMDMPasscodeState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9819f1eb3f80ff5875e5c2f4fd9e2cd5de", "path": "GoogleSignIn/Sources/GIDMDMPasscodeState.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987bec0a8c6a9c606d41b77e34867ebe3c", "path": "GoogleSignIn/Sources/GIDMDMPasscodeState_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d930db82e591457b45005340eb39c5ec", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDProfileData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ef7115dd968d86636af9a8942ebfd9c9", "path": "GoogleSignIn/Sources/GIDProfileData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e88293ce9d67532ca0897a9220af177d", "path": "GoogleSignIn/Sources/GIDProfileData_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98100ce395d701df77a34bfaff208d1253", "path": "GoogleSignIn/Sources/GIDScopes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c5045b960476a747347f1b2efbf3a808", "path": "GoogleSignIn/Sources/GIDScopes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b188ce5c191c73d18a9976046c491e3", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDSignIn.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c839f26c4f5a367847f58328177cb451", "path": "GoogleSignIn/Sources/GIDSignIn.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851c3e02c9dc6c25615328b32fbf32b78", "path": "GoogleSignIn/Sources/GIDSignIn_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981e77f00777ed4d55c48569ccce652ad8", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDSignInButton.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9836315df91bcf271eeef0a150bebbf406", "path": "GoogleSignIn/Sources/GIDSignInButton.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9881d33aaca95c7261c846663009bd6cd4", "path": "GoogleSignIn/Sources/GIDSignInCallbackSchemes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980dd67a773477e995b7e9785428b1ffe1", "path": "GoogleSignIn/Sources/GIDSignInCallbackSchemes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f4e623b0d28cc4e24d79c0fed9ad6f88", "path": "GoogleSignIn/Sources/GIDSignInInternalOptions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c6879121963eedc563d1f497fb9ffa85", "path": "GoogleSignIn/Sources/GIDSignInInternalOptions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98010c44de500f93b72e7aa5ddf680f3ed", "path": "GoogleSignIn/Sources/GIDSignInPreferences.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ff4cd640567ef89dc94df418d7856cec", "path": "GoogleSignIn/Sources/GIDSignInPreferences.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983931aa3b80c04bc74dca00b678d23fdb", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDSignInResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981252b3e6641420762ea379695dc1707c", "path": "GoogleSignIn/Sources/GIDSignInResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98326a37b423cbb3e9552378cd093c8887", "path": "GoogleSignIn/Sources/GIDSignInResult_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982eab66459aa13f03526caa247e24efc0", "path": "GoogleSignIn/Sources/GIDSignInStrings.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980d8afd3948fe6a29dc523511586e9d04", "path": "GoogleSignIn/Sources/GIDSignInStrings.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98214588920163a3495e470b353585d56d", "path": "GoogleSignIn/Sources/GIDTimedLoader/GIDTimedLoader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aea727540de6d618e221c3827b1c9e20", "path": "GoogleSignIn/Sources/GIDTimedLoader/GIDTimedLoader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98debab803cb08c4d76a36c67c9c9fba21", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982d3d86ef08f49fa80377d23335b92af0", "path": "GoogleSignIn/Sources/GIDToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9ef1befaaec0b7268bd572d8665cf05", "path": "GoogleSignIn/Sources/GIDToken_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d31147685c81fa4da524ee0a4a0a9eaf", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GoogleSignIn.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988d188fdc85fffcfa00c949203908d9b5", "path": "GoogleSignIn/Sources/NSBundle+GID3PAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc72e4ca232e820cae89bdb289b94ad1", "path": "GoogleSignIn/Sources/NSBundle+GID3PAdditions.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98efacf1f3bea800665418fcb4218e3ca1", "path": "GoogleSignIn/Sources/Strings/ar.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98aea2b86da715a7e3bc6b45067a232be0", "path": "GoogleSignIn/Sources/Strings/ca.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98e9665ee10877d278f1223a0b48fbba6c", "path": "GoogleSignIn/Sources/Strings/cs.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e985f073e6e46cc285443bd5cc03026552b", "path": "GoogleSignIn/Sources/Strings/da.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98721ded5665ace2eec6ad684c87ba562f", "path": "GoogleSignIn/Sources/Strings/de.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e984874337804b964ecae3c10c3e5301fcc", "path": "GoogleSignIn/Sources/Strings/el.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e983d4f65636261b2ed8f06139c70bc48f8", "path": "GoogleSignIn/Sources/Strings/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e988d0e3e09ef007faf9c9e80fdbe7fe0d2", "path": "GoogleSignIn/Sources/Strings/en_GB.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e985999abd62c500f10359907958df7026f", "path": "GoogleSignIn/Sources/Strings/es.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98bcb837e7756f5e25252441c6ba2f3345", "path": "GoogleSignIn/Sources/Strings/es_MX.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d3c0b61d811d0b3ee5ac9157cfe2a216", "path": "GoogleSignIn/Sources/Strings/fi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d6a23f6e2db86048e10997a3a8dd652e", "path": "GoogleSignIn/Sources/Strings/fr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9824684aa9553823aaa28a07ace3c144e8", "path": "GoogleSignIn/Sources/Strings/fr_CA.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e982f63ebc36d46bf8238f57afa72179f18", "path": "GoogleSignIn/Sources/Resources/google.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e98a47f31ac63194d0c8d6eb0764d6cace1", "path": "GoogleSignIn/Sources/Resources/<EMAIL>", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e98fd9dcd4339b54ee06e81e84ed43df781", "path": "GoogleSignIn/Sources/Resources/<EMAIL>", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98dad62f884507feac01979727533ec990", "path": "GoogleSignIn/Sources/Strings/he.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98c74c679f544b591514a7e697496f7f48", "path": "GoogleSignIn/Sources/Strings/hi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98fc99f818d8fbd00ab25d64c6c4c7566d", "path": "GoogleSignIn/Sources/Strings/hr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98ed4cdb3b19e3921013f9708631042ccf", "path": "GoogleSignIn/Sources/Strings/hu.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9861038256e2eedda025d6046d82014465", "path": "GoogleSignIn/Sources/Strings/id.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a4a3364af4207005292d2e62335592b7", "path": "GoogleSignIn/Sources/Strings/it.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e989c23dc9ffa8fdb42cf73b1113c0becb2", "path": "GoogleSignIn/Sources/Strings/ja.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98dfaaf003aeac5241a6cd88a5417c8140", "path": "GoogleSignIn/Sources/Strings/ko.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98e83a016a9a6b803e74da106320f1e030", "path": "GoogleSignIn/Sources/Strings/ms.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e987e94ae47427a4c724df24d6f8e66bbb1", "path": "GoogleSignIn/Sources/Strings/nb.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d3366872da9a5e2588e307609db85632", "path": "GoogleSignIn/Sources/Strings/nl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98fcf28b24088962e59458ae9c6c2b1af4", "path": "GoogleSignIn/Sources/Strings/pl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e982276522ef6c160f9d74157bede99f539", "path": "GoogleSignIn/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e980c83ebf31fe0126e9d29be3c1b465908", "path": "GoogleSignIn/Sources/Strings/pt.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98f96707c04642c58f188c950af1ecc4ef", "path": "GoogleSignIn/Sources/Strings/pt_BR.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9899c480c79391eeea994b82d52c687942", "path": "GoogleSignIn/Sources/Strings/pt_PT.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98abb9cc36f476c68760f814c0628cb2b0", "path": "GoogleSignIn/Sources/Strings/ro.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "file", "guid": "bfdfe7dc352907fc980b868725387e98829eb2dbc948234b89e54aee08058b5c", "path": "GoogleSignIn/Sources/Resources/Roboto-Bold.ttf", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e986943d46fd972f16744f77134df422870", "path": "GoogleSignIn/Sources/Strings/ru.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98ffc728340253f4c0df02fbd94fe4376c", "path": "GoogleSignIn/Sources/Strings/sk.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e985566c7e84c80970b2d3b6cef2a132c1d", "path": "GoogleSignIn/Sources/Strings/sv.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d8a0be71a15ee70363f7ea7db4e7c81e", "path": "GoogleSignIn/Sources/Strings/th.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98412b76172d3baa1d97515cb788151b33", "path": "GoogleSignIn/Sources/Strings/tr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e986c4a23e48a3cb5ef4fd93961d7ad7432", "path": "GoogleSignIn/Sources/Strings/uk.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e988df3edb6d8b4fb11836f4f4072eca339", "path": "GoogleSignIn/Sources/Strings/vi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d6f8bfeed2d7e04a13e2473f434c9173", "path": "GoogleSignIn/Sources/Strings/zh_CN.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9810b8f59b2adbfc6532dced5adc012dfa", "path": "GoogleSignIn/Sources/Strings/zh_TW.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d997edaab7fc9fddcd8b1aa5a13297d3", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9826479483216e882174e1b6f0e34d163f", "path": "GoogleSignIn.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9827fb3fba6fe4c039dbf61aff896d21e3", "path": "GoogleSignIn-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9849235c0aa2115e80daf5c17919efdc7d", "path": "GoogleSignIn-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec3f1817e8262b298778ddc4dff1f75d", "path": "GoogleSignIn-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985384f60a24784cd59845194f1430baf4", "path": "GoogleSignIn.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c3cbf1be765b2a76fc0b5f51971d58ca", "path": "GoogleSignIn.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f28d1fed9ea6b1987dfe790238c17f81", "path": "ResourceBundle-GoogleSignIn-GoogleSignIn-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9896b3223506a444354d448e7712ef3dc1", "name": "Support Files", "path": "../Target Support Files/GoogleSignIn", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834d302da61ae2f25048c9f068f7ca4c9", "name": "GoogleSignIn", "path": "GoogleSignIn", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc3b8c75c4c46793edc485415f8c4ff9", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULAppDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca657d0582eea6fa50501087f76c878e", "path": "GoogleUtilities/AppDelegateSwizzler/GULAppDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981cb55ac03285ba39b94c9fd7030cbc98", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULAppDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9892ef6e7a7ab2e5b852ed763300806428", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULApplication.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e81533698895f0dd52f161d6ba1cf10d", "path": "GoogleUtilities/Common/GULLoggerCodes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc24cf5a474584508027ecd28a04b6c3", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULSceneDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c9a99295eff7fcfe529ee67a4d942723", "path": "GoogleUtilities/AppDelegateSwizzler/GULSceneDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a73b3b87c475d571aea4d162c3f4c70", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULSceneDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9819231d037cf8e7c30fc0c8e0f6d3cad4", "name": "AppDelegateSwizzler", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c6d4e378d399f2a63dbe5772f1da1029", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULAppEnvironmentUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98da416ca7b2f64364f66d46815ffd2e52", "path": "GoogleUtilities/Environment/GULAppEnvironmentUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986182de47dace08ae726091120cb9ed07", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c30c410829e4a49173291db550f7afda", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980d3f0c7c277e2a5b5c78607d19c7e666", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3214b09af20cacd837c367c7e5e217d", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98952807383b9748e5c6f08421e09ea6b8", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULNetworkInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98269db2c28f910a22d67c526369ee5a92", "path": "GoogleUtilities/Environment/NetworkInfo/GULNetworkInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865e1d8cb6829ba42cc3d8c5aee128177", "path": "third_party/IsAppEncrypted/Public/IsAppEncrypted.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c18f69b063f29c26a863672696faf724", "path": "third_party/IsAppEncrypted/IsAppEncrypted.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f69edb9b175aa4c5061b7d8f4d4250a3", "name": "Environment", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9876516fc09d87de216139abdebba1d39c", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ca1a76a08ef10b618af84902958319b", "path": "GoogleUtilities/Logger/GULLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9879de865eb7c0849ded1c42d84f37d2c4", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLoggerLevel.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98202254741621ca87bff0a85c8181e780", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0c1a3113764775f7cc2e84a21240838", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULMutableDictionary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980a2bb7e7a2930d6c351d52e983f568ec", "path": "GoogleUtilities/Network/GULMutableDictionary.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3be412b3ab9b4f40c934b678c77dddc", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetwork.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9837cde83227a982d2e812af35447e6f0f", "path": "GoogleUtilities/Network/GULNetwork.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a0b44f7a49bc49ca4b4c965c963c88c0", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ac551213eb5080c29cc635179a4a352", "path": "GoogleUtilities/Network/GULNetworkConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802b2911fe016a96ef34c446c485ae062", "path": "GoogleUtilities/Network/GULNetworkInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802352500155ad1a6df48419991d1c0a2", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkLoggerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc31b0c84bc8432cf67f312fdbf2d38e", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkMessageCode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f794d014e8f95ab9def66d6aa1a017c7", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkURLSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984dc0531b67df3ce9377c62e97d3fcaad", "path": "GoogleUtilities/Network/GULNetworkURLSession.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98260d2ef1c4f01c10b88ff40832248f04", "name": "Network", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6e0d56647d577ea006dca7f266314ad", "path": "GoogleUtilities/NSData+zlib/Public/GoogleUtilities/GULNSData+zlib.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ebff26aee3487f1570e9b8523014491", "path": "GoogleUtilities/NSData+zlib/GULNSData+zlib.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dc25604f580aa5e9c66b56d4ef69bb3b", "name": "NSData+zlib", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98527ccc9efc30ceddf528ab4414132640", "path": "GoogleUtilities/Privacy/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ccc27de0ff29ab2cbd88a36abc4926cd", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e07a76836da6550d9948c0a89caa9e4", "name": "Privacy", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b664aae7a6e5d4240ee74c87d77278a0", "path": "GoogleUtilities/Reachability/Public/GoogleUtilities/GULReachabilityChecker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb611a3ab567266b3dab650f91161160", "path": "GoogleUtilities/Reachability/GULReachabilityChecker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c99e90576dd77afebbeeca4f6f6361c2", "path": "GoogleUtilities/Reachability/GULReachabilityChecker+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f866e1db60ad0daf7a6e1eca6be7a31f", "path": "GoogleUtilities/Reachability/GULReachabilityMessageCode.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bf3822d66a363b9c8fce2ad158467851", "name": "Reachability", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9874dfb9d303024df1cfbecdfcd3a88499", "path": "GoogleUtilities.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fc55a9378f26d41878bc7a97c3cbd94d", "path": "GoogleUtilities-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ec02ee9bbe7835217d9e143e234b4995", "path": "GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9831541c66897e10d2f2c70ac716af5915", "path": "GoogleUtilities-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a1299aa917988dde2066b8a92f10efbd", "path": "GoogleUtilities.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983faeac33dc3e6aa7f75eacf7a5b1c7c7", "path": "GoogleUtilities.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986b19fbcf1028ed8e9be5d3b99aa38008", "path": "ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e52ec712d5d637390ae99182c223b808", "name": "Support Files", "path": "../Target Support Files/GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f68b445fcf234fc73cbb8f1ad1dd26bf", "path": "GoogleUtilities/UserDefaults/Public/GoogleUtilities/GULUserDefaults.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac70ff5b61ebf2b5ba54b5e5889b9e1f", "path": "GoogleUtilities/UserDefaults/GULUserDefaults.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98967af39e1afd50c04227d140ff4be96f", "name": "UserDefaults", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98669f4ab6f5acd6a8bf233e5cb4fbbcb7", "name": "GoogleUtilities", "path": "GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9886a4a9a9bec4c3b099289122e012b5be", "path": "GTMAppAuth/Sources/AuthSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986d3da310a55657263c409055272984ee", "path": "GTMAppAuth/Sources/AuthSessionDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986b8e3252b509b9cf912005c06856d748", "path": "GTMAppAuth/Sources/AuthSessionStore.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a2bfc5bd0bf3d8572bc1e1333a9051aa", "path": "GTMAppAuth/Sources/KeychainStore/GTMOAuth2Compatibility.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98320a3d189a74e2644364a6d99f5283dc", "path": "GTMAppAuth/Sources/KeychainStore/KeychainAttribute.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984cfa1d57ce9cb1a6410eb2ad68251135", "path": "GTMAppAuth/Sources/KeychainStore/KeychainHelper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d665377034a90114fd4e790055632fd4", "path": "GTMAppAuth/Sources/KeychainStore/KeychainStore.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9808fe492ab75d12bee35ce395f8a3f993", "path": "GTMAppAuth/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9888c48a17bfc2ca33a2a1e3505ed66362", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98781dc8f6939cf03b1a39216e19ca22ee", "path": "GTMAppAuth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980edfe6827559a987d4a7ac6f2575a822", "path": "GTMAppAuth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d3b5e7e74cead5c478dc70e9f7b0f741", "path": "GTMAppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98df3b8c5db7a4a7f8b4ba242ac9a45a06", "path": "GTMAppAuth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98938708e7bbb39796c04490e705a92f47", "path": "GTMAppAuth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9815447fa0632a60e5905fc1b46b8f9e9d", "path": "GTMAppAuth.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d1e5ce959acff945d1bb048bf9158e00", "path": "ResourceBundle-GTMAppAuth_Privacy-GTMAppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982f2e9109b101e0a4eaaecf29f4d40d1a", "name": "Support Files", "path": "../Target Support Files/GTMAppAuth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b059943083491ff62f2a6ede30e65a73", "name": "GTMAppAuth", "path": "GTMAppAuth", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e13f19e171c19c2e6547d6e3cf4d4069", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a2f69039ef0968a2e5832cc721a086b3", "path": "Sources/Core/GTMSessionFetcher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec6f72065e3c1421b0cf5c5c7acacb30", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcherLogging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b29d6ff932b5349a531f4458bd0b4378", "path": "Sources/Core/GTMSessionFetcherLogging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833836f2141e029193648528f417ef6d1", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcherService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9810bec7a6e1975dd5459c466aa700a0e4", "path": "Sources/Core/GTMSessionFetcherService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ccc414de96e2be74d3f7731adb21a6c4", "path": "Sources/Core/GTMSessionFetcherService+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a2dde1b6ce29308bb43d5e187bff7b2b", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionUploadFetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ff09ec59d348a5117fc9f13699e03edd", "path": "Sources/Core/GTMSessionUploadFetcher.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e981bf250b807cb8db7a2d9439a4fa778c2", "path": "Sources/Core/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c290499934e6313db3c97b68195d5709", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5ab3d09307071262f426a57d8daad53", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c834098312d540c7cf6154da2e15001e", "path": "Sources/Full/Public/GTMSessionFetcher/GTMGatherInputStream.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a148bb39a902962f0eb7eb321606d98d", "path": "Sources/Full/GTMGatherInputStream.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dfa3629c63acb316a43443b57be72056", "path": "Sources/Full/Public/GTMSessionFetcher/GTMMIMEDocument.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c2c48df34fafdc1fffb38e1f7ae25412", "path": "Sources/Full/GTMMIMEDocument.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f19bba1e634ac885dca8a02892f340f6", "path": "Sources/Full/Public/GTMSessionFetcher/GTMReadMonitorInputStream.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98812a7fc667d2db3a77430771b72c45d5", "path": "Sources/Full/GTMReadMonitorInputStream.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9800e5c6d37a7d87d6fb0e7ea45c9081a6", "path": "Sources/Full/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981a6d50373048ef24cc09749b73af0c4b", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cc663fa3dbc8cfe8ee71c044eab3212", "name": "Full", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984b80fc9acc4118f1636613871528fdbe", "path": "GTMSessionFetcher.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af97c19117b604d0860308336c2981a4", "path": "GTMSessionFetcher-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bf5a32832474ae6a1bfa79ecf8e4f06f", "path": "GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98340779a5d3e4f7ec641086904f3342db", "path": "GTMSessionFetcher-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988cc75553b5015dc6dbb70ca075ac73ac", "path": "GTMSessionFetcher.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ef74ad3cc973f5d4d4d61b492444f6c4", "path": "GTMSessionFetcher.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984bcc7712f5071992baec4da504294f79", "path": "ResourceBundle-GTMSessionFetcher_Core_Privacy-GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b552dcb03485627d1eaeca7a41c7696b", "path": "ResourceBundle-GTMSessionFetcher_Full_Privacy-GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d36f1a8f206b6e1236947b31f220461f", "name": "Support Files", "path": "../Target Support Files/GTMSessionFetcher", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c710f75dd7016ac50ac993497c676668", "name": "GTMSessionFetcher", "path": "GTMSessionFetcher", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988187ff79ab54c441124d178965957325", "path": "pb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989d00ccf959c72ff4c83d96fc8dbfe85c", "path": "pb_common.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b17ec9ed57bd81816627109e836f0ce7", "path": "pb_common.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9832ce034cce35b3ad420658cb9a9176eb", "path": "pb_decode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c426d655cda939cfa00f22b02ef49eea", "path": "pb_decode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9801aaa1b6405591818dfafd3db7aeef28", "path": "pb_encode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858c25f723a7a97acb19ef653a6fa299b", "path": "pb_encode.h", "sourceTree": "<group>", "type": "file"}, {"guid": "bfdfe7dc352907fc980b868725387e98a9b003a3023f517a16cffc08ccffe227", "name": "decode", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98da2e5316fb6402669b8c77ab05becf19", "name": "encode", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9807319f21ac9735a9cd7d640b4a1f5fe9", "path": "spm_resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983c659a614b207b7c8e540709721f2200", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d1d34ccde49391597fcc2cdf33c94380", "path": "nanopb.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989ff44138ef7ebfe25ac05b67e8a23040", "path": "nanopb-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a3d24aade8fc605459cf83563a395b0c", "path": "nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9822bf31d9df5de3bd605264daef025535", "path": "nanopb-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874b8e5bd1c54259b71470f548e111d36", "path": "nanopb-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c037305d44b21c08d2244afa1b0e7cc6", "path": "nanopb.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ad6d572546e91260d35014467755b575", "path": "nanopb.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988598f3310a1f368264637110b1ce90b1", "path": "ResourceBundle-nanopb_Privacy-nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98770af85a74afc0a4d2faefd4e6d310da", "name": "Support Files", "path": "../Target Support Files/nanopb", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830d4ff8f610979c1596ab3924ac76e62", "name": "nanopb", "path": "nanopb", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e9897a9c3c66d8d51fb62d72a61c35f551e", "path": "iOS_SDK/OneSignalSDK/OneSignal_XCFramework/OneSignalFramework.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ee210753eac68b414b7a066b986a55a5", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ae16ef88133892a178c5108ca056ed0", "name": "OneSignal", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e982b829a8caa0bb572b6f259c695ab4afb", "path": "iOS_SDK/OneSignalSDK/OneSignal_Core/OneSignalCore.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e5deda1b96fdff4cf062cd6c31230d6a", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985253957b173c89b1f2b6819fa9b85262", "name": "OneSignalCore", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98ad1eebe3d9e51d446e15fee2f62f6883", "path": "iOS_SDK/OneSignalSDK/OneSignal_Extension/OneSignalExtension.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a3bc7849be6851e90561cc86f2cbe3b7", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a1064dd37d044f8e236cee33a4855470", "name": "OneSignalExtension", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e980260167e571371ab41b4ce7618d4a7c4", "path": "iOS_SDK/OneSignalSDK/OneSignal_InAppMessages/OneSignalInAppMessages.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9802eca8f93cab2452dbc15831a6d470bf", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987826cf75c0f310624b63d2e9b74da3ea", "name": "OneSignalInAppMessages", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98f30ddb188bea58d984985dbf510c3017", "path": "iOS_SDK/OneSignalSDK/OneSignal_LiveActivities/OneSignalLiveActivities.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c4a2d12203982ec514c72fa84c788c12", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebf63ce35243284b1512085c26e91776", "name": "OneSignalLiveActivities", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e9818e3facc0310298a7bee1128bb23d011", "path": "iOS_SDK/OneSignalSDK/OneSignal_Location/OneSignalLocation.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984f73043f571b1119cf5aa3ba59a6c0e3", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e646c38a6c6c641d48c4113849858d0", "name": "OneSignalLocation", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e981f04ed5b06fb67f1246603de81800fd3", "path": "iOS_SDK/OneSignalSDK/OneSignal_Notifications/OneSignalNotifications.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e945b26f0b80877b25668fe28c5bd72c", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df9e8629849d7384b5a33926b53ffba4", "name": "OneSignalNotifications", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e980b811b9ab3e898b7093f5e179919917d", "path": "iOS_SDK/OneSignalSDK/OneSignal_OSCore/OneSignalOSCore.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fe80fb6e80e189eea3da6000ad44c968", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c54299740feda3d1a8a33dda64120ae", "name": "OneSignalOSCore", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e980d480f015fc902de99204c1dafd40c94", "path": "iOS_SDK/OneSignalSDK/OneSignal_Outcomes/OneSignalOutcomes.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984d724b2a491e2d8509a9a090e13bf670", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b8330ccc57a98fae1887583e02c7927d", "name": "OneSignalOutcomes", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98e0865802d7e6b9342c962e0b1f88234c", "path": "iOS_SDK/OneSignalSDK/OneSignal_User/OneSignalUser.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d480085ac9fe988c30c3bea360b84a72", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985a41aff841c46983f4b227a317837367", "name": "OneSignalUser", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98513b52ee252ba8d886d212e0da1cdb65", "path": "OneSignalXCFramework-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d6b48df710877485aeffdf03f508f4e8", "path": "OneSignalXCFramework.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98964af3a896b6b64f7d9c914110a0d7bd", "path": "OneSignalXCFramework.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9847629ceee4ffdbf4c426fa048feeaf2d", "name": "Support Files", "path": "../Target Support Files/OneSignalXCFramework", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9803f78cc62a7176df7fe96480ba6f95d3", "name": "OneSignalXCFramework", "path": "OneSignalXCFramework", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f59754a4d6e3886742b94cf41bb6bae8", "path": "Sources/FBLPromises/include/FBLPromise.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984aa81d38ea13c725b78e50cebdebff66", "path": "Sources/FBLPromises/FBLPromise.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9813906b1a16d433394acb7d0734f2097d", "path": "Sources/FBLPromises/include/FBLPromise+All.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980b7bbd1d4761504dcbb436c611ca1902", "path": "Sources/FBLPromises/FBLPromise+All.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98578be75d14c45531890fea03ff34637b", "path": "Sources/FBLPromises/include/FBLPromise+Always.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98793d100f9774e76b6d5974c4a4593c52", "path": "Sources/FBLPromises/FBLPromise+Always.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989ae85a1786e50d5def7b63f6f49abe26", "path": "Sources/FBLPromises/include/FBLPromise+Any.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980b4fea024913093343a1a7311dcd3095", "path": "Sources/FBLPromises/FBLPromise+Any.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a6b07cf5c6b168bd5079e1779d86dc8", "path": "Sources/FBLPromises/include/FBLPromise+Async.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980970bbd708492fc40105c3ffd4cbab35", "path": "Sources/FBLPromises/FBLPromise+Async.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98485e17684c47628fe95f712e2b8b45b5", "path": "Sources/FBLPromises/include/FBLPromise+Await.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98be5b50dfb8d7af7e296224031bb95d08", "path": "Sources/FBLPromises/FBLPromise+Await.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ca4bfd5ee1ffae5d1f21b1592e850bbe", "path": "Sources/FBLPromises/include/FBLPromise+Catch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820ec7938e10a153095bb431c862969b4", "path": "Sources/FBLPromises/FBLPromise+Catch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98426798eb65843f8de91940da0a99d7c3", "path": "Sources/FBLPromises/include/FBLPromise+Delay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98062f6299c0252844c4f2a8ad49f5db9c", "path": "Sources/FBLPromises/FBLPromise+Delay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897f215327d01e292465faee1fde59f46", "path": "Sources/FBLPromises/include/FBLPromise+Do.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98491b20b9f7c05e2e7157f67d927fddf7", "path": "Sources/FBLPromises/FBLPromise+Do.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e91dadf5cc839c9bf6a7e45841a7470f", "path": "Sources/FBLPromises/include/FBLPromise+Race.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ab193588e18ff0489b9acd13bfc6ee39", "path": "Sources/FBLPromises/FBLPromise+Race.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bfe3603db217003bc20f8400731bc7ed", "path": "Sources/FBLPromises/include/FBLPromise+Recover.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98761a781ef8760dba6652252e2dc35c07", "path": "Sources/FBLPromises/FBLPromise+Recover.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d28b0e52cd4db162a6dcb138ae0c29e", "path": "Sources/FBLPromises/include/FBLPromise+Reduce.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987c0cc2655b66c3c6fb7ab80bc7b07807", "path": "Sources/FBLPromises/FBLPromise+Reduce.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983cd4bc581811ae42cc5a1c23054bc4d7", "path": "Sources/FBLPromises/include/FBLPromise+Retry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb6c3c349ab5891e073da36c39422b17", "path": "Sources/FBLPromises/FBLPromise+Retry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98207222a076e11076cc2a4685ee847bc7", "path": "Sources/FBLPromises/include/FBLPromise+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a7d921677739742407e858dfc1c0c31c", "path": "Sources/FBLPromises/FBLPromise+Testing.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc3fbc2902644cfeeb971ea5029f675c", "path": "Sources/FBLPromises/include/FBLPromise+Then.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981de71247c8bb8660a09f7f6ab09351e0", "path": "Sources/FBLPromises/FBLPromise+Then.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982dafd7ecd6d5f2bdaf25667958a2a2d3", "path": "Sources/FBLPromises/include/FBLPromise+Timeout.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d4b188bb8213eaec56fab2064027767d", "path": "Sources/FBLPromises/FBLPromise+Timeout.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9880898dc8fb6de46f0bd9dea13f1507bf", "path": "Sources/FBLPromises/include/FBLPromise+Validate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3a9145830a7967f6b1918cba944c429", "path": "Sources/FBLPromises/FBLPromise+Validate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e3d272cd771b6e919f87306bfe6ae075", "path": "Sources/FBLPromises/include/FBLPromise+Wrap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f0bafbbe40b4d8c42604048999ac985d", "path": "Sources/FBLPromises/FBLPromise+Wrap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3f51fa3e052555a3b00b41870901767", "path": "Sources/FBLPromises/include/FBLPromiseError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a9d6b7d468974d96ceb1152feba5a115", "path": "Sources/FBLPromises/FBLPromiseError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c32aae221f68c4468dd8b07f282a3cab", "path": "Sources/FBLPromises/include/FBLPromisePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874ccb88cb84fd8af466c45bfb4a5015c", "path": "Sources/FBLPromises/include/FBLPromises.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98dc02b8fe8cf5821e0014119b23b13db5", "path": "Sources/FBLPromises/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9843a0b6a6ec037280b03d4ab96ffe34bb", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9872f6d0fa1417c8d256a9f53b064ad936", "path": "PromisesObjC.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b7de9e53c08b5bc776f6620bb79c9120", "path": "PromisesObjC-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980d87d84f923bbed6f058aa207d13d210", "path": "PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c6f2bbd3eae5bfd54fb4138b6eea88c", "path": "PromisesObjC-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981be09ec3af090c293ea3da796f811372", "path": "PromisesObjC.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987bc5535718ccdff782c7fba91783ee89", "path": "PromisesObjC.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b0719d2d5d29bd3c0e7e51cb9df54157", "path": "ResourceBundle-FBLPromises_Privacy-PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e610a36b4fa9aba575fe399ab7cd136a", "name": "Support Files", "path": "../Target Support Files/PromisesObjC", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856b950d89cb79655da2bac9bffc713a9", "name": "PromisesObjC", "path": "PromisesObjC", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9866008ee144e104015cbcd931b55074f3", "path": "RecaptchaEnterprise/RecaptchaInterop/placeholder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d69610e91337ecb3c6206580d44e5c5a", "path": "RecaptchaEnterprise/RecaptchaInterop/Public/RecaptchaInterop/RCAActionProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0c1db9a03f6f9ebd2c676dc7e77bfd8", "path": "RecaptchaEnterprise/RecaptchaInterop/Public/RecaptchaInterop/RCARecaptchaClientProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9899ba7c03beec58fd64d222e606122e7a", "path": "RecaptchaEnterprise/RecaptchaInterop/Public/RecaptchaInterop/RCARecaptchaProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f321bb63d2c7fb339054ca8df065aee", "path": "RecaptchaEnterprise/RecaptchaInterop/Public/RecaptchaInterop/RecaptchaInterop.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98daaa129f62ec1f3b54ad1f19d5b23149", "path": "RecaptchaInterop.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98228c10af57aa0bffc644c09b9acccaeb", "path": "RecaptchaInterop-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9872fd33d0ab3a7bdce8f7a93b7041aafb", "path": "RecaptchaInterop-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9852382b4b4b9d737a051bd077a7ea3957", "path": "RecaptchaInterop-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802bc7fa8060e73da39347e076401fa68", "path": "RecaptchaInterop-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98469c05808b48126036984e6112f56c4d", "path": "RecaptchaInterop.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9815cbaacaa642cbe4878315358108de09", "path": "RecaptchaInterop.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98582e6a56205f3b0322249b91cf070ebc", "name": "Support Files", "path": "../Target Support Files/RecaptchaInterop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984fe47278ad2a685707cde5da7b5252ab", "name": "RecaptchaInterop", "path": "RecaptchaInterop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e915be307ee6e7e464b48b593f02f172", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98e3bcba65b363228404c5172e025fd24b", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/Desktop/FLUTTER/db-eats/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/FLUTTER/db-eats/ios/Pods", "targets": ["TARGET@v11_hash=bc21b20f2a48c76cd24ced8a0d43f287", "TARGET@v11_hash=b768804074d26c448029957cfa10a219", "TARGET@v11_hash=d530f342401257b928f823c65f5a3ac6", "TARGET@v11_hash=751029f553a140f5a1650ac5a7564497", "TARGET@v11_hash=072a9bc17a0d19e1b5a74035bac5818a", "TARGET@v11_hash=5f18976385e536195181e9558ee714b4", "TARGET@v11_hash=5fc7d14aa7507cf109b3ea2a4e0dd8d7", "TARGET@v11_hash=c9f9469a06b2da40be3d8f281b6df38a", "TARGET@v11_hash=1e756359f2f8006f2f88e38bce84daf9", "TARGET@v11_hash=97fbf3ce12a6a1d67185ea6c2813495d", "TARGET@v11_hash=12bb34ce3236b655361e705b1c346b85", "TARGET@v11_hash=78b53e6e59f1d8f8202ecfac56a80893", "TARGET@v11_hash=e70c64c2b065d17e7b3eeae9ef0039d4", "TARGET@v11_hash=6e121c93806aefda1557c3520beb3cc6", "TARGET@v11_hash=9917059ae4687592976424d1e5ad5395", "TARGET@v11_hash=5002fb00438f008b97a1782c52ed5719", "TARGET@v11_hash=a4a35a4f2544fc72dceb6b93b15ab3bb", "TARGET@v11_hash=ac8f463d2ea6330a05a2d61fd55be83d", "TARGET@v11_hash=5d8d889b4f2dc6f63bb20d3769229e4a", "TARGET@v11_hash=f4421b2a0d7f5d56fb0d400b134bed2f", "TARGET@v11_hash=0e9b29bf5af5df3c52c407e348a0c850", "TARGET@v11_hash=479381e991c1815a9047bac489a647a3", "TARGET@v11_hash=5e0fe4e71e03a77a418874aff6369e17", "TARGET@v11_hash=9c98df77385560539e25ce0b84a6cf44", "TARGET@v11_hash=d3987d6d7f566ec1a4fd35d1ceae6733", "TARGET@v11_hash=f9db7b1d4a9096a3fbe6d60a46fda95e", "TARGET@v11_hash=effb8dc40ed69b062f2d71c1988c9330", "TARGET@v11_hash=570a180e8fb84a8ab2ad761c2ec7f00b", "TARGET@v11_hash=7b7bdfc0331fa708a1572049acefec7b", "TARGET@v11_hash=f5da7b3dd3545e3a06f6a311d5bcef94", "TARGET@v11_hash=46cfa3f3e854eac5cae2351cf1de09aa", "TARGET@v11_hash=af095d7ecd588d76c84417447c0b9cf2", "TARGET@v11_hash=7c7896c6fdba58aad4fc7d6f893f37a5", "TARGET@v11_hash=427bf8c0b2ff49d133bfa298dc6908dd", "TARGET@v11_hash=1d0694cb5808a8871f050a914fdf709d", "TARGET@v11_hash=03de54b69f24a5339ace99f24d97c8ed", "TARGET@v11_hash=f57d51e483957fc2e0936436b43e36fe", "TARGET@v11_hash=63b710b8dc38845c60b010f40f24a8cb", "TARGET@v11_hash=a5f033bf9c61ebe6f9a55d412c4358ff", "TARGET@v11_hash=ee99624a71a5417f0537ae0912b8f4a5", "TARGET@v11_hash=bd5a8d99d01b5611c72192fea1dba042", "TARGET@v11_hash=6d96767e2cc419fcfe70b7b27abc0b40", "TARGET@v11_hash=6fe7ee7fed286eacb7b57e3e538a2579", "TARGET@v11_hash=cbfe11a4820dbfd928ddcc5e7fdac484", "TARGET@v11_hash=cd280fb2488cb61447f1c83bdcf72319", "TARGET@v11_hash=226d50d6da25280b0972fbb5d947de9c", "TARGET@v11_hash=254845b768993a5d5b5727d9bbf0546c", "TARGET@v11_hash=4f864c002ea6620a294464f6088f1dd4", "TARGET@v11_hash=f6bc1ac8c8c278d1ef49fca034fa916a", "TARGET@v11_hash=4794dd10b98e0d9523f327c612921cea", "TARGET@v11_hash=9f695a5cda029c44ca0aa33675105f08", "TARGET@v11_hash=4eb1d6124eaaa3d89b083dd686b20a9a", "TARGET@v11_hash=6f52b445ecd01fc4e02c40fcbc22a320", "TARGET@v11_hash=3d7426f690a8672608800a9b4c4a117e", "TARGET@v11_hash=b6638fd658b92e870ebf98e5b80f0fc3", "TARGET@v11_hash=c2812f3f35a90d02f77c6ad7efb11e30", "TARGET@v11_hash=f0a712f0e344f65798dd6e00b9de2151", "TARGET@v11_hash=98f5d40bf77d5ff150dff8338fd5baed", "TARGET@v11_hash=4a075e09194caab897813bc01b94ef47", "TARGET@v11_hash=6c47549632eadc83868c64c2109bd4ef", "TARGET@v11_hash=7433b1dea2227122a1e2cc8f71e04c21", "TARGET@v11_hash=01e7e10652d311a8306f4c46ec6c9d8c", "TARGET@v11_hash=b7f610a2138ecf7acae9d7bbb5d5da89", "TARGET@v11_hash=6bb876ce1e0b9d4d8090fa09e7f9d393", "TARGET@v11_hash=d9c5d3dedc75fb0dbcd88c2cb4d0603e", "TARGET@v11_hash=97c1df3fa6091602f243f924dee03dd7", "TARGET@v11_hash=ebf4691e399deca674fa31d123165687"]}