class AddCateringRequestModel {
  bool? status;
  String? message;
  int? cateringId;
  int? statusCode;

  AddCateringRequestModel(
      {this.status, this.message, this.cateringId, this.statusCode});

  AddCateringRequestModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    cateringId = json['catering_id'];
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['catering_id'] = this.cateringId;
    data['status_code'] = this.statusCode;
    return data;
  }
}
