import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/meal_plan_new/mealplan_checkout_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CheckoutChefs extends StatefulWidget {
  final List<Map<String, dynamic>> selectedChefsWithDetails;
  final int mealPlanId;

  const CheckoutChefs({
    super.key,
    required this.selectedChefsWithDetails,
    required this.mealPlanId,
  });

  @override
  State<CheckoutChefs> createState() => _CheckoutChefsState();
}

class _CheckoutChefsState extends State<CheckoutChefs> {
  bool _isLoading = false;

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final screenWidth = MediaQuery.of(context).size.width;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;

    return BlocListener<MealplanBloc, MealPlanState>(
      listener: (context, state) {
        if (state is Step5MealPlanLoading) {
          setState(() => _isLoading = true);
        } else if (state is Step5MealPlanSuccess) {
          setState(() => _isLoading = false);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => MealplanCheckoutPage(
                selectedChefsWithDetails: widget.selectedChefsWithDetails,
                mealPlanId: widget.mealPlanId,
              ),
            ),
          );
        } else if (state is Step5MealPlanFailed) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: const Color(0xFFE11900),
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF6F3EC),
          elevation: 0,
          automaticallyImplyLeading: false,
          title: Row(
            children: [
              IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: const Color(0xFF1F2122),
                  size: size.width * 0.06,
                ),
                onPressed: () => Navigator.pop(context),
              ),
              SizedBox(width: size.width * 0.02),
              Text(
                'Meal Plan',
                style: TextStyle(
                  fontFamily: 'Inter',
                  color: const Color(0xFF1F2122),
                  fontSize: isTablet ? size.width * 0.05 : eighteen,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Color(0xFFF6F3EC),
            statusBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.light,
          ),
        ),
        body: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: size.width * 0.05,
            //  vertical: size.height * 0.015,
          ),
          children: [
            Container(
              margin: EdgeInsets.only(bottom: size.height * 0.02),
              padding: EdgeInsets.all(size.width * 0.04),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(size.width * 0.04),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Selected Chefs',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: forteen,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF1F2122),
                    ),
                  ),
                  SizedBox(height: size.height * 0.02),
                  ...widget.selectedChefsWithDetails.map((chefDetail) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        IntrinsicHeight(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CircleAvatar(
                                radius: twenty,
                                backgroundImage: NetworkImage(
                                  ServerHelper.imageUrl +
                                      (chefDetail['image'] ?? ''),
                                ),
                                backgroundColor: Colors.grey[200],
                                onBackgroundImageError:
                                    (exception, stackTrace) {},
                              ),
                              SizedBox(width: size.width * 0.03),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Expanded(
                                          child: Text(
                                            chefDetail['name'],
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: sixteen,
                                              fontWeight: FontWeight.w600,
                                              color: const Color(0xFF1F2122),
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                        SizedBox(width: size.width * 0.01),
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: size.width * 0.02,
                                            vertical: size.height * 0.005,
                                          ),
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFE1E3E6),
                                            borderRadius: BorderRadius.circular(
                                                size.width * 0.03),
                                          ),
                                          child: Text(
                                            'Day ${widget.selectedChefsWithDetails.indexOf(chefDetail) + 1}',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: twelve,
                                              fontWeight: FontWeight.w500,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: size.height * 0.008),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: twelve / 2,
                                        vertical: 0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFE1E3E6),
                                        borderRadius: BorderRadius.circular(
                                            size.width * 0.04),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Image.asset(
                                            'assets/icons/thump.png',
                                            width: twelve,
                                            height: twelve,
                                            color: const Color(0xFF7C7F81),
                                          ),
                                          SizedBox(width: size.width * 0.01),
                                          Text(
                                            chefDetail['rating'] ?? "0",
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: twenty / 2,
                                              fontWeight: FontWeight.w500,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: size.height * 0.01),
                                    Builder(
                                      builder: (context) {
                                        final tags =
                                            chefDetail['tags'] as List<String>;
                                        final firstLine =
                                            tags.take(2).join(', ');
                                        final secondLine = tags.length > 2
                                            ? tags.skip(2).join(', ')
                                            : null;
                                        return Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              firstLine,
                                              style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontSize: twelve,
                                                fontWeight: FontWeight.w400,
                                                color: const Color(0xFF414346),
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            if (secondLine != null)
                                              Text(
                                                secondLine,
                                                style: TextStyle(
                                                  fontFamily: 'Inter',
                                                  fontSize: twelve,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      const Color(0xFF414346),
                                                ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                          ],
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (widget.selectedChefsWithDetails
                                .indexOf(chefDetail) !=
                            widget.selectedChefsWithDetails.length - 1) ...[
                          SizedBox(height: size.height * 0.02),
                          Divider(height: 1, color: const Color(0xFFE1E3E6)),
                          SizedBox(height: size.height * 0.02),
                        ],
                      ],
                    );
                  }),
                ],
              ),
            ),
          ],
        ),
        bottomNavigationBar: Padding(
          padding: EdgeInsets.all(size.width * 0.04),
          child: ElevatedButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MealplanCheckoutPage(
                    selectedChefsWithDetails: widget.selectedChefsWithDetails,
                    mealPlanId: widget.mealPlanId,
                  ),
                ),
              );
            },
            // ? null
            // : () {
            //     final chefs = widget.selectedChefsWithDetails
            //         .map((chef) => {
            //               'date': chef['date'],
            //               'chef_id': chef['chef_id'],
            //             })
            //         .toList();
            //     final mealPlanData = {
            //       'meal_plan_id': widget.mealPlanId,
            //       'chefs': chefs,
            //     };
            //     context
            //         .read<MealplanBloc>()
            //         .add(Step5MealPlanEvent(mealPlanData));
            //   },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF1F2122),
              minimumSize: Size(double.infinity, size.height * 0.07),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(twentyFour + twelve),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    width: size.width * 0.06,
                    height: size.width * 0.06,
                    child: const CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    'Continue to Checkout',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: size.width * 0.04,
                      fontWeight: FontWeight.w400,
                      color: Colors.white,
                    ),
                  ),
          ),
        ),
      ),
    );
  }
}
