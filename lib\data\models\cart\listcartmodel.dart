class ListCartModel {
  bool? status;
  List<Data>? data;
  int? statusCode;

  ListCartModel({this.status, this.data, this.statusCode});

  ListCartModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      if (data != null) 'data': data!.map((v) => v.toJson()).toList(),
      'status_code': statusCode,
    };
  }
}

class Data {
  int? chefId;
  String? chefName;
  String? chefPhoto;
  String? chefEmail;
  String? chefPhone;
  String? chefProfilePhoto;
  List<String>? chefOperationDays;
  ChefOperationTime? chefOperationTime;
  int? itemsCount;
  List<Items>? items;

  Data({
    this.chefId,
    this.chefName,
    this.chefPhoto,
    this.chefEmail,
    this.chefPhone,
    this.chefProfilePhoto,
    this.chefOperationDays,
    this.chefOperationTime,
    this.itemsCount,
    this.items,
  });

  Data.fromJson(Map<String, dynamic> json) {
    chefId = json['chef_id'];
    chefName = json['chef_name'];
    chefPhoto = json['chef_photo'];
    chefEmail = json['chef_email'];
    chefPhone = json['chef_phone'];
    chefProfilePhoto = json['chef_profile_photo'];
    chefOperationDays = json['chef_operation_days'] != null
        ? List<String>.from(json['chef_operation_days'])
        : null;
    chefOperationTime = json['chef_operation_time'] != null
        ? ChefOperationTime.fromJson(json['chef_operation_time'])
        : null;
    itemsCount = json['items_count'];
    if (json['items'] != null) {
      items = List<Items>.from(json['items'].map((v) => Items.fromJson(v)));
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'chef_id': chefId,
      'chef_name': chefName,
      'chef_photo': chefPhoto,
      'chef_email': chefEmail,
      'chef_phone': chefPhone,
      'chef_profile_photo': chefProfilePhoto,
      'chef_operation_days': chefOperationDays,
      'chef_operation_time': chefOperationTime?.toJson(),
      'items_count': itemsCount,
      if (items != null) 'items': items!.map((v) => v.toJson()).toList(),
    };
  }
}

class ChefOperationTime {
  int? id;
  String? startTime;
  String? endTime;

  ChefOperationTime({this.id, this.startTime, this.endTime});

  ChefOperationTime.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'start_time': startTime,
      'end_time': endTime,
    };
  }
}

class Items {
  int? cartItemId;
  int? dishId;
  String? dishName;
  String? dishPhoto;
  int? quantity;
  int? price;
  String? notes;

  Items({
    this.cartItemId,
    this.dishId,
    this.dishName,
    this.dishPhoto,
    this.quantity,
    this.price,
    this.notes,
  });

  Items.fromJson(Map<String, dynamic> json) {
    cartItemId = json['cart_item_id'];
    dishId = json['dish_id'];
    dishName = json['dish_name'];
    dishPhoto = json['dish_photo'];
    quantity = json['quantity'];
    price = json['price'];
    notes = json['notes'];
  }

  Map<String, dynamic> toJson() {
    return {
      'cart_item_id': cartItemId,
      'dish_id': dishId,
      'dish_name': dishName,
      'dish_photo': dishPhoto,
      'quantity': quantity,
      'price': price,
      'notes': notes,
    };
  }
}
