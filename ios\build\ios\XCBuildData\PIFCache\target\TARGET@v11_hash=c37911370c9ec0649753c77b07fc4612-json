{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981ba31bfbc82cc0697974d45fcff163aa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f4f9eda4ce661889e248dcf8a4e00e01", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98be4a26a834ccd6c4970347a96c3caf15", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e608f00cd54cdd5b716105e4438586", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98be4a26a834ccd6c4970347a96c3caf15", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e898cb3ee8970e69e080a7fd4d5232c9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9840fe700dfb334077afd9e5df782d476b", "guid": "bfdfe7dc352907fc980b868725387e98eb7eb9fd1df96cbe8e7002e3f87bf96c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98942bfa5b7989df325c7277798dc392cd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982e26fc23672a1d4545889c2d315dab61", "guid": "bfdfe7dc352907fc980b868725387e98f89e84374ac24e95bd32f7569d437c0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d46695b03a1b0fa7be043ad6c8c3754", "guid": "bfdfe7dc352907fc980b868725387e98fd50da4c267d5d777b67e84019dc92b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824fc624c705d20579a22825ceb10b9df", "guid": "bfdfe7dc352907fc980b868725387e98e2260773e72883f721beff4b61c83347"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c41477b602031daa984aeddc100db068", "guid": "bfdfe7dc352907fc980b868725387e981770213f2c6f83d3eb9c70f42a8f1549"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abab9b169ee393d37bab23fb2ee932dd", "guid": "bfdfe7dc352907fc980b868725387e98bd93e77f2526e30ec9b46ee6c6a08cec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bf6c989e10c98bfd5ee7b0dac618acc", "guid": "bfdfe7dc352907fc980b868725387e984f3f35580c55a4cf26c2c72cded74258"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac962b5d266521efc547dbb2fdfda4d0", "guid": "bfdfe7dc352907fc980b868725387e98b1210c5cd482802d2e983bb3ad2a0997"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989365e4c6334998c59234bf87e8eb7ceb", "guid": "bfdfe7dc352907fc980b868725387e98ef866eece8a7c957dbd685a7219d3c0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a90e20fccd5391732b268d48520b4310", "guid": "bfdfe7dc352907fc980b868725387e989fcf2ff75aa60c0d81b5aeb744cdf0e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842b66097e329aeefde4e0e31fccc5544", "guid": "bfdfe7dc352907fc980b868725387e98967eaeb9ebe3c1936d307a1a2730a059"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864984a842d9f7223b03368c23813d428", "guid": "bfdfe7dc352907fc980b868725387e98308c91dbac3eae7fa84674407f3da431"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4742968c41d7dbb6bbe8ee1f14ab15b", "guid": "bfdfe7dc352907fc980b868725387e983af483252785847ba52e1c29e3db2813"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cb53f6ab9ad55fc93226724cf6340cf", "guid": "bfdfe7dc352907fc980b868725387e98148e342b94b07553d6299e6a846f3659"}], "guid": "bfdfe7dc352907fc980b868725387e988bb746cc5fa67f9260b024ab2575770a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e984b547fdf5ce3e6fc48f5cb5553a98c10"}], "guid": "bfdfe7dc352907fc980b868725387e98dc3bd7a9bcd0e7d5936201c3551953dd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9814d615cfc9c3bf1853ce6dbd945ce442", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98ae1bb9944cffb539e77dc542edbdbc34", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}