{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d53086f9bc9e27c4384487d48ecaa65", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dcb993285b21686fbd746eb304ab6d2b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eaec5e5c8dd01159403a6cd3b0a1dd0d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98def607a5981d65b0ba4e1e333fab37f3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eaec5e5c8dd01159403a6cd3b0a1dd0d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982432ddf7bb18dd6d3e1187d64e979b49", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98985a6b8243172593c9569b494428b6b5", "guid": "bfdfe7dc352907fc980b868725387e986ddaea016d2b4831d8625f3672add00b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e19605d643d1d2a03b7414aeeb424e0d", "guid": "bfdfe7dc352907fc980b868725387e9819a3fd6412d1675fbe0dd6cfaf190e7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2b2c71ca617955b2d9017cc27423b37", "guid": "bfdfe7dc352907fc980b868725387e98060004ec17dcc6845bb03d57c1ece982"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df885793686fc1075301decb2b396b1a", "guid": "bfdfe7dc352907fc980b868725387e985e43649be9f9b14db23d7371fc7e5060", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed1bcffec308bbe782711fceae31c475", "guid": "bfdfe7dc352907fc980b868725387e983b1eba8e9f4967ae6c598ee7f5ce765a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f48a175c1ee811cbeaf0ca0e42276912", "guid": "bfdfe7dc352907fc980b868725387e98e797c71ae603e24e61a273362cac6c2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c563db6b1d2cb2caae13be72fbc50e1d", "guid": "bfdfe7dc352907fc980b868725387e9818db2f61479c08960301cd04c8feeecc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eb7750d3b02f3a227e3b293e6f3f589", "guid": "bfdfe7dc352907fc980b868725387e988fae60f07d50d001dee22543a9a6fac9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f0a057d7b7c405ae60d0afd92ff309e", "guid": "bfdfe7dc352907fc980b868725387e98131cd52e1badca77f9907d6366e6e8fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ef9baea0f560c4fc61b658d3f8d02e5", "guid": "bfdfe7dc352907fc980b868725387e98490608104d787b3d11d7290e989e2218", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5092118f4bef7838d466583334ee244", "guid": "bfdfe7dc352907fc980b868725387e98f26850bfdb4e6fb809155428df7336a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea0dfa6e9dbd7228563290440d6c48f6", "guid": "bfdfe7dc352907fc980b868725387e980d8ebe78cbe700296318438306d52166", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c029d754ba2c18ec8a93b096e8bb0e4c", "guid": "bfdfe7dc352907fc980b868725387e98efbb9c19b911598fc9933234997972fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982513dccffdffd006eac607f5b0826608", "guid": "bfdfe7dc352907fc980b868725387e9844fe3689e7abf9a606bd847f1ec75787", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf19a29c94a71aea41cc8c1d3d4a720b", "guid": "bfdfe7dc352907fc980b868725387e988e927eca52be98eb71e6b066b128e07f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832eecbe7c503961daca0c03f7a51bc53", "guid": "bfdfe7dc352907fc980b868725387e98f20234a73aadc4dcc960af38c74a1e64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983763c4d2768e47fdc8a18d75f2f10e66", "guid": "bfdfe7dc352907fc980b868725387e987a018f4e76c331da796d9ec9ef47d3be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a2a0e77afc34e248ae3ff8b708f44ba", "guid": "bfdfe7dc352907fc980b868725387e98a61543e5e98ede6cbd875019786d2c4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848e34c4fd9bbcce878e41eaad195db07", "guid": "bfdfe7dc352907fc980b868725387e98064cf263698eb128168b5720852a0abc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837754b17a3f149c7bf5de6e77ff56b40", "guid": "bfdfe7dc352907fc980b868725387e989bd9b823aa09b07ed6837373ed66bfec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e5c223c5bcb1bb57d73b3941bd50149", "guid": "bfdfe7dc352907fc980b868725387e98afd70fc4e540ea3ec363476590685660"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e992c24651a1dec2f4692b1fced7a9f1", "guid": "bfdfe7dc352907fc980b868725387e9840a34746d5ffe4ed4b5e318ab35ad2eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcf636d7209eb8aecef961c5e965a177", "guid": "bfdfe7dc352907fc980b868725387e985ea66aa1b4f5483618fe3b8034470bb8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb58fd8e476026f7e68552c853dd901f", "guid": "bfdfe7dc352907fc980b868725387e9803863dce225f96f0aab6c994e88f8684"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a699d1401023d3754071c4cc285fbdb5", "guid": "bfdfe7dc352907fc980b868725387e988377f76df444b0396cb4751c107c9c0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0323f2ab40209fe14672b13f8c8e9c9", "guid": "bfdfe7dc352907fc980b868725387e984fba66e60340d22a728e3a2012ae4329"}], "guid": "bfdfe7dc352907fc980b868725387e98e21bafce18dfd1d476714eb642ef3930", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9858f75aadaa8e62a709333d81fd43ced6", "guid": "bfdfe7dc352907fc980b868725387e98f21ec171e890bcb71e336d5091ce2e4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982074b2166667d192f0fc1e8bd66da50c", "guid": "bfdfe7dc352907fc980b868725387e98f4917cdd84da32539a572880a245d6eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98583c82365a0a48c9cae385a0a11050c8", "guid": "bfdfe7dc352907fc980b868725387e98b0ed5ee9b9bcf55ba08aed77fad147ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887af7868c042a365c229c972b8844713", "guid": "bfdfe7dc352907fc980b868725387e980a7c1aca2c10177c7d3b7ef6866627ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817cef0bc42178adc6e8011b5d12bd422", "guid": "bfdfe7dc352907fc980b868725387e982ed14e76030b425f8b2bc5f4fa10b050"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800819c8faaa9d789cff471d3494b87ad", "guid": "bfdfe7dc352907fc980b868725387e9842748f37db2eca93a365eb8e1b119cf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985365d183a43975c08587f5a372af8462", "guid": "bfdfe7dc352907fc980b868725387e98d089e6e73f126bd5a6aaa76d6d82cd1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af039e21294b36f99587e1232a1bcfd5", "guid": "bfdfe7dc352907fc980b868725387e9874442032a79e76c4ec7f07364c0945df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825f5f481790d13255815592fcace18c6", "guid": "bfdfe7dc352907fc980b868725387e9856382f6ec09c6406961e7e645eaa47f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98211c3fd3a34fb6cbfdde8deda599db44", "guid": "bfdfe7dc352907fc980b868725387e984899dc715516f7fe445ef56d9d84fb13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c64591d5d7750dd174ee6658a3e680a", "guid": "bfdfe7dc352907fc980b868725387e9895de2979825828442762e25d31c1b564"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8ebfef32e154a3c2c8983724ac9f00c", "guid": "bfdfe7dc352907fc980b868725387e980a46baffe3afc730a436510272d2755c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a00eeb05dd72445f824c2f5ca732d1c", "guid": "bfdfe7dc352907fc980b868725387e98582b461305077b7e30975d7dc5656000"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa3a89b9937327c2a83136e0a185f480", "guid": "bfdfe7dc352907fc980b868725387e982202c7c1082961bd466d9882b688a530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de3a1dfbb72c3a862d774fd63e86b35f", "guid": "bfdfe7dc352907fc980b868725387e9866da4d223c82dbd81d27b1f46834b1bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5df668a9c3daa0a13f08377cf53d95f", "guid": "bfdfe7dc352907fc980b868725387e983ca7da4e1bea4ebb12079592fcf18e30"}], "guid": "bfdfe7dc352907fc980b868725387e9889e8d7f70d016a60f75770798e44b6e0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9841a8b9a1fff7fbe91a549f6c3d6cc903"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e98100fd06b4202fc282872f0bd50223b1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e28c510bcdc3834514b09ab79a067e08", "guid": "bfdfe7dc352907fc980b868725387e98a12fca8140e36df4992645f35c32b743"}], "guid": "bfdfe7dc352907fc980b868725387e9877750943cc66e777d443cce81334cec3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982246b20f36523a8abd101a18db531f54", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e984c7b1936c2c1259a3ff89299524dbbbe", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}