// Main response model
class RecentPopularSearchModel {
  bool? status;
  int? statusCode;
  String? message;
  RecentPopularSearchModelData? data;

  RecentPopularSearchModel({
    this.status,
    this.statusCode,
    this.message,
    this.data,
  });

  RecentPopularSearchModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    statusCode = json['status_code'];
    message = json['message'];
    data = json['data'] != null
        ? RecentPopularSearchModelData.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['status_code'] = statusCode;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

// Data container model
class RecentPopularSearchModelData {
  List<Data>? data;
  int? page;
  int? limit;
  int? totalLength;
  List<PopularSearches>? popularSearches;

  RecentPopularSearchModelData({
    this.data,
    this.page,
    this.limit,
    this.totalLength,
    this.popularSearches,
  });

  RecentPopularSearchModelData.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
    page = json['page'];
    limit = json['limit'];
    totalLength = json['totalLength'];
    if (json['popular_searches'] != null) {
      popularSearches = <PopularSearches>[];
      json['popular_searches'].forEach((v) {
        popularSearches!.add(PopularSearches.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['page'] = page;
    data['limit'] = limit;
    data['totalLength'] = totalLength;
    if (popularSearches != null) {
      data['popular_searches'] =
          popularSearches!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

// Individual search data model
class Data {
  int? id;
  String? searchQuery;

  Data({
    this.id,
    this.searchQuery,
  });

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    searchQuery = json['search_query'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['search_query'] = searchQuery;
    return data;
  }
}

// Popular searches model
class PopularSearches {
  String? searchQuery;
  String? searchCount;

  PopularSearches({
    this.searchQuery,
    this.searchCount,
  });

  PopularSearches.fromJson(Map<String, dynamic> json) {
    searchQuery = json['search_query'];
    searchCount = json['search_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['search_query'] = searchQuery;
    data['search_count'] = searchCount;
    return data;
  }
}
