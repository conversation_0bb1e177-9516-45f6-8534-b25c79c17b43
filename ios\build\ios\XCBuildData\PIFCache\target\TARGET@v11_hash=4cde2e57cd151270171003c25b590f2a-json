{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a8f04394dc34c7f555b2179f0e20c0d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/onesignal_flutter/onesignal_flutter-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/onesignal_flutter/onesignal_flutter-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/onesignal_flutter/onesignal_flutter.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "onesignal_flutter", "PRODUCT_NAME": "onesignal_flutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8fd614a39305a6e957398af90ebfa83", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9891ca37479791ddefd3a6619b445f9596", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/onesignal_flutter/onesignal_flutter-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/onesignal_flutter/onesignal_flutter-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/onesignal_flutter/onesignal_flutter.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "onesignal_flutter", "PRODUCT_NAME": "onesignal_flutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989520d76d660f2a422c2367458a4f19da", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9891ca37479791ddefd3a6619b445f9596", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/onesignal_flutter/onesignal_flutter-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/onesignal_flutter/onesignal_flutter-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/onesignal_flutter/onesignal_flutter.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "onesignal_flutter", "PRODUCT_NAME": "onesignal_flutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985a8b218f2279d0e46904e5bd0c7b677f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981df2fa726ae84c1fa3619de6ba79fe2e", "guid": "bfdfe7dc352907fc980b868725387e98d2a0979de9b21f4d4f26b6b880003463", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b11fe1ed29dfa0ee59313fc473594b3", "guid": "bfdfe7dc352907fc980b868725387e988d7fda078a2edbf0993ccc7a65aa36bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc23d62244b62657eb77f5fa68511eb", "guid": "bfdfe7dc352907fc980b868725387e983d6f83bbcc952ca01b90012c40df9b5a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987705d89ed94b59bbcc2d5dac85118f9c", "guid": "bfdfe7dc352907fc980b868725387e988535cd4af320e5c9c0d672fc1099476e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867c90782f3f60f9fe993020d7a0a1a71", "guid": "bfdfe7dc352907fc980b868725387e981411cc9383e92b783171be108b08c624", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98869eced86043c48daca0285789131ae0", "guid": "bfdfe7dc352907fc980b868725387e98c3e0c070b43bd327131793ba785ddf56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b136c79e14ce2861203f64fbdeca5d8", "guid": "bfdfe7dc352907fc980b868725387e981f3f5f3d18af363165d7b5abccfa47cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d804094f5a2e83fddfb0c6ffbf155b0c", "guid": "bfdfe7dc352907fc980b868725387e986f2646ed2bafbda71daf924492b8c2dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afa8e3920ec28cde671f8de33c706f03", "guid": "bfdfe7dc352907fc980b868725387e98a6e9e5ce47a3c7cc901b0751a0181fa2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98558d41365fbfa96d01e8c2324c948a09", "guid": "bfdfe7dc352907fc980b868725387e98400403808eccc3452d2531e7363d8aad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b854045987670eb1c626bc6b58470278", "guid": "bfdfe7dc352907fc980b868725387e98963b6fe560180b7904ffbc3f8d08bbe3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d1ab47feb38e0c5732edef94818c80e5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f0bbc5e7f75cb39cd8f5ca8d3d92ade9", "guid": "bfdfe7dc352907fc980b868725387e982259d3a7b8f5a9bd3365066f78b3849c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e08b6e550272234187171444f53d05e", "guid": "bfdfe7dc352907fc980b868725387e9851ac74710acd1d3caca87c11cd05e1bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988da602c2bd85ad594204bf06e2d240ab", "guid": "bfdfe7dc352907fc980b868725387e98ebc2b0a83a8ed75af656423932d0091a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0a2b02212352f76dc7eb7479eb59d29", "guid": "bfdfe7dc352907fc980b868725387e98bf59b4fcdf49dbe47da7d67e6d356348"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98583ce2dc47efa83d2dbaf9a6792b4b8a", "guid": "bfdfe7dc352907fc980b868725387e9891dbceb2892e398e208dc0168b6e5c0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98775fea151e3d4a4f44f42dff7a8e79bb", "guid": "bfdfe7dc352907fc980b868725387e98af633626998289a9f87d44f67430d5b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988529209a93d905c31490b34859e8ed9b", "guid": "bfdfe7dc352907fc980b868725387e9854e0281e7223776992faee19d1fbdd63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838dadc69c99764da822468342f1e66d3", "guid": "bfdfe7dc352907fc980b868725387e9849abfebf1afbcf6d7b189434a55704ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f69d701076b37282e4394c4795589c6", "guid": "bfdfe7dc352907fc980b868725387e981d09f875b923b6a655bd44c772fe16f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98043f9ad217e40be334d77eb67403e6d9", "guid": "bfdfe7dc352907fc980b868725387e98339befadb37c7032a16a0d510c56ddc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878047e1c49eccaae3fe2322855ba165e", "guid": "bfdfe7dc352907fc980b868725387e9863a47f3e0eea12f36ceba9ddde28721a"}], "guid": "bfdfe7dc352907fc980b868725387e9867932396a40c237ff55ddd1e21d5d231", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9807f415839ae67220202613bacd8e36cd"}], "guid": "bfdfe7dc352907fc980b868725387e98663895ae45270bfd00867ce1f5453e1f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98733c5b7f4934b273ad6455bc91dda241", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9808993ee7e539dbc8104a52b3ab1a956e", "name": "OneSignalXCFramework"}], "guid": "bfdfe7dc352907fc980b868725387e98f4438af4c3c51df17ddafa29b7aae198", "name": "onesignal_flutter", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982015f089d5d93545d9616a3341903bda", "name": "onesignal_flutter.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}