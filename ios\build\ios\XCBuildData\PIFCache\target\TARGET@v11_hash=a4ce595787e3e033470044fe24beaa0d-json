{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9805e99277c6affce6464bba15e2d9f734", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d830ef0dfbb562ef46560afcd6bf5c2d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983608760497e4ebe8521ce1c08e9b33dd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98073969aa275bd3f9247eef375fbdd239", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983608760497e4ebe8521ce1c08e9b33dd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9854643c2b1a3a69cefb3806f55f789037", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989f64e1e88a1fd156b641daa38c6dd222", "guid": "bfdfe7dc352907fc980b868725387e98ae7ae731a3c9cac03966b0e9f797c9f5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cafa0f2d3c87275ac06ae02536a5c5d0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9848a4f4fe53a5f407fea2ab02e25a58cc", "guid": "bfdfe7dc352907fc980b868725387e981d4019573a16a81a7e1d03021c4c7983"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800f4f0a330c8856512db67c0afab30fc", "guid": "bfdfe7dc352907fc980b868725387e986bb8eaec5ef3c81674896d2db0cdb963"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800e826e6bed45964130400674bb93195", "guid": "bfdfe7dc352907fc980b868725387e9871fd16512dd3ae57c89931a4e15f5db3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98460b53a8253f028e6442c3bbfc522858", "guid": "bfdfe7dc352907fc980b868725387e982229128088332dd9bf813aa883cf546c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b893ce1204c2695eb79f1075831f17f", "guid": "bfdfe7dc352907fc980b868725387e98bcbde9d1cc55fba27ab468407a76a668"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805ea0e957042f7e7a24c48281823d24e", "guid": "bfdfe7dc352907fc980b868725387e9896075cd11510c16ec3db24bccb1dfa55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5001a0882078c09c9c2c29bc77eb0e8", "guid": "bfdfe7dc352907fc980b868725387e9868fa0764a82dfbe617f32f617e351003"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981abfaa87a80572a98818b9929e9c18d3", "guid": "bfdfe7dc352907fc980b868725387e98a4e8af77f11ef1fbce7b8b1d28799e23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cae8b46e5fc3548799fd0060ec603bf", "guid": "bfdfe7dc352907fc980b868725387e980774c3523c3d71ef9d16b1e3d3ebdfc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c02645cb5a76ae208b345008140f5b9", "guid": "bfdfe7dc352907fc980b868725387e98890ec1f104f9f9ca286042be6f6c2dc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b115e20ef90294e5cb62c2f3ff03673", "guid": "bfdfe7dc352907fc980b868725387e982e98e1b958e1aee726d7f0f240c226fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6cf198cd381b88f48c986fc70f10a68", "guid": "bfdfe7dc352907fc980b868725387e9879fbd6d54b76a9fde49d6029db3a7450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cec54863aae14381b2e9ae34ea3ec3e1", "guid": "bfdfe7dc352907fc980b868725387e9809b2fd16dd899f2a9874837361fcddee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9a6395f99bf1b7c37e75cc9b26f02b9", "guid": "bfdfe7dc352907fc980b868725387e982ed06ee9d598ada099eea665fd239bb6"}], "guid": "bfdfe7dc352907fc980b868725387e9846d025074a6bb95bca5ac80d739f3505", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9820cc3b39ccb8a1b40d06ed87c973596f"}], "guid": "bfdfe7dc352907fc980b868725387e98b7a1f50468a162cb35af87dbf4f7c857", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ed38d5155292fc43222aa07b1c9dd5a3", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98c795c9b578baeb5754544a36e3d5ad64", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}