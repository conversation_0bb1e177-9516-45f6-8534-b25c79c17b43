class GetFaqModel {
  bool? status;
  String? message;
  int? statusCode;
  GetFaqData? data;

  GetFaqModel({this.status, this.message, this.statusCode, this.data});

  GetFaqModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? new GetFaqData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class GetFaqData {
  Faq? faq;

  GetFaqData({this.faq});

  GetFaqData.fromJson(Map<String, dynamic> json) {
    faq = json['faq'] != null ? new Faq.fromJson(json['faq']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.faq != null) {
      data['faq'] = this.faq!.toJson();
    }
    return data;
  }
}

class Faq {
  int? id;
  String? question;
  String? answer;
  int? categoryId;
  int? platform;
  bool? status;
  String? createdAt;
  String? updatedAt;

  Faq({
    this.id,
    this.question,
    this.answer,
    this.categoryId,
    this.platform,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  Faq.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    question = json['question'];
    answer = json['answer'];
    categoryId = json['category_id'];
    platform = json['platform'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['question'] = this.question;
    data['answer'] = this.answer;
    data['category_id'] = this.categoryId;
    data['platform'] = this.platform;
    data['status'] = this.status;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}
