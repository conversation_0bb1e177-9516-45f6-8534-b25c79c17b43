import 'dart:async';
import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/catering/addcateringreqmodel.dart';
import 'package:db_eats/data/models/catering/cancelcateringmodel.dart';
import 'package:db_eats/data/models/catering/cateringacceptedlistmodel.dart';
import 'package:db_eats/data/models/catering/cateringdishdetailsmodel.dart';
import 'package:db_eats/data/models/catering/cateringdishlistmodel.dart';
import 'package:db_eats/data/models/catering/cateringordersummarymodel.dart';
import 'package:db_eats/data/models/catering/editcateringmodel.dart';
import 'package:db_eats/data/models/catering/viewcateringrequestsmodel.dart';
import 'package:db_eats/data/models/verifyrefreshtokenmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/storage/localstorage.dart';

class CateringBloc extends Bloc<CateringEvent, CateringState> {
  CateringBloc() : super(ListCateringrequestsLoading()) {
    on<ListCateringrequestsEvent>(_listCateringrequests);
    on<ListDishesInCatering>(_listDishesInCatering);
    on<GetCateringDishDetails>(_getCateringDishDetails);
    on<AddCateringRequestEvent>(_addCateringRequest);
    on<EditCateringRequestEvent>(_editCateringRequest);
    on<CancelCateringRequestEvent>(_cancelCateringRequest);
    on<AddDishToCateringCart>(_addDishToCateringCart);
    on<ViewCateringRequest>(_viewCateringRequest);
    on<UpdateDishQuantity>(_updateDishQuantity);
    on<RemoveDishFromRequest>(_removeDishFromRequest);
    on<CheckoutCateringRequest>(_checkoutCateringRequest);
    on<ViewCteringSummary>(_viewCteringSummary);
    on<RefreshTokenEvent>(_refreshToken);
  }
  FutureOr<void> _refreshToken(
      RefreshTokenEvent event, Emitter<CateringState> emit) async {
    emit(RefreshTokenLoading());
    try {
      Initializer.verifyRefreshTokenModel = VerifyRefreshTokenModel.fromJson(
          await ServerHelper.getrefresh(
              '/v1/customer/auth/verify-refresh-token'));
      if (Initializer.verifyRefreshTokenModel.status == true) {
        await LocalStorage.setAccessToken(
            Initializer.verifyRefreshTokenModel.data?.accessToken ?? "");
        emit(RefreshTokenSuccess());
        if (event.nextEvent != null) {
          add(event.nextEvent!);
        }
      } else if (Initializer.verifyRefreshTokenModel.statusCode == 401) {
        emit(RefreshTokenFailed());
        // await LocalStorage.setAccessToken('');
        // await LocalStorage.setRefreshToken('');
        // Navigator.of(context).pushAndRemoveUntil(
        //   MaterialPageRoute(builder: (context) => const Home()),
        //   (route) => false,
        // );
      }
    } catch (e) {}
  }

  Future<void> _listCateringrequests(
      ListCateringrequestsEvent event, Emitter<CateringState> emit) async {
    emit(ListCateringrequestsLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/catering/list-requests', {'status': event.data});
      log('Catering List Requests Response: $response');

      Initializer.cateringAcceptedListModel =
          CateringAcceptedListModel.fromJson(response);

      // Check for 401
      if (Initializer.cateringAcceptedListModel.statusCode == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (Initializer.cateringAcceptedListModel.status == true) {
        emit(ListCateringrequestsSuccess(
            Initializer.cateringAcceptedListModel.data));
      } else {
        emit(ListCateringrequestsFailed(
            response['message'] ?? 'Failed to list catering requests'));
      }
    } catch (e) {
      log('Error listing catering requests: $e');
      emit(ListCateringrequestsFailed(
          'Error occurred while listing catering requests'));
    }
  }

  Future<void> _listDishesInCatering(
      ListDishesInCatering event, Emitter<CateringState> emit) async {
    emit(ListDishesInCateringLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/catering/list-dish-by-category', event.data);

      log('Dishes In Catering Response: $response');

      Initializer.cateringDishListModel =
          CateringDishListModel.fromJson(response);

      if (Initializer.cateringDishListModel.statusCode == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (Initializer.cateringDishListModel.status == true) {
        emit(ListDishesInCateringSuccess(
            Initializer.cateringDishListModel.data));
      } else {
        emit(ListDishesInCateringFailed(
            response['message'] ?? 'Failed to list dishes in catering'));
      }
    } catch (e) {
      log('Error listing dishes in catering: $e');
      emit(ListDishesInCateringFailed(
          'Error occurred while listing dishes in catering'));
    }
  }

  Future<void> _addCateringRequest(
      AddCateringRequestEvent event, Emitter<CateringState> emit) async {
    emit(AddCateringRequestLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/catering/add-request', event.data);

      log(' Catering request: $response');

      AddCateringRequestModel addCateringRequestModel =
          AddCateringRequestModel.fromJson(response);

      if (addCateringRequestModel.statusCode == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (addCateringRequestModel.status == true) {
        emit(AddCateringRequestSuccess());
      } else {
        emit(AddCateringRequestFailed(
            response['message'] ?? 'Failed to add catering request'));
      }
    } catch (e) {
      log('Error : $e');
      emit(AddCateringRequestFailed(
          'Error occurred while adding catering request'));
    }
  }

  Future<void> _editCateringRequest(
      EditCateringRequestEvent event, Emitter<CateringState> emit) async {
    emit(EditCateringRequestLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/catering/edit-request', event.data);

      log('edit Catering request: $response');

      EditCateringRequestModel editCateringRequestModel =
          EditCateringRequestModel.fromJson(response);

      if (editCateringRequestModel.statusCode == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (editCateringRequestModel.status == true) {
        emit(EditCateringRequestSuccess());
      } else {
        emit(EditCateringRequestFailed(
            response['message'] ?? 'Failed to edit catering request'));
      }
    } catch (e) {
      log('Error : $e');
      emit(EditCateringRequestFailed(
          'Error occurred while Edit catering request'));
    }
  }

  Future<void> _cancelCateringRequest(
      CancelCateringRequestEvent event, Emitter<CateringState> emit) async {
    emit(CancelCateringRequestLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/catering/cancel-request', event.data);

      log('cancel Catering request: $response');

      CancelCateringRequestModel cancelCateringRequestModel =
          CancelCateringRequestModel.fromJson(response);

      if (cancelCateringRequestModel.statusCode == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (cancelCateringRequestModel.status == true) {
        emit(CancelCateringRequestSuccess());
      } else {
        emit(CancelCateringRequestFailed(
            response['message'] ?? 'Failed to cancel catering request'));
      }
    } catch (e) {
      log('Error : $e');
      emit(CancelCateringRequestFailed(
          'Error occurred while cancel catering request'));
    }
  }

  Future<void> _getCateringDishDetails(
      GetCateringDishDetails event, Emitter<CateringState> emit) async {
    emit(GetCateringDishDetailsLoading());
    try {
      final response = await ServerHelper.get1(
          '/v1/customer/catering/get-dish?dish_id=${event.data['dish_id']}&catering_id=${event.data['catering_id']}');
      log('Catering Dish Details Response: $response');

      Initializer.cateringDishDetailModel =
          CateringDishDetailsModel.fromJson(response);

      if (Initializer.cateringDishDetailModel.statusCode == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (Initializer.cateringDishDetailModel.status == true) {
        emit(GetCateringDishDetailsSuccess(
            Initializer.cateringDishDetailModel.data));
      } else {
        emit(GetCateringDishDetailsFailed(
            response['message'] ?? 'Failed to get catering dish details'));
      }
    } catch (e) {
      log('Error getting catering dish details: $e');
      emit(GetCateringDishDetailsFailed(
          'Error occurred while getting catering dish details'));
    }
  }

  Future<void> _addDishToCateringCart(
      AddDishToCateringCart event, Emitter<CateringState> emit) async {
    emit(AddDishToCateringCartLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/catering/add-dish', event.data);
      log('Add Dish To Catering Cart Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (response['status'] == true) {
        emit(AddDishToCateringCartSuccess(response['message']));
      } else {
        emit(AddDishToCateringCartFailed(
            response['message'] ?? 'Failed to add dish to catering cart'));
      }
    } catch (e) {
      log('Error adding dish to catering cart: $e');
      emit(AddDishToCateringCartFailed(
          'Error occurred while adding dish to catering cart'));
    }
  }

  Future<void> _viewCateringRequest(
      ViewCateringRequest event, Emitter<CateringState> emit) async {
    emit(ViewCateringRequestLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/catering/view-request',
          {'catering_id': event.cateringId});

      log('View Catering Request Response: $response');

      Initializer.viewCateringRequestModel =
          ViewCateringRequestModel.fromJson(response);

      if (Initializer.viewCateringRequestModel.statusCode == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (Initializer.viewCateringRequestModel.status == true) {
        emit(ViewCateringRequestSuccess(
            Initializer.viewCateringRequestModel.data));
      } else {
        emit(ViewCateringRequestFailed(
            response['message'] ?? 'Failed to view catering request'));
      }
    } catch (e) {
      log('Error viewing catering request: $e');
      emit(ViewCateringRequestFailed(
          'Error occurred while viewing catering request'));
    }
  }

  Future<void> _updateDishQuantity(
      UpdateDishQuantity event, Emitter<CateringState> emit) async {
    emit(UpdateDishQuantityLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/catering/update-dish-quantity', event.data);
      log('Update Dish Quantity Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (response['status'] == true) {
        emit(UpdateDishQuantitySuccess(response['message']));
      } else {
        emit(UpdateDishQuantityFailed(
            response['message'] ?? 'Failed to update dish quantity'));
      }
    } catch (e) {
      log('Error updating dish quantity: $e');
      emit(UpdateDishQuantityFailed(
          'Error occurred while updating dish quantity'));
    }
  }

  Future<void> _checkoutCateringRequest(
      CheckoutCateringRequest event, Emitter<CateringState> emit) async {
    emit(CheckoutCateringRequestLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/catering/request-checkout', event.data);
      log('Checkout Catering Request Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (response['status'] == true) {
        emit(CheckoutCateringRequestSuccess(response['message'],
         response['data']?['payment']?['id'] ?? '',
        response['data']?['payment']?['checkout_url'] ?? '',
             response['data']?['total'] ?? 0,));
      
      } else {
        emit(CheckoutCateringRequestFailed(
            response['message'] ?? 'Failed to checkout catering request'));
      }
    } catch (e) {
      log('Error checking out catering request: $e');
      emit(CheckoutCateringRequestFailed(
          'Error occurred while checking out catering request'));
    }
  }

  Future<void> _viewCteringSummary(
      ViewCteringSummary event, Emitter<CateringState> emit) async {
    emit(ViewCteringSummaryLoading());
    try {
      final response =
          await ServerHelper.post1('/v1/customer/catering/summary', event.data);
      log('View Catering Summary Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (response['status'] == true) {
        CateringOrderSummaryModel cateringOrderSummaryModel =
            CateringOrderSummaryModel.fromJson(response);
        emit(ViewCteringSummarySuccess(cateringOrderSummaryModel.data));
      } else {
        emit(ViewCteringSummaryFailed(
            response['message'] ?? 'Failed to get catering order summary'));
      }
    } catch (e) {
      log('Error getting catering order summary: $e');
      emit(ViewCteringSummaryFailed(
          'Error occurred while getting catering order summary'));
    }
  }

  Future<void> _removeDishFromRequest(
      RemoveDishFromRequest event, Emitter<CateringState> emit) async {
    emit(RemoveDishFromRequestLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/catering/remove-dish', event.data);
      log('Remove Dish From Request Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (response['status'] == true) {
        emit(RemoveDishFromRequestSuccess(response['message']));
      } else {
        emit(RemoveDishFromRequestFailed(
            response['message'] ?? 'Failed to remove dish from request'));
      }
    } catch (e) {
      log('Error removing dish from request: $e');
      emit(RemoveDishFromRequestFailed(
          'Error occurred while removing dish from request'));
    }
  }
}

class CateringDishDetailModel {}

// Events
abstract class CateringEvent {}

class ListCateringrequestsEvent extends CateringEvent {
  final String data;
  ListCateringrequestsEvent(this.data);
}

class AddCateringRequestEvent extends CateringEvent {
  final dynamic data;
  AddCateringRequestEvent(this.data);
}

class EditCateringRequestEvent extends CateringEvent {
  final dynamic data;
  EditCateringRequestEvent(this.data);
}

class CancelCateringRequestEvent extends CateringEvent {
  final dynamic data;
  CancelCateringRequestEvent(this.data);
}

class ListDishesInCatering extends CateringEvent {
  final Map<String, dynamic> data;
  ListDishesInCatering(this.data);
}

class GetCateringDishDetails extends CateringEvent {
  final Map<String, dynamic> data;
  GetCateringDishDetails(this.data);
}

class AddDishToCateringCart extends CateringEvent {
  final Map<String, dynamic> data;
  AddDishToCateringCart(this.data);
}

class ViewCateringRequest extends CateringEvent {
  final int cateringId;
  ViewCateringRequest(this.cateringId);
}

class UpdateDishQuantity extends CateringEvent {
  final Map<String, dynamic> data;
  UpdateDishQuantity(this.data);
}

class RemoveDishFromRequest extends CateringEvent {
  final Map<String, dynamic> data;
  RemoveDishFromRequest(this.data);
}

class CheckoutCateringRequest extends CateringEvent {
  final Map<String, dynamic> data;
  CheckoutCateringRequest(this.data);
}

class ViewCteringSummary extends CateringEvent {
  Map<String, dynamic> data;
  ViewCteringSummary(this.data);
}

// Add RefreshTokenEvent
class RefreshTokenEvent extends CateringEvent {
  final String refreshToken;
  final CateringEvent? nextEvent;
  RefreshTokenEvent({required this.refreshToken, required this.nextEvent});
}

// States
abstract class CateringState {}

class ListCateringrequestsLoading extends CateringState {}

class ListCateringrequestsSuccess extends CateringState {
  final dynamic data;
  ListCateringrequestsSuccess(this.data);
}

class ListCateringrequestsFailed extends CateringState {
  final String message;
  ListCateringrequestsFailed(this.message);
}

class ListDishesInCateringLoading extends CateringState {}

class ListDishesInCateringSuccess extends CateringState {
  final dynamic data;
  ListDishesInCateringSuccess(this.data);
}

class ListDishesInCateringFailed extends CateringState {
  final String message;
  ListDishesInCateringFailed(this.message);
}

class GetCateringDishDetailsLoading extends CateringState {}

class GetCateringDishDetailsSuccess extends CateringState {
  final dynamic data;
  GetCateringDishDetailsSuccess(this.data);
}

class GetCateringDishDetailsFailed extends CateringState {
  final String message;
  GetCateringDishDetailsFailed(this.message);
}

class AddCateringRequestLoading extends CateringState {}

class AddCateringRequestSuccess extends CateringState {
  AddCateringRequestSuccess();
}

class AddCateringRequestFailed extends CateringState {
  final String message;
  AddCateringRequestFailed(this.message);
}

class AddDishToCateringCartLoading extends CateringState {}

class AddDishToCateringCartSuccess extends CateringState {
  final String message;
  AddDishToCateringCartSuccess(this.message);
}

class AddDishToCateringCartFailed extends CateringState {
  final String message;
  AddDishToCateringCartFailed(this.message);
}

class ViewCateringRequestLoading extends CateringState {}

class ViewCateringRequestSuccess extends CateringState {
  final dynamic data;
  ViewCateringRequestSuccess(this.data);
}

class ViewCateringRequestFailed extends CateringState {
  final String message;
  ViewCateringRequestFailed(this.message);
}

class UpdateDishQuantityLoading extends CateringState {}

class UpdateDishQuantitySuccess extends CateringState {
  final String message;
  UpdateDishQuantitySuccess(this.message);
}

class UpdateDishQuantityFailed extends CateringState {
  final String message;
  UpdateDishQuantityFailed(this.message);
}

class RemoveDishFromRequestLoading extends CateringState {}

class RemoveDishFromRequestSuccess extends CateringState {
  final String message;
  RemoveDishFromRequestSuccess(this.message);
}

class RemoveDishFromRequestFailed extends CateringState {
  final String message;
  RemoveDishFromRequestFailed(this.message);
}

class CheckoutCateringRequestLoading extends CateringState {}

class CheckoutCateringRequestSuccess extends CateringState {
  final String message;
  final String? orderId;
  final String? checkoutUrl;
  final num? totalAmount;
  CheckoutCateringRequestSuccess(this.message,this.orderId, this.checkoutUrl, this.totalAmount);
}

class CheckoutCateringRequestFailed extends CateringState {
  final String message;
  CheckoutCateringRequestFailed(this.message);
}

class EditCateringRequestLoading extends CateringState {}

class EditCateringRequestSuccess extends CateringState {
  EditCateringRequestSuccess();
}

class EditCateringRequestFailed extends CateringState {
  final String message;
  EditCateringRequestFailed(this.message);
}

class CancelCateringRequestLoading extends CateringState {}

class CancelCateringRequestSuccess extends CateringState {
  CancelCateringRequestSuccess();
}

class CancelCateringRequestFailed extends CateringState {
  final String message;
  CancelCateringRequestFailed(this.message);
}

class ViewCteringSummaryLoading extends CateringState {}

class ViewCteringSummarySuccess extends CateringState {
  final dynamic data;
  ViewCteringSummarySuccess(this.data);
}

class ViewCteringSummaryFailed extends CateringState {
  final String message;
  ViewCteringSummaryFailed(this.message);
}

// Add RefreshToken states
class RefreshTokenLoading extends CateringState {}

class RefreshTokenSuccess extends CateringState {}

class RefreshTokenFailed extends CateringState {}
