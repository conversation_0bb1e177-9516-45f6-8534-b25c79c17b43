import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/ui/common/uihelper.dart';
import 'package:db_eats/ui/profile/faq/faq_detail.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../bloc/account_bloc.dart';

import '../../../data/models/account/faqcatagorymodel.dart';
import '../../../data/models/account/faqcatagorylistsmodel.dart';

class ManageFAQScreen extends StatefulWidget {
  const ManageFAQScreen({Key? key}) : super(key: key);

  @override
  State<ManageFAQScreen> createState() => _ManageFAQScreenState();
}

class _ManageFAQScreenState extends State<ManageFAQScreen> {
  Widget _faqListShimmer(BuildContext context) {
    return ListView.builder(
      itemCount: 6,
      itemBuilder: (context, index) {
        return Container(
          margin: ResponsiveUtils.pSymmetric(context, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: const Color.fromARGB(136, 158, 158, 158)),
            borderRadius: ResponsiveUtils.br(context, 8),
          ),
          child: Padding(
            padding: ResponsiveUtils.pAll(context, 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  height: ResponsiveUtils.h(context, 18),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                SizedBox(height: ResponsiveUtils.h(context, 8)),
                Container(
                  width: double.infinity,
                  height: ResponsiveUtils.h(context, 14),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  int? selectedCategoryId;
  List<FAQCatagoryData> categories = [];
  List<Faqs> faqs = [];
  bool isLoadingCategories = true;
  bool isLoadingFAQs = false;
  String searchQuery = '';
  final TextEditingController searchController = TextEditingController();
  late AccountBloc accountBloc;

  @override
  void initState() {
    super.initState();
    accountBloc = BlocProvider.of<AccountBloc>(context);
    _loadCategories();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAllFAQs();
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  void _loadCategories() {
    accountBloc.add(GetFAQ());
  }

  int currentPage = 1;
  int pageLimit = 10;

  void _loadFAQsByCategory(int? categoryId) {
    setState(() {
      isLoadingFAQs = true;
      selectedCategoryId = categoryId;
    });

    final data = <String, dynamic>{'page': currentPage, 'limit': pageLimit};
    if (categoryId != null && categoryId != 0) {
      data['category'] = categoryId;
    }
    accountBloc.add(GetFAQByCatagory(data: data));
  }

  void _loadAllFAQs() {
    setState(() {
      isLoadingFAQs = true;
      selectedCategoryId = 0;
    });
    final data = <String, dynamic>{'page': currentPage, 'limit': pageLimit};
    accountBloc.add(GetFAQByCatagory(data: data));
  }

  List<Faqs> get filteredFAQs {
    if (searchQuery.isEmpty) return faqs;
    return faqs.where((faq) {
      return faq.question!.toLowerCase().contains(searchQuery.toLowerCase()) ||
          faq.answer!.toLowerCase().contains(searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AccountBloc, AccountState>(
      listener: (context, state) {
        if (state is GetFAQSuccess) {
          setState(() {
            categories = Initializer.faqcategoryListModel.data ?? [];
            isLoadingCategories = false;
          });
        } else if (state is GetFAQFailed) {
          setState(() {
            isLoadingCategories = false;
          });
          ScaffoldMessenger.of(context)
              .showSnackBar(SnackBar(content: Text(state.message)));
        } else if (state is GetFAQByCatagorySuccess) {
          setState(() {
            faqs = Initializer.faqcategoryModel.data?.faqs ?? [];
            isLoadingFAQs = false;
          });
        } else if (state is GetFAQByCatagoryFailed) {
          setState(() {
            isLoadingFAQs = false;
          });
          ScaffoldMessenger.of(context)
              .showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Color(0xFFF6F3EC),
          appBar: AppBar(
            backgroundColor: Color(0xFFF6F3EC),
            leading: IconButton(
              icon: Icon(Icons.arrow_back),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ),
          body: Padding(
            padding: ResponsiveUtils.pSymmetric(context,
                horizontal: 16.0, vertical: 0.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'FAQ Management',
                  style: TextStyle(
                    fontFamily: 'Inter-SemiBold',
                    fontSize: ResponsiveUtils.f(context, 20),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: ResponsiveUtils.h(context, 12)),
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: ResponsiveUtils.pAll(context, 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Find answers to frequently asked questions and get the help you need.',
                            style: TextStyle(
                              fontSize: ResponsiveUtils.f(context, 14),
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          SizedBox(height: ResponsiveUtils.h(context, 16)),
                          Text(
                            "Browse categories",
                            style: TextStyle(
                              fontFamily: 'suisse-intl',
                              fontSize: ResponsiveUtils.f(context, 24),
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          SizedBox(height: ResponsiveUtils.h(context, 10)),
                          // Categories horizontal scroll
                          if (isLoadingCategories)
                            const Center(
                              child: Padding(
                                padding: EdgeInsets.all(16),
                                child: CircularProgressIndicator(),
                              ),
                            )
                          else
                            SizedBox(
                              height: ResponsiveUtils.h(context, 40),
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: categories.length + 1,
                                itemBuilder: (context, index) {
                                  if (index == 0) {
                                    final isSelected = selectedCategoryId == 0;
                                    return Padding(
                                      padding: EdgeInsets.only(
                                        right: ResponsiveUtils.w(context, 8),
                                      ),
                                      child: _buildCategoryTile(
                                        context,
                                        'All',
                                        isSelected,
                                        () {
                                          _loadAllFAQs();
                                        },
                                      ),
                                    );
                                  }

                                  final category = categories[index - 1];
                                  final isSelected =
                                      selectedCategoryId == category.id;

                                  return Padding(
                                    padding: EdgeInsets.only(
                                      right: ResponsiveUtils.w(context, 8),
                                    ),
                                    child: _buildCategoryTile(
                                      context,
                                      category.title ?? '',
                                      isSelected,
                                      () {
                                        _loadFAQsByCategory(category.id!);
                                      },
                                    ),
                                  );
                                },
                              ),
                            ),
                          SizedBox(height: ResponsiveUtils.h(context, 16)),
                          // FAQs Section
                          Expanded(
                            child: selectedCategoryId == null
                                ? _buildEmptyState()
                                : isLoadingFAQs
                                    ? _faqListShimmer(context)
                                    : filteredFAQs.isEmpty
                                        ? _buildNoResultsState()
                                        : _buildFAQsList(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCategoryTile(
    BuildContext context,
    String title,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: ResponsiveUtils.pSymmetric(
          context,
          horizontal: 8,
          // vertical: 4,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor.withOpacity(0.1)
              : Colors.transparent,
          border: Border.all(
            color: isSelected
                ? Theme.of(context).primaryColor
                : const Color.fromARGB(136, 158, 158, 158),
          ),
          borderRadius: ResponsiveUtils.br(context, 6),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              fontFamily: 'suisse-intl',
              fontSize: ResponsiveUtils.f(context, 12),
              fontWeight: FontWeight.w400,
              color:
                  isSelected ? Theme.of(context).primaryColor : Colors.black87,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.help_outline, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Select a category to view FAQs',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose from the categories above to see related questions and answers',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No FAQs found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            searchQuery.isNotEmpty
                ? 'Try adjusting your search terms'
                : 'No FAQs available for this category',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFAQsList() {
    return ListView.separated(
      itemCount: filteredFAQs.length,
      itemBuilder: (context, index) {
        final faq = filteredFAQs[index];
        return _buildFAQTile(context, faq);
      },
      separatorBuilder: (context, index) => Divider(
        color: Colors.grey[300],
        thickness: 1,
        height: 8,
        indent: 16,
        endIndent: 16,
      ),
    );
  }

  Widget _buildFAQTile(BuildContext context, Faqs faq) {
    return Container(
      margin: ResponsiveUtils.pSymmetric(context, vertical: 4),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          tilePadding: ResponsiveUtils.pSymmetric(
            context,
            horizontal: 16,
            vertical: 0,
          ),
          childrenPadding: ResponsiveUtils.pSymmetric(
            context,
            horizontal: 16,
            vertical: 8,
          ),
          title: Text(
            faq.question ?? '',
            style: TextStyle(
              fontFamily: 'suisse-intl',
              fontSize: ResponsiveUtils.f(context, 14),
              fontWeight: FontWeight.w400,
              color: Colors.black87,
            ),
          ),
          iconColor: Colors.black87,
          collapsedIconColor: Colors.black54,
          children: [
            InkWell(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => FAQDetailPage(faqId: faq.id ?? 0),
                  ),
                );
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text(
                      faq.answer ?? '',
                      style: TextStyle(
                        fontSize: ResponsiveUtils.f(context, 13),
                        color: Colors.black87,
                        height: 1.5,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward,
                    size: ResponsiveUtils.f(context, 16),
                    color: Colors.black38,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
