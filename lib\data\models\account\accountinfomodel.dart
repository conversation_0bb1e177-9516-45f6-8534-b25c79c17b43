class AccountInfoModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  AccountInfoModel({this.status, this.message, this.statusCode, this.data});

  AccountInfoModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? email;
  String? phone;
  String? firstName;
  String? lastName;
  String? profilePicture;

  Data({
    this.email,
    this.phone,
    this.firstName,
    this.lastName,
    this.profilePicture,
  });

  Data.fromJson(Map<String, dynamic> json) {
    email = json['email'];
    phone = json['phone'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    profilePicture = json['profile_picture'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['email'] = this.email;
    data['phone'] = this.phone;
    data['first_name'] = this.firstName;
    data['last_name'] = this.lastName;
    data['profile_picture'] = this.profilePicture;
    return data;
  }
}
