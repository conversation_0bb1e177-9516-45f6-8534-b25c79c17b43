import 'package:db_eats/bloc/order_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/support/customer_support.dart';
import 'package:db_eats/utils/dotteddivider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:lottie/lottie.dart' hide Marker;
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'dart:math' as math hide log;
import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';

class ConfirmedOrdersView extends StatefulWidget {
  final int orderId;

  const ConfirmedOrdersView({super.key, required this.orderId});

  @override
  _ConfirmedOrdersViewState createState() => _ConfirmedOrdersViewState();
}

class _ConfirmedOrdersViewState extends State<ConfirmedOrdersView> {
  GoogleMapController? _mapController;
  Set<Polyline> _polylines = {};
  Timer? _locationRefreshTimer;
  bool _initialMapBoundsSet = false;
  LatLng? _lastDriverLocation; // Track last driver location to detect changes

  // Cache for marker icons to avoid recreating them on every update
  final Map<String, BitmapDescriptor> _iconCache = {};

  String _formatTimeOnly(String deliveryTime) {
    try {
      final dateTime = DateTime.parse(deliveryTime);
      return "${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}";
    } catch (e) {
      return deliveryTime;
    }
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  String _formatTimeToAMPM(String time) {
    try {
      if (time.contains('AM') || time.contains('PM')) {
        return time;
      }

      if (time.contains(':')) {
        final parts = time.split(':');
        int hour = int.parse(parts[0]);
        final minute = parts[1];

        final period = hour >= 12 ? 'PM' : 'AM';
        hour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

        return '$hour:$minute $period';
      }

      return time;
    } catch (e) {
      return time;
    }
  }

  double getResponsiveSize(BuildContext context,
      {double small = 12,
      double medium = 16,
      double large = 20,
      double xlarge = 24}) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return small;
    if (width < 600) return medium;
    if (width < 900) return large;
    return xlarge;
  }

  // Helper method to determine progress bar segments based on status
  int getProgressSegments(String status) {
    switch (status) {
      case 'ACTIVE':
        return 1;
      case 'CONFIRMED':
        return 2;
      case 'PREPARING':
        return 3;
      case 'PICKING_UP':
        return 4;
      case 'DELIVERING':
        return 4;
      case 'DELIVERED':
        return 5;
      default:
        return 0;
    }
  }

  Widget _buildOrderItem(String quantity, String title, String price) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;

    return Row(
      children: [
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: size.width * 0.02,
            vertical: size.height * 0.005,
          ),
          decoration: BoxDecoration(
            color: Color(0xFFE1E3E6),
            borderRadius: BorderRadius.circular(size.width * 0.04),
          ),
          child: Text(
            quantity,
            style: TextStyle(
              fontSize: isLandscape ? size.height * 0.022 : forteen,
              fontFamily: 'Inter-medium',
              color: Color(0xFF1F2122),
            ),
          ),
        ),
        SizedBox(width: size.width * 0.03),
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              fontSize: isLandscape ? size.height * 0.025 : forteen,
              fontFamily: 'Inter-Semibold',
              color: Color(0xFF1F2122),
            ),
          ),
        ),
        Text(
          price,
          style: TextStyle(
            fontSize: isLandscape ? size.height * 0.022 : forteen,
            fontWeight: FontWeight.w400,
            fontFamily: 'Inter',
            color: Color(0xFF414346),
          ),
        ),
      ],
    );
  }

  Widget _buildTotalItem(String label, String amount, {bool isTotal = false}) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final isDiscount = label == 'Discounts' || label == 'DB Wallet Credits';

    return Padding(
      padding: EdgeInsets.only(
        bottom: isTotal ? 0 : size.height * 0.01,
        left: size.width * 0.01,
        right: size.width * 0.01,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal
                  ? (isLandscape ? size.height * 0.03 : sixteen)
                  : (isLandscape ? size.height * 0.025 : forteen),
              fontFamily: isTotal ? 'Inter-Semibold' : 'Inter',
              color: Color(0xFF1F2122),
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: isTotal
                  ? (isLandscape ? size.height * 0.03 : sixteen)
                  : (isLandscape ? size.height * 0.022 : forteen),
              fontFamily: isTotal ? 'Inter-Semibold' : 'Inter',
              color: isDiscount ? Color(0xFFD31510) : Color(0xFF414346),
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  void _startLocationRefreshTimer() {
    _locationRefreshTimer?.cancel();

    _locationRefreshTimer = Timer.periodic(const Duration(seconds: 8), (timer) {
      if (mounted) {
        context.read<OrderBloc>().add(ViewOrederdetailsEvent2(widget.orderId));

        if (_mapController != null &&
            Initializer.viewOrderDetailsModel.data?.assignedDriver
                    ?.driverCurrentLocation?.location?.coordinates !=
                null) {
          final driverCoords = Initializer.viewOrderDetailsModel.data!
              .assignedDriver!.driverCurrentLocation!.location!.coordinates!;
          if (driverCoords.length >= 2) {
            setState(() {
              // Trigger a rebuild to refresh the map with new markers
            });
          }
        }
      }
    });
  }

  void _updateMapBounds() {
    final orderData = Initializer.viewOrderDetailsModel.data;
    if (orderData == null || _mapController == null) return;

    List<LatLng> points = [];

    // Add driver location if available (prioritize driver location)
    if (orderData
                .assignedDriver?.driverCurrentLocation?.location?.coordinates !=
            null &&
        orderData.assignedDriver!.driverCurrentLocation!.location!.coordinates!
                .length >=
            2) {
      points.add(LatLng(
        orderData
            .assignedDriver!.driverCurrentLocation!.location!.coordinates![1],
        orderData
            .assignedDriver!.driverCurrentLocation!.location!.coordinates![0],
      ));
    }

    // Add chef location
    if (orderData.chef?.location?.coordinates != null &&
        orderData.chef!.location!.coordinates!.length >= 2) {
      points.add(LatLng(
        orderData.chef!.location!.coordinates![1],
        orderData.chef!.location!.coordinates![0],
      ));
    }

    // Add customer location
    if (orderData.address?.location?.coordinates != null &&
        orderData.address!.location!.coordinates!.length >= 2) {
      points.add(LatLng(
        orderData.address!.location!.coordinates![1],
        orderData.address!.location!.coordinates![0],
      ));
    }

    if (points.length >= 2) {
      LatLngBounds bounds = _calculateBoundsFromPoints(points);
      if (_mapController != null && !_initialMapBoundsSet) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLngBounds(bounds, 50),
        );
        _initialMapBoundsSet = true;
      }
    } else if (points.length == 1) {
      if (_mapController != null && !_initialMapBoundsSet) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLng(points[0]),
        );
        _initialMapBoundsSet = true;
      }
    }
  }

  LatLngBounds _calculateBoundsFromPoints(List<LatLng> points) {
    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;

    for (LatLng point in points) {
      minLat = math.min(minLat, point.latitude);
      maxLat = math.max(maxLat, point.latitude);
      minLng = math.min(minLng, point.longitude);
      maxLng = math.max(maxLng, point.longitude);
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  @override
  void dispose() {
    _locationRefreshTimer?.cancel();
    _mapController?.dispose();
    _lastDriverLocation = null; // Reset driver location tracking
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    context.read<OrderBloc>().add(ViewOrederdetailsEvent(widget.orderId));
    _initialMapBoundsSet = false; // Reset map bounds flag
    _lastDriverLocation = null; // Reset driver location tracking
    _startLocationRefreshTimer();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final itemSpacing = size.height * 0.02;
    final isLandscape = size.width > size.height;

    // Add responsive container sizing
    final cookingImageSize =
        isLandscape ? size.height * 0.30 : size.width * 0.40;
    final driverContainerWidth =
        isLandscape ? size.width * 0.2 : size.width * 0.45;
    final progressBarWidth =
        isLandscape ? size.width * 0.08 : size.width * 0.12;

    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC),
      body: SafeArea(
        child: BlocConsumer<OrderBloc, OrderState>(
          listener: (context, state) {
            if (state is ViewOrederdetailsFailed) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.message)),
              );
            } else if (state is ViewOrederdetailsSuccess) {
              // When we get new order details, we can refresh the map markers silently
              final orderStatus =
                  Initializer.viewOrderDetailsModel.data?.status;
              if (_mapController != null &&
                  (orderStatus == 'DELIVERING' ||
                      orderStatus == 'PICKING_UP')) {
                setState(() {
                  // Trigger a rebuild to refresh the map with new markers
                });
              }
            } else if (state is ViewOrederdetailsSuccess2) {
              // Handle periodic updates from timer - silent refresh
              final orderStatus =
                  Initializer.viewOrderDetailsModel.data?.status;
              if (_mapController != null &&
                  (orderStatus == 'DELIVERING' ||
                      orderStatus == 'PICKING_UP')) {
                setState(() {
                  // Trigger a rebuild to refresh the map with new markers
                });
              }
            }
          },
          buildWhen: (previous, current) {
            return current is ViewOrederdetailsLoading ||
                current is ViewOrederdetailsSuccess ||
                current is ViewOrederdetailsFailed;
          },
          builder: (context, state) {
            if (state is ViewOrederdetailsLoading) {
              return const Center(child: CupertinoActivityIndicator());
            }

            if (state is ViewOrederdetailsFailed ||
                Initializer.viewOrderDetailsModel.data == null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: size.width * 0.25,
                      child: Lottie.asset(
                        'assets/noorderes.json',
                        fit: BoxFit.contain,
                      ),
                    ),
                    Text(
                      "Failed to Load Order Details",
                      style: TextStyle(
                        fontSize: getResponsiveSize(context,
                            small: 16, medium: 18, large: 22, xlarge: 26),
                        fontWeight: FontWeight.w700,
                        color: const Color(0xFF1F2122),
                        fontFamily: 'Inter',
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: size.height * 0.01),
                    Text(
                      state is ViewOrederdetailsFailed
                          ? state.message
                          : "No order details available",
                      style: TextStyle(
                        fontSize: getResponsiveSize(context,
                            small: 12, medium: 14, large: 16, xlarge: 18),
                        color: const Color(0xFF66696D),
                        fontFamily: 'Inter',
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }

            final orderData = Initializer.viewOrderDetailsModel.data!;
            final orderNumber = orderData.orderNumber ?? 'Unknown';
            final chefName = orderData.chef?.name ?? 'Unknown Chef';
            final chefAvatar = orderData.chef?.photo != null
                ? ServerHelper.imageUrl + orderData.chef!.photo!
                : 'assets/images/chef_placeholder.png';
            final itemsCount = orderData.items?.length ?? 0;
            final deliveryAddress =
                orderData.address?.addressText ?? 'Unknown Address';
            final deliveryTimeDescription =
                orderData.deliveryTimes?.description ?? 'Unknown Time';
            final deliveryTime = orderData.deliveryTime ?? '';
            final status = orderData.status ?? 'Unknown';

            // Format delivery time for display (time only)
            String formattedDeliveryTime = '';
            if (deliveryTime.isNotEmpty) {
              formattedDeliveryTime = _formatTimeOnly(deliveryTime);
            } else {
              formattedDeliveryTime = deliveryTimeDescription;
            }

            final subtotal = orderData.subtotal?.toStringAsFixed(2) ?? '0.00';
            final deliveryFee =
                orderData.deliveryFee?.toStringAsFixed(2) ?? '0.00';
            final discount = orderData.discount?.toStringAsFixed(2) ?? '0.00';
            final walletCredits =
                orderData.walletCredits?.toStringAsFixed(2) ?? '0.00';
            final taxesAndFees =
                orderData.taxesAndFees?.toStringAsFixed(2) ?? '0.00';
            final total = orderData.total?.toStringAsFixed(2) ?? '0.00';

            // Extract promotions dynamically from coupons
            final promotions = orderData.coupons
                    ?.map((coupon) => coupon.couponCode ?? 'Unknown Promotion')
                    .join(', ') ??
                'No Promotions';

            return Container(
              color: const Color(0xFFF6F3EC),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: size.width * 0.04,
                      vertical: size.height * 0.02,
                    ),
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Row(
                        children: [
                          Icon(
                            Icons.chevron_left,
                            size: isLandscape ? size.height * 0.04 : twentyFour,
                            color: const Color(0xFF1F2122),
                          ),
                          const SizedBox(width: 8),
                          IntrinsicWidth(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  'Back',
                                  style: TextStyle(
                                    fontSize: isLandscape
                                        ? size.height * 0.03
                                        : sixteen,
                                    fontWeight: FontWeight.w600,
                                    fontFamily: 'Inter',
                                    color: Color(0xFF1F2122),
                                    decoration: TextDecoration.none,
                                  ),
                                ),
                                SizedBox(height: screenWidth * 0.0025),
                                Container(
                                  height: 1.5,
                                  width: double.infinity,
                                  color: Color(0xFF1F2122),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.all(size.width * 0.04),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Google Map for DELIVERING and PICKING_UP (like OngoingList)
                                if (status == 'DELIVERING' ||
                                    status == 'PICKING_UP') ...[
                                  Container(
                                    constraints: BoxConstraints(
                                      maxWidth: double.infinity,
                                      maxHeight: 400,
                                    ),
                                    width: double.infinity,
                                    height: 400,
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(12),
                                      child: FutureBuilder<Set<Marker>>(
                                        future: _createMarkers(orderData),
                                        builder: (context, snapshot) {
                                          if (!snapshot.hasData) {
                                            return const Center(
                                                child:
                                                    CupertinoActivityIndicator());
                                          }
                                          // Check and update polylines when driver location changes
                                          if ((status == 'PICKING_UP' ||
                                                  status == 'DELIVERING') &&
                                              orderData
                                                      .assignedDriver
                                                      ?.driverCurrentLocation
                                                      ?.location
                                                      ?.coordinates !=
                                                  null &&
                                              orderData.address?.location
                                                      ?.coordinates !=
                                                  null) {
                                            final driverLocation = LatLng(
                                              orderData
                                                  .assignedDriver!
                                                  .driverCurrentLocation!
                                                  .location!
                                                  .coordinates![1],
                                              orderData
                                                  .assignedDriver!
                                                  .driverCurrentLocation!
                                                  .location!
                                                  .coordinates![0],
                                            );
                                            final customerLocation = LatLng(
                                              orderData.address!.location!
                                                  .coordinates![1],
                                              orderData.address!.location!
                                                  .coordinates![0],
                                            );

                                            // Check if driver location has changed significantly (more than ~10 meters)
                                            bool shouldUpdateRoute =
                                                _lastDriverLocation == null ||
                                                    _calculateDistance(
                                                            _lastDriverLocation!,
                                                            driverLocation) >
                                                        0.0001;

                                            if (shouldUpdateRoute) {
                                              _lastDriverLocation =
                                                  driverLocation;
                                              _getRouteCoordinates(
                                                      driverLocation,
                                                      customerLocation)
                                                  .then((points) {
                                                if (points.isNotEmpty) {
                                                  setState(() {
                                                    _polylines = {
                                                      Polyline(
                                                        polylineId:
                                                            const PolylineId(
                                                                'driver_to_customer_route'),
                                                        points: points,
                                                        color:
                                                            Color(0xFF007A4D),
                                                        width: 7,
                                                      ),
                                                    };
                                                  });
                                                }
                                              });
                                            }
                                          } else {
                                            // Clear polylines if not in tracking status
                                            if (_polylines.isNotEmpty) {
                                              WidgetsBinding.instance
                                                  .addPostFrameCallback((_) {
                                                setState(() {
                                                  _polylines = {};
                                                  _lastDriverLocation = null;
                                                });
                                              });
                                            }
                                          }
                                          return GoogleMap(
                                            key: ValueKey(
                                                'map-${widget.orderId}'),
                                            initialCameraPosition:
                                                CameraPosition(
                                              target: LatLng(
                                                orderData
                                                        .assignedDriver
                                                        ?.driverCurrentLocation
                                                        ?.location
                                                        ?.coordinates?[1] ??
                                                    orderData.chef?.location
                                                        ?.coordinates?[1] ??
                                                    37.7749,
                                                orderData
                                                        .assignedDriver
                                                        ?.driverCurrentLocation
                                                        ?.location
                                                        ?.coordinates?[0] ??
                                                    orderData.chef?.location
                                                        ?.coordinates?[0] ??
                                                    -122.4194,
                                              ),
                                              zoom: 12,
                                            ),
                                            gestureRecognizers: <Factory<
                                                OneSequenceGestureRecognizer>>{
                                              Factory<OneSequenceGestureRecognizer>(
                                                  () =>
                                                      EagerGestureRecognizer()),
                                            },
                                            markers: snapshot.data!,
                                            polylines: _polylines,
                                            zoomControlsEnabled: true,
                                            myLocationEnabled: true,
                                            myLocationButtonEnabled: true,
                                            zoomGesturesEnabled: true,
                                            scrollGesturesEnabled: true,
                                            onMapCreated: (GoogleMapController
                                                controller) {
                                              _mapController = controller;

                                              // Slight delay to ensure map is fully loaded before updating bounds
                                              // Only set the bounds once when the map is initially created
                                              if (!_initialMapBoundsSet) {
                                                Future.delayed(
                                                    Duration(milliseconds: 300),
                                                    () {
                                                  if (mounted &&
                                                      _mapController != null) {
                                                    _updateMapBounds();
                                                  }
                                                });
                                              }
                                            },
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing),
                                ],
                                Container(
                                  padding: EdgeInsets.all(size.width * 0.05),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      // Status-based UI
                                      if (status == 'PREPARING') ...[
                                        Image.asset(
                                          'assets/images/cooking.png',
                                          height: cookingImageSize,
                                        ),
                                        SizedBox(height: itemSpacing * 0.5),
                                        Text(
                                          'Chef $chefName is preparing your order',
                                          style: TextStyle(
                                            fontSize: isLandscape
                                                ? size.height * 0.04
                                                : twenty,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black,
                                            fontFamily: 'Inter',
                                            height: 1.24,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                        SizedBox(height: itemSpacing * 0.5),
                                        Text(
                                          'Estimated arrival ${_formatTimeToAMPM(formattedDeliveryTime)}',
                                          style: TextStyle(
                                            fontSize: forteen,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Inter',
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                      ] else if (status == 'CONFIRMED') ...[
                                        Image.asset(
                                          'assets/images/confirmed_order.png',
                                          height: cookingImageSize,
                                        ),
                                        SizedBox(height: itemSpacing * 0.5),
                                        Text(
                                          'Order $orderNumber confirmed!',
                                          style: TextStyle(
                                            fontSize: isLandscape
                                                ? size.height * 0.04
                                                : twenty,
                                            fontWeight: FontWeight.w700,
                                            color: Colors.black,
                                            fontFamily: 'Inter',
                                            height: 1.24,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                        SizedBox(height: itemSpacing * 0.5),
                                        Text.rich(
                                          TextSpan(
                                            text:
                                                'We have received your order and Chef ',
                                            style: TextStyle(
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w400,
                                              fontFamily: 'Inter',
                                              color: const Color(0xFF1F2122),
                                            ),
                                            children: [
                                              TextSpan(
                                                text: chefName,
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                              TextSpan(
                                                text: ' is on it!',
                                              ),
                                            ],
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ] else if (status == 'ACTIVE') ...[
                                        Image.asset(
                                          'assets/images/confirmed_order.png',
                                          height: cookingImageSize,
                                        ),
                                        SizedBox(height: itemSpacing * 0.5),
                                        Text(
                                          'Order $orderNumber placed!',
                                          style: TextStyle(
                                            fontSize: isLandscape
                                                ? size.height * 0.04
                                                : twenty,
                                            fontWeight: FontWeight.w700,
                                            color: Colors.black,
                                            fontFamily: 'Inter',
                                            height: 1.24,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                        SizedBox(height: itemSpacing * 0.5),
                                        Text.rich(
                                          TextSpan(
                                            text:
                                                'We have received your order and Chef ',
                                            style: TextStyle(
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w400,
                                              fontFamily: 'Inter',
                                              color: const Color(0xFF1F2122),
                                            ),
                                            children: [
                                              TextSpan(
                                                text: chefName,
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                              TextSpan(
                                                text: ' is on it!',
                                              ),
                                            ],
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ] else if (status == 'PICKING_UP') ...[
                                        SizedBox(height: itemSpacing * 0.5),
                                        Text(
                                          'Driver is picking up your order',
                                          style: TextStyle(
                                            fontSize: isLandscape
                                                ? size.height * 0.04
                                                : twenty,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black,
                                            fontFamily: 'Inter',
                                            height: 1.24,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                        SizedBox(height: itemSpacing * 0.5),
                                        Text(
                                          'Estimated delivery ${_formatTimeToAMPM(formattedDeliveryTime)}',
                                          style: TextStyle(
                                            fontSize: forteen,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Inter',
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                      ] else if (status == 'DELIVERING') ...[
                                        SizedBox(height: itemSpacing * 0.5),
                                        Text(
                                          'Driver is now delivering your order',
                                          style: TextStyle(
                                            fontSize: isLandscape
                                                ? size.height * 0.04
                                                : twenty,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black,
                                            fontFamily: 'Inter',
                                            height: 1.24,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                        SizedBox(height: itemSpacing * 0.5),
                                        Text(
                                          'Estimated delivery ${_formatTimeToAMPM(formattedDeliveryTime)}',
                                          style: TextStyle(
                                            fontSize: forteen,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Inter',
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                      ] else if (status == 'DELIVERED') ...[
                                        Image.asset(
                                          'assets/images/delivered.png',
                                          height: cookingImageSize,
                                        ),
                                        SizedBox(height: itemSpacing * 0.5),
                                        Text(
                                          'Order successfully delivered!',
                                          style: TextStyle(
                                            fontSize: isLandscape
                                                ? size.height * 0.04
                                                : twenty,
                                            fontWeight: FontWeight.w700,
                                            color: Colors.black,
                                            fontFamily: 'Inter',
                                            height: 1.24,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ] else if (status == 'CANCELLED') ...[
                                        Image.asset(
                                          'assets/icons/cancel.png',
                                          height: cookingImageSize * 0.6,
                                        ),
                                        SizedBox(height: itemSpacing * 0.7),
                                        Text(
                                          'Order $orderNumber was cancelled',
                                          style: TextStyle(
                                            fontSize: isLandscape
                                                ? size.height * 0.04
                                                : twenty,
                                            fontWeight: FontWeight.w700,
                                            color: Colors.red,
                                            fontFamily: 'Inter',
                                            height: 1.24,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                        SizedBox(height: itemSpacing * 0.5),
                                        Text(
                                          'Please contact support for more details.',
                                          style: TextStyle(
                                            fontSize: forteen,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Inter',
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                      ] else ...[
                                        Text(
                                          'Your Order is ${status.replaceAll('_', ' ').toLowerCase()}',
                                          style: TextStyle(
                                            fontSize: isLandscape
                                                ? size.height * 0.04
                                                : twenty,
                                            fontWeight: FontWeight.w700,
                                            color: Colors.black,
                                            fontFamily: 'Inter',
                                            height: 1.24,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ],
                                      SizedBox(height: itemSpacing),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: List.generate(5, (index) {
                                          int filledBars = 1;
                                          switch (status) {
                                            case 'ACTIVE':
                                            case 'CANCELLED':
                                              filledBars = 1;
                                              break;
                                            case 'PREPARING':
                                            case 'PICKING_UP':
                                              filledBars = 3;
                                              break;
                                            case 'DELIVERING':
                                              filledBars = 4;
                                              break;
                                            case 'DELIVERED':
                                              filledBars = 5;
                                              break;
                                            default:
                                              filledBars = 1;
                                          }
                                          return Container(
                                            width: progressBarWidth,
                                            height: size.height * 0.01,
                                            margin: EdgeInsets.symmetric(
                                                horizontal: size.width * 0.019),
                                            decoration: BoxDecoration(
                                              color: index < filledBars
                                                  ? Colors.black
                                                  : const Color(0xFFD9D9D9),
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      size.width * 0.02),
                                            ),
                                          );
                                        }),
                                      ),
                                      SizedBox(height: itemSpacing),
                                      Text(
                                        'Your order will be delivered to $deliveryAddress at ${_formatTimeToAMPM(formattedDeliveryTime)}',
                                        style: TextStyle(
                                          fontSize: twelve,
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Inter',
                                          color: const Color(0xFF1F2122),
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      SizedBox(height: itemSpacing),
                                      // Driver Info (only for DELIVERING and PICKING_UP with valid driver data)
                                      if ((status == 'DELIVERING' ||
                                              status == 'PICKING_UP') &&
                                          orderData.assignedDriver != null &&
                                          (orderData.assignedDriver?.firstName
                                                  ?.isNotEmpty ??
                                              false ||
                                                  (orderData.assignedDriver
                                                              ?.lastName !=
                                                          null &&
                                                      orderData
                                                          .assignedDriver!
                                                          .lastName!
                                                          .isNotEmpty)))
                                        Container(
                                          width: driverContainerWidth,
                                          padding:
                                              EdgeInsets.all(size.width * 0.02),
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFF1F2F3),
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          child: Column(
                                            children: [
                                              Text(
                                                'Your Driver',
                                                style: TextStyle(
                                                  fontSize: forteen,
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Inter',
                                                  color:
                                                      const Color(0xFF414346),
                                                ),
                                              ),
                                              SizedBox(
                                                  height: itemSpacing * 0.2),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  CircleAvatar(
                                                    radius: isLandscape
                                                        ? size.height * 0.025
                                                        : baseTextSize,
                                                    child: ClipOval(
                                                      child: CachedNetworkImage(
                                                        imageUrl: orderData
                                                                .assignedDriver
                                                                ?.profilePictureUrl ??
                                                            '',
                                                        placeholder:
                                                            (context, url) =>
                                                                Container(
                                                          color:
                                                              Colors.grey[300],
                                                          child: const Center(
                                                            child:
                                                                CircularProgressIndicator(
                                                              strokeWidth: 2,
                                                            ),
                                                          ),
                                                        ),
                                                        errorWidget: (context,
                                                                url, error) =>
                                                            Image.asset(
                                                          'assets/images/driver.png',
                                                          fit: BoxFit.cover,
                                                        ),
                                                        fit: BoxFit.cover,
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(
                                                      width: size.width * 0.01),
                                                  Text(
                                                    '${orderData.assignedDriver?.firstName ?? ''} ${orderData.assignedDriver?.lastName ?? ''}',
                                                    style: TextStyle(
                                                      fontSize: forteen,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      fontFamily: 'Inter',
                                                      color: const Color(
                                                          0xFF1F2122),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              SizedBox(
                                                  height: itemSpacing * 0.2),
                                              Text(
                                                orderData.assignedDriver
                                                        ?.phone ??
                                                    'Phone not available',
                                                style: TextStyle(
                                                  fontSize: forteen,
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Inter',
                                                  color:
                                                      const Color(0xFF414346),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            'Need help? ',
                                            style: TextStyle(
                                              fontSize: twelve,
                                              fontFamily: 'Inter',
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                          GestureDetector(
                                            onTap: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      CustomerSupport(
                                                    orderId: Initializer
                                                            .viewOrderDetailsModel
                                                            .data
                                                            ?.id ??
                                                        0,
                                                  ),
                                                ),
                                              );
                                            },
                                            child: Text(
                                              'Contact support.',
                                              style: TextStyle(
                                                fontSize: twelve,
                                                fontFamily: 'Inter',
                                                fontWeight: FontWeight.w700,
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Order Summary card
                          Padding(
                            padding: EdgeInsets.all(size.width * 0.04),
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(size.width * 0.05),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Order Summary',
                                    style: TextStyle(
                                      fontSize: isLandscape
                                          ? size.height * 0.04
                                          : eighteen,
                                      fontWeight: FontWeight.w600,
                                      fontFamily: 'Inter',
                                      color: const Color(0xFF000000),
                                    ),
                                  ),
                                  DottedDivider(),
                                  SizedBox(height: itemSpacing * 0.3),
                                  Text(
                                    'Ordered From',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Row(
                                    children: [
                                      CircleAvatar(
                                        radius: isLandscape
                                            ? size.height * 0.015
                                            : twelve,
                                        backgroundImage:
                                            NetworkImage(chefAvatar),
                                      ),
                                      SizedBox(width: size.width * 0.02),
                                      Text(
                                        chefName,
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w600,
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  DottedDivider(),
                                  SizedBox(height: itemSpacing * 0.3),
                                  Text(
                                    'Order Details ($itemsCount Items)',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.8),
                                  if (orderData.items != null)
                                    ...orderData.items!.map((item) {
                                      return Column(
                                        children: [
                                          _buildOrderItem(
                                            '${item.quantity}x',
                                            item.dish?.name ?? 'Unknown Dish',
                                            '\$${item.totalPrice?.toStringAsFixed(2) ?? '0.00'}',
                                          ),
                                          SizedBox(height: itemSpacing * 0.5),
                                        ],
                                      );
                                    }),
                                  DottedDivider(),
                                  SizedBox(height: itemSpacing * 0.3),
                                  Text(
                                    'Promotions',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Text(
                                    (promotions.trim().isEmpty ||
                                            promotions == 'No Promotions')
                                        ? '- - -'
                                        : promotions,
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  DottedDivider(),
                                  SizedBox(height: itemSpacing * 0.3),
                                  Text(
                                    'Order Total',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.8),
                                  Column(
                                    children: [
                                      _buildTotalItem(
                                          'Subtotal', '\$$subtotal'),
                                      _buildTotalItem(
                                          'Delivery fee', '\$$deliveryFee'),
                                      _buildTotalItem(
                                          'Discounts', '-\$$discount'),
                                      _buildTotalItem('DB Wallet Credits',
                                          '-\$$walletCredits'),
                                      _buildTotalItem(
                                          'Taxes & Fees', '\$$taxesAndFees'),
                                      DottedDivider(),
                                      _buildTotalItem('Total', '\$$total',
                                          isTotal: true),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Delivery Details card
                          Padding(
                            padding: EdgeInsets.all(size.width * 0.04),
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(size.width * 0.05),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Delivery Details',
                                    style: TextStyle(
                                      fontSize: isLandscape
                                          ? size.height * 0.04
                                          : eighteen,
                                      fontWeight: FontWeight.w600,
                                      fontFamily: 'Inter',
                                      color: const Color(0xFF000000),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Text(
                                    'Address',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Row(
                                    children: [
                                      Icon(Icons.location_on_outlined,
                                          size: baseTextSize * 1.2,
                                          color: const Color(0xFF414346)),
                                      SizedBox(width: size.width * 0.01),
                                      Text(
                                        deliveryAddress.split(' ').length > 5
                                            ? '${deliveryAddress.split(' ').take(5).join(' ')}...'
                                            : deliveryAddress,
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Inter-medium',
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Text(
                                    'Delivery',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Row(
                                    children: [
                                      Icon(Icons.access_time,
                                          size: baseTextSize * 1.0,
                                          color: const Color(0xFF414346)),
                                      SizedBox(width: size.width * 0.01),
                                      Text(
                                        _formatTimeToAMPM(
                                            formattedDeliveryTime),
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w500,
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Divider(
                                    height: 0,
                                    thickness: 1,
                                    color: const Color(0xFFE1E3E6),
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Text(
                                    'Drop-Off Options',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontFamily: 'Inter-medium',
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Text(
                                    orderData.dropOffOption?.name ?? 'Unknown',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Inter',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Text(
                                    'Drop-Off Instructions',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontFamily: 'Inter-medium',
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Text(
                                    orderData.dropOffInstructions ?? 'None',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Divider(
                                    height: 0,
                                    thickness: 1,
                                    color: const Color(0xFFE1E3E6),
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Delivery Time',
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Inter-medium',
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                      Text(
                                        (orderData.deliveryTimes?.description ??
                                                '30-60 Minutes')
                                            .replaceFirst(
                                                'Delivery between ', ''),
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w500,
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Image.asset(
                                            'assets/icons/star_2.png',
                                            width: baseTextSize * 1.0,
                                            height: baseTextSize * 1.0,
                                            color: const Color(0xFF414346),
                                          ),
                                          SizedBox(width: size.width * 0.02),
                                          Text(
                                            orderData.deliveryTimes?.name ??
                                                'Priority',
                                            style: TextStyle(
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w500,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ],
                                      ),
                                      Text(
                                        '+\$${orderData.deliveryTimes?.cost ?? '0.00'}',
                                        style: TextStyle(
                                          fontSize: forteen,
                                          fontWeight: FontWeight.w600,
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: itemSpacing * 0.5),
                                  Text(
                                    (orderData.deliveryTimes?.description ??
                                            '30-60 Minutes')
                                        .replaceFirst('Delivery between ', ''),
                                    style: TextStyle(
                                      fontSize: twelve,
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF414346),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Payment card
                          Padding(
                            padding: EdgeInsets.all(size.width * 0.04),
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(size.width * 0.04),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Payment',
                                        style: TextStyle(
                                          fontSize: forteen * 1.0,
                                          fontWeight: FontWeight.w500,
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: const Color(0xFFE8F5E9),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Image.asset(
                                              'assets/icons/tick.png',
                                              width: 12,
                                              height: 12,
                                            ),
                                            SizedBox(width: 4),
                                            Text(
                                              'Paid',
                                              style: TextStyle(
                                                fontSize: twelve,
                                                color: const Color(0xFF2E7D32),
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: size.width * 0.03),
                                  Builder(
                                    builder: (_) {
                                      final walletCreditsVal =
                                          orderData.walletCredits ?? 0.0;
                                      final totalVal = orderData.total ?? 0.0;
                                      if (walletCreditsVal > 0 &&
                                          totalVal > 0) {
                                        return Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Image.asset(
                                                  'assets/icons/Visa.png',
                                                  height: baseTextSize * 1.2,
                                                  fit: BoxFit.contain,
                                                ),
                                                SizedBox(
                                                    width: size.width * 0.03),
                                                Expanded(
                                                  child: Text(
                                                    'Paid through card ending with 0001',
                                                    style: TextStyle(
                                                      fontSize: forteen,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      color: const Color(
                                                          0xFF1F2122),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: size.width * 0.01),
                                            Padding(
                                              padding: EdgeInsets.only(
                                                  left: size.width * 0.02),
                                              child: Text(
                                                '      Expires 03/2028',
                                                style: TextStyle(
                                                  fontSize: forteen,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      const Color(0xFF1F2122),
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: size.width * 0.02),
                                            Row(
                                              children: [
                                                Image.asset(
                                                  'assets/icons/payment.png',
                                                  height: baseTextSize * 1.2,
                                                  fit: BoxFit.contain,
                                                ),
                                                SizedBox(
                                                    width: size.width * 0.03),
                                                Expanded(
                                                  child: Text(
                                                    'Paid partially with DB Wallet',
                                                    style: TextStyle(
                                                      fontSize: forteen,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      color: const Color(
                                                          0xFF1F2122),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        );
                                      } else if (totalVal == 0 &&
                                          walletCreditsVal > 0) {
                                        return Row(
                                          children: [
                                            Image.asset(
                                              'assets/icons/payment.png',
                                              height: baseTextSize * 1.2,
                                              fit: BoxFit.contain,
                                            ),
                                            SizedBox(width: size.width * 0.03),
                                            Expanded(
                                              child: Text(
                                                'Paid through DB Wallet',
                                                style: TextStyle(
                                                  fontSize: forteen,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      const Color(0xFF1F2122),
                                                ),
                                              ),
                                            ),
                                          ],
                                        );
                                      } else if (walletCreditsVal == 0 &&
                                          totalVal > 0) {
                                        return Row(
                                          children: [
                                            Image.asset(
                                              'assets/icons/Visa.png',
                                              height: baseTextSize * 1.2,
                                              fit: BoxFit.contain,
                                            ),
                                            SizedBox(width: size.width * 0.03),
                                            Expanded(
                                              child: Text(
                                                'Paid through card ending with 0001',
                                                style: TextStyle(
                                                  fontSize: forteen,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      const Color(0xFF1F2122),
                                                ),
                                              ),
                                            ),
                                          ],
                                        );
                                      } else {
                                        return Text(
                                          'No payment information available',
                                          style: TextStyle(
                                            fontSize: forteen,
                                            fontWeight: FontWeight.w400,
                                            color: const Color(0xFF1F2122),
                                          ),
                                        );
                                      }
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Email notification
                          Padding(
                            padding: EdgeInsets.all(size.width * 0.04),
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(size.width * 0.02),
                              decoration: BoxDecoration(
                                color: const Color(0xFFE1DDD5),
                                borderRadius: BorderRadius.circular(6),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Align(
                                    alignment: Alignment.topCenter,
                                    child: Icon(
                                      Icons.mail_outline,
                                      size: baseTextSize * 1.25,
                                      color: const Color(0xFF414346),
                                    ),
                                  ),
                                  SizedBox(width: size.width * 0.02),
                                  Expanded(
                                    child: Text(
                                      'A copy of your invoice is on its way to your inbox. Please check your email.',
                                      style: TextStyle(
                                        fontSize: forteen,
                                        fontFamily: 'Inter',
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: screenHeight * 0.08),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Future<Set<Marker>> _createMarkers(orderData) async {
    final Set<Marker> markers = {};

    try {
      // Get or create chef icon
      if (!_iconCache.containsKey('chef')) {
        _iconCache['chef'] =
            await getResizedMarker('assets/icons/chef_icon.png', 100);
      }
      final chefIcon = _iconCache['chef']!;

      // Get or create driver icon
      if (!_iconCache.containsKey('driver')) {
        _iconCache['driver'] =
            await getResizedMarker('assets/icons/bike_icon.png', 100);
      }
      final driverIcon = _iconCache['driver']!;

      // Get or create customer icon
      if (!_iconCache.containsKey('customer')) {
        _iconCache['customer'] =
            await getResizedMarker('assets/icons/customer_icon.png', 100);
      }
      final customerIcon = _iconCache['customer']!;

      // Chef marker
      if (orderData.chef?.location?.coordinates != null &&
          orderData.chef!.location!.coordinates!.length >= 2) {
        markers.add(
          Marker(
            markerId: const MarkerId('chef'),
            position: LatLng(
              orderData.chef!.location!.coordinates![1],
              orderData.chef!.location!.coordinates![0],
            ),
            icon: chefIcon,
            infoWindow:
                InfoWindow(title: 'Chef: ${orderData.chef?.name ?? "Chef"}'),
          ),
        );
      }

      // Driver marker (if available)
      if (orderData.assignedDriver != null &&
          orderData.assignedDriver!.driverCurrentLocation != null &&
          orderData.assignedDriver!.driverCurrentLocation!.location != null &&
          orderData.assignedDriver!.driverCurrentLocation!.location!
                  .coordinates !=
              null &&
          orderData.assignedDriver!.driverCurrentLocation!.location!
                  .coordinates!.length >=
              2) {
        markers.add(
          Marker(
            markerId: const MarkerId('driver'),
            position: LatLng(
              orderData.assignedDriver!.driverCurrentLocation!.location!
                  .coordinates![1],
              orderData.assignedDriver!.driverCurrentLocation!.location!
                  .coordinates![0],
            ),
            icon: driverIcon,
            infoWindow: InfoWindow(
              title:
                  'Driver: ${orderData.assignedDriver?.firstName ?? ""} ${orderData.assignedDriver?.lastName ?? ""}',
            ),
            zIndex: 2, // Make driver marker appear on top
          ),
        );
      }

      // Customer marker
      if (orderData.address?.location?.coordinates != null &&
          orderData.address!.location!.coordinates!.length >= 2) {
        markers.add(
          Marker(
            markerId: const MarkerId("customer"),
            position: LatLng(
              orderData.address!.location!.coordinates![1],
              orderData.address!.location!.coordinates![0],
            ),
            icon: customerIcon,
            infoWindow: const InfoWindow(title: "Customer"),
          ),
        );
      }
    } catch (e) {
      print('Error creating markers: $e');
    }

    return markers;
  }

  Future<BitmapDescriptor> getResizedMarker(String path, int width) async {
    final ByteData data = await rootBundle.load(path);
    final codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    final frame = await codec.getNextFrame();
    final bytes = await frame.image.toByteData(format: ui.ImageByteFormat.png);
    return BitmapDescriptor.fromBytes(bytes!.buffer.asUint8List());
  }

  LatLngBounds _calculateBounds(LatLng chefLocation, LatLng customerLocation) {
    final double minLat =
        [chefLocation.latitude, customerLocation.latitude].reduce(math.min);
    final double maxLat =
        [chefLocation.latitude, customerLocation.latitude].reduce(math.max);
    final double minLng =
        [chefLocation.longitude, customerLocation.longitude].reduce(math.min);
    final double maxLng =
        [chefLocation.longitude, customerLocation.longitude].reduce(math.max);

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  Future<List<LatLng>> _getRouteCoordinates(
      LatLng origin, LatLng destination) async {
    const String apiKey = 'AIzaSyCpAdQaZ3fPe5H0wfkI0NqMXcT8J7AW9uY';
    final String url =
        'https://maps.googleapis.com/maps/api/directions/json?origin=${origin.latitude},${origin.longitude}&destination=${destination.latitude},${destination.longitude}&key=$apiKey';

    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'OK') {
          final String encodedPolyline =
              data['routes'][0]['overview_polyline']['points'];
          return _decodePolyline(encodedPolyline);
        }
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  List<LatLng> _decodePolyline(String encoded) {
    List<LatLng> polyline = [];
    int index = 0, len = encoded.length;
    int lat = 0, lng = 0;

    while (index < len) {
      int b, shift = 0, result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      polyline.add(LatLng(lat / 1e5, lng / 1e5));
    }
    return polyline;
  }

  // Calculate distance between two LatLng points (simple approximation)
  double _calculateDistance(LatLng point1, LatLng point2) {
    double deltaLat = point1.latitude - point2.latitude;
    double deltaLng = point1.longitude - point2.longitude;
    return math.sqrt(deltaLat * deltaLat + deltaLng * deltaLng);
  }
}
