import 'package:collection/collection.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/bloc/newmealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/ui/meal_plan/personailized_selectchef.dart';
import 'package:db_eats/ui/meal_plan_new/curated/choose_cuisines_new.dart';
import 'package:db_eats/ui/meal_plan_new/persoanilized/new_persoanilied_chef.dart';
import 'package:db_eats/ui/meal_plan_new/persoanilized/new_persoanilized_meal_plan_final.dart';
import 'package:flutter/material.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/data/models/meal_plan/servinglistmodel.dart';
import 'package:db_eats/data/models/meal_plan/timinglistmodel.dart';
import 'package:db_eats/data/models/meal_plan/existingmealplanmodel.dart';
import 'package:db_eats/data/models/new_meal_plan/mealplanprogresslatest.dart'
    as progress;

class ChoosePlanStep1 extends StatefulWidget {
  const ChoosePlanStep1({super.key});

  @override
  State<ChoosePlanStep1> createState() => _ChoosePlanStep1State();
}

class _ChoosePlanStep1State extends State<ChoosePlanStep1> {
  final _startDateController = TextEditingController();
  final _endDateController = TextEditingController();
  int _selectedServings = 0;
  int? _selectedServingId;
  String? _selectedTimeSlotId;
  int _numDishes = 0;
  bool _letChefChoose = false;
  bool _isLoading = false;
  bool _isDropdownExpanded = false;
  int? servingsno = 0;
  late List<String> _dates;
  int _currentDay = 0;
  late Map<String, Map<String, dynamic>> _mealData;
  late final MealplanBloc _mealPlanBloc;
  List<Timings> timeSlots = [];
  List<ServingSizes> servingSizes = [];
  String? _dateError;
  String? _timeSlotError;
  String? _servingError;
  String? _dishError;
  String? _weekdayError;
  String? _planError;
  String _selectedTimeSlotFormatted = '';
  int? _existingMealPlanId;
  DateTime? _startDate;
  DateTime? _endDate;
  String? _existingEndDate;
  int _selectedPlanDays = 5;
  Color kBlack = Color(0xFF1F2122);
  Color kSecondBlack = Color(0xFF414346);
  // Weekday selection
  Set<int> _selectedWeekdays = <int>{1, 2, 3, 4, 5};
  final List<String> _weekdayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];
  bool noChangesMade = false;

  @override
  void initState() {
    super.initState();
    _mealPlanBloc = BlocProvider.of<MealplanBloc>(context);
    context.read<NewmealplanBloc>().add(ListNewMealPlanSummaryEvent());
    _mealPlanBloc.add(ListTimingEvent());
    _mealPlanBloc.add(ListServingEvent());

    _dates = [];
    _mealData = {};
  }

  void _generateDatesFromRange() {
    if (_startDate == null || _endDate == null) return;

    _dates.clear();
    DateTime current = _startDate!;

    DateTime endDateInclusive = _endDate!.add(const Duration(days: 1));

    while (current.isBefore(endDateInclusive)) {
      if (current.weekday >= 1 &&
          current.weekday <= 5 &&
          _selectedWeekdays.contains(current.weekday)) {
        _dates.add(_formatStorageDate(current));
      }
      current = current.add(const Duration(days: 1));
    }

    _mealData = {
      for (var date in _dates)
        date: {
          "chefDetails": {"name": null, "image": null},
          "selectedDishes": <Map<String, dynamic>>[],
        }
    };

    // print(
    //     'Date range: ${_formatDisplayDate(_startDate!)} to ${_formatDisplayDate(_endDate!)}');
    // print('Selected weekdays: $_selectedWeekdays');
    // print('Generated delivery dates: $_dates');
    // print('Number of delivery days: ${_dates.length}');

    _validateDateRangeForPlan();
  }

  Future<void> _navigateBack() async {
    final savedFilters = await Initializer.getAppliedFilters();

    final requestData = <String, dynamic>{
      'latitude': Initializer.latitude,
      'longitude': Initializer.longitude,
    };

    if (savedFilters != null) {
      requestData.addAll(savedFilters);
    }

    context.read<HomeBloc>().add(GetHomeDataEvent(data: requestData));
    Navigator.pop(context);
  }

  void _validateDateRangeForPlan() {
    if (_startDate == null || _endDate == null || _selectedPlanDays == 0)
      return;

    final deliveryDaysCount = _dates.length;

    setState(() {
      if (deliveryDaysCount == 0) {
        _dateError =
            'No delivery days found in the selected date range. Please check your weekday selection.';
      } else if (deliveryDaysCount < _selectedPlanDays) {
        _dateError =
            'Date range must cover at least $_selectedPlanDays delivery days for the ${_selectedPlanDays}-day plan. Current range covers $deliveryDaysCount delivery days.';
      } else {
        // Clear date error if validation passes
        if (_dateError != null &&
            (_dateError!.contains('delivery days') ||
                _dateError!.contains('No delivery days') ||
                _dateError!.contains('Date range must cover'))) {
          _dateError = null;
        }
      }
    });
  }

  ExistingMealplandata? _convertToExistingMealPlanData(
      progress.MealPlanProgressLatestData? progressData) {
    if (progressData == null) return null;

    List<PersonalizedDays>? personalizedDays;
    if (progressData.selectedWeekdays != null &&
        progressData.selectedWeekdays!.isNotEmpty) {
      personalizedDays = progressData.selectedWeekdays!.map((weekday) {
        return PersonalizedDays(
          date: null,
          dayOfWeek: weekday,
        );
      }).toList();
    }

    return ExistingMealplandata(
      id: progressData.id,
      stepProgress: progressData.stepProgress,
      createdAt: progressData.createdAt,
      customerId: progressData.customerId,
      mealPlanDuration: progressData.mealPlanDuration,
      endDate: progressData.endDate,
      startDate: progressData.startDate,
      timeSlotId: progressData.timeSlotId,
      servingSizeId: progressData.servingSizeId,
      dishesPerDay: progressData.dishesPerDay,
      mealSelectionType: progressData.mealSelectionType,
      dietaryPreferenceId: progressData.dietaryPreferenceId,
      spiceLevelId: progressData.spiceLevelId,
      dropOffOptionId: progressData.dropOffOptionId,
      dropOffInstructions: progressData.dropOffInstructions,
      deliveryTimeId: progressData.deliveryTimeId != null
          ? progressData.deliveryTimeId as int?
          : null,
      subtotal: progressData.subtotal?.toString(),
      deliveryFee: progressData.deliveryFee?.toString(),
      discount: progressData.discount?.toString(),
      walletCredits: progressData.walletCredits?.toString(),
      taxesAndFees: progressData.taxesAndFees?.toString(),
      total: progressData.total?.toString(),
      status: progressData.status,
      totalDiscount: progressData.totalDiscount?.toDouble(),
      personalizedDays: personalizedDays,
      timeSlot: progressData.timeSlot != null
          ? TimeSlot(
              id: progressData.timeSlot!.id,
              startTime: progressData.timeSlot!.startTime,
              endTime: progressData.timeSlot!.endTime,
            )
          : null,
      servingSize: progressData.servingSize != null
          ? ServingSize(
              id: progressData.servingSize!.id,
              title: progressData.servingSize!.title,
              serves: progressData.servingSize!.serves,
            )
          : null,
    );
  }

  void _populateExistingMealPlan(ExistingMealplandata? mealPlanData) {
    if (mealPlanData == null) {
      setState(() {
        _mealData = {};
      });
      return;
    }

    setState(() {
      _existingMealPlanId = mealPlanData.id;
      _existingEndDate = mealPlanData.endDate;

      final progressData = Initializer.mealPlanProgressLatestModel.data;
      if (progressData?.selectedWeekdays != null) {
        final weekdayCount = progressData!.selectedWeekdays!.length;

        if (weekdayCount == 3) {
          _selectedPlanDays = 3;
        } else if (weekdayCount == 5) {
          _selectedPlanDays = 5;
        } else {
          // Fallback to meal plan duration or default
          _selectedPlanDays = mealPlanData.mealPlanDuration ?? 5;
        }

        _selectedWeekdays.clear();
        final weekdayMapping = {
          'MONDAY': 1,
          'TUESDAY': 2,
          'WEDNESDAY': 3,
          'THURSDAY': 4,
          'FRIDAY': 5,
        };

        for (String weekdayName in progressData.selectedWeekdays!) {
          final weekdayNumber = weekdayMapping[weekdayName];
          if (weekdayNumber != null) {
            _selectedWeekdays.add(weekdayNumber);
          }
        }
      } else {
        if (mealPlanData.mealPlanDuration != null) {
          _selectedPlanDays = mealPlanData.mealPlanDuration!;
        } else if (mealPlanData.personalizedDays != null) {
          _selectedPlanDays = mealPlanData.personalizedDays!.length;
        } else {
          _selectedPlanDays = 5;
        }

        if (_selectedPlanDays == 5) {
          _selectedWeekdays = {1, 2, 3, 4, 5};
        } else if (_selectedPlanDays == 3) {
          if (mealPlanData.personalizedDays != null &&
              mealPlanData.personalizedDays!.isNotEmpty) {
            _selectedWeekdays.clear();
            for (var day in mealPlanData.personalizedDays!) {
              if (day.date != null) {
                DateTime date = DateTime.parse(day.date!);
                _selectedWeekdays.add(date.weekday);
              }
            }
            if (_selectedWeekdays.length > 3) {
              _selectedWeekdays = _selectedWeekdays.take(3).toSet();
            }
          } else {
            _selectedWeekdays = {1, 3, 5};
          }
        }
      }

      if (mealPlanData.startDate != null) {
        DateTime startDate = DateTime.parse(mealPlanData.startDate!);

        if (startDate.isBefore(DateTime.now().subtract(Duration(days: 1)))) {
          DateTime today = DateTime.now();
          DateTime nextValidDate = today;

          while (!_selectedWeekdays.contains(nextValidDate.weekday) ||
              nextValidDate.weekday > 5 ||
              nextValidDate.weekday < 1) {
            nextValidDate = nextValidDate.add(Duration(days: 1));
          }

          _startDate = nextValidDate;
        } else {
          _startDate = startDate;
        }

        _startDateController.text = _formatDisplayDate(_startDate!);

        if (_existingEndDate != null) {
          DateTime endDate = DateTime.parse(_existingEndDate!);

          if (endDate.isBefore(_startDate!) ||
              endDate.isAtSameMomentAs(_startDate!)) {
            int daysToAdd = _selectedPlanDays * 2;
            _endDate = _startDate!.add(Duration(days: daysToAdd));
          } else {
            _endDate = endDate;
          }

          _endDateController.text = _formatDisplayDate(_endDate!);
        }

        _generateDatesFromRange();
      }

      if (mealPlanData.timeSlotId != null) {
        _selectedTimeSlotId = mealPlanData.timeSlotId.toString();
        if (mealPlanData.timeSlot != null) {
          _selectedTimeSlotFormatted =
              '${_formatTimeToAmPm(mealPlanData.timeSlot!.startTime)} - ${_formatTimeToAmPm(mealPlanData.timeSlot!.endTime)}';
        }
      }
      if (mealPlanData.servingSizeId != null) {
        _selectedServingId = mealPlanData.servingSizeId;
        if (mealPlanData.servingSize != null) {
          _selectedServings = mealPlanData.servingSize!.serves ?? 0;
        }
      }
      if (mealPlanData.dishesPerDay != null) {
        _numDishes = mealPlanData.dishesPerDay!;
      }
      _letChefChoose = mealPlanData.mealSelectionType == "CURATED";

      if (mealPlanData.personalizedDays != null &&
          mealPlanData.personalizedDays!.isNotEmpty) {
        for (var day in mealPlanData.personalizedDays!) {
          if (day.date != null && day.chef != null) {
            _mealData[day.date!] = {
              "chefDetails": {
                "name": "${day.chef!.firstName} ${day.chef!.lastName}",
                "image": day.chef!.profilePhoto,
              },
              "selectedDishes": day.items
                      ?.map((item) => {
                            "id": item.id,
                            "name": item.menuItem?.name,
                          })
                      .toList() ??
                  [],
            };
          }
        }
      }
    });
  }

  @override
  void dispose() {
    _startDateController.dispose();
    _endDateController.dispose();
    super.dispose();
  }

  String _formatDisplayDate(DateTime date) {
    return "${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}";
  }

  String _formatStorageDate(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  String _formatTimeToAmPm(String? time) {
    if (time == null) return '';
    try {
      final timeParts = time.split(':');
      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);
      String period = hour >= 12 ? 'PM' : 'AM';
      hour = hour > 12 ? hour - 12 : hour;
      hour = hour == 0 ? 12 : hour;
      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return time;
    }
  }

  String _getDateRangeText() {
    if (_startDate == null && _endDate == null) {
      return 'Select date range';
    } else if (_startDate != null && _endDate == null) {
      return '${_formatDisplayDate(_startDate!)} - Select end date';
    } else if (_startDate != null && _endDate != null) {
      return '${_formatDisplayDate(_startDate!)} - ${_formatDisplayDate(_endDate!)}';
    } else {
      return 'Select date range';
    }
  }

  List<String> _convertWeekdaysToNames() {
    final weekdayNames = {
      1: 'MONDAY',
      2: 'TUESDAY',
      3: 'WEDNESDAY',
      4: 'THURSDAY',
      5: 'FRIDAY',
    };

    return _selectedWeekdays.map((weekday) => weekdayNames[weekday]!).toList();
  }

  Future<void> _selectDateRange(BuildContext context) async {
    if (_startDate == null) {
      await _selectStartDateInternal(context);
      return;
    }

    if (_endDate == null) {
      await _selectEndDateInternal(context);
      return;
    }
    final choice = await showDialog<String>(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final screenHeight = MediaQuery.of(context).size.height;

        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
          child: Container(
            padding: EdgeInsets.all(screenWidth * 0.06),
            decoration: BoxDecoration(
              color: const Color(0xFFF6F3EC),
              borderRadius: BorderRadius.circular(screenWidth * 0.04),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Select Date to Change',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                    fontSize: screenWidth * 0.045,
                    color: kBlack,
                    letterSpacing: 0.5,
                  ),
                ),
                SizedBox(height: screenHeight * 0.01),
                Text(
                  'Which date would you like to change?',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: screenWidth * 0.035,
                    color: kSecondBlack,
                    height: 1.4,
                  ),
                ),
                SizedBox(height: screenHeight * 0.025),
                _buildDialogButton(
                  'Start Date',
                  () => Navigator.of(context).pop('start'),
                  screenWidth,
                  screenHeight,
                ),
                SizedBox(height: screenHeight * 0.015),
                _buildDialogButton(
                  'End Date',
                  () => Navigator.of(context).pop('end'),
                  screenWidth,
                  screenHeight,
                ),
                SizedBox(height: screenHeight * 0.015),
                _buildDialogButton(
                  'Both Dates',
                  () => Navigator.of(context).pop('both'),
                  screenWidth,
                  screenHeight,
                ),
                SizedBox(height: screenHeight * 0.02),
                // Cancel Button
                InkWell(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    width: double.infinity,
                    padding:
                        EdgeInsets.symmetric(vertical: screenHeight * 0.015),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(screenWidth * 0.03),
                      border: Border.all(
                        color: const Color(0xFFD2D4D7),
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        'Cancel',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w500,
                          fontSize: screenWidth * 0.04,
                          color: kSecondBlack,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );

    if (choice == 'start') {
      await _selectStartDateInternal(context);
    } else if (choice == 'end') {
      await _selectEndDateInternal(context);
    } else if (choice == 'both') {
      setState(() {
        _startDate = null;
        _endDate = null;
        _startDateController.clear();
        _endDateController.clear();
      });
      await _selectStartDateInternal(context);
    }
  }

  Future<void> _selectStartDateInternal(BuildContext context) async {
    final screenWidth = MediaQuery.of(context).size.width;

    DateTime initialDate = DateTime.now();
    if (_startDate != null && !_startDate!.isBefore(DateTime.now())) {
      initialDate = _startDate!;
    }
    while (initialDate.weekday > 5) {
      initialDate = initialDate.add(const Duration(days: 1));
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      selectableDayPredicate: (DateTime date) {
        return date.weekday >= 1 && date.weekday <= 5;
      },
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF1F2122),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF1F2122),
            ),
            textTheme: TextTheme(
              bodyMedium: TextStyle(fontSize: screenWidth * 0.04),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _dateError = null;
        _startDate = picked;
        _startDateController.text = _formatDisplayDate(picked);

        if (_endDate != null && _endDate!.isBefore(picked)) {
          _endDate = null;
          _endDateController.clear();
        }

        _generateDatesFromRange();
      });

      if (_endDate == null) {
        await _selectEndDateInternal(context);
      }
    }
  }

  Future<void> _selectEndDateInternal(BuildContext context) async {
    if (_startDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select start date first')),
      );
      return;
    }

    final screenWidth = MediaQuery.of(context).size.width;

    DateTime initialDate = _startDate!.add(const Duration(days: 1));
    if (_endDate != null && _endDate!.isAfter(_startDate!)) {
      initialDate = _endDate!;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: _startDate!.add(const Duration(days: 1)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      selectableDayPredicate: (DateTime date) {
        return date.weekday >= 1 && date.weekday <= 5;
      },
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF1F2122),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF1F2122),
            ),
            textTheme: TextTheme(
              bodyMedium: TextStyle(fontSize: screenWidth * 0.04),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _dateError = null;
        _endDate = picked;
        _endDateController.text = _formatDisplayDate(picked);
        _generateDatesFromRange();
      });
    }
  }

  void _clearErrors() {
    setState(() {
      _dateError = null;
      _timeSlotError = null;
      _servingError = null;
      _dishError = null;
      _weekdayError = null;
      _planError = null;
    });
  }

  bool _validateFields() {
    _clearErrors();
    bool isValid = true;
    setState(() {
      if (_selectedPlanDays == 0) {
        _planError = 'Please select a meal plan duration';
        isValid = false;
      } else if (_startDate == null) {
        _dateError = 'Please select a start date';
        isValid = false;
      } else if (_endDate == null) {
        _dateError = 'Please select an end date';
        isValid = false;
      } else if (_dates.isEmpty) {
        _dateError =
            'No delivery days found in the selected date range. Please check your weekday selection.';
        isValid = false;
      } else if (_dates.length < _selectedPlanDays) {
        _dateError =
            'Date range must cover at least $_selectedPlanDays delivery days for the ${_selectedPlanDays}-day plan. Current range covers ${_dates.length} delivery days.';
        isValid = false;
      } else if (_selectedTimeSlotId == null) {
        _timeSlotError = 'Please select a delivery time slot';
        isValid = false;
      } else if (_selectedServingId == null || _selectedServings == 0) {
        _servingError = 'Please select number of servings';
        isValid = false;
      } else if (_numDishes == 0) {
        _dishError = 'Please select number of dishes';
        isValid = false;
      } else if (_selectedWeekdays.isEmpty) {
        _weekdayError = 'Please select at least one weekday';
        isValid = false;
      } else if (_selectedPlanDays == 3 && _selectedWeekdays.length != 3) {
        _weekdayError = 'Please select exactly 3 weekdays for the 3-day plan';
        isValid = false;
      } else if (_selectedPlanDays == 5 && _selectedWeekdays.length != 5) {
        _weekdayError = 'All 5 weekdays must be selected for the 5-day plan';
        isValid = false;
      }
    });
    return isValid;
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return WillPopScope(
      onWillPop: () async {
        await _navigateBack();
        return false;
      },
      child: MultiBlocListener(
        listeners: [
          BlocListener<MealplanBloc, MealPlanState>(
            listener: (context, state) {
              if (state is ListTimingSuccess) {
                final timingData = state.data as TimingListModel;
                setState(() {
                  timeSlots = timingData.data?.timings ?? [];
                });
              }
              if (state is ListServingSuccess) {
                final servingData = state.data as ServingsListModel;
                setState(() {
                  servingSizes = servingData.data?.servingSizes ?? [];
                });
              }
            },
          ),
          BlocListener<NewmealplanBloc, NewMealPlanState>(
            listener: (context, state) {
              if (state is NewMealPlanSummaryLoaded) {
                final progressData =
                    Initializer.mealPlanProgressLatestModel.data;
                final existingMealPlanData =
                    _convertToExistingMealPlanData(progressData);

                // Store initial loaded values for comparison
                final initialValues = {
                  "planDays": existingMealPlanData?.mealPlanDuration,
                  "startDate": existingMealPlanData?.startDate,
                  "endDate": existingMealPlanData?.endDate,
                  "timeSlotId": existingMealPlanData?.timeSlotId,
                  "servingSizeId": existingMealPlanData?.servingSizeId,
                  "dishesPerDay": existingMealPlanData?.dishesPerDay,
                  "mealSelectionType": existingMealPlanData?.mealSelectionType,
                  "selectedWeekdays": progressData?.selectedWeekdays,
                };

                _populateExistingMealPlan(existingMealPlanData);
                final listEquality = const ListEquality();

                final initialWeekdays =
                    (initialValues["selectedWeekdays"] as List?)
                            ?.map((e) => e.toString())
                            .toList() ??
                        [];
                final currentWeekdays = _convertWeekdaysToNames();

                setState(() {
                  noChangesMade = _selectedPlanDays == initialWeekdays.length &&
                      _dateOnly(_startDate?.toIso8601String()) ==
                          _dateOnly(initialValues["startDate"] as String?) &&
                      _dateOnly(_endDate?.toIso8601String()) ==
                          _dateOnly(initialValues["endDate"] as String?) &&
                      _selectedTimeSlotId ==
                          initialValues["timeSlotId"]?.toString() &&
                      _selectedServingId == initialValues["servingSizeId"] &&
                      _numDishes == initialValues["dishesPerDay"] &&
                      _letChefChoose == false &&
                      listEquality.equals(
                        List.from(currentWeekdays)..sort(),
                        List.from(initialWeekdays)..sort(),
                      );
                });
              } else if (state is NewMealPlanSummaryLoadFailed) {
                setState(() {
                  _mealData = {};
                });
              }
              if (state is NewMealPlanStep1StateLoading) {
                setState(() {
                  _isLoading = true;
                });
              } else if (state is NewMealPlanStep1StateSuccess) {
                final response = state.response;

                setState(() {
                  _isLoading = false;
                  _existingMealPlanId = response?['data']?['id'];
                });

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.response != null &&
                            state.response['message'] != null &&
                            (state.response['message'] as String).isNotEmpty
                        ? state.response['message']
                        : 'Meal plan created successfully!'),
                    backgroundColor: Colors.green,
                    duration: Duration(seconds: 2),
                  ),
                );

                final mealPlanData = response?['data'];
                final isPersonalized = mealPlanData?['meal_selection_type']
                        ?.toString()
                        .toUpperCase() ==
                    "PERSONALIZED";

                final initialData =
                    Initializer.mealPlanProgressLatestModel.data;

                final dishesChanged = mealPlanData?['dishes_per_day'] !=
                    initialData?.dishesPerDay;
                final servingSizeChanged = mealPlanData?['serving_size_id'] !=
                    initialData?.servingSizeId;
                final timeSlotChanged =
                    mealPlanData?['time_slot_id'] != initialData?.timeSlotId;
                final startDateChanged =
                    _dateOnly(mealPlanData?['start_date'] as String?) !=
                        _dateOnly(initialData?.startDate);
                final endDateChanged =
                    _dateOnly(mealPlanData?['end_date'] as String?) !=
                        _dateOnly(initialData?.endDate);

                bool weekdaysChanged = false;
                if (mealPlanData?['selected_weekdays'] != null &&
                    initialData?.selectedWeekdays != null) {
                  final listEquality = const ListEquality();
                  final currentWeekdays =
                      List<String>.from(mealPlanData?['selected_weekdays']);
                  final initialWeekdays =
                      List<String>.from(initialData?.selectedWeekdays ?? []);

                  currentWeekdays.sort();
                  initialWeekdays.sort();

                  weekdaysChanged =
                      !listEquality.equals(currentWeekdays, initialWeekdays);
                }

                final anyChanges = dishesChanged ||
                    servingSizeChanged ||
                    timeSlotChanged ||
                    startDateChanged ||
                    endDateChanged ||
                    weekdaysChanged;

                if (anyChanges) {
                  setState(() {
                    noChangesMade = false;
                  });
                }

                // print('Field changes check:');
                // print(
                //     '- Dishes changed: $dishesChanged (${mealPlanData?['dishes_per_day']} vs ${initialData?.dishesPerDay})');
                // print(
                //     '- ServingSize changed: $servingSizeChanged (${mealPlanData?['serving_size_id']} vs ${initialData?.servingSizeId})');
                // print(
                //     '- TimeSlot changed: $timeSlotChanged (${mealPlanData?['time_slot_id']} vs ${initialData?.timeSlotId})');
                // print(
                //     '- StartDate changed: $startDateChanged (${_dateOnly(mealPlanData?['start_date'] as String?)} vs ${_dateOnly(initialData?.startDate)})');
                // print(
                //     '- EndDate changed: $endDateChanged (${_dateOnly(mealPlanData?['end_date'] as String?)} vs ${_dateOnly(initialData?.endDate)})');
                // print('- Weekdays changed: $weekdaysChanged');
                // print(
                //     '- Any changes detected: $anyChanges, noChangesMade: $noChangesMade');

                if (mealPlanData != null && mealPlanData['id'] != null) {
                  // print('Navigating with mealPlanData: $mealPlanData');
                  // print(
                  //     'isPersonalized: $isPersonalized, noChangesMade: $noChangesMade, _letChefChoose: $_letChefChoose');

                  Future.delayed(Duration(milliseconds: 100), () {
                    if (_letChefChoose) {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => ChooseCuisinesNew(
                            mealPlanId: mealPlanData['id'],
                          ),
                        ),
                      );
                    } else if (isPersonalized && noChangesMade) {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => NewPersoanilizedMealPlanFinal(
                            mealPlanId: mealPlanData['id'],
                          ),
                        ),
                      );
                    } else if (isPersonalized && !noChangesMade) {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => NewPersoaniliedChef(
                            mealPlanId: mealPlanData['id'],
                            dates: _dates,
                            currentday: _currentDay,
                            mealdata: _mealData,
                          ),
                        ),
                      );
                    }
                    // else: do nothing for other types
                  });
                } else {
                  // print('Navigation skipped - meal plan data or ID is null');
                  // print('mealPlanData: $mealPlanData');
                  // print('mealPlanData id: ${mealPlanData?['id']}');
                  // print(
                  //     'isPersonalized: $isPersonalized, noChangesMade: $noChangesMade, _letChefChoose: $_letChefChoose');
                }
              } else if (state is NewMealPlanStep1StateLoadFailed) {
                setState(() {
                  _isLoading = false;
                });

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.error.isNotEmpty
                        ? state.error
                        : 'Failed to create meal plan. Please try again.'),
                    backgroundColor: Colors.red,
                    duration: Duration(seconds: 3),
                  ),
                );
              }
            },
          ),
        ],
        child: Scaffold(
          backgroundColor: const Color(0xFFF6F3EC),
          appBar: AppBar(
            backgroundColor: const Color(0xFFF6F3EC),
            elevation: 0,
            scrolledUnderElevation: 0,
            surfaceTintColor: Colors.transparent,
            automaticallyImplyLeading: false,
            centerTitle: true,
            title: Image.asset(
              'assets/logo.png',
              width: 112,
              height: 29,
              fit: BoxFit.contain,
            ),
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(screenHeight * 0.002),
              child: Divider(
                color: Colors.grey[300],
                height: screenHeight * 0.002,
              ),
            ),
          ),
          body: Column(
            children: [
              SizedBox(height: screenHeight * 0.02),
              Padding(
                padding: EdgeInsets.fromLTRB(screenWidth * 0.04, 0,
                    screenWidth * 0.04, screenHeight * 0.03),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWell(
                      onTap: () => _navigateBack(),
                      child: Icon(Icons.close, size: screenWidth * 0.06),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Stack(
                      children: [
                        Container(
                          height: screenHeight * 0.01,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: const Color(0xFFE1DDD5),
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.01),
                          ),
                        ),
                        FractionallySizedBox(
                          widthFactor: 0.25,
                          child: Container(
                            height: screenHeight * 0.01,
                            decoration: BoxDecoration(
                              color: const Color(0xFF007A4D),
                              borderRadius:
                                  BorderRadius.circular(screenWidth * 0.01),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: screenHeight * 0.025),
                    Text(
                      '1 of 4',
                      style: TextStyle(
                        fontSize: screenWidth * 0.035,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: const Color(0xFF414346),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.all(screenWidth * 0.04),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildPlanSelection(screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.025),
                        _buildWeekdaySelection(screenWidth, screenHeight),
                        // SizedBox(height: screenHeight * 0.025),
                        _buildDateSelection(screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.025),
                        _buildDeliveryTimeSlot(screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.035),
                        _buildServingsSelection(screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.025),
                        _buildDishesPerDaySelector(screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.025),
                        _buildMealSelectionType(screenWidth, screenHeight),
                        SizedBox(height: screenHeight * 0.03),
                        _buildNavigationButtons(screenWidth, screenHeight),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDateSelection(double screenWidth, double screenHeight) {
    final double forteen = screenWidth * 0.035;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: screenHeight * 0.03),
        Text(
          'Select Date Range',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: forteen,
            color: kBlack,
            letterSpacing: 0.02 * screenWidth * 0.035,
          ),
        ),
        SizedBox(height: screenHeight * 0.01),
        GestureDetector(
          onTap: () => _selectDateRange(context),
          child: Container(
            height: screenHeight * 0.05,
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth * 0.075),
              border: Border.all(
                color: const Color(0xFFD2D4D7),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    _getDateRangeText(),
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: screenWidth * 0.035,
                      fontWeight: FontWeight.w400,
                      color: _startDate != null && _endDate != null
                          ? kBlack
                          : const Color(0xFF66696D),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Image.asset(
                  'assets/icons/calender.png',
                  width: screenWidth * 0.04,
                  height: screenWidth * 0.04,
                ),
              ],
            ),
          ),
        ),
        if (_dateError != null)
          Padding(
            padding: EdgeInsets.only(top: screenHeight * 0.01),
            child: Text(
              _dateError!,
              style: TextStyle(
                color: const Color(0xFFE11900),
                fontSize: screenWidth * 0.03,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        // if (_startDate != null && _endDate != null)
        //   Padding(
        //     padding: EdgeInsets.only(top: screenHeight * 0.01),
        //     child: Text(
        //       'Delivery days in selected range: ${_dates.length}',
        //       style: TextStyle(
        //         color: const Color(0xFF007A4D),
        //         fontSize: screenWidth * 0.03,
        //         fontFamily: 'Inter',
        //         fontWeight: FontWeight.w400,
        //       ),
        //     ),
        //   ),
      ],
    );
  }

  Widget _buildWeekdaySelection(double screenWidth, double screenHeight) {
    final double forteen = screenWidth * 0.035;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Delivery Days',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: forteen,
            letterSpacing: 0.02 * screenWidth * 0.035,
            color: kBlack,
          ),
        ),
        SizedBox(height: screenHeight * 0.015),
        Text(
          _selectedPlanDays == 5
              ? 'All weekdays are selected for 5-day plan (Mon-Fri):'
              : _selectedPlanDays == 3
                  ? 'Choose exactly 3 weekdays for delivery (${_selectedWeekdays.length}/3 selected):'
                  : 'Choose which weekdays you want meals delivered (weekends excluded):',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w400,
            fontSize: screenWidth * 0.03,
            color: kSecondBlack,
          ),
        ),
        SizedBox(height: screenHeight * 0.015),
        Wrap(
          spacing: screenWidth * 0.02,
          runSpacing: screenHeight * 0.01,
          children: List.generate(5, (index) {
            final weekday = index + 1; // Monday = 1, Friday = 5
            final isSelected = _selectedWeekdays.contains(weekday);
            final dayName = _weekdayNames[index];

            return InkWell(
              onTap: () {
                if (_selectedPlanDays == 5) {
                  return;
                }

                if (_selectedPlanDays == 3) {
                  setState(() {
                    _weekdayError = null;
                    if (isSelected) {
                      _selectedWeekdays.remove(weekday);
                    } else {
                      if (_selectedWeekdays.length < 3) {
                        _selectedWeekdays.add(weekday);
                      }
                    }
                    _generateDatesFromRange();
                  });
                } else {
                  setState(() {
                    _weekdayError = null;
                    if (isSelected) {
                      _selectedWeekdays.remove(weekday);
                    } else {
                      _selectedWeekdays.add(weekday);
                    }
                    _generateDatesFromRange();
                  });
                }
              },
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: screenWidth * 0.04,
                  vertical: screenHeight * 0.012,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(0xFFE1DDD5)
                      : const Color(0xFFF6F3EC),
                  border: Border.all(
                    color: isSelected
                        ? const Color(0xFF1F2122)
                        : const Color(0xFFB9B6AD),
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(screenWidth * 0.02),
                ),
                child: Text(
                  dayName,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                    fontSize: forteen,
                    color: const Color(0xFF1F2122),
                  ),
                ),
              ),
            );
          }),
        ),
        if (_weekdayError != null)
          Padding(
            padding: EdgeInsets.only(top: screenHeight * 0.01),
            child: Text(
              _weekdayError!,
              style: TextStyle(
                color: const Color(0xFFE11900),
                fontSize: screenWidth * 0.03,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDeliveryTimeSlot(double screenWidth, double screenHeight) {
    final double twelve = screenWidth * (12 / 13.751 * 0.035);
    final double forteen = screenWidth * 0.035;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Delivery Time Slot',
          style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w500,
              fontSize: forteen,
              letterSpacing: 0.02 * screenWidth * 0.035,
              color: kBlack),
        ),
        SizedBox(height: screenHeight * 0.01),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(screenWidth * 0.075),
            border: Border.all(
              color: const Color(0xFFD2D4D7),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isDropdownExpanded = !_isDropdownExpanded;
                  });
                },
                child: Container(
                  height: screenHeight * 0.05,
                  padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(screenWidth * 0.075),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _selectedTimeSlotFormatted.isNotEmpty
                            ? _selectedTimeSlotFormatted
                            : 'Select...',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: screenWidth * 0.035,
                          fontWeight: FontWeight.w400,
                          color: _selectedTimeSlotFormatted.isNotEmpty
                              ? kBlack
                              : const Color(0xFF66696D),
                        ),
                      ),
                      AnimatedRotation(
                        turns: _isDropdownExpanded ? 0.5 : 0.0,
                        duration: const Duration(milliseconds: 200),
                        child: Image.asset(
                          'assets/icons/chevron-down.png',
                          width: screenWidth * 0.04,
                          height: screenWidth * 0.04,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                height: _isDropdownExpanded
                    ? (timeSlots.length * screenHeight * 0.05)
                        .clamp(0.0, screenHeight * 0.25)
                    : 0,
                child: _isDropdownExpanded
                    ? Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(screenWidth * 0.075),
                            bottomRight: Radius.circular(screenWidth * 0.075),
                          ),
                          border: const Border(
                            top: BorderSide(
                              color: Color(0xFFD2D4D7),
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(screenWidth * 0.075),
                            bottomRight: Radius.circular(screenWidth * 0.075),
                          ),
                          child: ListView.builder(
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            physics: timeSlots.length > 5
                                ? const AlwaysScrollableScrollPhysics()
                                : const NeverScrollableScrollPhysics(),
                            itemCount: timeSlots.length,
                            itemBuilder: (context, index) {
                              final slot = timeSlots[index];
                              final formattedTime =
                                  '${_formatTimeToAmPm(slot.startTime)} - ${_formatTimeToAmPm(slot.endTime)}';
                              final isSelected =
                                  _selectedTimeSlotId == slot.id.toString();

                              return InkWell(
                                  onTap: () {
                                    setState(() {
                                      _timeSlotError = null;
                                      _selectedTimeSlotId = slot.id.toString();
                                      _selectedTimeSlotFormatted =
                                          formattedTime;
                                      _isDropdownExpanded = false;
                                    });
                                  },
                                  child: Container(
                                    height: screenHeight * 0.05,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: screenWidth * 0.04),
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? const Color(0xFFF5F5F5)
                                          : Colors.white,
                                      border: index < timeSlots.length - 1
                                          ? const Border(
                                              bottom: BorderSide(
                                                color: Color(0xFFE5E5E5),
                                                width: 0.5,
                                              ),
                                            )
                                          : null,
                                    ),
                                    child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        formattedTime,
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: screenWidth * 0.035,
                                          fontWeight: FontWeight.w400,
                                          color: kBlack,
                                        ),
                                      ),
                                    ),
                                  ));
                            },
                          ),
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
            ],
          ),
        ),
        if (_timeSlotError != null)
          Padding(
            padding: EdgeInsets.only(top: screenHeight * 0.01),
            child: Text(
              _timeSlotError!,
              style: TextStyle(
                color: const Color(0xFFE11900),
                fontSize: screenWidth * 0.03,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        SizedBox(height: screenHeight * 0.01),
        Text(
          'This is the time to expect your delivery each day.',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w400,
            fontSize: twelve,
            color: kSecondBlack,
          ),
        ),
      ],
    );
  }

  Widget _buildServingsSelection(double screenWidth, double screenHeight) {
    final double twelve = screenWidth * (12 / 13.751 * 0.035);
    final double forteen = screenWidth * 0.035;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Select Number Of Servings',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w500,
                fontSize: forteen,
                letterSpacing: 0.02 * screenWidth * 0.035,
                color: kBlack,
              ),
            ),
          ],
        ),
        SizedBox(height: screenHeight * 0.02),
        Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'How much food is 1 serving?',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: twelve,
                    letterSpacing: 0.02 * screenWidth * 0.03,
                    color: kSecondBlack,
                    decoration: TextDecoration.underline,
                    height: 1.3,
                    decorationColor: kSecondBlack,
                  ),
                ),
                // Container(
                //   height: 1.2,
                //   width: screenWidth * 0.35,
                //   color: Colors.black54,
                // ),
              ],
            ),
            SizedBox(width: screenWidth * 0.01),
            Container(
              width: screenWidth * 0.035,
              height: screenWidth * 0.035,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: const Color(0xFF414346)),
              ),
              child: Center(
                child: Text(
                  '?',
                  style: TextStyle(
                    fontSize: screenWidth * 0.025,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF414346),
                  ),
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: screenHeight * 0.025),
        ...servingSizes
            .map((serving) => Padding(
                  padding: EdgeInsets.only(bottom: screenHeight * 0.015),
                  child: _buildServingOption(
                    serving.serves ?? 1,
                    serving.title ?? '12oz',
                    screenWidth,
                    screenHeight,
                  ),
                ))
            .toList(),
        if (_servingError != null)
          Padding(
            padding: EdgeInsets.only(top: screenHeight * 0.01),
            child: Text(
              _servingError!,
              style: TextStyle(
                color: const Color(0xFFE11900),
                fontSize: screenWidth * 0.03,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildServingOption(
      int servings, String weight, double screenWidth, double screenHeight) {
    final double ten = screenWidth * (10 / 13.751 * 0.035);
    final double forteen = screenWidth * 0.035;
    final serving = servingSizes.firstWhere((s) => s.serves == servings);
    final bool isSelected = _selectedServings == servings;
    // servingsno=servings;
    return InkWell(
      onTap: () {
        setState(() {
          _servingError = null;
          _selectedServings = servings;
          _selectedServingId = serving.id;
          servingsno = servings;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
            horizontal: screenWidth * 0.035, vertical: screenHeight * 0.015),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFE1DDD5) : const Color(0xFFF6F3EC),
          border: Border.all(
            color:
                isSelected ? const Color(0xFF1F2122) : const Color(0xFFB9B6AD),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(screenWidth * 0.02),
        ),
        child: Row(
          children: [
            SizedBox(
              width: screenWidth * 0.1,
              height: screenHeight * 0.03,
              child: Stack(
                children: List.generate(
                  servings,
                  (index) => Positioned(
                    left: index * screenWidth * 0.015,
                    child: Image.asset(
                      'assets/icons/men.png',
                      width: screenWidth * 0.065,
                      height: screenWidth * 0.065,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
            ),
            //  SizedBox(width: screenWidth * 0.03),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '$servings ${servings == 1 ? 'Serving' : 'Servings'}',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                      fontSize: forteen,
                      letterSpacing: 0.02 * screenWidth * 0.035,
                      color: kBlack,
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.005),
                  if (servingSizes.any(
                      (s) => s.serves == servings && s.description != null))
                    Text(
                      servingSizes
                              .firstWhere((s) => s.serves == servings)
                              .description ??
                          '',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: ten,
                        color: kSecondBlack,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDishesPerDaySelector(double screenWidth, double screenHeight) {
    final double forteen = screenWidth * 0.035;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Number Of Dishes To Be Delivered Per Day',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: forteen,
            letterSpacing: 0.02 * screenWidth * 0.035,
            color: kBlack,
          ),
        ),
        SizedBox(height: screenHeight * 0.015),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _buildCircleButton(
              icon: Icons.remove,
              onPressed:
                  _numDishes > 1 ? () => setState(() => _numDishes--) : null,
              screenWidth: screenWidth,
              screenHeight: screenHeight,
            ),
            Container(
              width: screenWidth * 0.15,
              height: screenHeight * 0.05,
              margin: EdgeInsets.symmetric(horizontal: screenWidth * 0.025),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(screenWidth * 0.075),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(color: const Color(0xFFE1DDD5)),
              ),
              child: Center(
                child: Text(
                  _numDishes.toString(),
                  style: TextStyle(
                    fontSize: screenWidth * 0.04,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Inter',
                    color: const Color(0xFF66696D),
                  ),
                ),
              ),
            ),
            _buildCircleButton(
              icon: Icons.add,
              onPressed:
                  _numDishes < 5 ? () => setState(() => _numDishes++) : null,
              screenWidth: screenWidth,
              screenHeight: screenHeight,
            ),
          ],
        ),
        SizedBox(height: screenHeight * 0.01),
        if (_dishError != null)
          Padding(
            padding: EdgeInsets.only(top: screenHeight * 0.01),
            child: Text(
              _dishError!,
              style: TextStyle(
                color: const Color(0xFFE11900),
                fontSize: screenWidth * 0.03,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              'Each Dish is Good For ${servingsno == 1 ? '$servingsno Serving' : '$servingsno Servings'}',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w500,
                fontSize: screenWidth * 0.03,
                color: kBlack,
              ),
            ),
            SizedBox(width: screenWidth * 0.01),
            Container(
              width: screenWidth * 0.025,
              height: screenWidth * 0.025,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: const Color(0xFF414346)),
              ),
              child: Center(
                child: Text(
                  '?',
                  style: TextStyle(
                    fontSize: screenWidth * 0.02,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF414346),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCircleButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required double screenWidth,
    required double screenHeight,
  }) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.white,
        shape: CircleBorder(
          side: BorderSide(color: Colors.grey[300]!),
        ),
        child: InkWell(
          onTap: onPressed,
          customBorder: const CircleBorder(),
          child: Container(
            width: screenWidth * 0.1,
            height: screenWidth * 0.1,
            alignment: Alignment.center,
            child: Icon(icon,
                size: screenWidth * 0.05, color: const Color(0xFF1F2122)),
          ),
        ),
      ),
    );
  }

  Widget _buildMealSelectionType(double screenWidth, double screenHeight) {
    final double forteen = screenWidth * 0.035;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose Meal Selection Type',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: forteen,
            letterSpacing: 0.02 * screenWidth * 0.035,
            color: kBlack,
          ),
        ),
        SizedBox(height: screenHeight * 0.015),
        _buildMealSelectionOption(
          icon: Icons.restaurant,
          title: 'Let The Chef Choose For Me',
          subtitle: 'The chef chooses meals based on your\npreferences',
          isSelected: _letChefChoose,
          onTap: () => setState(() => _letChefChoose = true),
          screenWidth: screenWidth,
          screenHeight: screenHeight,
        ),
        SizedBox(height: screenHeight * 0.015),
        _buildMealSelectionOption(
          icon: Icons.restaurant_menu,
          title: 'I Want To Choose My Meals',
          subtitle: 'You choose your meals',
          isSelected: !_letChefChoose,
          onTap: () => setState(() => _letChefChoose = false),
          screenWidth: screenWidth,
          screenHeight: screenHeight,
        ),
      ],
    );
  }

  Widget _buildMealSelectionOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isSelected,
    required VoidCallback onTap,
    required double screenWidth,
    required double screenHeight,
  }) {
    final double twelve = screenWidth * (12 / 13.751 * 0.035);
    final double sixteen = screenWidth * (16 / 13.751 * 0.035);
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(screenWidth * 0.04),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFE1DDD5) : const Color(0xFFF6F3EC),
          borderRadius: BorderRadius.circular(screenWidth * 0.02),
          border: Border.all(
            color:
                isSelected ? const Color(0xFF1F2122) : const Color(0xFFE1DDD5),
            width: 1,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: screenHeight * 0.005),
              child: Image.asset(
                'assets/icons/meal.png',
                width: screenWidth * 0.045,
                height: screenWidth * 0.045,
                color: const Color(0xFF1F2122),
                fit: BoxFit.contain,
              ),
            ),
            SizedBox(width: screenWidth * 0.035),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                      fontSize: sixteen,
                      letterSpacing: 0.02 * screenWidth * 0.035,
                      color: kBlack,
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.005),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: twelve,
                      color: kSecondBlack,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButtons(double screenWidth, double screenHeight) {
    final double sixteen = screenWidth * (16 / 13.751 * 0.035);
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFF6F3EC),
            foregroundColor: Colors.black,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(screenWidth * 0.07),
              side: const BorderSide(color: Color(0xFF1F2122)),
            ),
            padding: EdgeInsets.symmetric(
                horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
            //  minimumSize: Size(screenWidth * 0.4, screenHeight * 0.06),
          ),
          child: Text(
            'Back',
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              fontSize: sixteen,
              letterSpacing: 0.32,
              color: const Color(0xFF1F2122),
            ),
          ),
        ),
        ElevatedButton(
          onPressed: _isLoading
              ? null
              : () {
                  if (!_validateFields()) return;

                  // Explicitly check if anything has changed from the initial values
                  final initialData =
                      Initializer.mealPlanProgressLatestModel.data;

                  // Check for weekday changes
                  bool weekdaysChanged = false;
                  if (initialData?.selectedWeekdays != null) {
                    final listEquality = const ListEquality();
                    final currentWeekdays = _convertWeekdaysToNames();
                    final initialWeekdays =
                        List<String>.from(initialData?.selectedWeekdays ?? []);

                    // Sort both lists to ensure proper comparison
                    currentWeekdays.sort();
                    initialWeekdays.sort();

                    weekdaysChanged =
                        !listEquality.equals(currentWeekdays, initialWeekdays);
                  }

                  final hasChanges = _numDishes != initialData?.dishesPerDay ||
                      _selectedServingId != initialData?.servingSizeId ||
                      _selectedTimeSlotId !=
                          initialData?.timeSlotId?.toString() ||
                      _dateOnly(_startDate?.toIso8601String()) !=
                          _dateOnly(initialData?.startDate) ||
                      _dateOnly(_endDate?.toIso8601String()) !=
                          _dateOnly(initialData?.endDate) ||
                      weekdaysChanged;

                  setState(() {
                    noChangesMade = !hasChanges;
                  });

                  // print('Checking for changes before submission:');
                  // print(
                  //     'Dishes changed: ${_numDishes != initialData?.dishesPerDay}, new: $_numDishes, initial: ${initialData?.dishesPerDay}');
                  // print(
                  //     'Serving changed: ${_selectedServingId != initialData?.servingSizeId}, new: $_selectedServingId, initial: ${initialData?.servingSizeId}');
                  // print(
                  //     'TimeSlot changed: ${_selectedTimeSlotId != initialData?.timeSlotId?.toString()}, new: $_selectedTimeSlotId, initial: ${initialData?.timeSlotId}');
                  // print(
                  //     'Start date changed: ${_dateOnly(_startDate?.toIso8601String()) != _dateOnly(initialData?.startDate)}, new: ${_dateOnly(_startDate?.toIso8601String())}, initial: ${_dateOnly(initialData?.startDate)}');
                  // print(
                  //     'End date changed: ${_dateOnly(_endDate?.toIso8601String()) != _dateOnly(initialData?.endDate)}, new: ${_dateOnly(_endDate?.toIso8601String())}, initial: ${_dateOnly(initialData?.endDate)}');
                  // print(
                  //     'Weekdays changed: $weekdaysChanged, current: ${_convertWeekdaysToNames()}, initial: ${initialData?.selectedWeekdays}');
                  // print(
                  //     'hasChanges: $hasChanges, noChangesMade: $noChangesMade');

                  final mealPlanData = <String, dynamic>{
                    "meal_plan_duration": _selectedPlanDays,
                    "start_date": _formatStorageDate(_startDate!),
                    "end_date": _formatStorageDate(_endDate!),
                    "time_slot_id": int.parse(_selectedTimeSlotId!),
                    "serving_size_id": _selectedServingId,
                    "dishes_per_day": _numDishes,
                    "meal_selection_type":
                        _letChefChoose ? "CURATED" : "PERSONALIZED",
                    "selected_weekdays": _convertWeekdaysToNames(),
                  };

                  // Only include meal_plan_id if it exists
                  if (_existingMealPlanId != null) {
                    mealPlanData["meal_plan_id"] = _existingMealPlanId;
                  }

                  context
                      .read<NewmealplanBloc>()
                      .add(NewStep1MEalPlanEvent(mealPlanData));
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.black,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(screenWidth * 0.07),
            ),
            padding: EdgeInsets.symmetric(
                horizontal:
                    _isLoading ? screenWidth * 0.04 : screenWidth * 0.05,
                vertical: screenHeight * 0.02),
            //  horizontal: screenWidth * 0.04, vertical: screenHeight * 0.015),
            //  minimumSize: Size(screenWidth * 0.4, screenHeight * 0.06),
          ),
          child: _isLoading
              ? SizedBox(
                  width: screenWidth * 0.05,
                  height: screenWidth * 0.05,
                  child: const CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : Text(
                  'Next',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: screenWidth * 0.04,
                    letterSpacing: 0.32,
                    color: Colors.white,
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildDialogButton(
    String text,
    VoidCallback onTap,
    double screenWidth,
    double screenHeight,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: screenHeight * 0.018),
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 3, 3, 3),
          borderRadius: BorderRadius.circular(screenWidth * 0.03),
          boxShadow: [
            BoxShadow(
              color: const Color.fromARGB(255, 78, 83, 81).withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w600,
              fontSize: screenWidth * 0.04,
              color: Colors.white,
              letterSpacing: 0.3,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlanSelection(double screenWidth, double screenHeight) {
    final double forteen = screenWidth * 0.035;
    final double sixteen = screenWidth * (16 / 13.751 * 0.035);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose when to start your meal plan',
          style: TextStyle(
            fontSize: 22,
            // fontWeight: FontWeight.w600,
            fontFamily: 'Inter-medium',
            letterSpacing: 1,
            height: 1.28,
            color: kBlack,
          ),
        ),
        SizedBox(height: screenHeight * 0.03),
        Container(
          padding: EdgeInsets.all(screenWidth * 0.03),
          decoration: BoxDecoration(
            color: const Color(0xFFE1DDD5),
            borderRadius: BorderRadius.circular(screenWidth * 0.015),
          ),
          child: Text(
            'Meal plans run on weekdays only. (ex. If you start on a Friday, your next meal will be delivered the following Monday.)',
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              fontSize: forteen,
              height: 1.4,
              color: kBlack,
            ),
          ),
        ),
        SizedBox(height: screenHeight * 0.03),
        Text(
          'Choose Your Meal Plan',
          style: TextStyle(
            fontSize: sixteen,
            fontWeight: FontWeight.w500,
            fontFamily: 'Inter',
            color: kBlack,
          ),
        ),
        if (_planError != null) ...[
          SizedBox(height: screenHeight * 0.01),
          Text(
            _planError!,
            style: TextStyle(
              color: Colors.red,
              fontSize: forteen,
              fontFamily: 'Inter',
            ),
          ),
        ],
        SizedBox(height: screenHeight * 0.015),
        Row(
          children: [
            Expanded(
              child:
                  _buildPlanOption(3, '3 Days Plan', screenWidth, screenHeight),
            ),
            SizedBox(width: screenWidth * 0.03),
            Expanded(
              child:
                  _buildPlanOption(5, '5 Days Plan', screenWidth, screenHeight),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPlanOption(
      int days, String title, double screenWidth, double screenHeight) {
    final bool isSelected = _selectedPlanDays == days;
    final double forteen = screenWidth * 0.035;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedPlanDays = days;
          _planError = null;

          if (days == 5) {
            _selectedWeekdays = {1, 2, 3, 4, 5};
          } else if (days == 3) {
            _selectedWeekdays.clear();
          }

          _generateDatesFromRange();
          _mealData.clear();
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: screenHeight * 0.015,
          horizontal: screenWidth * 0.025,
        ),
        decoration: BoxDecoration(
          color: isSelected ? Color.fromARGB(255, 5, 5, 5) : Colors.white,
          borderRadius: BorderRadius.circular(screenWidth * 0.075),
          border: Border.all(
            color: isSelected
                ? Color.fromARGB(255, 8, 8, 8)
                : const Color(0xFFE1DDD5),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 6,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              fontSize: forteen,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
              color: isSelected ? Colors.white : kBlack,
              letterSpacing: 0.2,
            ),
          ),
        ),
      ),
    );
  }

  String? _dateOnly(String? dateStr) {
    if (dateStr == null) return null;
    return dateStr.split('T').first;
  }
}
