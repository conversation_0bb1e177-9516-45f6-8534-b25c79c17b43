{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983ffa9fdb5daaaf6bb2e743165e7387bf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9858e50c3bff23dea907e7a6c5b7860cc6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f226f7830e5561e1c14723251f24fa05", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983165a6a205e4367fa5b76266608a270c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f226f7830e5561e1c14723251f24fa05", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fcc947ad67f497936af4ed57ae0ebf15", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9889ed4338c22d2a88ce75ba9d62334368", "guid": "bfdfe7dc352907fc980b868725387e98a3c619960d32a1527f98583deac9dca5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886373614aa6575471ed37bebee037d45", "guid": "bfdfe7dc352907fc980b868725387e983c94650ecf3fcd4fdddccfad33747a2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98440c0440196f71e26921d7883ba25a29", "guid": "bfdfe7dc352907fc980b868725387e9859aff8a71bc23b89e1bbffa9ac3eb758"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98767fb25c3637912ece52edef90f3304f", "guid": "bfdfe7dc352907fc980b868725387e98f7126e57181ba074a6b28801818cd8d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b04c0ce50de46388188868617ccea70", "guid": "bfdfe7dc352907fc980b868725387e98bbb9fc3b597ddb018bdc74e1502565f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f58c6e47def2b1461954c03cba92fc42", "guid": "bfdfe7dc352907fc980b868725387e98d52956aa663af496c4744d66f4d4a44f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d58460879761bc2233c9b7379c8e439c", "guid": "bfdfe7dc352907fc980b868725387e98bb4540d6acf938966b3a15efdd7e1da6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f55bbb99a7bf422d946db5c950b519c6", "guid": "bfdfe7dc352907fc980b868725387e98e35b7d2aee41937d975ff1d9e1581ad2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6026f4e9c205122a3eb79cf58ddf78d", "guid": "bfdfe7dc352907fc980b868725387e986144e43ef2ec1a00fbccfff92b49d1b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987721bed3d64f7459d7809df9dcb93fc4", "guid": "bfdfe7dc352907fc980b868725387e986070a7e9d57e6fa2b319bea74be38481", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984315a3977835ca00888a26f818c11ccb", "guid": "bfdfe7dc352907fc980b868725387e98205b1d5f96d7e243c563873dacf79de4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c03a7ae3258d2fb8fb48e8fd940ef02", "guid": "bfdfe7dc352907fc980b868725387e987ccb3e5f961df1c1383708a21002f659"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98333f54b64ae147356e64d5de814a50bb", "guid": "bfdfe7dc352907fc980b868725387e983e355dc76e6dfd35db34cc8dea4ae792"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887597eeac149cc8d3acfa68a363a5f63", "guid": "bfdfe7dc352907fc980b868725387e98842c5f6058864d34cc5e844bf28e9d91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985be5db38927f2299d71c7e9bf3771738", "guid": "bfdfe7dc352907fc980b868725387e98d6c99a2e9ceee0ff3cca233db69b8fc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e8b8128164fd6e9b57b3453a5169994", "guid": "bfdfe7dc352907fc980b868725387e98db8e766090c9096dadc281edcf5b34c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986214d22950b7f08257e74152a6d93877", "guid": "bfdfe7dc352907fc980b868725387e98d26891060ab585762b2b2808fa89729f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fe47050956b91ae60ec6ff7abee1164", "guid": "bfdfe7dc352907fc980b868725387e9860b3277c4ce5f64dcb0a7108ea219b75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986285402f73fc394724ded332f200e6a7", "guid": "bfdfe7dc352907fc980b868725387e9881a4e3ab2bc7a595af12668c1f40c0fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988975527d6f793139a175c1be5774058e", "guid": "bfdfe7dc352907fc980b868725387e986783e2894be38f50ce1630ecf9107715"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a32bbd6045cf99a748f8da7b8bc3fb6", "guid": "bfdfe7dc352907fc980b868725387e989766eb48ddf816f54f0c9e706736a1d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810b9efcda9b7673f7d3ee6fced36a1f2", "guid": "bfdfe7dc352907fc980b868725387e9863b1d649766bfb4c5c8a766935c98c01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3c810c63f8d49fd44b186b5f38b346f", "guid": "bfdfe7dc352907fc980b868725387e9886c10fc8831d6ff517c2ea68fa9c4556"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cc6c0e3d070a3aee783409596a078d9", "guid": "bfdfe7dc352907fc980b868725387e98634f26cf6c39ad61c62f097611ee7f4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859ca6f7c02d6d19991b6b9c19eff1038", "guid": "bfdfe7dc352907fc980b868725387e981b79eb3d1313e87348b92d98ab698cfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ef4aadf753f85acbc744b800a3b216e", "guid": "bfdfe7dc352907fc980b868725387e9823123b850ca64a79f4a7235c0662c1a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888cd9e5b19da20318041309509d0e9a1", "guid": "bfdfe7dc352907fc980b868725387e98de50843e4874cb67309925b3acfb7b57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897ad449977dbe92255789779a0ee2249", "guid": "bfdfe7dc352907fc980b868725387e98946add2f6a53abc99846a0d64ef3d0de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898bbfddb917b602fdbc06a07eac7c97a", "guid": "bfdfe7dc352907fc980b868725387e985cb142bca4c52b1dda09d4119d2dd92d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0374a88699c33534e467f9c2eb566f5", "guid": "bfdfe7dc352907fc980b868725387e988c39138f0e5adb2c76dc0bf1e0bee799"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9ad5a68b26b0c7e515bd9f69224e3d6", "guid": "bfdfe7dc352907fc980b868725387e98ee15782a77e9c5d831c04aad87124b2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4dcb9f8f841248aa59aa0a8079e0f38", "guid": "bfdfe7dc352907fc980b868725387e98bfd062945e6ebb8eae59a702b025a6f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983abc4f11966dfdb10d427805ba6ff3a1", "guid": "bfdfe7dc352907fc980b868725387e98eddf0608f9be87982b1046b9d9ddb6ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98983f84138b12be5533f3761ecc39479e", "guid": "bfdfe7dc352907fc980b868725387e9870ba563a38897e4f7697d09eebbcf829"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809349eee3c494a395b5f50692fd3470d", "guid": "bfdfe7dc352907fc980b868725387e9867832232c388cc8ab66c924e06914a84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984091ff7c3e8751e8ea2c28eac766d948", "guid": "bfdfe7dc352907fc980b868725387e988625c5de99aa26ecd0073ffb908ffdc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c5d03f1dd537d0ed06c6cfd587161a9", "guid": "bfdfe7dc352907fc980b868725387e9874143723d299fa7da9ce8f557b7e3fb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a02a8988886a0074e34a63cbda565ea4", "guid": "bfdfe7dc352907fc980b868725387e9892372cfce5cd0fdf29d9c80ae6fc75ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dce04739016b9a4782630eebed7d1d5", "guid": "bfdfe7dc352907fc980b868725387e986aadd9c19f270960c026a504e3f6f3cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809671df0d1c3ec1bea7805c8b8f9a5e3", "guid": "bfdfe7dc352907fc980b868725387e98102f20cbf7f699bc71a8391e19976407"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981732204795dd262a13bdfaf229b9bec7", "guid": "bfdfe7dc352907fc980b868725387e98ec97ea27efa4f9913669d88664f382fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98811545c1453d9952441d3fdc273a054b", "guid": "bfdfe7dc352907fc980b868725387e98d2cd4c090b5a129ae564ed59d3d9a962"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcabc46f46ba4f1bccc0f422c58dc384", "guid": "bfdfe7dc352907fc980b868725387e98b73a63a3c77c29fe782cf5468defc88c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c1feee33b411f6b0700a908367e5083", "guid": "bfdfe7dc352907fc980b868725387e98b7044921addb05fb5329f1359607ff7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ee9f374baf01a25b1f7aac02b5a2261", "guid": "bfdfe7dc352907fc980b868725387e985f88af944147830bb9a6b8e53def1d30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b14976ba6939f7fa0259a10a148a169d", "guid": "bfdfe7dc352907fc980b868725387e980546fb321071bacb4adc9f5a0ea5e9a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dacdde460a24c2b221629e40d0030b41", "guid": "bfdfe7dc352907fc980b868725387e982acaf7611c76b03413d1da628654a6be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aec5705867996ddca9d33f303f537cbd", "guid": "bfdfe7dc352907fc980b868725387e98c4a9a566b041fbc24571714882e6f344"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af288f7031202f8f98ebbb459fbbdcd2", "guid": "bfdfe7dc352907fc980b868725387e989653f1f3460e4dd66d1bd8a95078250b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853904d589b97eb8e32a5d4fc9ac4cd1a", "guid": "bfdfe7dc352907fc980b868725387e98844306150322a45cfa3145fe7d8325b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bba2eaa121ad9b00e7aec45c53ae8d1", "guid": "bfdfe7dc352907fc980b868725387e98b9594ce3189fa77652dc005ec4eb8f09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854aa01042ed40689e560988330937924", "guid": "bfdfe7dc352907fc980b868725387e98a8c4e5e1c70e0e106fd0f71125a614f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827294934d035380966741317c37e17f3", "guid": "bfdfe7dc352907fc980b868725387e985be6139025ed1c11e76ff6020bc59089"}], "guid": "bfdfe7dc352907fc980b868725387e9809e159d3ba23c4d725ae97baa014c88e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981512fe0c9753801cb2b488c10b2901c8", "guid": "bfdfe7dc352907fc980b868725387e98b6631050c6d4c520d3ee9fe45cc804df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eef913e01b2b54ca9ee55a30fcd69d9", "guid": "bfdfe7dc352907fc980b868725387e98b5b04ae0e8754481c7193f772a293767"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985784e04decadf57e67bb7f291fec66d4", "guid": "bfdfe7dc352907fc980b868725387e98877312c22ed77ee96c705258673a09ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98242e63295d00880d4e33cfbb99e700be", "guid": "bfdfe7dc352907fc980b868725387e98949eb2813fdd5506c642d1177d69e0b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa694bf4a704f4605a4d41b6157786f5", "guid": "bfdfe7dc352907fc980b868725387e98d65de41d0971603519e9c2c67236d69e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9820b4618b9963037341f1cb689f5b9", "guid": "bfdfe7dc352907fc980b868725387e981a68f46a51395bcb9020c1066745df57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849754da8172c9bec6f1edf68e289c109", "guid": "bfdfe7dc352907fc980b868725387e98503b2c85f5702f7c335b9985c6174a92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bd0df55ff8217005f65bdf6834a780d", "guid": "bfdfe7dc352907fc980b868725387e9816b35ce51dd60b31fd47a40a8155bf34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aaa1b78ecbd08129324ca8d97fbb9a4", "guid": "bfdfe7dc352907fc980b868725387e9858ceb040e2389ea33128acc50444edb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983787e995d652e866c2d6d504d3c8b89f", "guid": "bfdfe7dc352907fc980b868725387e98c8e2621cc9a2cac64b993474f0b284a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d3f33bda4cf70ddbd4141c4936073b7", "guid": "bfdfe7dc352907fc980b868725387e9824803698619ac1af42776f129692a327"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1d19c8fccf7d87306e4ca47ee7256d7", "guid": "bfdfe7dc352907fc980b868725387e98cba3c74503134de581721a10bc2033ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f1ea6fc7011b06a1f3ae162fa791031", "guid": "bfdfe7dc352907fc980b868725387e98ac35574e8b326909bf5843d1c5d9699e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d28cc66f247265d3e034043d4f6833a5", "guid": "bfdfe7dc352907fc980b868725387e98e208a8eb5c10e04e94dacf866aff216f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c9922de9be686b1963ae032966d8995", "guid": "bfdfe7dc352907fc980b868725387e9804a02df9755af9a099ae1af05a7b0f26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855a64d0d9d6018e083ada79de83341f2", "guid": "bfdfe7dc352907fc980b868725387e98431f99cd726bd82ba93f7da9d003dddd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835b73d42d5f0c5b67124050e80ca03b2", "guid": "bfdfe7dc352907fc980b868725387e9868fe349cb7dbc0364929f75c884b34bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98014ad640dfc975a425a4e0ee25b5462c", "guid": "bfdfe7dc352907fc980b868725387e98f89a64aea566f4628cfd9d16b968c669"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883cda5affec358987b8354ee269add69", "guid": "bfdfe7dc352907fc980b868725387e98eee2459bf2aad2ecfcc87679ecf9c4c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815c7859c078041ef068e8de4a45264bc", "guid": "bfdfe7dc352907fc980b868725387e98a7119adceb7d10af24335598b617eaf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826b35af41ea6b195bcbc7d8ac04c15f6", "guid": "bfdfe7dc352907fc980b868725387e9844cf89f177629a9cbada59fa283b9c5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bac9bbdbafd130f5b54332d5148e9d4a", "guid": "bfdfe7dc352907fc980b868725387e98e324b2ec81457e1bc8c018dc0b4f971f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866b9306fc4f1a2b37d294bd5323b047b", "guid": "bfdfe7dc352907fc980b868725387e98e1d9cf78f9a22faac72c9b2fb42b3f54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982831a3341f02b1cf9010e59f0ae80e95", "guid": "bfdfe7dc352907fc980b868725387e98c31e75c036e2c6a9dd91988f3439f1f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5eee2baf56b3e1a8982f01617f5f286", "guid": "bfdfe7dc352907fc980b868725387e9849da8861bb63d89b42882c6bae8993ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983079343f569451ea7e5827ec6fadebd3", "guid": "bfdfe7dc352907fc980b868725387e98a792e69298335b36b5c24ce4e3270cd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b955ef92ab110a80210e1e6f5dffe8e", "guid": "bfdfe7dc352907fc980b868725387e988983d518ea3e61f2e58753113b6f217a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebe1acf0aaff3b8a357e8537c06bc990", "guid": "bfdfe7dc352907fc980b868725387e9841c19acbc4641c1fae49a797e02b8167"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828bf6aa6882a12feb340e732609bd113", "guid": "bfdfe7dc352907fc980b868725387e98f14764f8d728878e21ad56174725a184"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805fb9917f291a9a5b79815266a588027", "guid": "bfdfe7dc352907fc980b868725387e98edabcfabb2e7a54f630d8fa17eb8550e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ab6a09be32cc475a6cc0508239d56a1", "guid": "bfdfe7dc352907fc980b868725387e980f85f088186042670bde379754dcfdad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcde85646cb6ecc472e182dc9634dafc", "guid": "bfdfe7dc352907fc980b868725387e981c849aaf9c965b0125cc523e8048dbac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c6f8a7b2deba6baed7f67f96be0f450", "guid": "bfdfe7dc352907fc980b868725387e98b1dc63251e624c84aae534978781e3b3"}], "guid": "bfdfe7dc352907fc980b868725387e98ab7a45f5ddfeb2d1b8d28d74583e7ea7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e983f37553b1df20523190b53170072e040"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e28c510bcdc3834514b09ab79a067e08", "guid": "bfdfe7dc352907fc980b868725387e98cff71eb5c11b7567c1e90de0c77ffe44"}], "guid": "bfdfe7dc352907fc980b868725387e98d24df46f9b015519c9bc54a32fe455f5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e48ed6d9a95eb7a0427a4051bb127c16", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e986e4840475ce09748531a17aa3b96bc8b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}