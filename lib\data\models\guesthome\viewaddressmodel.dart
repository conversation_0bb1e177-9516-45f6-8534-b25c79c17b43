class ViewAddressModel {
  bool? status;
  int? statusCode;
  ViewAddressData? data;

  ViewAddressModel({this.status, this.statusCode, this.data});

  ViewAddressModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    statusCode = json['status_code'];
    data = json['data'] != null ? new ViewAddressData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class ViewAddressData {
  int? id;
  int? customerId;
  Location? location;
  String? addressText;
  String? buildingType;
  String? houseNumber;
  String? landmark;
  bool? isCurrent;
  String? createdAt;
  String? updatedAt;

  ViewAddressData(
      {this.id,
      this.customerId,
      this.location,
      this.addressText,
      this.buildingType,
      this.houseNumber,
      this.landmark,
      this.isCurrent,
      this.createdAt,
      this.updatedAt});

  ViewAddressData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    customerId = json['customer_id'];
    location = json['location'] != null
        ? new Location.fromJson(json['location'])
        : null;
    addressText = json['address_text'];
    buildingType = json['building_type'];
    houseNumber = json['house_number'];
    landmark = json['landmark'];
    isCurrent = json['is_current'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['customer_id'] = this.customerId;
    if (this.location != null) {
      data['location'] = this.location!.toJson();
    }
    data['address_text'] = this.addressText;
    data['building_type'] = this.buildingType;
    data['house_number'] = this.houseNumber;
    data['landmark'] = this.landmark;
    data['is_current'] = this.isCurrent;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}

class Location {
  Crs? crs;
  String? type;
  List<double>? coordinates;

  Location({this.crs, this.type, this.coordinates});

  Location.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? new Crs.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates'].cast<double>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.crs != null) {
      data['crs'] = this.crs!.toJson();
    }
    data['type'] = this.type;
    data['coordinates'] = this.coordinates;
    return data;
  }
}

class Crs {
  String? type;
  Properties? properties;

  Crs({this.type, this.properties});

  Crs.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'] != null
        ? new Properties.fromJson(json['properties'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    if (this.properties != null) {
      data['properties'] = this.properties!.toJson();
    }
    return data;
  }
}

class Properties {
  String? name;

  Properties({this.name});

  Properties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    return data;
  }
}
