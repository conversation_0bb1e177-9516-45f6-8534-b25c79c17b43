import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/saversplan/listsaversplanmodel.dart';
import 'package:db_eats/data/models/saversplan/saversplansummarymodel.dart';
import 'package:db_eats/data/models/verifyrefreshtokenmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/storage/localstorage.dart';

class SaversplanBloc extends Bloc<SaversPlanEvent, SaversPlanState> {
  SaversplanBloc() : super(SaversPlanInitial()) {
    on<ListSaversPassPlansEvent>(_listSaversPassPlans);
    on<SubscribeSaversPassEvent>(_subscribeSaversPass);
    on<CancelSubscribeSaversPassEvent>(_cancelSubscribeSaversPass);
    on<CheckoutSummarySaversPassEvent>(_checkoutSummarySaversPass);
    on<SaversSendResendEmailOtp>(_saversSendResendEmailOtp);
    on<SaversVerifyEmailOtp>(_saversVerifyEmailOtp);
    on<CheckoutSaversPassEvent>(_checkoutSaversPass);
    on<RefreshTokenEvent>(_refreshToken);
  }

  Future<void> _listSaversPassPlans(
      ListSaversPassPlansEvent event, Emitter<SaversPlanState> emit) async {
    emit(ListSaversPassPlansLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/savers_pass/plans', event.data);
      log('List Savers Plans Response: $response');

      // Check for 401 unauthorized
      if (response['status_code'] == 401) {
        final refreshToken = await LocalStorage.getRefreshToken();
        add(RefreshTokenEvent(
            nextEvent: event, refreshToken: refreshToken.toString()));
        return;
      }

      Initializer.listSaversPlanModel = ListSaversPlanModel.fromJson(response);

      if (Initializer.listSaversPlanModel.status == true) {
        emit(ListSaversPassPlansSuccess(Initializer.listSaversPlanModel.data));
      } else {
        emit(ListSaversPassPlansFailed(
            response['message'] ?? 'Failed to list savers plans'));
      }
    } catch (e) {
      log('Error listing savers plans: $e');
      emit(ListSaversPassPlansFailed(
          'Error occurred while listing savers plans'));
    }
  }

  Future<void> _subscribeSaversPass(
      SubscribeSaversPassEvent event, Emitter<SaversPlanState> emit) async {
    emit(SubscribeSaversPassLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/savers_pass/subscribe', event.data);
      log('Subscribe Savers Pass Response: $response');

      // Check for 401 unauthorized
      if (response['status_code'] == 401) {
        final refreshToken = await LocalStorage.getRefreshToken();
        add(RefreshTokenEvent(
            nextEvent: event, refreshToken: refreshToken.toString()));
        return;
      }

      if (response['status'] == true) {
        final subscriptionId = response['data']?['id']?.toString();
        emit(SubscribeSaversPassSuccess(
            response['message'] ?? 'Subscribed successfully',
            subscriptionId: subscriptionId));
      } else {
        emit(SubscribeSaversPassFailed(
            response['message'] ?? 'Failed to subscribe to savers pass'));
      }
    } catch (e) {
      log('Error subscribing to savers pass: $e');
      emit(SubscribeSaversPassFailed(
          'Error occurred while subscribing to savers pass'));
    }
  }

  Future<void> _cancelSubscribeSaversPass(CancelSubscribeSaversPassEvent event,
      Emitter<SaversPlanState> emit) async {
    emit(CancelSubscribeSaversPassLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/savers_pass/cancel', event.data);
      log('Cancel Subscribe Savers Pass Response: $response');

      // Check for 401 unauthorized
      if (response['status_code'] == 401) {
        final refreshToken = await LocalStorage.getRefreshToken();
        add(RefreshTokenEvent(
            nextEvent: event, refreshToken: refreshToken.toString()));
        return;
      }

      if (response['status'] == true) {
        emit(CancelSubscribeSaversPassSuccess(
            response['message'] ?? 'Cancelled subscription successfully'));
      } else {
        emit(CancelSubscribeSaversPassFailed(
            response['message'] ?? 'Failed to cancel subscription'));
      }
    } catch (e) {
      log('Error cancelling subscription: $e');
      emit(CancelSubscribeSaversPassFailed(
          'Error occurred while cancelling subscription'));
    }
  }

  Future<void> _checkoutSummarySaversPass(CheckoutSummarySaversPassEvent event,
      Emitter<SaversPlanState> emit) async {
    emit(CheckoutSummarySaversPassLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/savers_pass/checkout_summary', event.data);
      log('Checkout Summary Savers Pass Response: $response');

      // Check for 401 unauthorized
      if (response['status_code'] == 401) {
        final refreshToken = await LocalStorage.getRefreshToken();
        add(RefreshTokenEvent(
            nextEvent: event, refreshToken: refreshToken.toString()));
        return;
      }

      Initializer.saversPlanSummaryModel =
          SaversPlanSummaryModel.fromJson(response);

      if (Initializer.saversPlanSummaryModel.status == true) {
        emit(CheckoutSummarySaversPassSuccess(
            response['message'] ?? 'Checkout summary retrieved successfully'));
      } else {
        emit(CheckoutSummarySaversPassFailed(
            response['message'] ?? 'Failed to retrieve checkout summary'));
      }
    } catch (e) {
      log('Error retrieving checkout summary: $e');
      emit(CheckoutSummarySaversPassFailed(
          'Error occurred while retrieving checkout summary'));
    }
  }

  Future<void> _saversSendResendEmailOtp(
      SaversSendResendEmailOtp event, Emitter<SaversPlanState> emit) async {
    emit(SaversSendResendEmailOtpLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/savers_pass/resend-email-otp', event.data);
      log('Savers Send Resend Email OTP Response: $response');

      if (response['status'] == true) {
        emit(SaversSendResendEmailOtpSuccess(
            response['message'] ?? 'OTP sent successfully'));
      } else {
        emit(SaversSendResendEmailOtpFailed(
            response['message'] ?? 'Failed to send OTP'));
      }
    } catch (e) {
      log('Error sending resend email OTP: $e');
      emit(SaversSendResendEmailOtpFailed(
          'Error occurred while sending resend email OTP'));
    }
  }

  Future<void> _saversVerifyEmailOtp(
      SaversVerifyEmailOtp event, Emitter<SaversPlanState> emit) async {
    emit(SaversVerifyEmailOtpLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/savers_pass/verify-email', event.data);
      log('Savers Verify Email OTP Response: $response');

      if (response['status'] == true) {
        emit(SaversVerifyEmailOtpSuccess(
            response['message'] ?? 'OTP verified successfully'));
      } else {
        emit(SaversVerifyEmailOtpFailed(
            response['message'] ?? 'Failed to verify OTP'));
      }
    } catch (e) {
      log('Error verifying email OTP: $e');
      emit(SaversVerifyEmailOtpFailed(
          'Error occurred while verifying email OTP'));
    }
  }

  Future<void> _checkoutSaversPass(
      CheckoutSaversPassEvent event, Emitter<SaversPlanState> emit) async {
    emit(CheckoutSaversPassLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/savers_pass/checkout', event.data);
      log('Checkout Summary Savers Pass Response: $response');

      // Check for 401 unauthorized
      if (response['status_code'] == 401) {
        final refreshToken = await LocalStorage.getRefreshToken();
        add(RefreshTokenEvent(
            nextEvent: event, refreshToken: refreshToken.toString()));
        return;
      }

      if (response['status'] == true) {
        final data = response['data'];
        final payment = data?['payment'];
        emit(CheckoutSaversPassSuccess(
          response['message'] ?? 'Payment processed successfully',
          paymentUrl: payment?['checkout_url']?.toString(),
          total: data?['total']?.toDouble(),
          subscriptionId: data?['id']?.toString(),
        ));
      } else {
        emit(CheckoutSaversPassFailed(
            response['message'] ?? 'Failed to retrieve checkout summary'));
      }
    } catch (e) {
      log('Error retrieving checkout summary: $e');
      emit(CheckoutSaversPassFailed(
          'Error occurred while retrieving checkout summary'));
    }
  }

  Future<void> _refreshToken(
      RefreshTokenEvent event, Emitter<SaversPlanState> emit) async {
    emit(RefreshTokenLoading());
    try {
      final response = await ServerHelper.getrefresh(
          '/v1/customer/auth/verify-refresh-token');

      Initializer.verifyRefreshTokenModel =
          VerifyRefreshTokenModel.fromJson(response);

      if (Initializer.verifyRefreshTokenModel.status == true) {
        await LocalStorage.setAccessToken(
            Initializer.verifyRefreshTokenModel.data?.accessToken ?? "");
        emit(RefreshTokenSuccess());

        // Re-execute the next event after successful token refresh
        if (event.nextEvent != null) {
          add(event.nextEvent!);
        }
      } else if (Initializer.verifyRefreshTokenModel.statusCode == 401) {
        emit(RefreshTokenFailed('Session expired. Please login again.'));
        // Clear stored tokens
        await LocalStorage.setAccessToken('');
        await LocalStorage.setRefreshToken('');
      } else {
        emit(RefreshTokenFailed(
            response['message'] ?? 'Failed to refresh token'));
      }
    } catch (e) {
      log('Error refreshing token: $e');
      emit(RefreshTokenFailed('Error occurred while refreshing token'));
    }
  }
}

// Events
abstract class SaversPlanEvent {
  const SaversPlanEvent();
}

class ListSaversPassPlansEvent extends SaversPlanEvent {
  final Map<String, dynamic> data;

  const ListSaversPassPlansEvent({required this.data});
}

class SubscribeSaversPassEvent extends SaversPlanEvent {
  final Map<String, dynamic> data;

  const SubscribeSaversPassEvent({required this.data});
}

class CancelSubscribeSaversPassEvent extends SaversPlanEvent {
  final Map<String, dynamic> data;

  const CancelSubscribeSaversPassEvent({required this.data});
}

class CheckoutSummarySaversPassEvent extends SaversPlanEvent {
  final Map<String, dynamic> data;

  const CheckoutSummarySaversPassEvent({required this.data});
}

class SaversSendResendEmailOtp extends SaversPlanEvent {
  final Map<String, dynamic> data;

  const SaversSendResendEmailOtp({required this.data});
}

class SaversVerifyEmailOtp extends SaversPlanEvent {
  final Map<String, dynamic> data;

  const SaversVerifyEmailOtp({required this.data});
}

class RefreshTokenEvent extends SaversPlanEvent {
  final String refreshToken;
  final SaversPlanEvent? nextEvent;

  const RefreshTokenEvent(
      {required this.refreshToken, required this.nextEvent});
}

class CheckoutSaversPassEvent extends SaversPlanEvent {
  final Map<String, dynamic> data;

  const CheckoutSaversPassEvent({required this.data});
}

// States
abstract class SaversPlanState {}

class SaversPlanInitial extends SaversPlanState {}

// List Savers Plans States
class ListSaversPassPlansLoading extends SaversPlanState {}

class ListSaversPassPlansSuccess extends SaversPlanState {
  final dynamic data;

  ListSaversPassPlansSuccess(this.data);
}

class ListSaversPassPlansFailed extends SaversPlanState {
  final String message;

  ListSaversPassPlansFailed(this.message);
}

// Subscribe Savers Pass States
class SubscribeSaversPassLoading extends SaversPlanState {}

class SubscribeSaversPassSuccess extends SaversPlanState {
  final String message;
  final String? subscriptionId;
  SubscribeSaversPassSuccess(this.message, {this.subscriptionId});
}

class SubscribeSaversPassFailed extends SaversPlanState {
  final String message;
  SubscribeSaversPassFailed(this.message);
}

// Cancel Subscribe Savers Pass States
class CancelSubscribeSaversPassLoading extends SaversPlanState {}

class CancelSubscribeSaversPassSuccess extends SaversPlanState {
  final String message;
  CancelSubscribeSaversPassSuccess(this.message);
}

class CancelSubscribeSaversPassFailed extends SaversPlanState {
  final String message;
  CancelSubscribeSaversPassFailed(this.message);
}

// Checkout Summary Savers Pass States
class CheckoutSummarySaversPassLoading extends SaversPlanState {}

class CheckoutSummarySaversPassSuccess extends SaversPlanState {
  final String message;
  CheckoutSummarySaversPassSuccess(this.message);
}

class CheckoutSummarySaversPassFailed extends SaversPlanState {
  final String message;
  CheckoutSummarySaversPassFailed(this.message);
}

// Resend Email OTP States
class SaversSendResendEmailOtpLoading extends SaversPlanState {}

class SaversSendResendEmailOtpSuccess extends SaversPlanState {
  final String message;
  SaversSendResendEmailOtpSuccess(this.message);
}

class SaversSendResendEmailOtpFailed extends SaversPlanState {
  final String message;
  SaversSendResendEmailOtpFailed(this.message);
}

// Verify Email OTP States
class SaversVerifyEmailOtpLoading extends SaversPlanState {}

class SaversVerifyEmailOtpSuccess extends SaversPlanState {
  final String message;
  SaversVerifyEmailOtpSuccess(this.message);
}

class SaversVerifyEmailOtpFailed extends SaversPlanState {
  final String message;
  SaversVerifyEmailOtpFailed(this.message);
}

// Checkout Summary Savers Pass States
class CheckoutSaversPassLoading extends SaversPlanState {}

class CheckoutSaversPassSuccess extends SaversPlanState {
  final String message;
  final String? paymentUrl;
  final double? total;
  final String? subscriptionId;

  CheckoutSaversPassSuccess(
    this.message, {
    this.paymentUrl,
    this.total,
    this.subscriptionId,
  });
}

class CheckoutSaversPassFailed extends SaversPlanState {
  final String message;
  CheckoutSaversPassFailed(this.message);
}

// Token Refresh States
class RefreshTokenLoading extends SaversPlanState {}

class RefreshTokenSuccess extends SaversPlanState {}

class RefreshTokenFailed extends SaversPlanState {
  final String message;

  RefreshTokenFailed(this.message);
}
