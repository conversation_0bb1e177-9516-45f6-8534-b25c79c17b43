import 'package:db_eats/utils/customtoggle.dart';
import 'package:flutter/material.dart';

class PersonalizedCheckoutPage extends StatefulWidget {
  final int mealPlanId;

  const PersonalizedCheckoutPage({super.key, required this.mealPlanId});

  @override
  State<PersonalizedCheckoutPage> createState() =>
      _PersonalizedCheckoutPageState();
}

class _PersonalizedCheckoutPageState extends State<PersonalizedCheckoutPage> {
  String dropOffOption = 'Meet at my door';
  String instructions = 'Example: Doorbell is broken, please knock';
  String selectedDeliveryOption = 'standard';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF6F3EC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF6F3EC),
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF1F2122)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Checkout',
          style: TextStyle(
            color: Color(0xFF1F2122),
            fontSize: 18,
            fontWeight: FontWeight.w600,
            fontFamily: 'Inter',
            height: 1.24,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Delivery Details Card
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Delivery Details',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      height: 1.24,
                      color: Color(0xFF1F2122),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Address
                  const Text(
                    'Address',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      height: 1.14,
                      letterSpacing: 0.28,
                      color: Color(0xFF1F2122),
                    ),
                  ),
                  const SizedBox(height: 15),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          SizedBox(
                            width: 10.67,
                            height: 12.88,
                            child: Icon(
                              Icons.location_on_outlined,
                              size: 12.88,
                              color: const Color(0xFF414346),
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            '3800 N Central Expy',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              height: 1.43,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ],
                      ),
                      TextButton(
                        onPressed: () {},
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                          minimumSize: const Size(30, 20),
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: const Text(
                          'Edit',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                            height: 1.0,
                            color: Color(0xFF1F2122),
                            decoration: TextDecoration.underline,
                            decorationThickness: 1.5,
                            letterSpacing: 0.7,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Delivery Time
                  const Text(
                    'Delivery',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      height: 1.14,
                      letterSpacing: 0.28,
                      color: Color(0xFF1F2122),
                    ),
                  ),
                  const SizedBox(height: 15),
                  Row(
                    children: [
                      SizedBox(
                        child: Icon(
                          Icons.access_time_rounded,
                          size: 12,
                          color: Color(0xFF1F2122),
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'Today, 8:30AM',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          height: 1.43,
                          color: Color(0xFF1F2122),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Divider(
                    color: const Color(0xFFE1E3E6),
                    thickness: 1,
                  ),
                  const SizedBox(height: 16),

                  // Drop-Off Options
                  const Text(
                    'Drop-Off Options',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      height: 1.14,
                      letterSpacing: 0.28,
                      color: Color(0xFF1F2122),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Container(
                    height: 40,
                    padding: const EdgeInsets.symmetric(horizontal: 18),
                    decoration: BoxDecoration(
                      border: Border.all(color: const Color(0xFFE1E3E6)),
                      borderRadius: BorderRadius.circular(35),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: dropOffOption,
                        isExpanded: true,
                        icon: const Icon(Icons.keyboard_arrow_down,
                            color: Color(0xFF1F2122)),
                        items: <String>[
                          'Meet at my door',
                          'Hand it to me',
                          'Leave with doorman'
                        ].map<DropdownMenuItem<String>>((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(
                              value,
                              style: const TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w400,
                                fontSize: 16,
                                height: 1.5,
                                color: Color(0xFF66696D),
                              ),
                            ),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          setState(() {
                            dropOffOption = newValue!;
                          });
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Instructions
                  const Text(
                    'Drop-Off Instructions',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      height: 1.14,
                      letterSpacing: 0.28,
                      color: Color(0xFF1F2122),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: TextEditingController(), // No initial text here
                    onChanged: (value) {
                      setState(() {
                        instructions = value;
                      });
                    },
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.all(12),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Color(0xFFE1E3E6)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Color(0xFFE1E3E6)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(
                            color: Color(0xFF1F2122), width: 1.5),
                      ),
                      hintText:
                          instructions, // Set instructions as the hint text
                      hintStyle: const TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                        height: 1.5,
                        color: Color(0xFF66696D),
                      ),
                    ),
                    minLines: 5,
                    maxLines: 8,
                    cursorColor: const Color(0xFF1F2122),
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: 16,
                      height: 1.5,
                      color: Color(0xFF66696D),
                    ),
                  ),

                  const SizedBox(height: 16),
                  Divider(
                    color: const Color(0xFFE1E3E6),
                    thickness: 1,
                  ),
                  const SizedBox(height: 16),

                  // Delivery Time Options
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: const [
                          Text(
                            'Delivery Time',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                          Text(
                            '45-50 Minutes',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      // Priority Option
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedDeliveryOption = 'priority';
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: selectedDeliveryOption == 'priority'
                                  ? const Color(0xFF1F2122)
                                  : const Color(0xFFE1E3E6),
                            ),
                            color: selectedDeliveryOption == 'priority'
                                ? const Color(0xFFF1f2f3)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(top: 2),
                                child: Image.asset(
                                  'assets/icons/star.png',
                                  width: 18,
                                  height: 18,
                                  fit: BoxFit.contain,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: const [
                                    Text(
                                      'Priority',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: Color(0xFF1F2122),
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      'XX - XX min delivery time',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w400,
                                        fontSize: 12,
                                        height: 16 / 12,
                                        color: Color(0xFF1F2122),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Text(
                                '+\$5.00',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 8),

// Standard Option
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedDeliveryOption = 'standard';
                          });
                        },
                        child: Container(
                          height: 55,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: selectedDeliveryOption == 'standard'
                                  ? const Color(0xFF1F2122)
                                  : const Color(0xFFE1E3E6),
                            ),
                            color: selectedDeliveryOption == 'standard'
                                ? const Color(0xFFF1f2f3)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/icons/timer.png',
                                width: 18,
                                height: 18,
                                fit: BoxFit.contain,
                              ),
                              const SizedBox(width: 12),
                              const Expanded(
                                child: Text(
                                  'Standard',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF1F2122),
                                  ),
                                ),
                              ),
                              const Text(
                                '+\$2.00',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 8),

// Saver Option
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedDeliveryOption = 'saver';
                          });
                        },
                        child: Container(
                          height: 55,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: selectedDeliveryOption == 'saver'
                                  ? const Color(0xFF1F2122)
                                  : const Color(0xFFE1E3E6),
                            ),
                            color: selectedDeliveryOption == 'saver'
                                ? const Color(0xFFF1f2f3)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/icons/persentage.png',
                                width: 18,
                                height: 18,
                                fit: BoxFit.contain,
                              ),
                              const SizedBox(width: 12),
                              const Expanded(
                                child: Text(
                                  'Saver',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF1F2122),
                                  ),
                                ),
                              ),
                              const Text(
                                '+\$1.00',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

// Payment Card
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Payment',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      height: 1.24,
                      color: Color(0xFF1F2122),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Default Card
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 30,
                            height: 20,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Image.asset(
                              'assets/icons/Visa.png',
                              fit: BoxFit.contain,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Text(
                                    'Card ending with 0001',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      color: Color(0xFF1F2122),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 3,
                                    ),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFCEF8E0),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: const Text(
                                      'Default',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                        height: 1.0, // 12px line height
                                        letterSpacing: 0.24, // 2% of 12px
                                        color: Color(0xFF007A4D),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const Text(
                                'Expires 03/2028',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  height: 1.43, // 20px line height (20/14)
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      IconButton(
                        icon: const Icon(Icons.more_vert,
                            color: Color(0xFF1F2122)),
                        onPressed: () {},
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                        iconSize: 20,
                      ),
                    ],
                  ),
                  const Divider(height: 20, color: Color(0xFFE1E3E6)),

                  // Amount
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 30,
                            height: 20,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Image.asset(
                              'assets/icons/db.png',
                              fit: BoxFit.contain,
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            '\$ 200.00',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              height: 1.43, // 20px line height
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ],
                      ),
                      IconButton(
                        icon: const Icon(Icons.more_vert,
                            color: Color(0xFF1F2122)),
                        onPressed: () {},
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                        iconSize: 20,
                      ),
                    ],
                  ),
                  const Divider(height: 20, color: Color(0xFFE1E3E6)),

                  // Secondary Card
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 30,
                            height: 20,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Image.asset(
                              'assets/icons/Visa.png',
                              fit: BoxFit.contain,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Card ending with 0001',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  height: 1.43, // 20px line height (20/14)
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                              const Text(
                                'Expires 03/2028',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  height: 1.43, // 20px line height (20/14)
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      IconButton(
                        icon: const Icon(Icons.more_vert,
                            color: Color(0xFF1F2122)),
                        onPressed: () {},
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                        iconSize: 20,
                      ),
                    ],
                  ),
                  const Divider(height: 20, color: Color(0xFFE1E3E6)),
                  const SizedBox(height: 12),

                  // Add Payment Method
                  TextButton.icon(
                    onPressed: () {},
                    icon: const Icon(
                      Icons.add,
                      size: 20,
                      color: Color(0xFF1F2122),
                    ),
                    label: const Text(
                      'Add Payment Method',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        color: Color(0xFF414346),
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        height: 1.0, // 16px line height
                        letterSpacing: 0.32, // 2% of 16px
                        decoration: TextDecoration.underline,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.zero,
                      minimumSize: const Size(30, 20),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      alignment: Alignment.centerLeft,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

// Order Summary Card
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Order Summary',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      height: 1.24,
                      color: Color(0xFF1F2122),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Delivery Time Slot
                  Row(
                    children: List.generate(50, (index) {
                      return Container(
                        width: 2.5,
                        height: 1,
                        margin: const EdgeInsets.symmetric(horizontal: 2),
                        color: const Color(0xFFE1E3E6),
                      );
                    }),
                  ),
                  const SizedBox(height: 14),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Delivery Time Slot',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF1F2122),
                        ),
                      ),
                      Row(
                        children: const [
                          Icon(
                            Icons.access_time_rounded,
                            size: 16,
                            color: Color(0xFF1F2122),
                          ),
                          SizedBox(width: 4),
                          Text(
                            '12:00PM-1:00PM',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 14),
                  Row(
                    children: List.generate(50, (index) {
                      return Container(
                        width: 2.5,
                        height: 1,
                        margin: const EdgeInsets.symmetric(horizontal: 2),
                        color: const Color(0xFFE1E3E6),
                      );
                    }),
                  ),
                  const SizedBox(height: 12),

                  // Order Details
                  const Text(
                    'Order Details (30 Items)',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      height: 1.0, // 16px line height
                      letterSpacing: 0.32, // 2% of 16px
                      color: Color(0xFF1F2122),
                    ),
                  ),
                  const SizedBox(height: 8),

                  // Daily Orders
                  _buildDailyOrderItem(
                    date: 'Jun 13, 2024, Mon',
                    chef: 'Vishwanathan C.',
                    price: 9.00,
                  ),
                  _buildDailyOrderItem(
                    date: 'Jun 14, 2024, Tue',
                    chef: 'Hiroshi S.',
                    price: 9.00,
                  ),
                  _buildDailyOrderItem(
                    date: 'Jun 15, 2024, Wed',
                    chef: 'Jennifer W.',
                    price: 9.00,
                  ),
                  _buildDailyOrderItem(
                    date: 'Jun 16, 2024, Thu',
                    chef: 'Omar H.',
                    price: 9.00,
                  ),
                  _buildDailyOrderItem(
                    date: 'Jun 17, 2024, Fri',
                    chef: 'Sandeep M.',
                    price: 9.00,
                  ),

                  const SizedBox(height: 16),
                  Row(
                    children: List.generate(50, (index) {
                      return Container(
                        width: 2.5,
                        height: 1,
                        margin: const EdgeInsets.symmetric(horizontal: 2),
                        color: const Color(0xFFE1E3E6),
                      );
                    }),
                  ),
                  const SizedBox(height: 16),

                  // Promo Code
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: const [
                          Icon(
                            Icons.percent,
                            size: 16,
                            color: Color(0xFF1F2122),
                          ),
                          SizedBox(width: 4),
                          Text(
                            'Add promo code',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ],
                      ),
                      TextButton(
                        onPressed: () {},
                        style: TextButton.styleFrom(
                          minimumSize: Size.zero,
                          padding: EdgeInsets.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.add,
                              size: 15,
                              color: Color(0xFF1F2122),
                            ),
                            const SizedBox(width: 4),
                            Column(
                              children: [
                                const Text(
                                  'Add',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w600,
                                    fontSize: 12,
                                    height: 1.0, // 12px line height
                                    letterSpacing: 0.24, // 2% of 12px
                                    color: Color(0xFF414346),
                                  ),
                                ),
                                const SizedBox(height: 1),
                                Container(
                                  height: 1,
                                  width: 21, // Width matching "Add" text
                                  color: Color(0xFF1F2122),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Row(
                  //   children: [
                  //     Container(
                  //       width: 24,
                  //       height: 17,
                  //       decoration: BoxDecoration(
                  //         color: const Color(0xFF1F2122),
                  //         borderRadius: BorderRadius.circular(4),
                  //       ),
                  //       alignment: Alignment.center,
                  //       child: const Text(
                  //         'DB',
                  //         style: TextStyle(
                  //           fontFamily: 'Inter',
                  //           fontSize: 10,
                  //           fontWeight: FontWeight.bold,
                  //           color: Colors.white,
                  //         ),
                  //       ),
                  //     ),
                  //     const SizedBox(width: 8),
                  //     CustomToggle(
                  //       value: false,
                  //       onChanged: (bool newValue) {
                  //         // Handle toggle
                  //       },
                  //     ),
                  //   ],
                  // ),
                  const SizedBox(height: 40),
                  const SizedBox(height: 16),
                  Row(
                    children: List.generate(50, (index) {
                      return Container(
                        width: 2.5,
                        height: 1,
                        margin: const EdgeInsets.symmetric(horizontal: 2),
                        color: const Color(0xFFE1E3E6),
                      );
                    }),
                  ),
                  const SizedBox(height: 12),

                  // Order Total Section
                  Column(
                    children: [
                      // Order Total Header
                      const Row(
                        children: [
                          Text(
                            'Order Total',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // Subtotal
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: const [
                          Text(
                            'Subtotal',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              height: 1.43, // 20px line height (20/14)
                              color: Color(0xFF1F2122),
                            ),
                          ),
                          Text(
                            '\$70.00',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              height: 1.33, // 16px line height (16/12)
                              color: Color(0xFF414346),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),

                      // Delivery fee
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: const [
                          Text(
                            'Delivery fee',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              height: 1.43, // 20px line height (20/14)
                              color: Color(0xFF1F2122),
                            ),
                          ),
                          Text(
                            '\$5.00',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),

                      // Discounts
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: const [
                          Text(
                            'Discounts',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              height: 1.43, // 20px line height (20/14)
                              color: Color(0xFF1F2122),
                            ),
                          ),
                          Text(
                            '-\$2.00',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFFD31510),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),

                      // DB Wallet Credits
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: const [
                          Text(
                            'DB Wallet Credits',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              height: 1.43, // 20px line height (20/14)
                              color: Color(0xFF1F2122),
                            ),
                          ),
                          Text(
                            '-\$3.00',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFFD31510),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),

                      // Taxes & Fees
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: const [
                          Text(
                            'Taxes & Fees',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              height: 1.43, // 20px line height (20/14)
                              color: Color(0xFF1F2122),
                            ),
                          ),
                          Text(
                            '\$0.00',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      // Total Row with thicker divider above
                      Row(
                        children: List.generate(50, (index) {
                          return Container(
                            width: 2.5,
                            height: 1,
                            margin: const EdgeInsets.symmetric(horizontal: 2),
                            color: const Color(0xFFE1E3E6),
                          );
                        }),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: const [
                          Text(
                            'Total',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              height: 1.5, // 24px line height (24/16)
                              color: Color(0xFF1F2122),
                            ),
                          ),
                          Text(
                            '\$70.00',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              height: 1.5, // 24px line height (24/16)
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      // Place Order Button
                      SizedBox(
                        width: double.infinity,
                        height: 40,
                        child: ElevatedButton(
                          onPressed: () {},
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF1F2122),
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(24),
                            ),
                            elevation: 0,
                          ),
                          child: const Text(
                            'Place Order',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              height: 1.0, // 16px line height
                              letterSpacing: 0.32, // 2% of 16px
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 18),

                      // Disclaimer Text
                      const Text(
                        "By clicking the 'Place Order' button, a one-time payment for the 5-day meal plan will be charged.",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          height: 1.43, // 20px line height (20/14)
                          color: Color(0xFF66696D),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 25),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Add the helper method for daily order items
  Widget _buildDailyOrderItem({
    required String date,
    required String chef,
    required double price,
  }) {
    Map<String, String> chefImages = {
      'Vishwanathan C.': 'assets/images/chef_10.png',
      'Hiroshi S.': 'assets/images/chef_2.png',
      'Jennifer W.': 'assets/images/chef_3.png',
      'Omar H.': 'assets/images/chef_4.png',
      'Sandeep M.': 'assets/images/chef_7.png',
    };

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          Text(
            date,
            style: const TextStyle(
              fontFamily: 'Inter',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1F2122),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                children: [
                  CircleAvatar(
                    radius: 12, // Smaller radius for chef image
                    backgroundImage: AssetImage(chefImages[chef] ??
                        'assets/images/chef_placeholder.png'),
                    backgroundColor: const Color(0xFFE1DDD5),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFE1E3E6),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      '6×',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        height: 16 / 14, // Line height of 16px
                        letterSpacing: 0.28, // 2% of 14px
                        color: Color(0xFF1F2122),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Chef $chef',
                      style: const TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    const SizedBox(height: 7),
                    Padding(
                      padding: const EdgeInsets.only(left: 9.0),
                      child: const Text(
                        '2 servings, 3 dishes',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF1F2122),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.only(left: 9.0),
                      child: TextButton(
                        onPressed: () {},
                        style: TextButton.styleFrom(
                          minimumSize: Size.zero,
                          padding: EdgeInsets.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: const Text(
                          'Edit',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            height: 1.0, // 12px line height
                            letterSpacing: 0.24, // 2% of 12px
                            color: Color(0xFF414346),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '\$${price.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF1F2122),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
