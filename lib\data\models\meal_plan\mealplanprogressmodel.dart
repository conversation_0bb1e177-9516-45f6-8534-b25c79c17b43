class MealPlanProgressModel {
  bool? status;
  Data? data;
  int? statusCode;

  MealPlanProgressModel({this.status, this.data, this.statusCode});

  MealPlanProgressModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['status_code'] = statusCode;
    return data;
  }
}

class Data {
  int? id;
  int? stepProgress;
  String? createdAt;
  int? customerId;
  int? mealPlanDuration;
  String? endDate;
  String? startDate;
  int? timeSlotId;
  int? servingSizeId;
  int? dishesPerDay;
  String? mealSelectionType;
  int? dietaryPreferenceId;
  int? spiceLevelId;
  dynamic dropOffOptionId;
  dynamic dropOffInstructions;
  dynamic deliveryTimeId;
  String? subtotal;
  String? deliveryFee;
  String? discount;
  String? walletCredits;
  String? taxesAndFees;
  num? total;
  String? status;
  int? totalDiscount;
  int? serviceFeePercentage;
  num? serviceFee;
  int? taxPercentage;
  num? tax;
  num? walletBalance;
  TimeSlot? timeSlot;
  ServingSize? servingSize;
  DietaryPreference? dietaryPreference;
  DietaryPreference? spiceLevel;
  List<Cuisine>? cuisines;
  List<Subcuisine>? subcuisines;
  List<Localcuisine>? localcuisines;
  List<MealPlanDay>? mealPlanDays;
  List<MealPlanDay>? personalizedDays;

  Data({
    this.id,
    this.stepProgress,
    this.createdAt,
    this.customerId,
    this.mealPlanDuration,
    this.endDate,
    this.startDate,
    this.timeSlotId,
    this.servingSizeId,
    this.dishesPerDay,
    this.mealSelectionType,
    this.dietaryPreferenceId,
    this.spiceLevelId,
    this.dropOffOptionId,
    this.dropOffInstructions,
    this.deliveryTimeId,
    this.subtotal,
    this.deliveryFee,
    this.discount,
    this.walletCredits,
    this.taxesAndFees,
    this.total,
    this.status,
    this.totalDiscount,
    this.serviceFeePercentage,
    this.serviceFee,
    this.taxPercentage,
    this.tax,
    this.walletBalance,
    this.timeSlot,
    this.servingSize,
    this.dietaryPreference,
    this.spiceLevel,
    this.cuisines,
    this.subcuisines,
    this.localcuisines,
    this.mealPlanDays,
    this.personalizedDays,
  });

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    stepProgress = json['step_progress'];
    createdAt = json['created_at'];
    customerId = json['customer_id'];
    mealPlanDuration = json['meal_plan_duration'];
    endDate = json['end_date'];
    startDate = json['start_date'];
    timeSlotId = json['time_slot_id'];
    servingSizeId = json['serving_size_id'];
    dishesPerDay = json['dishes_per_day'];
    mealSelectionType = json['meal_selection_type'];
    dietaryPreferenceId = json['dietary_preference_id'];
    spiceLevelId = json['spice_level_id'];
    dropOffOptionId = json['drop_off_option_id'];
    dropOffInstructions = json['drop_off_instructions'];
    deliveryTimeId = json['delivery_time_id'];
    subtotal = json['subtotal']?.toString();
    deliveryFee = json['delivery_fee']?.toString();
    discount = json['discount']?.toString();
    walletCredits = json['wallet_credits']?.toString();
    taxesAndFees = json['taxes_and_fees']?.toString();
    total = json['total'];
    status = json['status'];
    totalDiscount = json['total_discount']?.toInt();
    serviceFeePercentage = json['service_fee_percentage'];
    serviceFee = json['service_fee'];
    taxPercentage = json['tax_percentage'];
    tax = json['tax'];
    walletBalance = json['wallet_balance'];
    timeSlot =
        json['timeSlot'] != null ? TimeSlot.fromJson(json['timeSlot']) : null;
    servingSize = json['serving_size'] != null
        ? ServingSize.fromJson(json['serving_size'])
        : null;
    dietaryPreference = json['dietary_preference'] != null
        ? DietaryPreference.fromJson(json['dietary_preference'])
        : null;
    spiceLevel = json['spice_level'] != null
        ? DietaryPreference.fromJson(json['spice_level'])
        : null;

    if (json['cuisines'] != null) {
      cuisines = <Cuisine>[];
      json['cuisines'].forEach((v) {
        cuisines!.add(Cuisine.fromJson(v));
      });
    }

    if (json['subcuisines'] != null) {
      subcuisines = <Subcuisine>[];
      json['subcuisines'].forEach((v) {
        subcuisines!.add(Subcuisine.fromJson(v));
      });
    }

    if (json['localcuisines'] != null) {
      localcuisines = <Localcuisine>[];
      json['localcuisines'].forEach((v) {
        localcuisines!.add(Localcuisine.fromJson(v));
      });
    }

    if (json['meal_plan_days'] != null) {
      mealPlanDays = <MealPlanDay>[];
      json['meal_plan_days'].forEach((v) {
        mealPlanDays!.add(MealPlanDay.fromJson(v));
      });
    }

    if (json['personalized_days'] != null) {
      personalizedDays = <MealPlanDay>[];
      json['personalized_days'].forEach((v) {
        personalizedDays!.add(MealPlanDay.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['step_progress'] = stepProgress;
    data['created_at'] = createdAt;
    data['customer_id'] = customerId;
    data['meal_plan_duration'] = mealPlanDuration;
    data['end_date'] = endDate;
    data['start_date'] = startDate;
    data['time_slot_id'] = timeSlotId;
    data['serving_size_id'] = servingSizeId;
    data['dishes_per_day'] = dishesPerDay;
    data['meal_selection_type'] = mealSelectionType;
    data['dietary_preference_id'] = dietaryPreferenceId;
    data['spice_level_id'] = spiceLevelId;
    data['drop_off_option_id'] = dropOffOptionId;
    data['drop_off_instructions'] = dropOffInstructions;
    data['delivery_time_id'] = deliveryTimeId;
    data['subtotal'] = subtotal;
    data['delivery_fee'] = deliveryFee;
    data['discount'] = discount;
    data['wallet_credits'] = walletCredits;
    data['taxes_and_fees'] = taxesAndFees;
    data['total'] = total;
    data['status'] = status;
    data['total_discount'] = totalDiscount;
    data['service_fee_percentage'] = serviceFeePercentage;
    data['service_fee'] = serviceFee;
    data['tax_percentage'] = taxPercentage;
    data['tax'] = tax;
    data['wallet_balance'] = walletBalance;

    if (timeSlot != null) {
      data['timeSlot'] = timeSlot!.toJson();
    }
    if (servingSize != null) {
      data['servingSize'] = servingSize!.toJson();
    }
    if (dietaryPreference != null) {
      data['dietaryPreference'] = dietaryPreference!.toJson();
    }
    if (spiceLevel != null) {
      data['spiceLevel'] = spiceLevel!.toJson();
    }
    if (cuisines != null) {
      data['cuisines'] = cuisines!.map((v) => v.toJson()).toList();
    }
    if (subcuisines != null) {
      data['subcuisines'] = subcuisines!.map((v) => v.toJson()).toList();
    }
    if (localcuisines != null) {
      data['localcuisines'] = localcuisines!.map((v) => v.toJson()).toList();
    }
    if (mealPlanDays != null) {
      data['meal_plan_days'] = mealPlanDays!.map((v) => v.toJson()).toList();
    }
    if (personalizedDays != null) {
      data['personalized_days'] =
          personalizedDays!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class MealPlanDay {
  int? id;
  String? date;
  String? dayOfWeek;
  Chef? chef;
  String? dayTotal;
  int? dayNumber;
  num? discount;
  num? price;
  num? deliveryFee;
  num? distance;
  int? duration;
  String? durationText;
  int? serviceFeePercentage;
  num? serviceFee;
  int? taxPercentage;
  num? tax;
  List<MealItem>? items;

  MealPlanDay({
    this.id,
    this.date,
    this.dayOfWeek,
    this.chef,
    this.dayTotal,
    this.dayNumber,
    this.discount,
    this.price,
    this.deliveryFee,
    this.distance,
    this.duration,
    this.durationText,
    this.serviceFeePercentage,
    this.serviceFee,
    this.taxPercentage,
    this.tax,
    this.items,
  });

  MealPlanDay.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    date = json['date'];
    dayOfWeek = json['day_of_week'];
    chef = json['chef'] != null ? Chef.fromJson(json['chef']) : null;
    dayTotal = json['day_total'];
    dayNumber = json['day_number'];
    discount = json['discount'];
    price = json['price'];
    deliveryFee = json['delivery_fee'];
    distance = json['distance'];
    duration = json['duration'];
    durationText = json['duration_text'];
    serviceFeePercentage = json['service_fee_percentage'];
    serviceFee = json['service_fee'];
    taxPercentage = json['tax_percentage'];
    tax = json['tax'];

    if (json['items'] != null) {
      items = <MealItem>[];
      json['items'].forEach((v) {
        items!.add(MealItem.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['date'] = date;
    data['day_of_week'] = dayOfWeek;
    if (chef != null) {
      data['chef'] = chef!.toJson();
    }
    data['day_total'] = dayTotal;
    data['day_number'] = dayNumber;
    data['discount'] = discount;
    data['price'] = price;
    data['delivery_fee'] = deliveryFee;
    data['distance'] = distance;
    data['duration'] = duration;
    data['duration_text'] = durationText;
    data['service_fee_percentage'] = serviceFeePercentage;
    data['service_fee'] = serviceFee;
    data['tax_percentage'] = taxPercentage;
    data['tax'] = tax;
    if (items != null) {
      data['items'] = items!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Chef {
  int? id;
  String? firstName;
  String? lastName;
  String? profilePhoto;
  List<String>? searchTags;

  Chef({
    this.id,
    this.firstName,
    this.lastName,
    this.profilePhoto,
    this.searchTags,
  });

  Chef.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    profilePhoto = json['profile_photo'];
    searchTags = json['search_tags']?.cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    data['profile_photo'] = profilePhoto;
    data['search_tags'] = searchTags;
    return data;
  }
}

class MealItem {
  int? id;
  int? chefMenuItemId;
  int? quantity;
  String? price;
  MenuItem? menuItem;

  MealItem({
    this.id,
    this.chefMenuItemId,
    this.quantity,
    this.price,
    this.menuItem,
  });

  MealItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    chefMenuItemId = json['chef_menu_item_id'];
    quantity = json['quantity'];
    price = json['price'];
    menuItem =
        json['menu_item'] != null ? MenuItem.fromJson(json['menu_item']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['chef_menu_item_id'] = chefMenuItemId;
    data['quantity'] = quantity;
    data['price'] = price;
    if (menuItem != null) {
      data['menu_item'] = menuItem!.toJson();
    }
    return data;
  }
}

class MenuItem {
  int? id;
  String? name;
  String? photo;

  MenuItem({this.id, this.name, this.photo});

  MenuItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['photo'] = photo;
    return data;
  }
}

class TimeSlot {
  int? id;
  String? startTime;
  String? endTime;

  TimeSlot({this.id, this.startTime, this.endTime});

  TimeSlot.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    return data;
  }
}

class ServingSize {
  int? id;
  String? title;
  int? serves;

  ServingSize({this.id, this.title, this.serves});

  ServingSize.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    serves = json['serves'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['serves'] = serves;
    return data;
  }
}

class DietaryPreference {
  int? id;
  String? name;

  DietaryPreference({this.id, this.name});

  DietaryPreference.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class Cuisine {
  int? id;
  String? name;

  Cuisine({this.id, this.name});

  Cuisine.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class Subcuisine {
  int? id;
  String? name;

  Subcuisine({this.id, this.name});

  Subcuisine.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class Localcuisine {
  int? id;
  String? name;

  Localcuisine({this.id, this.name});

  Localcuisine.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}
