{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e225dbd93effac5081b5b44c982839f4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988a0c5d3135dcab14b3d150767d7a63d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988a0c5d3135dcab14b3d150767d7a63d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98785e9a264f9ebde2e8009f5327bcce6c", "guid": "bfdfe7dc352907fc980b868725387e985f0085899a9f5aec5a0202605a8918e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844cd285448a7385d6648e33b4669516e", "guid": "bfdfe7dc352907fc980b868725387e98611718009e578d911dfa24820f8938c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8073a49e97a5a1d34404e2da2d4e535", "guid": "bfdfe7dc352907fc980b868725387e98a6682a128e26f73988588a0b9dc99c4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce9235f9471b577f8f2d127db562d7a4", "guid": "bfdfe7dc352907fc980b868725387e98a3fcd6fa162c0d365d634616e789d6ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98722e462f183e87a9f1b6d7465452941a", "guid": "bfdfe7dc352907fc980b868725387e983e6167a4e115668fb3799c465a0cfc40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc17a3bba280f4ee45aad0a3e913236", "guid": "bfdfe7dc352907fc980b868725387e98322b1742f5524a6d0e37f0b0497cc76c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5007b9742661baabc0cad56709f0e82", "guid": "bfdfe7dc352907fc980b868725387e9843ada8db56df3fa463ceac3d83ee854b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abefde05b4748025d74463c0e48231cd", "guid": "bfdfe7dc352907fc980b868725387e980ac4bf4b39ebe460e5e8c74c9450f291", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cafc2d2acce24811162969cbba1ff7d", "guid": "bfdfe7dc352907fc980b868725387e98bbd8a28e44ee02016bb2b7154fb70861", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcefaf1ce3a8510dbfcdc1e8f716dc2c", "guid": "bfdfe7dc352907fc980b868725387e9843fad2db3368a046c718d5256715c904", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f80dd7985126d034236434abc8aff891", "guid": "bfdfe7dc352907fc980b868725387e9828d12d05508c1581ba2436be33f4de22", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98224621af2c30fcfd48aeaf44b2879aa5", "guid": "bfdfe7dc352907fc980b868725387e982a76ee02c6f40170d9e26dcda3f47392", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855ccb998c790e3cc8aeeafd28a15123d", "guid": "bfdfe7dc352907fc980b868725387e982179a03359c5c1547754ab9df73dc6ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc46265b397560e8a7e52a97f94ad725", "guid": "bfdfe7dc352907fc980b868725387e98d678334a5d238374f2ad9c04b29d4364", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98263db4639836237e06b340778ee42546", "guid": "bfdfe7dc352907fc980b868725387e98775d03fe3e3492d0099115e1bf707a2c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987348d69ed4266433dbc5ca6d0ec8f0d1", "guid": "bfdfe7dc352907fc980b868725387e98079c00739040ad2f95375b026b07ccfc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ea49cb0e466dab6cffd4817aa01c075", "guid": "bfdfe7dc352907fc980b868725387e98e3932ebd3a8b73ab380edcbb9178253b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899ac66b9c459f0ea84f11af8bda3d886", "guid": "bfdfe7dc352907fc980b868725387e98a280d73544069688727cb901c0e4c23b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5884918b8adb8653e507a8ed9c20174", "guid": "bfdfe7dc352907fc980b868725387e98b0f7618034ed189c15db88fd3fea3d89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1acc13f7cd5e86648ef305f014a9bee", "guid": "bfdfe7dc352907fc980b868725387e98c601d2214f03bd2cd74b3097ecac9d80", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983e20fa1914f5025baa6322abccfa8584", "guid": "bfdfe7dc352907fc980b868725387e98adadec3b201175c8d0e30e0e0875c721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98390825ad6d5e19ef4b8c072003fc1efc", "guid": "bfdfe7dc352907fc980b868725387e9855bbf862dbacc561aea6d6c3b71beffd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a78d93cdc522f0b0327be160524a8d7a", "guid": "bfdfe7dc352907fc980b868725387e980933bd8b7809f3d220559f518c73495b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815fe1109492a7c89a4e18450a196bde6", "guid": "bfdfe7dc352907fc980b868725387e984f3927b5025fbab50f810894b2944645"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981505fc1950c5825cff29e69cb131d1dd", "guid": "bfdfe7dc352907fc980b868725387e98a7337d83be2cab6dc31cc46cebb38081"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855b9f688f00896397a0d7a286a36ccec", "guid": "bfdfe7dc352907fc980b868725387e98c97a8ecbd5b2762aca0093db40716ef3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da8e2a82c2f9ad6bcd7fb4e1ac79a236", "guid": "bfdfe7dc352907fc980b868725387e98ec11d162d18201d0db7fc2a6c6450791"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eb53f92ee16a630390f3892bd6a699c", "guid": "bfdfe7dc352907fc980b868725387e98b89d056539b9db589f8f435fb0ee22cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983474aa27b8d37621640bc3d78202957c", "guid": "bfdfe7dc352907fc980b868725387e98e74e91e4c474192ff4f6c671250611a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835a1625893a4a5d40cde997e117b16d2", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aad191772c5add3c46a3ade3d8408bd1", "guid": "bfdfe7dc352907fc980b868725387e98f3c9db90a8372e8e2e2d76a4d3f5b63f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882d088f0186819cd625813d9068ecdb0", "guid": "bfdfe7dc352907fc980b868725387e98ac4a1bc695ff6aa06833c9cbb5c44312"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989faef2ed70f34b4ffc7a7c37cdf5af1f", "guid": "bfdfe7dc352907fc980b868725387e984219fb5f96a080c36f13a083e2af6774"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b6f2db848fe02accbfb804719dd05f6", "guid": "bfdfe7dc352907fc980b868725387e98ad13bb31d800eda1d0c0c6976c915b1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800a0e7083269859f8763dfcf33fe563c", "guid": "bfdfe7dc352907fc980b868725387e98fdd592debdee1d55b93b79d74af0e92b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0f75a9c63eee06d9ed26bc40ba01228", "guid": "bfdfe7dc352907fc980b868725387e98cf4f6f22792b8db021e8e1c754326233"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}