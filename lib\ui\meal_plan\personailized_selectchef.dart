import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/meal_plan/filterchefsmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/meal_plan/personailized_selectmeals.dart';
import 'package:db_eats/ui/meal_plan/personalized_final_mealplan.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';

class PersonailizedSelectchef extends StatefulWidget {
  final List<String>? dates;
  final int currentday;
  final int mealPlanId;
  final Map<String, Map<String, dynamic>>? mealdata;
  final Map<String, dynamic>? dataresponce;
  final String? timeSlots;
  final bool isEditing;
  final int? editDayId;
  final String? editDate;

  const PersonailizedSelectchef({
    super.key,
    this.dates,
    required this.mealPlanId,
    required this.currentday,
    this.mealdata,
    this.dataresponce,
    this.timeSlots,
    this.isEditing = false,
    this.editDayId,
    this.editDate,
  });

  @override
  State<PersonailizedSelectchef> createState() =>
      _PersonailizedSelectchefState();
}

class _PersonailizedSelectchefState extends State<PersonailizedSelectchef> {
  late final MealplanBloc _mealPlanBloc;
  List<Chefs> _chefs = [];
  bool _isLoading = true;
  final String _originalStartDate = '';
  String _planduration = '';
  String _displayStartDate = '';
  final String _deliveryTime = '';
  final int _maxDishesPerDay = 0;
  late int _servingSizeId;
  late int _timeSlotId;
  final List<String> _generatedDates = [];
  int _currentDay = 0;
  final int _selectedDate = 0;

  Color kBlack = Color(0xFF1F2122);
  Color kSecondBlack = Color(0xFF414346);

  String _startDate = '';
  String _endDate = '';
  List<String> _workingDays = [];
  int _mealPlanDuration = 5; // default duration

  int? _selectedChefId;
  String? _preservedDate;

  // Add new property to store view day response
  Map<String, dynamic>? viewDayData;

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  void initState() {
    super.initState();
    _mealPlanBloc = BlocProvider.of<MealplanBloc>(context);

    if (widget.dataresponce != null) {
      Map<String, dynamic> dataresponce = Map.from(widget.dataresponce!);
      _servingSizeId = dataresponce['serving_size_id'] ?? 0;
      _timeSlotId = dataresponce['time_slot_id'] ?? 0;
      _startDate = dataresponce['start_date'] ?? DateTime.now().toString();
      _endDate = dataresponce['end_date'] ?? '';
      _planduration = (dataresponce['meal_plan_duration'] ?? 5).toString();
      _mealPlanDuration = int.parse(_planduration);

      _generateAndSetWorkingDays();
      _updateToNextIncompleteDay();

      // Load filtered chefs after initialization
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadFilteredChefs();
      });
    }

    // If in edit mode, fetch day details
    if (widget.isEditing && widget.editDayId != null) {
      _preservedDate = widget.editDate;
      context.read<MealplanBloc>().add(ViewDayEvent(widget.editDayId!));
    }
  }

  void _generateAndSetWorkingDays() {
    if (_startDate.isNotEmpty && _endDate.isNotEmpty) {
      _workingDays = _generateWorkingDaysFromRange(_startDate, _endDate);
    } else {
      _workingDays = _generateWorkingDays(_startDate, _mealPlanDuration);
    }
  }

  void _updateToNextIncompleteDay() {
    _currentDay = _findNextIncompleteDay();
    _displayStartDate = _getSelectedDate(_currentDay);
  }

  List<String> _generateWorkingDays(String startDate, int planDuration) {
    List<String> dates = [];
    DateTime currentDate = DateTime.parse(startDate);

    while (dates.length < planDuration) {
      // Skip weekends (Saturday and Sunday)
      if (currentDate.weekday != DateTime.saturday &&
          currentDate.weekday != DateTime.sunday) {
        dates.add(_formatStorageDate(currentDate));
      }
      currentDate = currentDate.add(const Duration(days: 1));
    }

    return dates;
  }

  List<String> _generateWorkingDaysFromRange(String startDate, String endDate) {
    List<String> dates = [];
    try {
      DateTime start = DateTime.parse(startDate);
      DateTime end = DateTime.parse(endDate);
      DateTime current = start;

      while (!current.isAfter(end)) {
        if (current.weekday != DateTime.saturday &&
            current.weekday != DateTime.sunday) {
          dates.add(_formatStorageDate(current));
        }
        current = current.add(const Duration(days: 1));
      }
    } catch (e) {
      print("Error generating working days from range: $e");
    }
    return dates;
  }

  int _findNextIncompleteDay() {
    // Check each working day until finding first incomplete one
    for (int i = 0; i < _workingDays.length; i++) {
      String date = _workingDays[i];
      if (!_isDayCompleted(date)) {
        return i + 1; // Return 1-based index
      }
    }
    // If all completed, return last day
    return _workingDays.isEmpty ? 1 : _workingDays.length;
  }

  String _getSelectedDate(int dayIndex) {
    int index = dayIndex - 1;
    if (_workingDays.isNotEmpty && index >= 0 && index < _workingDays.length) {
      return _workingDays[index];
    }
    return _startDate;
  }

  void _loadFilteredChefs() {
    String selectedDate = _getSelectedDate(_currentDay);
    Map<String, dynamic> requestData = {
      'search_keyword': '',
      'latitude': Initializer.latitude ?? '0',
      'longitude': Initializer.longitude ?? '0',
      'serving_size_id': _servingSizeId,
      'time_slot_id': _timeSlotId,
      'date': selectedDate,
    };

    print("Loading chefs with data: $requestData"); // Add debug log
    context.read<MealplanBloc>().add(FilterChefsEvent(requestData));
  }

  String _formatDate(String date) {
    if (date.isEmpty) return "Loading...";
    final DateTime dateTime = DateTime.parse(date);
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return "${months[dateTime.month - 1]} ${dateTime.day}, ${dateTime.year}, ${days[dateTime.weekday - 1]}";
  }

  String _formatTimeToAmPm(String? time) {
    if (time == null) return '';
    try {
      final timeParts = time.split(':');
      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);
      String period = hour >= 12 ? 'PM' : 'AM';
      hour = hour > 12 ? hour - 12 : hour;
      hour = hour == 0 ? 12 : hour;
      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}$period';
    } catch (e) {
      return time;
    }
  }

  void _validateDateRange() {
    if (_startDate.isEmpty || _endDate.isEmpty) return;

    DateTime start = DateTime.parse(_startDate);
    DateTime end = DateTime.parse(_endDate);

    // Calculate number of working days between start and end
    int workingDays = 0;
    DateTime current = start;

    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      if (current.weekday != DateTime.saturday &&
          current.weekday != DateTime.sunday) {
        workingDays++;
      }
      current = current.add(const Duration(days: 1));
    }

    if (workingDays != _mealPlanDuration) {
      // Recalculate end date based on meal plan duration
      _workingDays = _generateWorkingDays(_startDate, _mealPlanDuration);
      _endDate = _workingDays.last;
    }
  }

  String _formatStorageDate(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  bool _isDayCompleted(String date) {
    return (widget.mealdata ?? {}).containsKey(date) &&
        widget.mealdata?[date] != null &&
        widget.mealdata?[date]?['selectedDishes'] != null &&
        ((widget.mealdata?[date]?['selectedDishes'] as List?)?.isNotEmpty ??
            false);
  }

  bool _areAllDaysCompleted() {
    final planDuration = int.tryParse(
            widget.dataresponce?['meal_plan_duration']?.toString() ?? '5') ??
        5;
    final dates =
        _generatedDates.isNotEmpty ? _generatedDates : widget.dates ?? [];

    if (dates.length < planDuration) return false;

    for (int i = 0; i < planDuration; i++) {
      if (!_isDayCompleted(dates[i])) {
        return false;
      }
    }
    return true;
  }

  Widget _buildChefShimmerGrid() {
    return Column(
      children: List.generate(
        3,
        (index) => Padding(
          padding: EdgeInsets.fromLTRB(sixteen, 0, sixteen, sixteen),
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(sixteen),
              ),
              child: Padding(
                padding: EdgeInsets.all(sixteen),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: twelve),
                        Container(
                          width: 150,
                          height: 20,
                          color: Colors.white,
                        ),
                      ],
                    ),
                    SizedBox(height: twelve),
                    Container(
                      width: 80,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(twelve),
                      ),
                    ),
                    SizedBox(height: twelve),
                    Container(
                      width: double.infinity,
                      height: sixteen,
                      color: Colors.white,
                    ),
                    SizedBox(height: eighteen),
                    Container(
                      width: 120,
                      height: forteen,
                      color: Colors.white,
                    ),
                    SizedBox(height: twelve),
                    Container(
                      width: double.infinity,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(28),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoChefMessage() {
    return Container(
      margin: EdgeInsets.fromLTRB(sixteen, 32, sixteen, sixteen),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(sixteen),
        border: Border.all(color: const Color(0xFFE1DDD5)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            'assets/images/cooking.png',
            width: 90,
            height: 90,
          ),
          SizedBox(height: sixteen),
          Text(
            'No Chefs Available',
            style: TextStyle(
              fontSize: eighteen,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
              color: Color(0xFF1F2122),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Sorry, there are no chefs available in your area at the moment.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: forteen,
              fontWeight: FontWeight.w400,
              fontFamily: 'Inter',
              color: Color(0xFF414346),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateTimeShimmer() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: sixteen),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: sixteen,
                  height: sixteen,
                  color: Colors.white,
                ),
                const SizedBox(width: 8),
                Container(
                  width: 150,
                  height: forteen,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const Spacer(),
                Row(
                  children: List.generate(
                    5,
                    (index) => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: Container(
                        width: 25,
                        height: 25,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  width: sixteen,
                  height: sixteen,
                  color: Colors.white,
                ),
                const SizedBox(width: 8),
                Container(
                  width: 100,
                  height: forteen,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: sixteen, vertical: twelve),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.calendar_today,
                            size: sixteen, color: kSecondBlack),
                        SizedBox(width: 8),
                        Text(
                          _formatDate(_displayStartDate),
                          style: TextStyle(
                            fontSize: forteen,
                            fontWeight: FontWeight.w500,
                            color: kBlack,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    if (widget.isEditing &&
                        viewDayData != null &&
                        viewDayData!['time_slot'] != null)
                      Row(
                        children: [
                          Icon(Icons.access_time,
                              size: sixteen, color: kSecondBlack),
                          SizedBox(width: 8),
                          Text(
                            "${_formatTimeToAmPm(viewDayData!['time_slot']['start_time'])} - ${_formatTimeToAmPm(viewDayData!['time_slot']['end_time'])}",
                            style: TextStyle(
                              fontSize: twelve,
                              color: kSecondBlack,
                            ),
                          ),
                        ],
                      )
                    else if (!widget.isEditing)
                      Row(
                        children: [
                          Icon(Icons.access_time,
                              size: sixteen, color: kSecondBlack),
                          SizedBox(width: 8),
                          Text(
                            widget.timeSlots ?? "_",
                            style: TextStyle(
                              fontSize: twelve,
                              color: kSecondBlack,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
              // Non-interactive date indicator row with connecting lines
              Row(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(
                  _mealPlanDuration * 2 - 1,
                  (index) {
                    if (index.isOdd) {
                      // Draw connecting line
                      return Container(
                        width: sixteen,
                        height: 1,
                        color: const Color(0xFFE1DDD5),
                      );
                    } else {
                      // Draw circle
                      final dayNumber = (index ~/ 2) + 1;
                      final isSelected = dayNumber == _currentDay;
                      final isCompleted = _workingDays.isNotEmpty &&
                          dayNumber <= _workingDays.length &&
                          _isDayCompleted(_workingDays[dayNumber - 1]);

                      return Container(
                        // Removed GestureDetector
                        width: 22,
                        height: 22,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isSelected
                              ? kBlack
                              : isCompleted
                                  ? const Color(0xFF1F2122)
                                  : Colors.transparent,
                          border: Border.all(
                            color: isSelected || isCompleted
                                ? kBlack
                                : const Color(0xFFE1DDD5),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            '$dayNumber',
                            style: TextStyle(
                              fontSize: forteen,
                              fontWeight: FontWeight.w500,
                              color: isSelected || isCompleted
                                  ? Colors.white
                                  : kSecondBlack,
                            ),
                          ),
                        ),
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            print("Current state: $state"); // Add debug log
            if (state is ViewDaySuccess) {
              // Get chef ID from the response
              final chefId = state.data['chef']?['id'];
              final dayDate =
                  state.data['date']; // Extract the date from the response

              setState(() {
                _selectedChefId = chefId;
                _displayStartDate = dayDate ?? widget.editDate ?? '';
                viewDayData = state.data; // Store view day response

                // Only in edit mode, load chefs with the date from the response
                if (widget.isEditing) {
                  Map<String, dynamic> requestData = {
                    'search_keyword': '',
                    'latitude': Initializer.latitude ?? '0',
                    'longitude': Initializer.longitude ?? '0',
                    'serving_size_id': _servingSizeId,
                    'time_slot_id': _timeSlotId,
                    'date': dayDate ?? widget.editDate ?? '',
                  };

                  print(
                      "Loading chefs with data: $requestData"); // Add debug log
                  context
                      .read<MealplanBloc>()
                      .add(FilterChefsEvent(requestData));
                } else {
                  // In non-edit mode, just load chefs as before
                  _loadFilteredChefs();
                }
              });
            } else if (state is FilterChefsSuccess) {
              setState(() {
                _chefs = state.data.chefs ?? [];
                _isLoading = false;

                // If we have a selected chef ID, ensure that chef is in the list
                if (_selectedChefId != null) {
                  final selectedChefExists =
                      _chefs.any((c) => c.chefId == _selectedChefId);
                  if (!selectedChefExists) {
                    _selectedChefId = null; // Reset if chef not found
                  }
                }
              });
            } else if (state is FilterChefsFailed) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: const Color(0xFFE11900),
                ),
              );
            }
          },
        ),
      ],
      child: BlocBuilder<HomeBloc, HomeState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: const Color(0xFFF6F3EC),
            appBar: AppBar(
              backgroundColor: const Color(0xFFF6F3EC),
              elevation: 0,
              scrolledUnderElevation: 0,
              foregroundColor: const Color(0xFF1F2122),
              surfaceTintColor: Colors.transparent,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Color(0xFF1F2122)),
                onPressed: () => Navigator.of(context).pop(),
              ),
              title: const Text(''),
              centerTitle: false,
            ),
            body: ListView(
              children: [
                Padding(
                  padding: EdgeInsets.fromLTRB(sixteen, 0, sixteen, 4),
                  child: Text(
                    "Select Chef",
                    style: TextStyle(
                      fontSize: eighteen,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                      color: Color(0xFF1F2122),
                    ),
                  ),
                ),
                // const SizedBox(height: twelve),
                _isLoading ? _buildDateTimeShimmer() : _buildDateSelector(),
                // Padding(
                //   padding: const EdgeInsets.fromLTRB(sixteen, 8, sixteen, sixteen),
                //   child: _isLoading
                //       ? Shimmer.fromColors(
                //           baseColor: Colors.grey[300]!,
                //           highlightColor: Colors.grey[100]!,
                //           child: Container(
                //             height: 52,
                //             decoration: BoxDecoration(
                //               color: Colors.white,
                //               borderRadius: BorderRadius.circular(28),
                //             ),
                //           ),
                //         )
                //       : Container(
                //           height: 48,
                //           decoration: BoxDecoration(
                //             borderRadius: BorderRadius.circular(28),
                //             border: Border.all(color: const Color(0xFF1F2122)),
                //           ),
                //           child: InkWell(
                //             borderRadius: BorderRadius.circular(28),
                //             onTap: () {
                //               // Handle filter action
                //             },
                //             child: Row(
                //               mainAxisAlignment: MainAxisAlignment.center,
                //               children: const [
                //                 Icon(Icons.tune,
                //                     size: sixteen, color: Color(0xFF1F2122)),
                //                 SizedBox(width: 8),
                //                 Text(
                //                   "View Filters",
                //                   style: TextStyle(
                //                     fontSize: twelve
                //                     fontWeight: FontWeight.w600,
                //                     fontFamily: 'Inter',
                //                     color: Color(0xFF1F2122),
                //                   ),
                //                 ),
                //               ],
                //             ),
                //           ),
                //         ),
                // ),
                SizedBox(height: ten),
                _isLoading
                    ? _buildChefShimmerGrid()
                    : _chefs.isEmpty
                        ? _buildNoChefMessage()
                        : Column(
                            children: _chefs
                                .map((chef) => _buildChefCard(chef))
                                .toList(),
                          ),
              ],
            ),
            bottomNavigationBar: _areAllDaysCompleted()
                ? Container(
                    padding: EdgeInsets.all(sixteen),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        top: BorderSide(color: Color(0xFFE1E3E6)),
                      ),
                    ),
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pushReplacement(
                          MaterialPageRoute(
                            builder: (context) => PersonailizedMealplanFinal(
                              mealPlanId: widget.mealPlanId,
                            ),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF1F2122),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(100),
                        ),
                        padding: EdgeInsets.symmetric(vertical: eighteen),
                      ),
                      child: Text(
                        'Continue to Checkout',
                        style: TextStyle(
                          fontSize: sixteen,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          );
        },
      ),
    );
  }

  // String _getSelectedDate(int dayIndex) {
  //   // Convert from 1-based to 0-based index
  //   int index = dayIndex - 1;

  //   // First try to get from working days array
  //   if (_workingDays.isNotEmpty && index >= 0 && index < _workingDays.length) {
  //     return _workingDays[index];
  //   }

  //   // Fallback to dates from widget if available
  //   if (widget.dates.isNotEmpty && index >= 0 && index < widget.dates.length) {
  //     return widget.dates[index];
  //   }

  //   // If no valid date found, calculate next working day from start date
  //   if (_startDate.isNotEmpty) {
  //     DateTime baseDate = DateTime.parse(_startDate);
  //     int workingDaysToAdd = index;

  //     while (workingDaysToAdd > 0) {
  //       baseDate = baseDate.add(const Duration(days: 1));
  //       if (baseDate.weekday != DateTime.saturday &&
  //           baseDate.weekday != DateTime.sunday) {
  //         workingDaysToAdd--;
  //       }
  //     }

  //     return _formatStorageDate(baseDate);
  //   }

  //   return '';
  // }

  // Usage example when day number is tapped
  void onDayTapped(int dayNumber) {
    setState(() {
      _currentDay = dayNumber;
      _displayStartDate = _getSelectedDate(dayNumber);
      // Trigger chef loading for the new date
      _loadFilteredChefs();
    });
  }

  Future<bool> _showChangeChefConfirmation(Chefs newChef) async {
    return await showDialog(
          context: context,
          builder: (context) => Dialog(
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(sixteen)),
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Change Chef?',
                    style: TextStyle(
                      fontSize: eighteen,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                      color: Color(0xFF1F2122),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Are you sure you want to change the chef for this day? Your current meal selections will be cleared.',
                    style: TextStyle(
                      fontSize: forteen,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: Color(0xFF414346),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context, false),
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                              horizontal: sixteen, vertical: twelve),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            fontSize: forteen,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Inter',
                            color: Color(0xFF414346),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () => Navigator.pop(context, true),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF1F2122),
                          padding: EdgeInsets.symmetric(
                              horizontal: twentyFour, vertical: twelve),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(24),
                          ),
                        ),
                        child: Text(
                          'Change Chef',
                          style: TextStyle(
                            fontSize: forteen,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Inter',
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ) ??
        false;
  }

  Widget _buildChefCard(Chefs chef) {
    final bool isSelected = chef.chefId == _selectedChefId;

    return InkWell(
        onTap: () {
          setState(() {
            _selectedChefId = chef.chefId;
          });
        },
        child: Container(
            margin: EdgeInsets.fromLTRB(sixteen, 0, sixteen, sixteen),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(sixteen),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color:
                    isSelected ? const Color(0xFF1F2122) : Colors.transparent,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(sixteen),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: twelve,
                        backgroundImage: chef.profilePhoto != null
                            ? NetworkImage(
                                ServerHelper.imageUrl + chef.profilePhoto!)
                            : const AssetImage(
                                    'assets/images/no_image_avatar.png')
                                as ImageProvider,
                      ),
                      SizedBox(width: twelve),
                      Expanded(
                        child: Text(
                          '${chef.chef?.firstName} ${chef.chef?.lastName}',
                          style: TextStyle(
                            fontSize: sixteen,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Inter',
                            color: Color(0xFF1F2122),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFFE1E3E6),
                      borderRadius: BorderRadius.circular(twelve),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Image.asset(
                          'assets/icons/thump.png',
                          width: ten,
                          height: ten,
                          color: const Color(0xFF1F2122),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          "${chef.ratingPercentage ?? '0'}% (${chef.rating ?? 0})",
                          style: TextStyle(
                            fontSize: ten,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Inter',
                            color: Color(0xFF1F2122),
                            height: 1.0,
                            letterSpacing: 0.02,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    chef.searchTags?.join(", ") ?? '',
                    style: TextStyle(
                      fontSize: twelve,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: Color(0xFF414346),
                      height: 20 / 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Image.asset(
                        'assets/icons/calender_2.png',
                        width: ten,
                        height: ten,
                        color: Colors.black54,
                      ),
                      SizedBox(width: 2),
                      Text(
                        chef.chef?.operationDays
                                ?.map((day) =>
                                    day.day?.name?.substring(0, 1) ?? '')
                                .join(", ") ??
                            '',
                        style: TextStyle(
                          fontSize: twelve,
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  OutlinedButton(
                    onPressed: _areAllDaysCompleted()
                        ? null
                        : () async {
                            // Show confirmation dialog if editing and selecting different chef
                            if (widget.isEditing && !isSelected) {
                              final shouldChange =
                                  await _showChangeChefConfirmation(chef);
                              if (!shouldChange) return;
                            }

                            setState(() {
                              _selectedChefId = chef.chefId;
                            });

                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => PersonailizedSelectmeals(
                                  mealPlanId: widget.mealPlanId,
                                  id: chef.chefId ?? 0,
                                  selectedDay: _currentDay,
                                  deliveryTime: _deliveryTime,
                                  selectedDate: _displayStartDate,
                                  dates: _workingDays,
                                  servingSizeId: _servingSizeId,
                                  maxDishesPerDay: _maxDishesPerDay,
                                  mealData: widget.mealdata ?? {},
                                  timeSlots: widget.timeSlots,
                                  dataresponce: widget.dataresponce,
                                  distance: chef.distance ?? 0,
                                  isEditing: widget.isEditing &&
                                      isSelected, // Only pass true if editing and this is selected chef
                                  editDayId: isSelected
                                      ? widget.editDayId
                                      : null, // Only pass editDayId if this is selected chef
                                  viewDayData: viewDayData,
                                ),
                              ),
                            );
                          },
                    style: OutlinedButton.styleFrom(
                      minimumSize: Size(twelve, twenty + twelve),
                      padding: EdgeInsets.symmetric(horizontal: twelve),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(28),
                      ),
                      side: BorderSide(
                          color: _areAllDaysCompleted()
                              ? const Color(0xFFE1DDD5)
                              : const Color(0xFF1F2122)),
                    ),
                    child: Text(
                      // Update button text logic
                      (widget.isEditing && isSelected)
                          ? 'Edit Meals'
                          : 'View Chef Menu',
                      style: TextStyle(
                        fontSize: twelve,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: _areAllDaysCompleted()
                            ? const Color(0xFFE1DDD5)
                            : const Color(0xFF1F2122),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: ten * 12 + sixteen,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: chef.chef?.dishes?.length ?? 0,
                      itemBuilder: (context, index) {
                        final dish = chef.chef?.dishes?[index];
                        return Container(
                          width: ten * 15 + eighteen,
                          margin: EdgeInsets.only(
                            right: index == (chef.chef?.dishes?.length ?? 0) - 1
                                ? 0
                                : 8,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: const Color(0xFFE1DDD5)),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ClipRRect(
                                borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(8),
                                ),
                                child: dish?.photo != null
                                    ? Image.network(
                                        '${ServerHelper.imageUrl}${dish?.photo}',
                                        height: ten * 8 + twelve,
                                        width: double.infinity,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return Container(
                                            // height: 120,
                                            color: const Color(0xFFE1DDD5),
                                            child: const Center(
                                              child: Icon(Icons.restaurant_menu,
                                                  color: Color(0xFF1F2122)),
                                            ),
                                          );
                                        },
                                      )
                                    : Container(
                                        height: ten * 12,
                                        color: const Color(0xFFE1DDD5),
                                        child: const Center(
                                          child: Icon(Icons.restaurant_menu,
                                              color: Color(0xFF1F2122)),
                                        ),
                                      ),
                              ),
                              Padding(
                                padding: EdgeInsets.all(twelve),
                                child: Text(
                                  dish?.name ?? '',
                                  style: TextStyle(
                                    fontSize: twelve,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF1F2122),
                                    height: 1.43,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            )));
  }
}
