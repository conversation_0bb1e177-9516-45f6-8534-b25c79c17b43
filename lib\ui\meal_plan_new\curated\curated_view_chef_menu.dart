import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/newmealplan_bloc.dart';
import 'package:db_eats/data/models/chef/viewchefdetailsmodel.dart';

import 'package:db_eats/data/models/new_meal_plan/newfiltereddishesmodel.dart'
    as newDishes;
import 'package:db_eats/server/serverhelper.dart';

import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/ui/meal_plan_new/curated/checkout_chefs.dart';
import 'package:db_eats/ui/meal_plan_new/mealplan_checkout_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:shimmer/shimmer.dart';

class CuratedViewChefMenu extends StatefulWidget {
  final int id;
  final int selectedDay;
  final String deliveryTime;
  final String selectedDate;
  final String? startDate;
  final String? endDate;
  final int mealPlanId;
  final int mealPlanDuration;
  final bool isEditing;
  final int? dayId;
  final dynamic viewDayData;
  final String? chefLocation;

  const CuratedViewChefMenu({
    Key? key,
    required this.id,
    required this.selectedDay,
    required this.deliveryTime,
    required this.selectedDate,
    this.startDate,
    this.endDate,
    required this.mealPlanId,
    required this.mealPlanDuration,
    this.isEditing = false,
    this.dayId,
    this.viewDayData,
    this.chefLocation,
  }) : super(key: key);

  @override
  State<CuratedViewChefMenu> createState() => _CuratedViewChefMenuState();
}

class _CuratedViewChefMenuState extends State<CuratedViewChefMenu>
    with TickerProviderStateMixin {
  ChefDetailsModel? chefDetails;
  newDishes.NewFilteredDishesModel? dishesData;
  TabController? _tabController;
  List<String> _categories = [];
  String _deliveryTime = '9:00AM-10:00AM';
  bool _isLoading = false;
  bool _isDishesLoading = true;
  bool _isSelectingChef = false;
  int _selectedDate = 0;

  @override
  void initState() {
    super.initState();

    _deliveryTime = widget.deliveryTime;
    _selectedDate = widget.selectedDay;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HomeBloc>().add(ViewChefDetailsEvent(
            data: {
              "chef_id": widget.id.toString(),
              "latitude": Initializer.latitude.toString(),
              "longitude": Initializer.longitude.toString(),
            },
          ));
    });
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  void _navigateBack() {
    Navigator.pop(context);
  }

  void _selectChefForDay() {
    if (chefDetails?.data?.chef == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Chef details not available'),
          backgroundColor: Color(0xFFE11900),
        ),
      );
      return;
    }

    String weekdayName = '';

    // Always calculate weekday based on the selected date, not backend data
    try {
      String dateOnly = widget.selectedDate.split(',')[0].trim();
      DateTime selectedDateTime = DateTime.parse(dateOnly);
      List<String> weekdayNames = [
        'MONDAY',
        'TUESDAY',
        'WEDNESDAY',
        'THURSDAY',
        'FRIDAY',
        'SATURDAY',
        'SUNDAY'
      ];
      weekdayName = weekdayNames[selectedDateTime.weekday - 1];

      print('DEBUG: _selectChefForDay - selectedDate: ${widget.selectedDate}');
      print('DEBUG: _selectChefForDay - dateOnly: $dateOnly');
      print('DEBUG: _selectChefForDay - selectedDateTime: $selectedDateTime');
      print('DEBUG: _selectChefForDay - calculated weekday: $weekdayName');
    } catch (e) {
      print('DEBUG: _selectChefForDay - Date parsing error: $e');
      // Fallback to backend data if date parsing fails
      final progressData = Initializer.mealPlanProgressLatestModel.data;
      if (progressData?.mealPlanDays != null &&
          progressData!.mealPlanDays!.isNotEmpty) {
        final currentDayData = progressData.mealPlanDays!.firstWhere(
          (day) => day.dayNumber == widget.selectedDay,
          orElse: () => progressData.mealPlanDays!.first,
        );
        weekdayName = currentDayData.dayOfWeek ?? 'MONDAY';
      } else {
        weekdayName = 'MONDAY';
      }
    }

    Map<String, dynamic> requestData = {
      'mealPlanId': widget.mealPlanId,
      'chef_id': chefDetails?.data?.chef?.chefId ?? 0,
      'weekdays': [weekdayName],
    };

    print('DEBUG: _selectChefForDay - Final request data: $requestData');

    context.read<NewmealplanBloc>().add(NewEditChefEvent(requestData));
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return MultiBlocListener(
      listeners: [
        BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is ChefDetailsSuccess) {
              setState(() {
                chefDetails = state.data;
              });

              context.read<NewmealplanBloc>().add(ListNewMealPlanSummaryEvent(
                  data: {'meal_plan_id': widget.mealPlanId}));
            }
          },
        ),
        BlocListener<NewmealplanBloc, NewMealPlanState>(
          listener: (context, state) {
            if (state is NewEditChefStateLoading) {
              setState(() {
                _isSelectingChef = true;
              });
            } else if (state is NewFilterDishesStateLoading) {
              setState(() {
                _isDishesLoading = true;
              });
            } else if (state is NewFilterDishesStateSuccess) {
              final newFilteredDishesData =
                  Initializer.newFilteredDishesModel.data;
              setState(() {
                _isDishesLoading = false;
                _categories = newFilteredDishesData?.categoryBasedList
                        ?.map((cat) => cat.category?.name ?? '')
                        .where((name) => name.isNotEmpty)
                        .toList() ??
                    [];

                dishesData = Initializer.newFilteredDishesModel;

                _tabController?.dispose();
                if (_categories.isNotEmpty) {
                  _tabController = TabController(
                    length: _categories.length,
                    vsync: this,
                  );
                }
              });
            } else if (state is NewFilterDishesStateLoadFailed) {
              setState(() {
                _isDishesLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.error),
                  backgroundColor: const Color(0xFFE11900),
                ),
              );
            } else if (state is NewEditChefStateSuccess) {
              setState(() {
                _isSelectingChef = false;
              });

              if (widget.isEditing) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Chef updated successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => MealplanCheckoutPage(
                      mealPlanId: widget.mealPlanId,
                    ),
                  ),
                );
              } else {
                final previousSelections = ModalRoute.of(context)
                        ?.settings
                        .arguments as List<Map<String, dynamic>>? ??
                    [];

                Map<String, dynamic> chefData = {
                  'chef_id': chefDetails?.data?.chef?.chefId ?? 0,
                  'name':
                      '${chefDetails?.data?.chef?.chef?.firstName ?? ''} ${chefDetails?.data?.chef?.chef?.lastName ?? ''}'
                          .trim(),
                  'image': chefDetails?.data?.chef?.profilePhoto ?? '',
                  'tags': chefDetails?.data?.chef?.searchTags ?? [],
                  'date': widget.selectedDate,
                  'rating': (() {
                    final rating = chefDetails?.data?.chef?.ratingPercentage;
                    final totalRatings =
                        chefDetails?.data?.chef?.totalRatings ?? 0;

                    if (rating == null) return '0% (0)';

                    final ratingStr = rating % 1 == 0
                        ? rating.toInt().toString()
                        : rating.toString();
                    return '$ratingStr% ($totalRatings)';
                  })(),
                };

                previousSelections.add(chefData);

                if (widget.selectedDay == widget.mealPlanDuration) {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CheckoutChefs(
                        selectedChefsWithDetails: previousSelections,
                        mealPlanId: widget.mealPlanId,
                      ),
                    ),
                  );
                } else {
                  Navigator.pop(context, chefData);
                }
              }
            } else if (state is NewEditChefStateLoadFailed) {
              setState(() {
                _isSelectingChef = false;
              });

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.error),
                  backgroundColor: const Color(0xFFE11900),
                ),
              );
            } else if (state is NewMealPlanSummaryLoaded) {
              final progressData = Initializer.mealPlanProgressLatestModel.data;
              setState(() {
                _isLoading = false;

                if (progressData?.timeSlot != null) {
                  _deliveryTime =
                      '${_formatTimeToAmPm(progressData?.timeSlot?.startTime)}-${_formatTimeToAmPm(progressData?.timeSlot?.endTime)}';
                }
              });

              setState(() {
                _isDishesLoading = true;
              });

              String actualStartDate = '';
              String actualEndDate = '';

              if (progressData?.mealPlanDays != null &&
                  progressData!.mealPlanDays!.isNotEmpty) {
                final currentDayData = progressData.mealPlanDays!.firstWhere(
                  (day) => day.dayNumber == widget.selectedDay,
                  orElse: () => progressData.mealPlanDays!.first,
                );
                actualStartDate =
                    currentDayData.date ?? progressData.startDate ?? '';
                actualEndDate =
                    currentDayData.date ?? progressData.endDate ?? '';
              } else {
                actualStartDate = progressData?.startDate ?? '';
                actualEndDate = progressData?.endDate ?? '';
              }

              Map<String, dynamic> requestData = {
                'chef_id': widget.id.toString(),
                'serving_size_id': progressData?.servingSizeId,
                'cuisine_ids':
                    progressData?.cuisines?.map((c) => c.id).toList() ?? [],
                'sub_cuisine_ids':
                    progressData?.subcuisines?.map((c) => c.id).toList() ?? [],
                'local_cuisine_ids':
                    progressData?.localcuisines?.map((c) => c.id).toList() ??
                        [],
                'time_slot_id': progressData?.timeSlotId?.toString() ?? '',
                'start_date': actualStartDate,
                'end_date': actualEndDate,
                'weekday': _getWeekdayNameForAPI(widget.selectedDay - 1),
              };

              if (progressData?.dietaryPreferenceId != null) {
                requestData['dietary_id'] =
                    progressData!.dietaryPreferenceId.toString();
              }

              if (progressData?.spiceLevelId != null) {
                requestData['spice_level_id'] =
                    progressData!.spiceLevelId.toString();
              }

              context
                  .read<NewmealplanBloc>()
                  .add(NewFilterDishesEvent(requestData));
            }
          },
        ),
        BlocListener<AccountBloc, AccountState>(
          listener: (context, state) {
            if (state is AddFavouriteChefSuccess) {
              if (chefDetails?.data != null) {
                setState(() {
                  chefDetails!.data!.isFavourite = true;
                });
              }
            } else if (state is RemoveFavouriteChefSuccess) {
              if (chefDetails?.data != null) {
                setState(() {
                  chefDetails!.data!.isFavourite = false;
                });
              }
            }
          },
        ),
      ],
      child: Builder(
        builder: (context) {
          final isLoading = _isDishesLoading || dishesData == null;
          return SafeArea(
            child: Scaffold(
              backgroundColor: const Color(0xFFF6F3EC),
              appBar: AppBar(
                backgroundColor: const Color(0xFFF6F3EC),
                centerTitle: false,
                scrolledUnderElevation: 0,
                titleSpacing: 0,
                automaticallyImplyLeading: false,
                leading: IconButton(
                  icon: Image.asset(
                    'assets/icons/left_arrow.png',
                    width: size.width * 0.03,
                    height: size.width * 0.03,
                  ),
                  onPressed: _navigateBack,
                ),
                elevation: 0,
                systemOverlayStyle: const SystemUiOverlayStyle(
                  statusBarColor: Color(0xFFF6F3EC),
                  statusBarIconBrightness: Brightness.dark,
                  statusBarBrightness: Brightness.light,
                ),
              ),
              body: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: size.width * 0.04,
                        //vertical: size.height * 0.01
                      ),
                      child: Text(
                        "Select Chef",
                        style: TextStyle(
                          fontSize: isTablet ? size.width * 0.05 : eighteen,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Inter',
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                    ),
                    SizedBox(height: size.height * 0.015),
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: size.width * 0.04),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Date row with number selector
                          Row(
                            children: [
                              Image.asset(
                                'assets/icons/date_range.png',
                                width: twelve,
                                height: twelve,
                                color: const Color(0xFF1F2222),
                              ),
                              SizedBox(width: size.width * 0.02),
                              Text(
                                widget.selectedDate.isNotEmpty
                                    ? _getWeekdayName(widget.selectedDay - 1)
                                    : "Loading...",
                                style: TextStyle(
                                  fontSize: twelve,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter',
                                  color: const Color(0xFF414346),
                                ),
                              ),
                              const Spacer(),
                              _buildDateSelector(
                                size,
                              ),
                            ],
                          ),
                          SizedBox(height: size.height * 0.01),
                          Row(
                            children: [
                              Icon(Icons.access_time,
                                  size: twelve, color: const Color(0xFF1F2222)),
                              SizedBox(width: size.width * 0.02),
                              Text(
                                _isLoading ? "Loading..." : _deliveryTime,
                                style: TextStyle(
                                  fontSize: twelve,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter',
                                  color: const Color(0xFF414346),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: size.height * 0.03),
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: size.width * 0.04),
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: twelve,
                            backgroundImage: NetworkImage(
                              ServerHelper.imageUrl +
                                  (chefDetails?.data?.chef?.profilePhoto ?? ''),
                            ),
                            backgroundColor: Colors.grey[200],
                            onBackgroundImageError: (_, __) => Container(),
                          ),
                          SizedBox(
                            width: twelve,
                          ),
                          Expanded(
                            child: Text(
                              '${chefDetails?.data?.chef?.chef?.firstName ?? ''} ${chefDetails?.data?.chef?.chef?.lastName ?? ''}',
                              style: TextStyle(
                                fontSize: sixteen,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: twelve,
                    ),
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: size.width * 0.04),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              // Container(
                              //   padding: EdgeInsets.symmetric(
                              //       horizontal: twelve / 2, vertical: 0),
                              //   constraints: BoxConstraints(
                              //       minWidth: ten * 5, minHeight: ten * 3.4),
                              //   decoration: BoxDecoration(
                              //     color: const Color(0xFFF6F3EC),
                              //     borderRadius: BorderRadius.circular(ten),
                              //     border: Border.all(
                              //         color: const Color(0xFFB9B6AD)),
                              //   ),
                              //   child: Row(
                              //     children: [
                              //       Icon(Icons.access_time,
                              //           size: sixteen,
                              //           color: Color(0xFF414346)),
                              //       SizedBox(width: sixteen / 4),
                              //       Text(
                              //         "35 mins",
                              //         style: TextStyle(
                              //           fontFamily: 'Inter',
                              //           fontSize: forteen,
                              //           fontWeight: FontWeight.w600,
                              //           height: 24 / 16,
                              //           color: Color(0xFF1F2122),
                              //         ),
                              //       ),
                              //     ],
                              //   ),
                              // ),
                              SizedBox(width: sixteen / 2),
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: twelve / 2, vertical: 0),
                                constraints: BoxConstraints(
                                    minWidth: ten * 6.1, minHeight: ten * 3.4),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFF6F3EC),
                                  borderRadius: BorderRadius.circular(ten),
                                  border: Border.all(
                                      color: const Color(0xFFB9B6AD)),
                                ),
                                child: Row(
                                  children: [
                                    Icon(Icons.star,
                                        size: sixteen,
                                        color: Color(0xFF414346)),
                                    SizedBox(width: sixteen / 4),
                                    Text(
                                      chefDetails?.data?.chef?.averageRating ??
                                          '0',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w500,
                                        height: 24 / 16,
                                        color: Color(0xFF1F2122),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(width: sixteen / 2),
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: twelve / 2, vertical: 0),
                                constraints: BoxConstraints(
                                    minWidth: ten * 8.7, minHeight: ten * 3.4),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFF6F3EC),
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                      color: const Color(0xFFB9B6AD)),
                                ),
                                child: Row(
                                  children: [
                                    Icon(Icons.location_on_outlined,
                                        size: ten * 1.7,
                                        color: Color(0xFF414346)),
                                    SizedBox(width: sixteen / 4),
                                    Text(
                                      _formatDistance(
                                          chefDetails?.data?.chef?.distance),
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w500,
                                        height: 24 / 16,
                                        color: Color(0xFF1F2122),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(width: sixteen / 2),
                              _buildFavoriteButton(context, screenWidth),
                              // const SizedBox(width: sixteen/2),
                              // Container(
                              //   width: 34,
                              //   height: 34,
                              //   decoration: BoxDecoration(
                              //     color: Colors.white,
                              //     borderRadius: BorderRadius.circular(10),
                              //     boxShadow: [
                              //       BoxShadow(
                              //         color: Colors.black.withOpacity(0.05),
                              //         blurRadius: sixteen/4,
                              //         offset: const Offset(0, 2),
                              //       ),
                              //     ],
                              //   ),
                              //   child: IconButton(
                              //     icon: Image.asset(
                              //       'assets/icons/favorites.png',
                              //       width: twentyFour
                              //       height: twentyFour
                              //       color: Color(0xFF1F2122),
                              //     ),
                              //     onPressed: () {},
                              //     padding: EdgeInsets.zero,
                              //   ),
                              // ),
                            ],
                          ),
                          // Wrap(
                          //   spacing: size.width * 0.02,
                          //   children: [
                          //     _buildMetricContainer(
                          //         size, Icons.access_time, "35 mins"),
                          //     _buildMetricContainer(size, Icons.star, "4.9"),
                          //     _buildMetricContainer(
                          //         size,
                          //         Icons.location_on_outlined,
                          //         widget.chefLocation ?? "_"),
                          //     Container(
                          //       width: size.width * 0.1,
                          //       height: size.width * 0.1,
                          //       decoration: BoxDecoration(
                          //         color: Colors.white,
                          //         borderRadius:
                          //             BorderRadius.circular(size.width * 0.02),
                          //         boxShadow: [
                          //           BoxShadow(
                          //             color: Colors.black.withOpacity(0.05),
                          //             blurRadius: 4,
                          //             offset: const Offset(0, 2),
                          //           ),
                          //         ],
                          //       ),
                          //       child: IconButton(
                          //         icon: Image.asset(
                          //           'assets/icons/favorites.png',
                          //           width: size.width * 0.06,
                          //           height: size.width * 0.06,
                          //           color: const Color(0xFF1F2122),
                          //         ),
                          //         onPressed: () {},
                          //         padding: EdgeInsets.zero,
                          //       ),
                          //     ),
                          //   ],
                          // ),
                          SizedBox(height: screenHeight * 0.016),
                          Wrap(
                            spacing: screenWidth * 0.03,
                            runSpacing: screenHeight * 0.005,
                            children:
                                (chefDetails?.data?.chef?.searchTags ?? [])
                                    .map((tag) {
                              return Text(
                                tag,
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFF414346),
                                ),
                              );
                            }).toList(),
                          ),
                          SizedBox(height: screenHeight * 0.02),
                          Text(
                            chefDetails?.data?.chef?.description ?? '',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: forteen,
                              fontWeight: FontWeight.w400,
                              color: const Color(0xFF414346),
                            ),
                            textAlign: TextAlign.justify,
                          ),
                          SizedBox(height: screenHeight * 0.017),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: size.width * 0.04,
                          vertical: size.height * 0.015),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _categories.isNotEmpty
                              ? SizedBox(
                                  height: screenHeight * 0.04,
                                  child: ListView(
                                    scrollDirection: Axis.horizontal,
                                    children: _categories
                                        .asMap()
                                        .entries
                                        .map((entry) {
                                      final index = entry.key;
                                      final category = entry.value;
                                      final isSelected =
                                          _tabController!.index == index;
                                      return GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            _tabController!.animateTo(index);
                                          });
                                        },
                                        child: Container(
                                          margin: EdgeInsets.only(
                                              right: screenWidth * 0.02),
                                          padding: EdgeInsets.symmetric(
                                            horizontal: screenWidth * 0.03,
                                            vertical: screenHeight * 0.005,
                                          ),
                                          decoration: BoxDecoration(
                                            color: isSelected
                                                ? const Color(0xFFB9B6AD)
                                                : const Color(0xFFE1DDD5),
                                            borderRadius: BorderRadius.circular(
                                                screenWidth * 0.05),
                                          ),
                                          child: Center(
                                            child: Text(
                                              category,
                                              style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontSize: forteen,
                                                fontWeight: FontWeight.w500,
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                )
                              : const SizedBox(),
                          SizedBox(height: screenHeight * 0.033),
                          // Padding(
                          //   // padding: EdgeInsets.symmetric(
                          //   //     vertical: size.height * 0.02),
                          //              padding: EdgeInsets.zero,
                          //   child: Container(
                          //     height: size.height * 0.05,
                          //     decoration: BoxDecoration(
                          //       borderRadius:
                          //           BorderRadius.circular(size.width * 0.06),
                          //       border:
                          //           Border.all(color: const Color(0xFF1F2122)),
                          //     ),
                          //     child: InkWell(
                          //       borderRadius:
                          //           BorderRadius.circular(size.width * 0.06),
                          //       onTap: () {},
                          //       child: Row(
                          //         mainAxisAlignment: MainAxisAlignment.center,
                          //         children: [
                          //           Icon(Icons.tune,
                          //                size: twelve,
                          //               color: const Color(0xFF1F2122)),
                          //           SizedBox(width: size.width * 0.02),
                          //           Text(
                          //             "View Filters",
                          //             style: TextStyle(
                          //               fontSize: twelve,
                          //               fontWeight: FontWeight.w600,
                          //               color: const Color(0xFF1F2122),
                          //             ),
                          //           ),
                          //         ],
                          //       ),
                          //     ),
                          //   ),
                          // ),
                          SizedBox(height: screenHeight * 0.02),
                          Text(
                            "Featured Items",
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: twenty,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF1F2122),
                            ),
                          ),
                          SizedBox(height: screenHeight * 0.017),
                          isLoading
                              ? _buildShimmerCards(size, isTablet)
                              : _buildFeaturedItems(
                                  size, isTablet, isLandscape),
                          SizedBox(height: screenHeight * 0.01),
                          isLoading
                              ? _buildShimmerCategoryContent(size)
                              : _buildCategoryContent(
                                  size, isTablet, isLandscape),
                          SizedBox(height: screenHeight * 0.03),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              bottomNavigationBar: Container(
                padding: EdgeInsets.all(size.width * 0.04),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      offset: const Offset(0, -2),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const Spacer(),
                    ElevatedButton(
                      onPressed: _isSelectingChef ? null : _selectChefForDay,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF1F2122),
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: size.width * 0.06,
                          vertical: size.height * 0.02,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(size.width * 0.07),
                        ),
                      ),
                      child: _isSelectingChef
                          ? SizedBox(
                              width: size.width * 0.05,
                              height: size.width * 0.05,
                              child: const CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              widget.isEditing
                                  ? 'Edit Chef'
                                  : 'Select Chef for ${_getWeekdayName(widget.selectedDay - 1)}',
                              style: TextStyle(
                                fontSize: forteen,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDateSelector(Size size) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final circleSize = (size.width * 0.06).clamp(20.0, 25.0);
        final connectorWidth = (size.width * 0.02).clamp(5.0, 7.0);

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(widget.mealPlanDuration, (index) {
            final number = index + 1;

            bool isSelected = false;
            if (widget.isEditing) {
              isSelected = number == widget.selectedDay;
            } else {
              isSelected =
                  _selectedDate == number || number <= widget.selectedDay;
            }

            final isFirst = index == 0;
            final isLast = index == widget.mealPlanDuration - 1;

            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (!isFirst)
                  Container(
                    width: connectorWidth,
                    height: 1,
                    color: const Color(0xFFB9B6AD),
                  ),
                Container(
                  width: circleSize,
                  height: circleSize,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected
                        ? const Color(0xFF1F2122)
                        : Colors.transparent,
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF1F2122)
                          : const Color(0xFFB9B6AD),
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      "$number",
                      style: TextStyle(
                        fontSize: circleSize * 0.5,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                        color:
                            isSelected ? Colors.white : const Color(0xFF1F2122),
                      ),
                    ),
                  ),
                ),
                if (!isLast)
                  Container(
                    width: connectorWidth,
                    height: 1,
                    color: const Color(0xFFB9B6AD),
                  ),
              ],
            );
          }),
        );
      },
    );
  }

  String _getWeekdayName(int dayIndex) {
    try {
      print(
          '🔍 _getWeekdayName called with dayIndex: $dayIndex, selectedDay: ${widget.selectedDay}');
      print('📅 selectedDate: ${widget.selectedDate}');

      // First try to use the widget.selectedDate if it's available and valid
      if (widget.selectedDate.isNotEmpty) {
        try {
          final dateOnly = widget.selectedDate.split(',')[0].trim();
          final date = DateTime.parse(dateOnly);

          const shortWeekdayNames = [
            'Mon',
            'Tue',
            'Wed',
            'Thu',
            'Fri',
            'Sat',
            'Sun'
          ];
          final weekdayName = shortWeekdayNames[date.weekday - 1];
          print(
              '✅ Returning weekday from selectedDate: $weekdayName (from date: $dateOnly)');
          return weekdayName;
        } catch (e) {
          print('⚠️ Error parsing selectedDate, trying meal plan data: $e');
        }
      }

      // Fallback to meal plan data
      final progressData = Initializer.mealPlanProgressLatestModel.data;
      if (progressData?.mealPlanDays != null &&
          progressData!.mealPlanDays!.isNotEmpty) {
        print('📊 Found ${progressData.mealPlanDays!.length} meal plan days');

        // Try to find the day data by day number
        final currentDayData = progressData.mealPlanDays!.firstWhere(
          (day) => day.dayNumber == widget.selectedDay,
          orElse: () {
            print(
                '⚠️ Could not find day with dayNumber ${widget.selectedDay}, using first day');
            return progressData.mealPlanDays!.first;
          },
        );

        print(
            '📍 Found day data: dayNumber=${currentDayData.dayNumber}, date=${currentDayData.date}, dayOfWeek=${currentDayData.dayOfWeek}');

        if (currentDayData.date != null) {
          final date = DateTime.parse(currentDayData.date!);
          const shortWeekdayNames = [
            'Mon',
            'Tue',
            'Wed',
            'Thu',
            'Fri',
            'Sat',
            'Sun'
          ];
          final weekdayName = shortWeekdayNames[date.weekday - 1];
          print(
              '✅ Returning weekday from meal plan data: $weekdayName (from date: ${currentDayData.date})');
          return weekdayName;
        }
      }

      print('❌ No valid date found, using fallback');
      return "Day ${dayIndex + 1}";
    } catch (e) {
      print('❌ Error in _getWeekdayName: $e');
      return "Day ${dayIndex + 1}";
    }
  }

  String _getWeekdayNameForAPI(int dayIndex) {
    try {
      print(
          '🔍 _getWeekdayNameForAPI called with dayIndex: $dayIndex, selectedDay: ${widget.selectedDay}');

      // Always calculate weekday based on the selected date, not backend data (same logic as _selectChefForDay)
      try {
        String dateOnly = widget.selectedDate.split(',')[0].trim();
        DateTime selectedDateTime = DateTime.parse(dateOnly);
        List<String> weekdayNames = [
          'MONDAY',
          'TUESDAY',
          'WEDNESDAY',
          'THURSDAY',
          'FRIDAY',
          'SATURDAY',
          'SUNDAY'
        ];
        String weekdayName = weekdayNames[selectedDateTime.weekday - 1];

        print(
            'DEBUG: _getWeekdayNameForAPI - selectedDate: ${widget.selectedDate}');
        print('DEBUG: _getWeekdayNameForAPI - dateOnly: $dateOnly');
        print(
            'DEBUG: _getWeekdayNameForAPI - selectedDateTime: $selectedDateTime');
        print(
            'DEBUG: _getWeekdayNameForAPI - calculated weekday: $weekdayName');

        return weekdayName;
      } catch (e) {
        print('DEBUG: _getWeekdayNameForAPI - Date parsing error: $e');
        // Fallback to backend data if date parsing fails
        final progressData = Initializer.mealPlanProgressLatestModel.data;
        if (progressData?.mealPlanDays != null &&
            progressData!.mealPlanDays!.isNotEmpty) {
          final currentDayData = progressData.mealPlanDays!.firstWhere(
            (day) => day.dayNumber == widget.selectedDay,
            orElse: () => progressData.mealPlanDays!.first,
          );
          if (currentDayData.date != null) {
            final date = DateTime.parse(currentDayData.date!);
            const fullWeekdayNames = [
              'MONDAY',
              'TUESDAY',
              'WEDNESDAY',
              'THURSDAY',
              'FRIDAY',
              'SATURDAY',
              'SUNDAY'
            ];
            final weekdayName = fullWeekdayNames[date.weekday - 1];
            print(
                '📅 Calculated weekday from meal plan date: $weekdayName (date: ${currentDayData.date})');
            return weekdayName;
          }
          // Only use dayOfWeek as last resort
          if (currentDayData.dayOfWeek != null) {
            print(
                '📅 Using backend dayOfWeek as fallback: ${currentDayData.dayOfWeek}');
            return currentDayData.dayOfWeek!;
          }
        }
        return 'MONDAY';
      }
    } catch (e) {
      print('❌ Error in _getWeekdayNameForAPI: $e');
      return 'MONDAY';
    }
  }

  String _formatTimeToAmPm(String? time) {
    if (time == null) return '';
    try {
      final timeParts = time.split(':');
      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);
      String period = hour >= 12 ? 'PM' : 'AM';
      hour = hour > 12 ? hour - 12 : hour;
      hour = hour == 0 ? 12 : hour;
      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}$period';
    } catch (e) {
      return time;
    }
  }

  Widget _buildFeaturedItems(Size size, bool isTablet, bool isLandscape) {
    final featuredList = dishesData?.data?.featuredList;

    if (featuredList == null || featuredList.isEmpty) {
      return Container(
        height: ten * 26 + sixteen,
        width: double.infinity,
        margin: EdgeInsets.symmetric(horizontal: size.width * 0.04),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(size.width * 0.04),
          border: Border.all(color: Colors.grey[300]!, width: 1),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.star_outline,
                size: size.width * 0.08,
                color: Colors.grey[400],
              ),
              SizedBox(height: size.height * 0.01),
              Text(
                'No featured meals available',
                style: TextStyle(
                  fontSize: size.width * 0.04,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                  fontFamily: 'Inter',
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SizedBox(
      height: ten * 26 + sixteen,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.only(right: size.width * 0.04),
        children: featuredList
            .map((dish) => Container(
                  width: ten * 20 + eighteen,
                  margin: EdgeInsets.only(right: screenWidth * 0.04),
                  child: _buildDishCard({
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices!.first.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices!.first.servingSize?.title ?? '',
                  }, size),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategorySection(
      String category, Size size, bool isTablet, bool isLandscape) {
    final categoryList = dishesData?.data?.categoryBasedList
        ?.firstWhere((cat) => cat.category?.name == category,
            orElse: () => newDishes.CategoryBasedList())
        .dishList;

    if (categoryList == null || categoryList.isEmpty) {
      return Container(
        height: screenHeight * 0.23,
        width: double.infinity,
        margin: EdgeInsets.symmetric(vertical: screenHeight * 0.01),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFFFBFAF8),
              const Color(0xFFF5F3F0),
            ],
          ),
          borderRadius: BorderRadius.circular(screenWidth * 0.02),
          border: Border.all(
            color: const Color(0xFFE8E4DD),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.03),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.all(screenWidth * 0.04),
                decoration: BoxDecoration(
                  color: const Color(0xFFEDE9E0),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.restaurant_outlined,
                  size: screenWidth * 0.08,
                  color: const Color(0xFF9C8B7A),
                ),
              ),
              SizedBox(height: screenHeight * 0.02),
              Text(
                'No $category meals available',
                style: TextStyle(
                  fontSize: eighteen,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF6B5B4F),
                  fontFamily: 'Inter',
                  letterSpacing: -0.3,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: screenHeight * 0.008),
              Text(
                'Check back later for delicious options',
                style: TextStyle(
                  fontSize: forteen,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xFF9C8B7A),
                  fontFamily: 'Inter',
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SizedBox(
      height: ten * 26 + sixteen,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.only(right: size.width * 0.04),
        children: categoryList
            .map((dish) => Container(
                  width: isTablet ? size.width * 0.4 : ten * 20 + eighteen,
                  margin: EdgeInsets.only(right: size.width * 0.04),
                  child: _buildDishCard({
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices!.first.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices!.first.servingSize?.title ?? '',
                  }, size),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategoryContent(Size size, bool isTablet, bool isLandscape) {
    if (dishesData?.data?.categoryBasedList == null) return const SizedBox();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: dishesData!.data!.categoryBasedList!.map((category) {
        final categoryName = category.category?.name ?? '';

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: size.height * 0.01),
              child: Text(
                categoryName,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: twenty,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1F2122),
                ),
              ),
            ),
            SizedBox(height: size.height * 0.01),
            _buildCategorySection(categoryName, size, isTablet, isLandscape),
            SizedBox(height: size.height * 0.02),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildShimmerCards(Size size, bool isTablet) {
    return SizedBox(
      height: size.height * (isTablet ? 0.5 : 0.45),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 3,
        itemBuilder: (context, index) {
          return Container(
            width: isTablet ? size.width * 0.4 : size.width * 0.75,
            margin: EdgeInsets.only(right: size.width * 0.04),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(size.width * 0.04),
            ),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: size.height * 0.25,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.vertical(
                          top: Radius.circular(size.width * 0.04)),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.all(size.width * 0.04),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: size.width * 0.5,
                          height: size.height * 0.03,
                          color: Colors.white,
                        ),
                        SizedBox(height: size.height * 0.015),
                        Container(
                          width: size.width * 0.3,
                          height: size.height * 0.02,
                          color: Colors.white,
                        ),
                        SizedBox(height: size.height * 0.015),
                        Container(
                          width: size.width * 0.2,
                          height: size.height * 0.02,
                          color: Colors.white,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildShimmerCategoryContent(Size size) {
    return Column(
      children: List.generate(
        3,
        (index) => Padding(
          padding: EdgeInsets.symmetric(
              horizontal: size.width * 0.04, vertical: size.height * 0.01),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: size.width * 0.4,
                  height: size.height * 0.03,
                  color: Colors.white,
                ),
              ),
              SizedBox(height: size.height * 0.015),
              _buildShimmerCards(size, false),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDishCard(Map<String, dynamic> dish, Size size) {
    String rating = "90";
    final priceDouble = double.tryParse(dish['price'] ?? '0.00') ?? 0.0;
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(screenWidth * 0.04),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius:
                BorderRadius.vertical(top: Radius.circular(screenWidth * 0.04)),
            child: Image.network(
              ServerHelper.imageUrl + (dish['photo'] ?? ''),
              height: ten * 15 + twelve,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: ten * 15 + twelve,
                  width: double.infinity,
                  color: Colors.grey[200],
                  child: const Center(child: Text('Image not available')),
                );
              },
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
                top: screenWidth * 0.025,
                bottom: screenWidth * 0.015,
                right: screenWidth * 0.04,
                left: screenWidth * 0.04),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  dish['name'] ?? 'Unknown Dish',
                  style: TextStyle(
                    fontSize: forteen,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF1F2122),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: screenHeight * 0.015),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Wrap(
                            spacing: screenWidth * 0.02,
                            runSpacing: screenHeight * 0.01,
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: screenWidth * 0.015,
                                  vertical: screenWidth * 0.005,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFE1E3E6),
                                  borderRadius:
                                      BorderRadius.circular(screenWidth * 0.03),
                                ),
                                child: Text(
                                  "${dish['serving_size'].split(' ').first} Servings",
                                  style: TextStyle(
                                    fontSize: ten,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Inter',
                                  ),
                                ),
                              ),
                              // Container(
                              //   padding: EdgeInsets.symmetric(
                              //     horizontal: screenWidth * 0.015,
                              //     vertical: screenWidth * 0.005,
                              //   ),
                              //   decoration: BoxDecoration(
                              //     color: const Color(0xFFE1E3E6),
                              //     borderRadius:
                              //         BorderRadius.circular(screenWidth * 0.03),
                              //   ),
                              //   child: Row(
                              //     mainAxisSize: MainAxisSize.min,
                              //     children: [
                              //       Image.asset(
                              //         'assets/icons/thump.png',
                              //         width: screenWidth * 0.03,
                              //         height: screenWidth * 0.0275,
                              //         color: Colors.black54,
                              //       ),
                              //       SizedBox(width: screenWidth * 0.01),
                              //       Text(
                              //         "$rating%",
                              //         style: TextStyle(
                              //           fontSize: ten,
                              //           fontWeight: FontWeight.w500,
                              //           fontFamily: 'Inter',
                              //         ),
                              //       ),
                              //       SizedBox(width: screenWidth * 0.005),
                              //       Text(
                              //         "($rating)",
                              //         style: TextStyle(
                              //           fontSize: ten,
                              //           fontWeight: FontWeight.w500,
                              //           fontFamily: 'Inter',
                              //         ),
                              //       ),
                              //     ],
                              //   ),
                              // ),
                            ],
                          ),
                          SizedBox(height: screenHeight * 0.02),
                          Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: '\$',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: twelve,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                                TextSpan(
                                  text: priceDouble.toStringAsFixed(2),
                                  style: TextStyle(
                                    fontFamily: 'Roboto',
                                    fontSize: twelve,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 4,
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteButton(BuildContext context, double screenWidth) {
    return Container(
      width: screenWidth * 0.08,
      height: screenWidth * 0.08,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(screenWidth * 0.02),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: BlocBuilder<AccountBloc, AccountState>(
          builder: (context, state) {
            bool isFavorite = chefDetails?.data?.isFavourite ?? false;

            if (state is AddFavouriteChefLoading ||
                state is RemoveFavouriteChefLoading) {
              return SizedBox(
                width: screenWidth * 0.05,
                height: screenWidth * 0.05,
                child: CupertinoActivityIndicator(
                  radius: screenWidth * 0.010,
                  color: const Color(0xFF1F2122),
                ),
              );
            }

            return Icon(
              isFavorite ? Icons.favorite : Icons.favorite_border,
              color: isFavorite ? Colors.red : const Color(0xFF1F2122),
              size: screenWidth * 0.05,
            );
          },
        ),
        onPressed: () {
          final isFavorite = chefDetails?.data?.isFavourite ?? false;
          final chefId = chefDetails?.data?.chef?.chefId ?? 0;

          if (isFavorite) {
            context.read<AccountBloc>().add(RemoveFavouriteChefEvent(chefId));
          } else {
            context.read<AccountBloc>().add(AddFavouriteChefEvent(chefId));
          }
        },
        padding: EdgeInsets.zero,
        tooltip: chefDetails?.data?.isFavourite ?? false
            ? 'Remove from favorites'
            : 'Add to favorites',
      ),
    );
  }

  String _formatDistance(num? distance) {
    if (distance == null) return 'Unknown';

    // Convert meters to kilometers
    final kilometers = distance / 1000;

    // Format to one decimal place
    return '${kilometers.toStringAsFixed(1)} km';
  }
}
