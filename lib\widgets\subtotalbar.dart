import 'package:db_eats/bloc/catering_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SubtotalBar extends StatefulWidget {
  final double amount;
  final VoidCallback onSubmit;
  final double screenWidth;
  final double screenHeight;

  const SubtotalBar({
    super.key,
    required this.amount,
    required this.onSubmit,
    required this.screenWidth,
    required this.screenHeight,
  });

  @override
  State<SubtotalBar> createState() => _SubtotalBarState();
}

class _SubtotalBarState extends State<SubtotalBar> {
  @override
  Widget build(BuildContext context) {
    final double ten = widget.screenWidth * 0.02545;
    final double twelve = widget.screenWidth * 0.03054;
    final double forteen = widget.screenWidth * 0.035;
    final double sixteen = widget.screenWidth * 0.04073;
    final double eighteen = widget.screenWidth * 0.04582;
    final double twenty = widget.screenWidth * 0.05091;
    final double twentyFour = widget.screenWidth * 0.06109;

    return BlocConsumer<CateringBloc, CateringState>(
      listener: (context, state) {
        if (state is AddDishToCateringCartSuccess ||
            state is ViewCateringRequestSuccess) {
          setState(() {});
        }
      },
      builder: (context, state) {
        return Container(
          padding: EdgeInsets.only(
              left: sixteen, right: sixteen, top: sixteen, bottom: twentyFour),
          color: Colors.white,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'Subtotal: \$${widget.amount.toStringAsFixed(2)}',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: forteen,
                  decoration: TextDecoration.underline,
                  height: 1,
                  color: Color(0xFF1F2122),
                ),
              ),
              ElevatedButton(
                onPressed: widget.onSubmit,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1F2122),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32),
                  ),
                  padding: EdgeInsets.symmetric(
                      horizontal: twentyFour, vertical: forteen),
                ),
                child: Text(
                  'Review Order',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: twelve,
                    fontWeight: FontWeight.w400,
                    letterSpacing: 0.32,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
