import 'package:db_eats/bloc/saversplan_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class VerifyStudentStatusPage extends StatefulWidget {
  final int? planId;

  const VerifyStudentStatusPage({super.key, this.planId});

  @override
  State<VerifyStudentStatusPage> createState() =>
      _VerifyStudentStatusPageState();
}

class _VerifyStudentStatusPageState extends State<VerifyStudentStatusPage> {
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _institutionController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _otpController = TextEditingController();

  bool _isOtpSent = false;
  bool _isSubscribed = false;
  String? _subscriptionId;

  @override
  void dispose() {
    _fullNameController.dispose();
    _institutionController.dispose();
    _emailController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xfff6f3ec),
      appBar: AppBar(
        backgroundColor: const Color(0xfff6f3ec),
        elevation: 0,
        scrolledUnderElevation: 0,
        shadowColor: Colors.transparent,
        automaticallyImplyLeading: false,
        titleSpacing: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.close,
            color: Colors.black,
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        centerTitle: true,
        title: const Text(
          'Verify Your Student Status',
          style: TextStyle(
            color: Color(0xff1F2122),
            fontWeight: FontWeight.w600,
            fontSize: 16,
            fontFamily: 'Inter',
          ),
        ),
      ),
      body: BlocConsumer<SaversplanBloc, SaversPlanState>(
        listenWhen: (previous, current) {
          return previous != current;
        },
        listener: (context, state) {
          if (state is SubscribeSaversPassSuccess) {
            setState(() {
              _subscriptionId = state.subscriptionId;
              _isSubscribed = true;
            });

            final otpData = {
              "id": _subscriptionId,
            };
            context.read<SaversplanBloc>().add(
                  SaversSendResendEmailOtp(data: otpData),
                );
          } else if (state is SubscribeSaversPassFailed) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: Duration(seconds: 2),
              ),
            );
          } else if (state is SaversSendResendEmailOtpSuccess) {
            setState(() {
              _isOtpSent = true;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('OTP sent successfully to your email'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: Duration(seconds: 2),
              ),
            );
          } else if (state is SaversSendResendEmailOtpFailed) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to send OTP: ${state.message}'),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: Duration(seconds: 3),
              ),
            );
          } else if (state is SaversVerifyEmailOtpSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Email verified successfully'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: Duration(seconds: 2),
              ),
            );
            Navigator.pop(context, true);
          } else if (state is SaversVerifyEmailOtpFailed) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: Duration(seconds: 2),
              ),
            );
          }
        },
        builder: (context, state) {
          return _buildBody(state);
        },
      ),
    );
  }

  Widget _buildBody(SaversPlanState state) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    final horizontalPadding = screenWidth * 0.04;
    final verticalSpacing = screenHeight * 0.012;
    final fontSize12 = screenWidth * 0.03;
    final fontSize14 = screenWidth * 0.035;
    final fontSize16 = screenWidth * 0.04;

    return SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(height: verticalSpacing),
          Container(
            margin: EdgeInsets.symmetric(
                horizontal: horizontalPadding, vertical: 0),
            padding: EdgeInsets.all(horizontalPadding),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth * 0.03),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Get access to the Student Saver plan at \$25/year. To confirm your eligibility, please provide your student details and verify your email:',
                  style: TextStyle(
                    color: Color(0xff414346),
                    fontSize: fontSize12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                SizedBox(height: verticalSpacing * 1.5),
                Text(
                  'Student Verification',
                  style: TextStyle(
                    color: Color(0xff1F2122),
                    fontWeight: FontWeight.w500,
                    fontSize: fontSize14,
                  ),
                ),
                SizedBox(height: verticalSpacing),
                Text(
                  'Full Name',
                  style: TextStyle(
                    color: Color(0xff1F2122),
                    fontWeight: FontWeight.w500,
                    fontSize: fontSize14,
                  ),
                ),
                SizedBox(height: verticalSpacing * 0.8),
                Container(
                  height: screenHeight * 0.050,
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x1A000000),
                        blurRadius: screenWidth * 0.02,
                        offset: Offset(0, screenHeight * 0.002),
                      ),
                    ],
                    borderRadius: BorderRadius.circular(screenWidth * 0.055),
                  ),
                  child: TextField(
                    controller: _fullNameController,
                    enabled: !_isSubscribed,
                    style: TextStyle(
                      fontSize: MediaQuery.of(context).size.width * 0.04,
                      color:
                          _isSubscribed ? Color(0xff9E9E9E) : Color(0xff1F2122),
                    ),
                    decoration: InputDecoration(
                      hintText: 'Enter full name',
                      hintStyle: TextStyle(
                        color: Color(0xff66696D),
                        fontSize: fontSize14,
                      ),
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: screenWidth * 0.045,
                          vertical: screenHeight * 0.02),
                      filled: true,
                      fillColor:
                          _isSubscribed ? Color(0xFFF5F5F5) : Colors.white,
                      border: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.055),
                        borderSide: BorderSide(
                            color: Color(0xffE1E3E6),
                            width: screenWidth * 0.002),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.055),
                        borderSide: BorderSide(
                            color: Color(0xffE1E3E6),
                            width: screenWidth * 0.002),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.055),
                        borderSide: BorderSide(
                            color: Color(0xff1F2122),
                            width: screenWidth * 0.003),
                      ),
                    ),
                  ),
                ),
                SizedBox(height: verticalSpacing * 1.2),
                Text(
                  'Institute Name',
                  style: TextStyle(
                    color: Color(0xff1F2122),
                    fontWeight: FontWeight.w500,
                    fontSize: fontSize14,
                  ),
                ),
                SizedBox(height: verticalSpacing * 0.5),
                Container(
                  height: screenHeight * 0.050,
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x1A000000),
                        blurRadius: screenWidth * 0.02,
                        offset: Offset(0, screenHeight * 0.002),
                      ),
                    ],
                    borderRadius: BorderRadius.circular(screenWidth * 0.055),
                  ),
                  child: TextField(
                    controller: _institutionController,
                    enabled: !_isSubscribed, // Disable after subscription
                    style: TextStyle(
                      fontSize: MediaQuery.of(context).size.width * 0.04,
                      color:
                          _isSubscribed ? Color(0xff9E9E9E) : Color(0xff1F2122),
                    ),
                    decoration: InputDecoration(
                      hintText: 'Enter Institute name',
                      hintStyle: TextStyle(
                        color: Color(0xff66696D),
                        fontSize: fontSize14,
                      ),
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: screenWidth * 0.045,
                          vertical: screenHeight * 0.02),
                      filled: true,
                      fillColor:
                          _isSubscribed ? Color(0xFFF5F5F5) : Colors.white,
                      border: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.055),
                        borderSide: BorderSide(
                            color: Color(0xffE1E3E6),
                            width: screenWidth * 0.002),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.055),
                        borderSide: BorderSide(
                            color: Color(0xffE1E3E6),
                            width: screenWidth * 0.002),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.055),
                        borderSide: BorderSide(
                            color: Color(0xff1F2122),
                            width: screenWidth * 0.003),
                      ),
                    ),
                  ),
                ),
                SizedBox(height: verticalSpacing * 1.2),
                Text(
                  'Email Address',
                  style: TextStyle(
                    color: Color(0xff1F2122),
                    fontWeight: FontWeight.w500,
                    fontSize: fontSize14,
                  ),
                ),
                SizedBox(height: verticalSpacing * 0.8),
                Container(
                  height: screenHeight * 0.050,
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x1A000000),
                        blurRadius: screenWidth * 0.02,
                        offset: Offset(0, screenHeight * 0.002),
                      ),
                    ],
                    borderRadius: BorderRadius.circular(screenWidth * 0.055),
                  ),
                  child: TextField(
                    controller: _emailController,
                    enabled: !_isSubscribed,
                    keyboardType: TextInputType.emailAddress,
                    style: TextStyle(
                      fontSize: MediaQuery.of(context).size.width * 0.04,
                      color:
                          _isSubscribed ? Color(0xff9E9E9E) : Color(0xff1F2122),
                    ),
                    decoration: InputDecoration(
                      hintText: 'Enter email',
                      hintStyle: TextStyle(
                        color: Color(0xff66696D),
                        fontWeight: FontWeight.w400,
                        fontSize: fontSize14,
                      ),
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: screenWidth * 0.045,
                          vertical: screenHeight * 0.02),
                      filled: true,
                      fillColor:
                          _isSubscribed ? Color(0xFFF5F5F5) : Colors.white,
                      border: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.055),
                        borderSide: BorderSide(
                            color: Color(0xffE1E3E6),
                            width: screenWidth * 0.002),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.055),
                        borderSide: BorderSide(
                            color: Color(0xffE1E3E6),
                            width: screenWidth * 0.002),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.055),
                        borderSide: BorderSide(
                            color: Color(0xff1F2122),
                            width: screenWidth * 0.003),
                      ),
                    ),
                  ),
                ),
                if (_isOtpSent) ...[
                  SizedBox(height: verticalSpacing * 1.2),
                  Text(
                    'Enter OTP',
                    style: TextStyle(
                      color: Color(0xff1F2122),
                      fontWeight: FontWeight.w500,
                      fontSize: fontSize14,
                    ),
                  ),
                  SizedBox(height: verticalSpacing * 0.8),
                  Container(
                    height: screenHeight * 0.050,
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0x1A000000),
                          blurRadius: screenWidth * 0.02,
                          offset: Offset(0, screenHeight * 0.002),
                        ),
                      ],
                      borderRadius: BorderRadius.circular(screenWidth * 0.055),
                    ),
                    child: TextField(
                      controller: _otpController,
                      keyboardType: TextInputType.number,
                      maxLength: 6,
                      style: TextStyle(
                        fontSize: MediaQuery.of(context).size.width * 0.04,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Enter 6-digit OTP',
                        hintStyle: TextStyle(
                          color: Color(0xff66696D),
                          fontWeight: FontWeight.w400,
                          fontSize: fontSize14,
                        ),
                        counterText: '', // Hide the counter
                        contentPadding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.045,
                            vertical: screenHeight * 0.02),
                        filled: true,
                        fillColor: Colors.white,
                        border: OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.055),
                          borderSide: BorderSide(
                              color: Color(0xffE1E3E6),
                              width: screenWidth * 0.002),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.055),
                          borderSide: BorderSide(
                              color: Color(0xffE1E3E6),
                              width: screenWidth * 0.002),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.055),
                          borderSide: BorderSide(
                              color: Color(0xff1F2122),
                              width: screenWidth * 0.003),
                        ),
                      ),
                    ),
                  ),
                  // SizedBox(height: verticalSpacing),

                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      onPressed: (state is SaversSendResendEmailOtpLoading)
                          ? null
                          : _resendOtp,
                      child: Text(
                        (state is SaversSendResendEmailOtpLoading)
                            ? 'Sending...'
                            : 'Resend OTP',
                        style: TextStyle(
                          color: (state is SaversSendResendEmailOtpLoading)
                              ? Color(0xff9E9E9E)
                              : Color(0xff1F2122),
                          fontWeight: FontWeight.w400,
                          fontSize: fontSize12,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ),
                ],
                SizedBox(height: verticalSpacing * 1.3),
                SizedBox(
                  width: double.infinity,
                  height: screenHeight * 0.065,
                  child: ElevatedButton(
                    onPressed: _isOtpSent
                        ? ((state is SaversVerifyEmailOtpLoading)
                            ? null
                            : _verifyOtp)
                        : ((state is SubscribeSaversPassLoading ||
                                state is SaversSendResendEmailOtpLoading)
                            ? null
                            : _subscribe),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xff1F2122),
                      disabledBackgroundColor: const Color(0xff9E9E9E),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(screenWidth * 0.07),
                      ),
                      padding: EdgeInsets.symmetric(
                          horizontal: screenWidth * 0.04,
                          vertical: screenHeight * 0.015),
                      elevation: 0,
                    ),
                    child: _isOtpSent
                        ? ((state is SaversVerifyEmailOtpLoading)
                            ? SizedBox(
                                height: fontSize16 * 1.2,
                                width: fontSize16 * 1.2,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                'Verify OTP',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w400,
                                  fontSize: fontSize16,
                                ),
                              ))
                        : ((state is SubscribeSaversPassLoading ||
                                state is SaversSendResendEmailOtpLoading)
                            ? SizedBox(
                                height: fontSize16 * 1.2,
                                width: fontSize16 * 1.2,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                'Subscribe',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w400,
                                  fontSize: fontSize16,
                                ),
                              )),
                  ),
                ),
                SizedBox(height: verticalSpacing * 0.8),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _subscribe() {
    if (_fullNameController.text.isEmpty ||
        _institutionController.text.isEmpty ||
        _emailController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill all fields'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final subscriptionData = {
      "savers_pass_id": widget.planId,
      "verification_method": "email",
      "full_name": _fullNameController.text,
      "institution_name": _institutionController.text,
      "email": _emailController.text,
    };

    context.read<SaversplanBloc>().add(
          SubscribeSaversPassEvent(data: subscriptionData),
        );
  }

  void _verifyOtp() {
    if (_otpController.text.isEmpty || _otpController.text.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid 6-digit OTP'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final verificationData = {
      "id": _subscriptionId,
      "otp": _otpController.text,
    };

    context.read<SaversplanBloc>().add(
          SaversVerifyEmailOtp(data: verificationData),
        );
  }

  void _resendOtp() {
    final otpData = {
      "id": _subscriptionId,
    };

    context.read<SaversplanBloc>().add(
          SaversSendResendEmailOtp(data: otpData),
        );
  }
}
