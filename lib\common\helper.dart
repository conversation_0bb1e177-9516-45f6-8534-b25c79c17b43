// ignore_for_file: deprecated_member_use, use_build_context_synchronously

import 'dart:async';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/main.dart';
import 'package:db_eats/utils/showdialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'dart:developer' as dev;

import 'package:fluttertoast/fluttertoast.dart';
import 'package:geolocator/geolocator.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:page_transition/page_transition.dart';
import 'package:shared_preferences/shared_preferences.dart';
// Import removed: package:db_eats/ui/main.dart

class Helper {
  // DONOT DELETE 👇
  // border: Border.all(color: Color(0xffF05353)),
  static GlobalKey key = NavigationService.navigatorKey;
  static height(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  static showSnack(String? text) {
    ScaffoldMessenger.of(getContext()).hideCurrentSnackBar();
    ScaffoldMessenger.of(getContext()).showSnackBar(
        SnackBar(duration: const Duration(seconds: 5), content: Text(text!)));
  }

  static shrink() {
    return const SizedBox.shrink();
  }

  static showToast1(String msg) {
    Fluttertoast.cancel(); // for immediate stopping
    return Fluttertoast.showToast(msg: msg);
  }

  static showLog(msg) {
    return dev.log('${(msg)} ');
    //- ${DateTime.now().difference(Initializer.logTime!).inHours}h:${DateTime.now().difference(Initializer.logTime!).inMinutes}m ~ ${DateTime.now().difference(Initializer.logTime!).inSeconds}
  }

  // static void showLoading() {
  //   showDialog(
  //     barrierColor: Colors.white70,
  //     barrierDismissible: false,
  //     context: Helper.getContext()!,
  //     builder: (context) => const CupertinoActivityIndicator(),
  //   );
  // }
  static getContext() => key.currentContext;

  static loading(BuildContext context) {
    return showDialog(
        barrierColor: Colors.white70,
        barrierDismissible: false,
        context: context,
        builder: (context) => const ShowDialog());
  }

  static pop(BuildContext context) {
    return Navigator.pop(context);
  }

  static width(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  static allowHeight(double height) {
    return SizedBox(height: height);
  }

  static allowWidth(double width) {
    return SizedBox(width: width);
  }

  static showLoading(BuildContext context, [msg]) {
    return showDialog(
        barrierColor: Colors.white70,
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            backgroundColor: Colors.transparent,
            elevation: 0,
            content: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  SizedBox(
                    width: 36,
                    height: 36,
                    child: LoadingAnimationWidget.threeArchedCircle(
                      color: Colors.black,
                      size: 42,
                    ),
                  ),
                  msg != null
                      ? Helper.allowHeight(10.0)
                      : const SizedBox.shrink(),
                  msg != null
                      ? Text(
                          msg,
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        )
                      : const SizedBox.shrink(),
                ],
              ),
            ),
          );
        });
  }

  static text(
      String text, size, Color? color, FontWeight? fontWeight, double padding) {
    return Padding(
      padding: EdgeInsets.all(padding),
      child: Text(text,
          style: TextStyle(
            color: color,
            fontWeight: fontWeight ?? FontWeight.bold,
            fontSize: double.parse(
              size.toString(),
            ),
          )),
    );
  }

  static textformfileld(
      String? label,
      TextEditingController textcontroller,
      bool? keyboartext,
      bool? validation,
      BuildContext context,
      String? keyboardtype,
      int? maxline) {
    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: SizedBox(
        // height: 58,
        height: 55,
        // width: 320,
        width: MediaQuery.of(context).size.width / 1.110,
        child: TextFormField(
          autovalidateMode: AutovalidateMode.onUserInteraction,
          maxLines: maxline,
          // validator: (value) {
          //   if (value!.isEmpty) {
          //     if (validation == false) {
          //       return null;
          //     } else {
          //       return "this field is required";
          //     }
          //   }
          //   return null;
          // },
          showCursor: true,
          cursorColor: Colors.black,
          autocorrect: true,
          controller: textcontroller,
          // obscureText: !show,
          textInputAction: TextInputAction.done,
          keyboardType:
              keyboartext == true ? TextInputType.text : TextInputType.number,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(vertical: 10.0, horizontal: 10),
            border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: Colors.black)),
            enabledBorder: const OutlineInputBorder(
              borderSide: BorderSide(
                color: Colors.black,
                width: 1.5,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: const BorderSide(
                color: Colors.black54, // Change the color to your preference
                width: 2.0,
              ),
            ),
            fillColor: Colors.grey[50],
            labelText: label,
          ),
        ),
      ),
    );
  }

  static textformfiled(hinttext, TextEditingController textcontroller,
      String keyboardtype, bool validation, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: SizedBox(
        height: 55,
        // width: 320,
        width: MediaQuery.of(context).size.width / 1.110,
        child: TextFormField(
          inputFormatters: [
            keyboardtype == "text"
                ? LengthLimitingTextInputFormatter(1000)
                : LengthLimitingTextInputFormatter(10),
          ],
          autovalidateMode: AutovalidateMode.onUserInteraction,
          validator: (value) {
            if (value!.isEmpty) {
              if (validation == false) {
                return null;
              } else {
                return "this field is required";
              }
            }
            return null;
          },
          showCursor: true,
          cursorColor: Colors.black,
          autocorrect: true,
          controller: textcontroller,
          textInputAction: TextInputAction.done,
          keyboardType: keyboardtype == "text"
              ? TextInputType.text
              : TextInputType.number,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            fillColor: Colors.grey[50],
            labelText: hinttext,
            // labelStyle: AppStyles.buttonloginText,
          ),
        ),
      ),
    );
  }

  void coreSnackbar(GlobalKey<ScaffoldState>? scaffoldKey, String? content,
      {IconData? icon, Color? color}) {
    var snackBar = SnackBar(
      backgroundColor: Colors.white,
      content: Row(
        // mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            icon!,
            color: color!,
          ),
          const SizedBox(
            width: 15,
          ),
          Flexible(
            child: Text(
              content!,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(color: Colors.black, fontSize: 14),
            ),
          ),
        ],
      ),
      behavior: SnackBarBehavior.floating,
      duration: const Duration(seconds: 2),
    );
    ScaffoldMessenger.of(scaffoldKey!.currentContext!).showSnackBar(snackBar);
  }

  static RegExp exp =
      RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
  static RegExp phoexp = RegExp(r"^[0-9]{10}$");
  static String formatEmail(String newText) {
    // Regular expression to check for valid email
    RegExp exp = RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
    if (exp.hasMatch(newText)) {
      return newText;
    }
    return "";
  }

  static showToast(String s, {required msg}) {
    Fluttertoast.cancel(); // for immediate stopping
    return Fluttertoast.showToast(msg: msg);
  }

  static locaStorageInst() async {
    return SharedPreferences.getInstance();
  }

  static push(dynamic route, String name) {
    return Navigator.push(
      Helper.getContext(),
      PageTransition(
        duration: const Duration(milliseconds: 400),
        type: PageTransitionType.fade,
        isIos: true,
        child: route,
      ),
    );
  }

  static void showLoading1() {
    showDialog(
      barrierColor: Colors.white70,
      barrierDismissible: false,
      context: Helper.getContext(),
      builder: (context) => const CupertinoActivityIndicator(),
    );
  }

  static pop1({value}) {
    return Navigator.pop(Helper.getContext(), value ?? false);
  }

  static Future<void> makeADelay({required int delayInMilliseconds}) async {
    return Future.delayed(Duration(milliseconds: delayInMilliseconds));
  }

  static pushWithThen(dynamic route, String name, Function? then) {
    return Navigator.push(
      Helper.getContext(),
      PageTransition(
        duration: const Duration(milliseconds: 400),
        type: PageTransitionType.fade,
        isIos: true,
        child: route,
      ),
    ).then((value) => then);
  }

  static pushReplacementRemove(dynamic namedRoute, String? name) {
    Navigator.of(Helper.getContext()).pushAndRemoveUntil(
        PageTransition(
          type: PageTransitionType.fade,
          isIos: true,
          child: namedRoute,
        ),
        (Route<dynamic> route) => false);
  }

  static pushReplacement(dynamic route, String? name) {
    return Navigator.pushReplacement(
        Helper.getContext(),
        PageTransition(
          type: PageTransitionType.fade,
          isIos: true,
          child: route,
        )
        // MaterialPageRoute(
        //     settings: RouteSettings(arguments: name),
        //     builder: ((context) => route)),
        );
  }

  static emailRegexp() {
    return RegExp(
        r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+");
  }

  static pushReplacementWithDelay(BuildContext context, dynamic route,
      [delay]) {
    return Future.delayed(Duration(seconds: delay ?? 3), () async {
      Navigator.pushReplacement(
          context, MaterialPageRoute(builder: ((context) => route)));
    });
  }

  static alert(BuildContext context) {
    showDialog(
        barrierColor: Colors.white70,
        context: context,
        builder: ((context) => AlertDialog(
              actions: <Widget>[
                TextButton(
                  child: const Text("Ok"),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
              content: Column(
                children: [
                  Image.asset(
                    "assets/images/greentick.jpg",
                  ),
                  Helper.allowHeight(40),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Text(
                        "Congrats",
                        style: TextStyle(
                          fontSize: 36,
                          color: Color(0xffF05353),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Helper.allowHeight(20),
                      const Text(
                        "Your order has been placed successully!!",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Helper.allowHeight(30),
                      const Text(
                        "Thank You!!!... ",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            )));
  }

  static showCustomDialog(BuildContext context) {
    AwesomeDialog(
      context: context,
      dialogType: DialogType.noHeader,
      animType: AnimType.rightSlide,
      dismissOnTouchOutside: false,
      width: 300,
      body: Container(
        constraints: const BoxConstraints(maxWidth: 300),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 16),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 2,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: ClipOval(
                child: Image.asset(
                  'assets/images/logo.png',
                  height: 70,
                  width: 70,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Login Required',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'You need to login to access it.',
              style: TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
      btnOkText: 'Ok',
      btnOkOnPress: () {},
      btnOkColor: Colors.green,
      buttonsTextStyle: const TextStyle(color: Colors.white),
      buttonsBorderRadius: BorderRadius.circular(8),
      btnCancelText: null,
    ).show();
  }

  // static getVersion() async {
  //   PackageInfo packageInfo = await PackageInfo.fromPlatform();
  //   Initializer.version = '${packageInfo.version}+${packageInfo.buildNumber}';
  // }
  static Future<bool> checkLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();

    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }

    // If permission is still denied after requesting
    if (permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever) {
      return false;
    }

    return permission == LocationPermission.whileInUse ||
        permission == LocationPermission.always;
  }

  // Function to show a dialog when location permission is denied
  static Future<void> showLocationPermissionDeniedDialog(
      BuildContext context) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Location Permission Required'),
          content: const Text(
            'This app needs location access to function properly. Please enable location permissions.',
            style: TextStyle(fontSize: 14),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Open App Settings'),
              onPressed: () async {
                Navigator.of(context).pop();
                await Geolocator.openAppSettings();
              },
            ),
          ],
        );
      },
    );
  }

  // Function to check if location services are enabled
  static Future<bool> isLocationServicesEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  // Function to get the current location
  static Future<Position?> getCurrentLocation(BuildContext context) async {
    try {
      bool hasPermission = await checkLocationPermission();
      if (hasPermission && await isLocationServicesEnabled()) {
        return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
        );
      } else {
        // Show permission denied dialog
        await showLocationPermissionDeniedDialog(context);
        return null;
      }
    } catch (e) {
      debugPrint('Error getting location: $e');
      return null;
    }
  }

  // Function to request location services if disabled
  static Future<void> requestLocationServices(BuildContext context) async {
    bool isEnabled = await isLocationServicesEnabled();
    if (!isEnabled) {
      // Show a dialog to prompt the user to enable location services
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Location Required'),
            content: const Text(
              'Location services are disabled. Please enable them in your settings.',
              style: TextStyle(fontSize: 14),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            actions: <Widget>[
              TextButton(
                child: const Text('Cancel'),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              TextButton(
                child: const Text('Open Settings'),
                onPressed: () async {
                  Navigator.of(context).pop();
                  await Geolocator.openLocationSettings();
                },
              ),
            ],
          );
        },
      );
    }
  }

  static Future<void> getCordinates(BuildContext context) async {
    Initializer.latitude = '';
    Initializer.longitude = '';
    Initializer.accuracy = '';
    // Check if location permissions are granted
    bool hasPermission = await checkLocationPermission();
    if (!hasPermission) return;

    bool isLocationServicesEnabled = await checkLocationPermission();
    if (!isLocationServicesEnabled) return;

    try {
      // Get current position with high accuracy
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.best,
        timeLimit: const Duration(seconds: 10), // 10-second timeout
      );

      // Convert accuracy to numeric value for comparison
      double accuracyValue = position.accuracy;

      // Check if accuracy is greater than 20 meters
      if (accuracyValue > 20) {
        // Show confirmation dialog
        bool? continueWithLowAccuracy = await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Low GPS Accuracy',
                  style: TextStyle(fontSize: 17)),
              content: Text(
                'Current GPS accuracy is ${accuracyValue.toStringAsFixed(2)} meters. '
                'Do you want to continue with this lower accuracy?',
              ),
              actions: [
                TextButton(
                  child: const Text('Cancel',
                      style: TextStyle(color: Colors.green, fontSize: 13)),
                  onPressed: () => Navigator.of(context).pop(false),
                ),
                TextButton(
                  child: const Text('Continue',
                      style: TextStyle(color: Colors.green, fontSize: 13)),
                  onPressed: () => Navigator.of(context).pop(true),
                ),
              ],
            );
          },
        );

        // If user cancels, exit the method
        if (continueWithLowAccuracy != true) {
          return;
        }
      }

      // Update Initializer with GPS data
      Initializer.latitude = position.latitude.toStringAsFixed(6);
      Initializer.longitude = position.longitude.toStringAsFixed(6);
      Initializer.accuracy = '${position.accuracy.toStringAsFixed(2)} meters';

      dev.log(
        'Location: ${Initializer.latitude}, ${Initializer.longitude}, ${Initializer.accuracy}',
      );
    } catch (e) {
      // Handle any errors in getting location
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error getting location: ${e.toString()}')),
      );
    }
  }

  static String getFormattedCurrentTime() {
    DateTime now = DateTime.now();

    // Save the date in the format YYYY-MM-DD
    Initializer.date =
        "${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}";

    // Save the time in the format HH:mm:ss
    Initializer.time =
        "${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}";

    dev.log('Saved date: ${Initializer.date}, Saved time: ${Initializer.time}');

    // Return the formatted time (if needed for display or other purposes)
    return Initializer.time;
  }

  static String getCurrentDate() {
    DateTime now = DateTime.now();
    // Format the date as per your requirement
    return "${now.day.toString().padLeft(2, '0')}/${now.month.toString().padLeft(2, '0')}/${now.year}";
  }

  static Widget dropdown({
    required List<String> items,
    required String hint,
    required String? selectedItem,
    String? Function(String?)? validator,
    required Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        contentPadding:
            const EdgeInsets.symmetric(vertical: 14.0, horizontal: 16.0),
        fillColor: Colors.grey[100],
        filled: true,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide(
            color: Colors.grey.shade300,
            width: 1.0,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: const BorderSide(
            color: Color.fromARGB(255, 10, 165, 85),
            width: 2.0,
          ),
        ),
      ),
      hint: Text(
        hint,
        style: const TextStyle(
          fontSize: 16.0,
          fontWeight: FontWeight.w400,
          color: Colors.black54,
        ),
      ),
      value: selectedItem,
      onChanged: onChanged,
      items: items.map<DropdownMenuItem<String>>((String value) {
        return DropdownMenuItem<String>(
          value: value,
          child: SizedBox(
            height: 36.0, // Adjust item height
            child: Row(
              children: [
                Icon(Icons.circle, size: 12.0, color: Colors.grey.shade400),
                const SizedBox(width: 15.0),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 12.0, // Smaller text size
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
      dropdownColor: Colors.white,
      icon: const Icon(
        Icons.arrow_drop_down,
        color: Colors.black,
      ),
      iconSize: 24.0,
      validator: validator,
    );
  }

  static Widget dropdown1({
    required List<String> items,
    required String hint,
    required String? selectedItem,
    String? Function(String?)? validator,
    required Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        contentPadding:
            const EdgeInsets.symmetric(vertical: 14.0, horizontal: 16.0),
        fillColor: Colors.grey[100],
        filled: true,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide(
            color: Colors.grey.shade300,
            width: 1.0,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: const BorderSide(
            color: Color.fromARGB(255, 10, 165, 85),
            width: 2.0,
          ),
        ),
      ),
      hint: Text(
        hint,
        style: const TextStyle(
          fontSize: 16.0,
          fontWeight: FontWeight.w400,
          color: Colors.black54,
        ),
      ),
      value: selectedItem,
      onChanged: onChanged,
      items: items.map<DropdownMenuItem<String>>((String value) {
        return DropdownMenuItem<String>(
          value: value,
          child: SizedBox(
            height: 55.0, // Increased item height to accommodate longer text
            child: Row(
              children: [
                Icon(Icons.circle, size: 12.0, color: Colors.grey.shade400),
                const SizedBox(width: 15.0),
                Expanded(
                  // Ensures the text takes up available space
                  child: Text(
                    value,
                    style: const TextStyle(
                      fontSize: 14.0, // Increased text size for readability
                      color: Colors.black,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
      dropdownColor: Colors.white,
      icon: const Icon(
        Icons.arrow_drop_down,
        color: Colors.black,
      ),
      iconSize: 24.0,
      validator: validator,
      isExpanded: true, // Ensures the dropdown button expands fully
      selectedItemBuilder: (BuildContext context) {
        return items.map<Widget>((String item) {
          return Text(
            item,
            style: const TextStyle(
              fontSize:
                  16.0, // Ensure this is large enough for the selected text
              fontWeight: FontWeight.w400,
              color: Colors.black,
            ),
          );
        }).toList();
      },
    );
  }

  // Method to show a confirmation dialog
  static Future<void> showConfirmationDialog({
    required BuildContext context,
    required String title,
    required String message,
    required String confirmText,
    required String cancelText,
    required VoidCallback onConfirm,
    required VoidCallback onCancel,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: true, // User must tap a button to close the dialog
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
          title: Text(
            title,
            style: const TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
          ),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              onPressed: onCancel,
              child: Text(
                cancelText,
                style: const TextStyle(color: Colors.green),
              ),
            ),
            TextButton(
              onPressed: onConfirm,
              child: Text(
                confirmText,
                style: const TextStyle(color: Colors.green),
              ),
            ),
          ],
        );
      },
    );
  }

  static showConfirmationBackDialog(
      {required BuildContext context,
      required String title,
      required String message,
      required String confirmText,
      required String cancelText,
      required Function() onCancel,
      required Function() onConfirm}) {
    return showDialog<void>(
      context: context,
      barrierDismissible: true, // User must tap a button to close the dialog
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
          title: Text(
            title,
            style: const TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
          ),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              onPressed: onCancel,
              child: Text(
                cancelText,
                style: const TextStyle(color: Colors.green),
              ),
            ),
            TextButton(
              onPressed: onConfirm,
              child: Text(
                confirmText,
                style: const TextStyle(color: Colors.green),
              ),
            ),
          ],
        );
      },
    );
  }

  static void showLoading_2(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible:
          false, // Prevent dismissing the dialog by tapping outside
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(), // Loading spinner
              SizedBox(width: 20),
              Text("Sending form data..."),
            ],
          ),
        );
      },
    );
  }

  static void hideLoading(BuildContext context) {
    Navigator.of(context).pop(); // Dismiss the dialog
  }

  void showInfoDialog({
    required BuildContext context,
    required String title,
    required String content,
  }) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius:
              BorderRadius.circular(16.0), // Rounded corners for the dialog
        ),
        title: Row(
          children: [
            const Icon(Icons.info_outline,
                color: Colors.green, size: 20.0), // "i" icon
            const SizedBox(width: 8.0), // Spacing between the icon and title
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 18.0, // Reduced title size
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
            ),
          ],
        ),
        content: Text(
          content,
          style: const TextStyle(
            fontSize: 14.0,
            color: Colors.black87,
          ),
        ),
        actions: [
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: Colors.green, // Green color for the button text
            ),
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Close',
              style: TextStyle(fontSize: 14.0),
            ),
          ),
        ],
      ),
    );
  }
}
