import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/server/serverhelper.dart'; // Assumed ServerHelper for image URL
import 'package:db_eats/bloc/order_bloc.dart'; // Assumed OrderBloc

class DriverRatingPage extends StatefulWidget {
  final int orderId;

  const DriverRatingPage({
    Key? key,
    required this.orderId,
  }) : super(key: key);

  @override
  State<DriverRatingPage> createState() => _DriverRatingPageState();
}

class _DriverRatingPageState extends State<DriverRatingPage> {
  int _rating = 0;
  final List<String> _attributes = [
    'Good Communication',
    'Handled With Care',
    'Delivery Time',
    'Followed Instructions',
    'Service',
    'Hygienic',
  ];
  final Set<String> _selectedAttributes = {};
  final TextEditingController _commentController = TextEditingController();
  final List<double> _tipOptions = [1.00, 2.00, 3.00, 5.00, 0.00];
  int _selectedTipIndex = 1; // Default to $2.00
  String _paymentMethod = "Visa...1234";
  bool _isLoading = false;
  String? _driverName;
  String? _driverImage;
  int? _driverId;
  String? _chefName;
  String? _chefImage;
  int? _chefId;

  @override
  void initState() {
    super.initState();
    // Dispatch event to fetch order details
    context.read<OrderBloc>().add(ViewOrederdetailsEvent(widget.orderId));
  }

  // Helper method to get responsive sizes based on screen width
  double getResponsiveSize(BuildContext context,
      {double small = 12,
      double medium = 16,
      double large = 20,
      double xlarge = 24}) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return small;
    if (width < 600) return medium;
    if (width < 900) return large;
    return xlarge;
  }

  // Helper method to get responsive padding
  EdgeInsets getResponsivePadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final isLandscape = width > height;

    if (width < 360) {
      return EdgeInsets.all(width * 0.03);
    } else if (width < 600) {
      return EdgeInsets.all(width * 0.04);
    } else {
      return EdgeInsets.all(isLandscape ? width * 0.03 : width * 0.05);
    }
  }

  void _addAttributeToComment(String attribute, bool isSelected) {
    final currentText = _commentController.text.trim();
    List<String> currentAttributes = currentText.isEmpty
        ? []
        : currentText.split(', ').map((e) => e.trim()).toList();

    if (isSelected) {
      currentAttributes.remove(attribute);
    } else {
      if (!currentAttributes.contains(attribute)) {
        currentAttributes.add(attribute);
      }
    }

    _commentController.text = currentAttributes.join(', ');
    _commentController.selection = TextSelection.fromPosition(
      TextPosition(offset: _commentController.text.length),
    );
  }

  void _submitReview() {
    if (_rating == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a rating')),
      );
      return;
    }

    if (_driverId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Driver details not loaded')),
      );
      return;
    }

    try {
      final data = {
        'driver_id': _driverId,
        'order_id': widget.orderId,
        'star_rating': _rating,
        'comment': _commentController.text.trim(),
        'tip_amount': _tipOptions[_selectedTipIndex],
      };

      setState(() => _isLoading = true);
      context.read<OrderBloc>().add(SubmitDriverRatingEvent(data));
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error submitting review: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;

    final maxContentWidth = size.width > 900
        ? 800.0
        : size.width > 600
            ? 600.0
            : size.width;

    final baseTextSize = getResponsiveSize(context,
        small: 12, medium: 14, large: 16, xlarge: 18);
    final headingTextSize = baseTextSize * 1.4286;
    final subheadingTextSize = baseTextSize * 1.1429;
    final captionTextSize = baseTextSize * 0.8571;

    final contentPadding = getResponsivePadding(context);
    final itemSpacing = size.height * 0.015;

    final avatarRadius = isLandscape
        ? size.height * 0.08
        : size.width * (size.width < 600 ? 0.08 : 0.06);

    return BlocListener<OrderBloc, OrderState>(
      listener: (context, state) {
        if (state is ViewOrederdetailsLoading) {
          setState(() => _isLoading = true);
        } else if (state is ViewOrederdetailsSuccess) {
          setState(() {
            _isLoading = false;
            final driver = state.data?.assignedDriver;
            final chef = state.data?.chef;
            if (driver != null) {
              _driverName = '${driver.firstName} ${driver.lastName}';
              _driverImage = driver.profilePictureUrl;
              _driverId = driver.driverDeliveryOrderId;
            }
            if (chef != null) {
              _chefName = chef.name;
              _chefImage = chef.photo;
              _chefId = chef.id;
            }
          });
        } else if (state is DriverRatingSubmitted) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              duration: Duration(seconds: 2),
            ),
          );
          Navigator.pop(context);
        } else if (state is DriverRatingFailed) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        } else if (state is ViewOrederdetailsFailed) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        body: SafeArea(
          child: Center(
            child: LayoutBuilder(
              builder: (context, constraints) {
                return SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: EdgeInsets.symmetric(
                    vertical: size.height * 0.02,
                    horizontal: size.width * 0.04,
                  ),
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: maxContentWidth,
                    ),
                    child: Column(
                      children: [
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.1),
                                spreadRadius: 1,
                                blurRadius: 5,
                              ),
                            ],
                          ),
                          padding: contentPadding,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              FittedBox(
                                fit: BoxFit.scaleDown,
                                child: Text(
                                  'Rate your driver',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: headingTextSize,
                                    fontFamily: 'Inter-Semibold',
                                    color: Color(0xFF000000),
                                  ),
                                ),
                              ),
                              SizedBox(height: itemSpacing),
                              if (_isLoading) ...[
                                Container(
                                  width: 120,
                                  height: 18,
                                  color: Colors.grey[300],
                                  margin: EdgeInsets.only(
                                      bottom: itemSpacing * 0.7),
                                ),
                                CircleAvatar(
                                  radius: avatarRadius,
                                  backgroundColor: Colors.grey[300],
                                  child: const Center(
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2),
                                  ),
                                ),
                                SizedBox(height: itemSpacing),
                              ] else if (_driverName != null) ...[
                                Text(
                                  'Food by ${_driverName}',
                                  style: TextStyle(
                                    fontSize: subheadingTextSize,
                                    fontFamily: 'Inter-Medium',
                                    color: Color(0xFF1F2122),
                                  ),
                                ),
                                SizedBox(height: itemSpacing * 0.7),
                                CircleAvatar(
                                  radius: avatarRadius,
                                  backgroundColor: const Color(0xFFE1E3E6),
                                  child: ClipRRect(
                                    borderRadius:
                                        BorderRadius.circular(avatarRadius),
                                    child: CachedNetworkImage(
                                      imageUrl: _driverImage ?? '',
                                      width: avatarRadius * 2,
                                      height: avatarRadius * 2,
                                      fit: BoxFit.cover,
                                      placeholder: (context, url) => Container(
                                        width: avatarRadius * 2,
                                        height: avatarRadius * 2,
                                        color: Colors.grey[300],
                                        child: const Center(
                                          child: CircularProgressIndicator(
                                              strokeWidth: 2),
                                        ),
                                      ),
                                      errorWidget: (context, url, error) =>
                                          Icon(
                                        Icons.person,
                                        size: avatarRadius,
                                        color: Colors.grey[400],
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(height: itemSpacing),
                              ],
                              SizedBox(height: itemSpacing),
                              Wrap(
                                spacing: size.width * 0.02,
                                children: List.generate(5, (index) {
                                  return GestureDetector(
                                    onTap: () =>
                                        setState(() => _rating = index + 1),
                                    child: Icon(
                                      _rating > index
                                          ? Icons.star
                                          : Icons.star_border,
                                      size: isLandscape
                                          ? size.height * 0.07
                                          : size.width *
                                              (size.width < 600 ? 0.13 : 0.06),
                                      color: const Color(0xFFFFBE16),
                                    ),
                                  );
                                }),
                              ),
                              SizedBox(height: itemSpacing * 0.7),
                              Divider(
                                color: Color(0xFFE1E3E6),
                                thickness: 1,
                              ),
                              SizedBox(height: itemSpacing * 0.7),
                              Container(
                                width: double.infinity,
                                alignment: Alignment.center,
                                child: Wrap(
                                  spacing: size.width * 0.02,
                                  runSpacing: itemSpacing,
                                  alignment: WrapAlignment.center,
                                  children: _attributes.map((attribute) {
                                    final isSelected =
                                        _selectedAttributes.contains(attribute);
                                    return GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          if (isSelected) {
                                            _selectedAttributes
                                                .remove(attribute);
                                          } else {
                                            _selectedAttributes.add(attribute);
                                          }
                                          _addAttributeToComment(
                                              attribute, isSelected);
                                        });
                                      },
                                      child: Container(
                                        margin: EdgeInsets.symmetric(
                                          horizontal: size.width * 0.005,
                                          vertical: size.height * 0.002,
                                        ),
                                        padding: EdgeInsets.symmetric(
                                          horizontal: size.width * 0.03,
                                          vertical: size.height * 0.008,
                                        ),
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? Colors.black
                                              : Color(0xFFE1E3E6),
                                          borderRadius:
                                              BorderRadius.circular(16),
                                        ),
                                        child: Text(
                                          attribute,
                                          style: TextStyle(
                                            color: isSelected
                                                ? Colors.white
                                                : Color(0xFF1F2122),
                                            fontSize: captionTextSize,
                                            fontFamily: 'Inter-Medium',
                                          ),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 1.2),
                              AnimatedContainer(
                                duration: Duration(milliseconds: 300),
                                padding: EdgeInsets.symmetric(
                                    horizontal: size.width * 0.03,
                                    vertical: size.height * 0.01),
                                decoration: BoxDecoration(
                                  color: Color(0xFFFFFFFF),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Color(0xFFE1E3E6),
                                    width: 1,
                                  ),
                                ),
                                child: TextField(
                                  controller: _commentController,
                                  maxLines: isKeyboardVisible ? 2 : 3,
                                  style: TextStyle(
                                    fontSize: baseTextSize,
                                    fontFamily: 'Inter',
                                  ),
                                  decoration: InputDecoration(
                                    hintText: 'Leave a comment',
                                    hintStyle: TextStyle(
                                      color: const Color(0xFF66696D),
                                      fontSize: baseTextSize,
                                      fontFamily: 'Inter',
                                    ),
                                    border: InputBorder.none,
                                    contentPadding: EdgeInsets.zero,
                                  ),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 1.2),
                              Container(
                                width: double.infinity,
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  'Tip your driver',
                                  style: TextStyle(
                                    fontSize: subheadingTextSize,
                                    fontFamily: 'Inter-Semibold',
                                    color: Color(0xFF000000),
                                  ),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.8),
                              Container(
                                width: double.infinity,
                                alignment: Alignment.centerLeft,
                                child: Wrap(
                                  alignment: WrapAlignment.start,
                                  spacing: size.width * 0.02,
                                  runSpacing: size.height * 0.01,
                                  children: List.generate(_tipOptions.length,
                                      (index) {
                                    final isSelected =
                                        _selectedTipIndex == index;
                                    return GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          _selectedTipIndex = index;
                                        });
                                      },
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: size.width * 0.02,
                                          vertical: size.height * 0.008,
                                        ),
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? Colors.black
                                              : Color(0xFFE1E3E6),
                                          borderRadius:
                                              BorderRadius.circular(20),
                                        ),
                                        child: Text(
                                          _tipOptions[index] == 0.0
                                              ? 'Other'
                                              : "+\$${_tipOptions[index].toStringAsFixed(2)}",
                                          style: TextStyle(
                                            color: isSelected
                                                ? Colors.white
                                                : Colors.black,
                                            fontWeight: FontWeight.w500,
                                            fontSize: baseTextSize,
                                          ),
                                        ),
                                      ),
                                    );
                                  }),
                                ),
                              ),
                              AnimatedContainer(
                                duration: Duration(milliseconds: 300),
                                margin:
                                    EdgeInsets.symmetric(vertical: itemSpacing),
                                child: Divider(
                                  color: Color(0xFFE1E3E6),
                                  thickness: 1,
                                ),
                              ),
                              Container(
                                width: double.infinity,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Payment',
                                      style: TextStyle(
                                        fontSize: subheadingTextSize,
                                        fontFamily: 'Inter-Semibold',
                                        color: Color(0xFF000000),
                                      ),
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                          _paymentMethod,
                                          style: TextStyle(
                                            fontSize: baseTextSize,
                                            fontFamily: 'Inter-Semibold',
                                            color: Color(0xFF000000),
                                          ),
                                        ),
                                        SizedBox(width: 4),
                                        Icon(
                                          Icons.chevron_right,
                                          size: baseTextSize * 1.2,
                                          color: Colors.black,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: itemSpacing * 1.2),
                              SizedBox(
                                width: double.infinity,
                                height: isLandscape
                                    ? size.height * 0.08
                                    : size.height * 0.06,
                                child: ElevatedButton(
                                  onPressed: _isLoading ? null : _submitReview,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.black,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                          size.width > 600 ? 28 : 24),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: _isLoading
                                      ? SizedBox(
                                          height: baseTextSize,
                                          width: baseTextSize,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                    Colors.white),
                                          ),
                                        )
                                      : Text(
                                          'Submit',
                                          style: TextStyle(
                                            fontSize: baseTextSize,
                                            fontFamily: 'Inter-Medium',
                                            color: Colors.white,
                                          ),
                                        ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }
}
