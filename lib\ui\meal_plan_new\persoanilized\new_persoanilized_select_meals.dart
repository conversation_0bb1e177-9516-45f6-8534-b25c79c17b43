import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/newmealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/chef/viewchefdetailsmodel.dart';
import 'package:db_eats/data/models/new_meal_plan/newfiltereddishesmodel.dart';
import 'package:db_eats/data/models/new_meal_plan/mealplanprogresslatest.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/meal_plan_new/mealplan_checkout_page.dart';
import 'package:db_eats/ui/meal_plan_new/persoanilized/new_persoanilied_chef.dart';
import 'package:db_eats/ui/meal_plan_new/persoanilized/new_persoanilized_meal_plan_final.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:shimmer/shimmer.dart';

class MealItem {
  final int mealId;
  final String name;
  final String photo;
  final double price;
  final String servingSize;
  final int? dayId;

  MealItem({
    required this.mealId,
    required this.name,
    required this.photo,
    required this.price,
    required this.servingSize,
    this.dayId,
  });

  Map<String, dynamic> toMap() {
    return {
      'meal_id': mealId,
      'name': name,
      'photo': photo,
      'price': price,
      'servings': servingSize.split(' ').first,
    };
  }
}

class NewPersoanilizedSelectMeals extends StatefulWidget {
  final int id;
  final int selectedDay;
  final String selectedDate;
  final int mealPlanId;
  final Map<String, Map<String, dynamic>> mealData;
  final bool isEditing;
  final int? editDayId;
  final String? editDate;
  final Map<String, dynamic>? viewDayData;

  const NewPersoanilizedSelectMeals({
    super.key,
    required this.id,
    required this.selectedDay,
    required this.selectedDate,
    required this.mealPlanId,
    required this.mealData,
    this.isEditing = false,
    this.editDayId,
    this.editDate,
    this.viewDayData,
  });

  @override
  State<NewPersoanilizedSelectMeals> createState() =>
      _NewPersoanilizedSelectMealsState();
}

class _NewPersoanilizedSelectMealsState
    extends State<NewPersoanilizedSelectMeals> with TickerProviderStateMixin {
  ChefDetailsModel? chefDetails;
  late TabController _tabController;
  List<String> _categories = [];
  bool _isLoading = true;
  int _currentDay = 0;
  String _displayStartDate = '';
  List<MealItem> selectedMeals = [];

  late final MealPlanProgressLatestData? _mealPlanData;
  late final int _maxDishesPerDay;

  Color kBlack = const Color(0xFF1F2122);
  Color kSecondBlack = const Color(0xFF414346);
  @override
  void initState() {
    super.initState();

    _mealPlanData = Initializer.mealPlanProgressLatestModel.data;
    _maxDishesPerDay = _mealPlanData?.dishesPerDay ?? 1;

    _currentDay = widget.selectedDay;

    // Initialize categories
    _categories = ['All'];
    _tabController = TabController(length: _categories.length, vsync: this);

    // Override max dishes per day in edit mode if needed
    if (widget.isEditing &&
        widget.viewDayData != null &&
        widget.viewDayData!['items'] != null) {
      final items = widget.viewDayData!['items'] as List?;
      if (items != null && items.isNotEmpty) {
        // In edit mode, allow same number of items as currently selected
        // This allows editing without forcing the exact same count
      }
    }

    // Initialize selected meals from viewDayData if available
    if (widget.viewDayData != null && widget.isEditing) {
      final items = widget.viewDayData!['items'] as List?;
      if (items != null) {
        // Only initialize selected meals if the chef ID matches
        if (widget.viewDayData!['chef']?['id'] == widget.id) {
          selectedMeals = items
              .map((item) => MealItem(
                    mealId: item['chef_menu_item_id'],
                    name: item['menu_item']['name'],
                    photo: item['menu_item']['photo'],
                    price: double.parse(item['price'].toString()),
                    servingSize: '1 Serving',
                    dayId: widget.editDayId,
                  ))
              .toList();
        }
      }
    }

    // Load chef details
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final bloc = context.read<HomeBloc>();
      bloc.add(ViewChefDetailsEvent(
        data: {
          "chef_id": widget.id.toString(),
          "latitude": Initializer.latitude.toString(),
          "longitude": Initializer.longitude.toString(),
        },
      ));
    });
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  void _navigateBack() {
    Navigator.pop(context);
  }

  void _saveMealSelection() {
    if (selectedMeals.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select at least one meal')),
      );
      return;
    }

    // Add validation to ensure user has selected the maximum required dishes
    if (selectedMeals.length < _maxDishesPerDay && _maxDishesPerDay > 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Please select $_maxDishesPerDay dish${_maxDishesPerDay > 1 ? 'es' : ''} to continue'),
        ),
      );
      return;
    }

    _handleContinue();
  }

  void _toggleMealSelection(Map<String, dynamic> dish) {
    // Ensure _maxDishesPerDay is at least 1 to prevent unlimited selection
    final effectiveMaxDishes = _maxDishesPerDay > 0 ? _maxDishesPerDay : 1;

    setState(() {
      final existingIndex =
          selectedMeals.indexWhere((meal) => meal.mealId == dish['id']);

      if (existingIndex != -1) {
        // Always allow unselecting
        selectedMeals.removeAt(existingIndex);
      } else if (selectedMeals.length < effectiveMaxDishes) {
        // Add new meal if under limit
        selectedMeals.add(MealItem(
          mealId: dish['id'],
          name: dish['name'],
          photo: dish['photo'] ?? '',
          price: double.parse(dish['price'].toString()),
          servingSize: dish['serving_size'] ?? '',
          dayId: widget.editDayId,
        ));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'You can only select $effectiveMaxDishes dish${effectiveMaxDishes > 1 ? 'es' : ''} per day. Please unselect a dish first.'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    });
  }

  String _formatDayName() {
    // In edit mode, use the editDate which should contain the day of week
    if (widget.isEditing && widget.editDate != null) {
      // widget.editDate in edit mode contains the day of week like "MONDAY", "TUESDAY", etc.
      final editDayOfWeek = widget.editDate!;
      print("DEBUG: In edit mode, editDate = '$editDayOfWeek'"); // Debug log

      // Convert weekday to display format
      switch (editDayOfWeek.toUpperCase()) {
        case 'MONDAY':
          return 'Monday';
        case 'TUESDAY':
          return 'Tuesday';
        case 'WEDNESDAY':
          return 'Wednesday';
        case 'THURSDAY':
          return 'Thursday';
        case 'FRIDAY':
          return 'Friday';
        case 'SATURDAY':
          return 'Saturday';
        case 'SUNDAY':
          return 'Sunday';
        default:
          return editDayOfWeek; // fallback to original string
      }
    }

    final summaryData = Initializer.mealPlanProgressLatestModel.data;
    final selectedWeekdays = summaryData?.selectedWeekdays ?? [];

    // Get the current weekday name from selected weekdays
    if (selectedWeekdays.isNotEmpty &&
        _currentDay > 0 &&
        _currentDay <= selectedWeekdays.length) {
      final weekday = selectedWeekdays[_currentDay - 1];

      // Convert weekday to display format
      switch (weekday) {
        case 'MONDAY':
          return 'Monday';
        case 'TUESDAY':
          return 'Tuesday';
        case 'WEDNESDAY':
          return 'Wednesday';
        case 'THURSDAY':
          return 'Thursday';
        case 'FRIDAY':
          return 'Friday';
        case 'SATURDAY':
          return 'Saturday';
        case 'SUNDAY':
          return 'Sunday';
        default:
          return weekday;
      }
    }

    // Fallback: derive day name from display date
    if (_displayStartDate.isNotEmpty) {
      try {
        final DateTime dateTime = DateTime.parse(_displayStartDate);
        final days = [
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday',
          'Sunday'
        ];
        return days[dateTime.weekday - 1];
      } catch (e) {
        return "Unknown Day";
      }
    }

    return "Loading...";
  }

  // Remove the old _validateDateRange method as it's no longer needed
  // since we're getting all data from the Initializer

  bool _isDayCompleted(int dayIndex) {
    // Use Initializer.mealPlanProgressLatestModel for completion
    final summaryData = Initializer.mealPlanProgressLatestModel.data;
    final selectedWeekdays = summaryData?.selectedWeekdays ?? [];
    final mealPlanDays = summaryData?.mealPlanDays ?? [];
    if (selectedWeekdays.isEmpty ||
        dayIndex < 0 ||
        dayIndex >= selectedWeekdays.length) return false;
    final weekday = selectedWeekdays[dayIndex];
    return mealPlanDays.any((day) =>
        day.dayOfWeek == weekday && day.items != null && day.items!.isNotEmpty);
  }

  Widget _buildDateSelector() {
    final summaryData = Initializer.mealPlanProgressLatestModel.data;
    final selectedWeekdays = summaryData?.selectedWeekdays ?? [];
    final dayCount = selectedWeekdays.isNotEmpty
        ? selectedWeekdays.length
        : (_mealPlanData?.mealPlanDuration ?? 5);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: sixteen, vertical: twelve),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.calendar_today,
                            size: sixteen, color: kSecondBlack),
                        SizedBox(width: sixteen / 2),
                        Text(
                          _formatDayName(),
                          style: TextStyle(
                            fontSize: forteen,
                            fontWeight: FontWeight.w500,
                            color: kBlack,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: sixteen / 4),
                    // Show time slot from view day response when in edit mode
                    if (widget.isEditing &&
                        widget.viewDayData != null &&
                        widget.viewDayData!['time_slot'] != null)
                      Row(
                        children: [
                          Icon(Icons.access_time,
                              size: sixteen, color: kSecondBlack),
                          SizedBox(width: sixteen / 2),
                          Text(
                            "${_formatTimeToAmPm(widget.viewDayData!['time_slot']['start_time'])} - ${_formatTimeToAmPm(widget.viewDayData!['time_slot']['end_time'])}",
                            style: TextStyle(
                              fontSize: twelve,
                              color: kSecondBlack,
                            ),
                          ),
                        ],
                      )
                    else
                      Row(
                        children: [
                          Icon(Icons.access_time,
                              size: sixteen, color: kSecondBlack),
                          SizedBox(width: sixteen / 2),
                          Text(
                            _mealPlanData?.timeSlot != null
                                ? "${_formatTimeToAmPm(_mealPlanData?.timeSlot?.startTime ?? '')} - ${_formatTimeToAmPm(_mealPlanData?.timeSlot?.endTime ?? '')}"
                                : "12:00PM-1:00PM",
                            style: TextStyle(
                              fontSize: twelve,
                              color: kSecondBlack,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
              // In edit mode, show only the current editing day
              // In non-edit mode, show all days with navigation
              if (widget.isEditing)
                Container(
                  width: ten * 2.2,
                  height: ten * 2.2,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: kSecondBlack,
                    border: Border.all(
                      color: kSecondBlack,
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      '1', // Show as day 1 in edit mode since it's the specific day being edited
                      style: TextStyle(
                        fontSize: forteen,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                )
              else
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(dayCount * 2 - 1, (index) {
                    if (index.isOdd) {
                      return Container(
                        width: sixteen,
                        height: 1,
                        color: const Color(0xFFE1DDD5),
                      );
                    } else {
                      final dayNumber = (index ~/ 2) + 1;
                      final isSelected = dayNumber == _currentDay;
                      final isCompleted = _isDayCompleted(dayNumber - 1);
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _currentDay = dayNumber;
                          });
                          // Get weekday for this day
                          String weekdayName = selectedWeekdays.isNotEmpty &&
                                  dayNumber - 1 < selectedWeekdays.length
                              ? selectedWeekdays[dayNumber - 1]
                              : '';
                          context
                              .read<NewmealplanBloc>()
                              .add(NewFilterDishesEvent({
                                "chef_id": widget.id,
                                "serving_size_id": _mealPlanData?.servingSizeId,
                                "time_slot_id": Initializer
                                    .mealPlanProgressLatestModel
                                    .data
                                    ?.timeSlotId,
                                "start_date": Initializer
                                        .mealPlanProgressLatestModel
                                        .data
                                        ?.startDate ??
                                    '',
                                "end_date": Initializer
                                        .mealPlanProgressLatestModel
                                        .data
                                        ?.endDate ??
                                    '',
                                "weekday": weekdayName,
                              }));
                        },
                        child: Container(
                          width: ten * 2.2,
                          height: ten * 2.2,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isSelected
                                ? kSecondBlack
                                : isCompleted
                                    ? kSecondBlack
                                    : Colors.transparent,
                            border: Border.all(
                              color: isSelected || isCompleted
                                  ? kSecondBlack
                                  : const Color(0xFFE1DDD5),
                              width: 1,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              '$dayNumber',
                              style: TextStyle(
                                fontSize: forteen,
                                fontWeight: FontWeight.w500,
                                color: isSelected || isCompleted
                                    ? Colors.white
                                    : kSecondBlack,
                              ),
                            ),
                          ),
                        ),
                      );
                    }
                  }),
                ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<NewmealplanBloc, NewMealPlanState>(
          listener: (context, state) {
            if (state is NewFilterDishesStateSuccess) {
              final filteredDishesModel = Initializer.newFilteredDishesModel;
              final newCategories = filteredDishesModel.data?.categoryBasedList
                      ?.where((cat) => cat.category?.name != null)
                      .map((cat) => cat.category!.name!)
                      .toList() ??
                  [];
              setState(() {
                _isLoading = false;
                if (_categories.length != newCategories.length) {
                  _tabController.dispose();
                  _tabController =
                      TabController(length: newCategories.length, vsync: this);
                }
                _categories = newCategories;
              });
            } else if (state is NewFilterDishesStateLoadFailed) {
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.error),
                  backgroundColor: const Color(0xFFE11900),
                ),
              );
            } else if (state is NewEditChefStateSuccess) {
              if (widget.isEditing) {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => MealplanCheckoutPage(
                      mealPlanId: widget.mealPlanId,
                    ),
                  ),
                );
              } else {
                bool willAllDaysBeCompleted =
                    _willCurrentSelectionCompleteAllDays();

                if (willAllDaysBeCompleted) {
                  Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (context) => NewPersoanilizedMealPlanFinal(
                        mealPlanId: widget.mealPlanId,
                      ),
                    ),
                    (route) => false,
                  );
                } else {
                  final isLastDay =
                      _currentDay >= (_mealPlanData?.mealPlanDuration ?? 5);
                  if (isLastDay) {
                    Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                        builder: (context) => NewPersoanilizedMealPlanFinal(
                          mealPlanId: widget.mealPlanId,
                        ),
                      ),
                      (route) => false,
                    );
                  } else {
                    Map<String, Map<String, dynamic>> updatedMealData =
                        Map.from(widget.mealData);
                    updatedMealData[_displayStartDate] = {
                      'chef_id': widget.id,
                      'selectedDishes':
                          selectedMeals.map((meal) => meal.toMap()).toList(),
                    };

                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                        builder: (context) => NewPersoaniliedChef(
                          mealPlanId: widget.mealPlanId,
                          currentday: widget.selectedDay + 1,
                          mealdata: updatedMealData,
                        ),
                      ),
                    );
                  }
                }
              }
            } else if (state is NewEditChefStateLoadFailed) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.error),
                  duration: const Duration(seconds: 2),
                ),
              );
            }
          },
        ),
        BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is ChefDetailsSuccess) {
              setState(() {
                chefDetails = state.data;
              });
              if (Initializer.mealPlanProgressLatestModel.data?.timeSlotId ==
                  null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Time slot ID is missing'),
                    backgroundColor: Color(0xFFE11900),
                  ),
                );
                return;
              }

              String weekdayName = '';
              final summaryData = Initializer.mealPlanProgressLatestModel.data;
              final selectedWeekdays = summaryData?.selectedWeekdays ?? [];

              if (selectedWeekdays.isNotEmpty &&
                  widget.selectedDay > 0 &&
                  widget.selectedDay <= selectedWeekdays.length) {
                weekdayName = selectedWeekdays[widget.selectedDay - 1];
              } else {
                try {
                  DateTime selectedDateTime =
                      DateTime.parse(widget.selectedDate);
                  List<String> weekdayNames = [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY'
                  ];
                  weekdayName = weekdayNames[selectedDateTime.weekday - 1];
                } catch (e) {
                  weekdayName = 'MONDAY';
                }
              }

              context.read<NewmealplanBloc>().add(NewFilterDishesEvent({
                    "chef_id": widget.id,
                    "serving_size_id": _mealPlanData?.servingSizeId,
                    "time_slot_id": Initializer
                        .mealPlanProgressLatestModel.data?.timeSlotId,
                    "start_date": Initializer
                            .mealPlanProgressLatestModel.data?.startDate ??
                        '',
                    "end_date":
                        Initializer.mealPlanProgressLatestModel.data?.endDate ??
                            '',
                    "weekday": weekdayName,
                  }));
            }
          },
        ),
        BlocListener<AccountBloc, AccountState>(
          listener: (context, state) {
            if (state is AddFavouriteChefSuccess) {
              if (chefDetails?.data != null) {
                setState(() {
                  chefDetails!.data!.isFavourite = true;
                });
              }
            } else if (state is RemoveFavouriteChefSuccess) {
              if (chefDetails?.data != null) {
                setState(() {
                  chefDetails!.data!.isFavourite = false;
                });
              }
            }
          },
        ),
      ],
      child: BlocBuilder<HomeBloc, HomeState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: const Color(0xFFF6F3EC),
            appBar: AppBar(
              backgroundColor: const Color(0xFFF6F3EC),
              centerTitle: false,
              scrolledUnderElevation: 0,
              titleSpacing: 0,
              automaticallyImplyLeading: false,
              leading: IconButton(
                icon: Image.asset('assets/icons/left_arrow.png',
                    width: twelve, height: twelve),
                onPressed: _navigateBack,
              ),
              elevation: 0,
            ),
            body: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding:
                        EdgeInsets.fromLTRB(sixteen, 0, sixteen, sixteen / 4),
                    child: Text(
                      "Select Meals",
                      style: TextStyle(
                        fontSize: eighteen,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                        color: Color(0xFF1F2122),
                      ),
                    ),
                  ),
                  SizedBox(height: sixteen / 2),
                  _isLoading
                      ? _buildShimmerDateSelector()
                      : _buildDateSelector(),
                  const SizedBox(height: 26),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: sixteen),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: twenty,
                          backgroundImage: NetworkImage(
                            ServerHelper.imageUrl +
                                (chefDetails?.data?.chef?.profilePhoto ?? ''),
                          ),
                        ),
                        SizedBox(width: twelve),
                        Expanded(
                          child: Text(
                            '${chefDetails?.data?.chef?.chef?.firstName ?? ''} ${chefDetails?.data?.chef?.chef?.lastName ?? ''}',
                            style: TextStyle(
                              fontSize: eighteen,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: ten),
                  Padding(
                    padding: EdgeInsets.only(
                        left: forteen,
                        right: forteen,
                        top: sixteen / 2,
                        bottom: 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            // Container(
                            //   padding: EdgeInsets.symmetric(
                            //       horizontal: twelve / 2, vertical: 0),
                            //   constraints: BoxConstraints(
                            //       minWidth: ten * 5, minHeight: ten * 3.4),
                            //   decoration: BoxDecoration(
                            //     color: const Color(0xFFF6F3EC),
                            //     borderRadius: BorderRadius.circular(ten),
                            //     border:
                            //         Border.all(color: const Color(0xFFB9B6AD)),
                            //   ),
                            //   child: Row(
                            //     children: [
                            //       Icon(Icons.access_time,
                            //           size: sixteen, color: Color(0xFF414346)),
                            //       SizedBox(width: sixteen / 4),
                            //       Text(
                            //         "35 mins",
                            //         style: TextStyle(
                            //           fontFamily: 'Inter',
                            //           fontSize: forteen,
                            //           fontWeight: FontWeight.w600,
                            //           height: 24 / 16,
                            //           color: Color(0xFF1F2122),
                            //         ),
                            //       ),
                            //     ],
                            //   ),
                            // ),
                            SizedBox(width: sixteen / 2),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: twelve / 2, vertical: 0),
                              constraints: BoxConstraints(
                                  minWidth: ten * 6.1, minHeight: ten * 3.4),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF6F3EC),
                                borderRadius: BorderRadius.circular(ten),
                                border:
                                    Border.all(color: const Color(0xFFB9B6AD)),
                              ),
                              child: Row(
                                children: [
                                  Icon(Icons.star,
                                      size: sixteen, color: Color(0xFF414346)),
                                  SizedBox(width: sixteen / 4),
                                  Text(
                                    chefDetails?.data?.chef?.averageRating ??
                                        '',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      height: 24 / 16,
                                      color: Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: sixteen / 2),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: twelve / 2, vertical: 0),
                              constraints: BoxConstraints(
                                  minWidth: ten * 8.7, minHeight: ten * 3.4),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF6F3EC),
                                borderRadius: BorderRadius.circular(10),
                                border:
                                    Border.all(color: const Color(0xFFB9B6AD)),
                              ),
                              child: Row(
                                children: [
                                  Icon(Icons.location_on_outlined,
                                      size: ten * 1.7,
                                      color: Color(0xFF414346)),
                                  SizedBox(width: sixteen / 4),
                                  Text(
                                    _formatDistance(
                                        chefDetails?.data?.chef?.distance),
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      height: 24 / 16,
                                      color: Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: sixteen / 2),
                            _buildFavoriteButton(context, screenWidth),
                            // Container(
                            //   width: 34,
                            //   height: 34,
                            //   decoration: BoxDecoration(
                            //     color: Colors.white,
                            //     borderRadius: BorderRadius.circular(10),
                            //     boxShadow: [
                            //       BoxShadow(
                            //         color: Colors.black.withOpacity(0.05),
                            //         blurRadius: sixteen/4,
                            //         offset: const Offset(0, 2),
                            //       ),
                            //     ],
                            //   ),
                            //   child: IconButton(
                            //     icon: Image.asset(
                            //       'assets/icons/favorites.png',
                            //       width: twentyFour
                            //       height: twentyFour
                            //       color: Color(0xFF1F2122),
                            //     ),
                            //     onPressed: () {},
                            //     padding: EdgeInsets.zero,
                            //   ),
                            // ),
                          ],
                        ),
                        SizedBox(height: sixteen),
                        Wrap(
                          alignment: WrapAlignment.start,
                          runSpacing: 2,
                          children: (chefDetails?.data?.chef?.searchTags ?? [])
                              .map((tag) {
                            return Container(
                              padding: EdgeInsets.only(right: twelve),
                              child: Text(
                                tag,
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF414346),
                                  height: 20 / 14,
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                        SizedBox(height: twenty),
                        Text(
                          chefDetails?.data?.chef?.description ?? '',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: forteen,
                            fontWeight: FontWeight.w400,
                            letterSpacing: -0.3,
                            color: Color(0xFF414346),
                          ),
                          textAlign: TextAlign.justify,
                        ),
                        SizedBox(height: twentyFour),
                        SizedBox(
                          height: 30,
                          child: ListView(
                            scrollDirection: Axis.horizontal,
                            padding: const EdgeInsets.symmetric(horizontal: 0),
                            children: _categories.asMap().entries.map((entry) {
                              final index = entry.key;
                              final category = entry.value;
                              final isSelected = _tabController.index == index;
                              return GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _tabController.animateTo(index);
                                  });
                                },
                                child: Container(
                                  margin: EdgeInsets.only(right: sixteen / 2),
                                  padding: EdgeInsets.symmetric(
                                      horizontal: twelve, vertical: 0),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? const Color(0xFFB9B6AD)
                                        : const Color(0xFFE1DDD5),
                                    borderRadius: BorderRadius.circular(twenty),
                                  ),
                                  child: Center(
                                    child: Text(
                                      category,
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w500,
                                        letterSpacing: 0.08,
                                        color: isSelected
                                            ? const Color(0xFF1F2122)
                                            : const Color(0xFF1F2122),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                        SizedBox(height: twenty),
                        // Padding(
                        //   padding: const EdgeInsets.only(
                        //       top: sixteen, bottom: sixteen, right: forteen),
                        //   child: Container(
                        //     height: 46,
                        //     decoration: BoxDecoration(
                        //       borderRadius: BorderRadius.circular(twentyFour),
                        //       border:
                        //           Border.all(color: const Color(0xFF1F2122)),
                        //     ),
                        //     child: InkWell(
                        //       borderRadius: BorderRadius.circular(twentyFour),
                        //       onTap: () {},
                        //       child: Row(
                        //         mainAxisAlignment: MainAxisAlignment.center,
                        //         children: const [
                        //           Icon(Icons.tune,
                        //               size: sixteen, color: Color(0xFF1F2122)),
                        //           SizedBox(width: sixteen/2),
                        //           Text(
                        //             "View Filters",
                        //             style: TextStyle(
                        //               fontSize: twelve,
                        //               fontWeight: FontWeight.w600,
                        //               color: Color(0xFF1F2122),
                        //             ),
                        //           ),
                        //         ],
                        //       ),
                        //     ),
                        //   ),
                        // ),
                        Text(
                          "Featured Items",
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: twenty,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1F2122),
                          ),
                        ),
                        SizedBox(height: twenty),
                        _isLoading
                            ? _buildShimmerCards()
                            : _buildFeaturedItems(),
                        SizedBox(height: eighteen),
                        _isLoading
                            ? _buildShimmerCategoryContent()
                            : _buildCategoryContent(),
                        SizedBox(height: twenty),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            bottomNavigationBar: Container(
              padding: EdgeInsets.all(sixteen),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    offset: const Offset(0, -2),
                    blurRadius: sixteen / 2,
                  ),
                ],
              ),
              child: Row(
                children: [
                  IntrinsicWidth(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${selectedMeals.length}/$_maxDishesPerDay Dishes",
                          style: TextStyle(
                            fontSize: sixteen,
                            fontWeight: FontWeight.w400,
                            color: selectedMeals.length < _maxDishesPerDay
                                ? Colors.red
                                : const Color(0xFF1F2122),
                          ),
                        ),
                        const SizedBox(height: 0),
                        Container(
                          height: 1.5,
                          width: double.infinity, // matches text width
                          color: selectedMeals.length < _maxDishesPerDay
                              ? Colors.red
                              : const Color(0xFF1F2122),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  ElevatedButton(
                    onPressed: selectedMeals.length == _maxDishesPerDay
                        ? _saveMealSelection
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1F2122),
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(
                          horizontal: twentyFour, vertical: sixteen),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28)),
                      disabledBackgroundColor: const Color(0xFFE1DDD5),
                    ),
                    child: Text(
                      widget.isEditing
                          ? 'Save Changes'
                          : _willCurrentSelectionCompleteAllDays()
                              ? 'Continue to Checkout'
                              : _currentDay <
                                      (_mealPlanData?.mealPlanDuration ?? 5)
                                  ? 'Continue to Day ${_currentDay + 1} Meals'
                                  : 'Continue to Checkout',
                      style: TextStyle(
                          fontSize: sixteen, fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _handleContinue() {
    // Check if user has selected the required number of dishes
    if (selectedMeals.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one meal'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    // Add validation to ensure user has selected the maximum required dishes
    if (selectedMeals.length < _maxDishesPerDay && _maxDishesPerDay > 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Please select $_maxDishesPerDay dish${_maxDishesPerDay > 1 ? 'es' : ''} to continue'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    // Get the weekday for the current day selection
    String weekdayName = '';

    // In edit mode, use the editDate which contains the day of week
    if (widget.isEditing && widget.editDate != null) {
      weekdayName = widget.editDate!.toUpperCase();
    } else {
      // Non-edit mode logic
      final summaryData = Initializer.mealPlanProgressLatestModel.data;
      final selectedWeekdays = summaryData?.selectedWeekdays ?? [];

      if (selectedWeekdays.isNotEmpty &&
          _currentDay > 0 &&
          _currentDay <= selectedWeekdays.length) {
        weekdayName = selectedWeekdays[_currentDay - 1];
      } else {
        // Fallback: derive weekday from the display date
        try {
          DateTime selectedDateTime = DateTime.parse(_displayStartDate);
          List<String> weekdayNames = [
            'MONDAY',
            'TUESDAY',
            'WEDNESDAY',
            'THURSDAY',
            'FRIDAY',
            'SATURDAY',
            'SUNDAY'
          ];
          weekdayName = weekdayNames[selectedDateTime.weekday - 1];
        } catch (e) {
          weekdayName = 'MONDAY';
        }
      }
    }

    final requestData = {
      "mealPlanId": widget.mealPlanId,
      // "date": _displayStartDate,
      "weekdays": [weekdayName],
      "chef_id": widget.id,
      "items": selectedMeals
          .map((meal) => {"chef_menu_item_id": meal.mealId})
          .toList()
    };

    // Add day_id when in edit mode
    if (widget.isEditing && widget.editDayId != null) {
      requestData["day_id"] = widget.editDayId!;
    }

    // Add the meal plan and let the BlocListener handle navigation
    context.read<NewmealplanBloc>().add(NewEditChefEvent(requestData));
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Widget _buildShimmerDateSelector() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: sixteen),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          children: [
            Row(
              children: [
                Container(width: sixteen, height: sixteen, color: Colors.white),
                SizedBox(width: sixteen / 2),
                Container(
                  width: ten * 15,
                  height: forteen,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(sixteen / 4)),
                ),
                const Spacer(),
                Row(
                  children: List.generate(
                    5,
                    (index) => Padding(
                      padding: EdgeInsets.symmetric(horizontal: sixteen / 4),
                      child: Container(
                        width: ten * 2.5,
                        height: ten * 2.5,
                        decoration: const BoxDecoration(
                            color: Colors.white, shape: BoxShape.circle),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: sixteen / 4),
            Row(
              children: [
                Container(width: sixteen, height: sixteen, color: Colors.white),
                SizedBox(width: sixteen / 2),
                Container(
                  width: ten * 10,
                  height: forteen,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(sixteen / 4)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedItems() {
    if (Initializer.newFilteredDishesModel.data?.featuredList == null ||
        Initializer.newFilteredDishesModel.data!.featuredList!.isEmpty) {
      return Container(
        height: ten * 10,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(twelve),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.restaurant_menu, size: 32, color: Color(0xFF9E9E9E)),
              SizedBox(height: sixteen / 2),
              Text(
                "No featured items available",
                style: TextStyle(
                    fontSize: sixteen,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF9E9E9E),
                    fontFamily: 'Inter'),
              ),
            ],
          ),
        ),
      );
    }
    return SizedBox(
      height: ten * 19.5, // Reduced from 284
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.only(right: sixteen),
        children: Initializer.newFilteredDishesModel.data!.featuredList!
            .map((dish) => Container(
                  width: ten * 20, // Reduced from ten*22
                  margin: EdgeInsets.only(right: sixteen),
                  child: _buildDishCard({
                    'id': dish.id ?? 0,
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices?.first.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices?.first.servingSize?.title ?? '',
                  }),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategorySection(String category) {
    final categoryList = Initializer
        .newFilteredDishesModel.data?.categoryBasedList
        ?.firstWhere((cat) => cat.category?.name == category,
            orElse: () => CategoryBasedList())
        .dishList;
    if (categoryList?.isEmpty ?? true) {
      return Container(
        height: ten * 10,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(twelve),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.no_meals, size: 32, color: Color(0xFF9E9E9E)),
              SizedBox(height: sixteen / 2),
              Text(
                "No items available in $category",
                style: TextStyle(
                    fontSize: forteen,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF9E9E9E),
                    fontFamily: 'Inter'),
              ),
            ],
          ),
        ),
      );
    }
    return SizedBox(
      height: ten * 19.5, // Reduced from 284
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.only(right: sixteen),
        children: categoryList!
            .map((dish) => Container(
                  width: ten * 20, // Reduced from ten*22
                  margin: EdgeInsets.only(right: sixteen),
                  child: _buildDishCard({
                    'id': dish.id ?? 0,
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices?.first.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices?.first.servingSize?.title ?? '',
                  }),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategoryContent() {
    if (Initializer.newFilteredDishesModel.data?.categoryBasedList == null)
      return const SizedBox();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: Initializer.newFilteredDishesModel.data!.categoryBasedList!
          .map((category) {
        final categoryName = category.category?.name ?? '';
        final dishes = category.dishList ?? [];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding:
                  EdgeInsets.symmetric(horizontal: 0, vertical: sixteen / 2),
              child: Text(
                categoryName,
                style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: twenty,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1F2122)),
              ),
            ),
            SizedBox(height: sixteen / 2),
            dishes.isEmpty
                ? Container(
                    height: ten * 10,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(twelve),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.no_meals,
                              size: ten * 3.2, color: Color(0xFF9E9E9E)),
                          SizedBox(height: sixteen / 2),
                          Text(
                            "No items available in $categoryName",
                            style: TextStyle(
                                fontSize: forteen,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF9E9E9E),
                                fontFamily: 'Inter'),
                          ),
                        ],
                      ),
                    ),
                  )
                : _buildCategorySection(categoryName),
            SizedBox(height: eighteen),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildShimmerCards() {
    return SizedBox(
      height: ten * 28.4,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 3,
        padding: EdgeInsets.only(right: sixteen),
        itemBuilder: (context, index) {
          return Container(
            width: ten * 22,
            margin: EdgeInsets.only(right: sixteen / 2),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(sixteen)),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: ten * 17.4,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(12)),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.all(sixteen),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                            width: ten * 10,
                            height: forteen,
                            color: Colors.white),
                        SizedBox(height: twelve),
                        Container(
                            width: ten * 5, height: ten, color: Colors.white),
                        SizedBox(height: sixteen / 2),
                        Container(
                            width: ten * 5, height: ten, color: Colors.white),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildShimmerCategoryContent() {
    return Column(
      children: List.generate(
        3,
        (index) => Padding(
          padding: EdgeInsets.symmetric(
              horizontal: forteen, vertical: sixteen / 2.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                    width: ten * 15, height: twenty, color: Colors.white),
              ),
              SizedBox(height: twelve),
              _buildShimmerCards(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDishCard(Map<String, dynamic> dish) {
    final isSelected = selectedMeals.any((meal) => meal.mealId == dish['id']);
    final isMaxReached =
        selectedMeals.length >= _maxDishesPerDay && _maxDishesPerDay > 0;
    final isDisabled = isMaxReached && !isSelected;

    return GestureDetector(
        onTap: () => _toggleMealSelection(dish),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(twelve),
            boxShadow: [
              BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: sixteen / 2,
                  offset: const Offset(0, 2)),
            ],
            border: Border.all(
                color:
                    isSelected ? const Color(0xFF1F2122) : Colors.transparent,
                width: 2),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(12)),
                child: Image.network(
                  ServerHelper.imageUrl + (dish['photo'] ?? ''),
                  height: ten * 10,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    height: ten * 10,
                    width: double.infinity,
                    color: Colors.grey[200],
                    child: const Center(child: Text('Image unavailable')),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                    right: twelve,
                    left: twelve,
                    top: sixteen / 2,
                    bottom: 6), // Reduced padding
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      dish['name'],
                      style: TextStyle(
                          fontSize: forteen, // Reduced from forteen
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF1F2122)),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: sixteen / 2), // Reduced from twelve
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Wrap(
                              spacing: 6, // Reduced from sixteen/2
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: sixteen / 4, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFE1E3E6),
                                    borderRadius: BorderRadius.circular(twelve),
                                  ),
                                  child: Text(
                                    "${dish['serving_size'].split(' ')[0]} Servings",
                                    style: const TextStyle(
                                        fontSize: 9, // Reduced from 10
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Inter'),
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: sixteen / 4, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFE1E3E6),
                                    borderRadius: BorderRadius.circular(twelve),
                                  ),
                                  child: Row(
                                    children: [
                                      Image.asset('assets/icons/thump.png',
                                          width: 9, // Reduced from 10
                                          height: 9, // Reduced from 10
                                          color: Colors.black54),
                                      const SizedBox(
                                          width: 3), // Reduced from sixteen/4
                                      const Text("90%",
                                          style: TextStyle(
                                              fontSize: 9, // Reduced from 10
                                              fontWeight: FontWeight.w500,
                                              fontFamily: 'Inter')),
                                      const Text("(50)",
                                          style: TextStyle(
                                              fontSize: 9, // Reduced from 10
                                              fontWeight: FontWeight.w500,
                                              fontFamily: 'Inter')),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(
                                height: sixteen / 2), // Reduced from twelve
                            Text(
                              '\$${double.tryParse(dish['price'] ?? '0.00')?.toStringAsFixed(2) ?? '0.00'}',
                              style: TextStyle(
                                  fontSize: twelve, // Reduced from twelve
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF1F2122)),
                            ),
                          ],
                        ),
                        GestureDetector(
                          onTap: () => _toggleMealSelection(dish),
                          child: Container(
                            width: ten * 2.2, // Reduced from 24
                            height: ten * 2.2, // Reduced from 24
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: isSelected
                                  ? const Color(0xFF1F2122)
                                  : Colors.transparent,
                              border: Border.all(
                                color: isSelected
                                    ? const Color(0xFF1F2122)
                                    : isDisabled
                                        ? const Color(0xFFE1DDD5)
                                        : const Color(0xFF1F2122),
                                width: 1,
                              ),
                            ),
                            child: Center(
                              child: Icon(
                                isSelected ? Icons.check : Icons.add,
                                size: forteen, // Reduced from sixteen
                                color: isSelected
                                    ? Colors.white
                                    : isDisabled
                                        ? const Color(0xFFE1DDD5)
                                        : const Color(0xFF1F2122),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  String _formatTimeToAmPm(String time) {
    if (time.isEmpty) return '';

    // Extract hours and minutes from the time string (format: "HH:MM:SS")
    final parts = time.split(':');
    if (parts.length < 2) return time;

    int hours = int.tryParse(parts[0]) ?? 0;
    final minutes = parts[1];
    final period = hours >= twelve ? 'PM' : 'AM';

    // Convert to twelve-hour format
    hours = hours > 12 ? hours - 12 : (hours == 0 ? 12 : hours);

    return '$hours:$minutes$period';
  }

  bool _willCurrentSelectionCompleteAllDays() {
    // Get data from Initializer
    final summaryData = Initializer.mealPlanProgressLatestModel.data;
    final selectedWeekdays = summaryData?.selectedWeekdays ?? [];
    final mealPlanDays = summaryData?.mealPlanDays ?? [];

    if (selectedWeekdays.isEmpty) return false;

    // Check all days except the current one which we're about to complete
    for (int i = 0; i < selectedWeekdays.length; i++) {
      final weekday = selectedWeekdays[i];

      // If this is the current day we're selecting meals for, consider it completed
      if (i == _currentDay - 1) continue;

      // Check if this weekday is already completed
      final isWeekdayCompleted = mealPlanDays.any((day) =>
          day.dayOfWeek == weekday &&
          day.items != null &&
          day.items!.isNotEmpty);

      if (!isWeekdayCompleted) return false;
    }

    // If we get here, all days except possibly the current one are completed
    // Since we're about to complete the current one, all days will be completed
    return true;
  }

  Widget _buildFavoriteButton(BuildContext context, double screenWidth) {
    return Container(
      width: screenWidth * 0.08,
      height: screenWidth * 0.08,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(screenWidth * 0.02),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: BlocBuilder<AccountBloc, AccountState>(
          builder: (context, state) {
            bool isFavorite = chefDetails?.data?.isFavourite ?? false;

            if (state is AddFavouriteChefLoading ||
                state is RemoveFavouriteChefLoading) {
              return SizedBox(
                width: screenWidth * 0.05,
                height: screenWidth * 0.05,
                child: CupertinoActivityIndicator(
                  radius: screenWidth * 0.010,
                  color: const Color(0xFF1F2122),
                ),
              );
            }

            return Icon(
              isFavorite ? Icons.favorite : Icons.favorite_border,
              color: isFavorite ? Colors.red : const Color(0xFF1F2122),
              size: screenWidth * 0.05,
            );
          },
        ),
        onPressed: () {
          final isFavorite = chefDetails?.data?.isFavourite ?? false;
          final chefId = chefDetails?.data?.chef?.chefId ?? 0;

          if (isFavorite) {
            context.read<AccountBloc>().add(RemoveFavouriteChefEvent(chefId));
          } else {
            context.read<AccountBloc>().add(AddFavouriteChefEvent(chefId));
          }
        },
        padding: EdgeInsets.zero,
        tooltip: chefDetails?.data?.isFavourite ?? false
            ? 'Remove from favorites'
            : 'Add to favorites',
      ),
    );
  }
}

String _formatDistance(num? distance) {
  if (distance == null) return 'Unknown';

  // Convert meters to kilometers
  final kilometers = distance / 1000;

  // Format to one decimal place
  return '${kilometers.toStringAsFixed(1)} km';
}
