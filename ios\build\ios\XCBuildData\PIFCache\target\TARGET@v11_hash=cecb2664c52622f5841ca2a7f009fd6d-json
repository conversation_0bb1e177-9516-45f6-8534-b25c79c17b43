{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989d57d005c8531c1121c1cc62729f1b30", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98be1fb10da6d9a9941f0ccde27cf4602b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b1c913001e713820ed6f947c73fd926", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98328b38e9aa85ed4b4baa958e739619fe", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b1c913001e713820ed6f947c73fd926", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986c5a63d36c78d58b547ef0310133cd74", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9820df5350723d054e6294bf53401a9fcc", "guid": "bfdfe7dc352907fc980b868725387e9851d6c65b218a3985033e2366e1ea1c59", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c3afa80fc1ed90f972100cc470f88a4", "guid": "bfdfe7dc352907fc980b868725387e98c9dce2b4486295c697258819aa1648f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983629f6c7be76f1d4a49fec214284ee44", "guid": "bfdfe7dc352907fc980b868725387e9862a4a413a93ef332a9340a865365f314", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c03f91efee9d6e878af6caefbf16169", "guid": "bfdfe7dc352907fc980b868725387e9852f6f810da25a94e4a2dda7c235febb8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc7529eee8281d4392c8db8d268d4ccc", "guid": "bfdfe7dc352907fc980b868725387e98fb86c3bbd8aaafd76811e18bddd08db6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815f80edab65164cf48866b3a80636acf", "guid": "bfdfe7dc352907fc980b868725387e98d527656d9e42d38ab394e76c80c02a61", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b863944304edcc56a6b738e336b3f6f2", "guid": "bfdfe7dc352907fc980b868725387e984489dd20016afef4a9564da685132489", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a89421a577365e5de4acec2029f2ca7b", "guid": "bfdfe7dc352907fc980b868725387e984b31b6f36c5016861fd986fc703a63ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98725428406de73808e7c9bdc7f13c2113", "guid": "bfdfe7dc352907fc980b868725387e985a8cbb180186296b3f65997244995d90", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985e2f889ac6b246f49c6cf0e7399ccce0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f350c2d64d0af6d98ce8b5b9bc110458", "guid": "bfdfe7dc352907fc980b868725387e983104d4eeeffc86ccc0b125bc9a43a9e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fde32525d35ebf13acb5b16c5883525", "guid": "bfdfe7dc352907fc980b868725387e9851c9b125f43441cdba59052d53f10517"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816f51552bdd925077281706e9673bdac", "guid": "bfdfe7dc352907fc980b868725387e98f8f68301b987820ab5258c5bfcb90f2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985667843c516b465ba1a043af68138796", "guid": "bfdfe7dc352907fc980b868725387e98c786effd3277dbf7d994c2f65c85363e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876c505661d3eeb868fe92df5256f22f3", "guid": "bfdfe7dc352907fc980b868725387e98233fa4ec24991dcadac3f3ab0dc027b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a788f607e31baf2eca919be3264c1518", "guid": "bfdfe7dc352907fc980b868725387e9878d17278d2bb0ebeed0f025c620754ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4ce15e9ea05708a4343b881f0a88180", "guid": "bfdfe7dc352907fc980b868725387e981f095fc9dd0bce2458b5cab9f559aa60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d217e6f64660eef7d45f5c6764be1d29", "guid": "bfdfe7dc352907fc980b868725387e9837efa19d44b313a224dc79b6aa7a2fe4"}], "guid": "bfdfe7dc352907fc980b868725387e981fb1f2ebd7558dfafa9574be5dd7324c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e9876b2281418757e472013489b9c1c6e8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e9816a9b23c3abba8fe5bd0e0aad0896356"}], "guid": "bfdfe7dc352907fc980b868725387e98af6f0c60f47f7fcc183347de23df69b4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984e0c138d7c777327669fce5e861f0b8d", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e989978420a644b5b2d3915fd115801bc2a", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98fb0be58402213050e88cce905c19ccdf", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}