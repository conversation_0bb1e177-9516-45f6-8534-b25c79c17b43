import 'package:db_eats/bloc/promocode_blok.dart';
import 'package:db_eats/data/models/promocode/availablecouponmodel.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ApplyCouponPage extends StatefulWidget {
  final int chefid;

  const ApplyCouponPage({super.key, required this.chefid});

  @override
  _ApplyCouponPageState createState() => _ApplyCouponPageState();
}

class _ApplyCouponPageState extends State<ApplyCouponPage> {
  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;

  final TextEditingController _couponController = TextEditingController();

  // Track loading state for the apply button
  bool _isApplyButtonLoading = false;

  @override
  void initState() {
    super.initState();
    // Call the EligiblePromocodeEvent with chefid in data
    context.read<PromocodeBloc>().add(
          EligiblePromocodeEvent({
            "chef_id": widget.chefid,
          }),
        );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;
    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  void _showAppliedToast(String couponCode) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Applied $couponCode'),
        duration: Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorToast(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.red,
      ),
    );
  }

  void _applyFromTextField() {
    if (_couponController.text.isNotEmpty) {
      // Trigger the CheckPromocodeEvent
      context.read<PromocodeBloc>().add(
            CheckPromocodeEvent({
              "chef_id": widget.chefid,
              "coupon_code": _couponController.text.trim(),
            }),
          );
    }
  }

  void _applyFromAvailableCoupons(String couponCode) {
    _showAppliedToast(couponCode);
    // Pop back to previous page with the selected coupon
    Navigator.pop(context, couponCode);
  }

  Widget _buildLabel(String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: ten, left: 0),
      child: Text(
        text,
        style: TextStyle(
          fontFamily: 'Inter',
          fontWeight: FontWeight.w500,
          fontSize: forteen,
          height: 16 / forteen,
          letterSpacing: 0.02,
        ),
      ),
    );
  }

  Widget _buildTextField(String hintText,
      {TextEditingController? controller,
      int maxLines = 1,
      bool isPhoneNumber = false}) {
    return Container(
      margin: EdgeInsets.only(bottom: eighteen),
      padding: EdgeInsets.symmetric(horizontal: eighteen),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE1E3E6)),
        borderRadius: BorderRadius.circular(maxLines > 2 ? 10 : 35),
      ),
      child: Row(
        children: [
          Expanded(
            child: SizedBox(
              height: maxLines == 1 ? 40 : null,
              child: TextField(
                controller: controller,
                maxLines: maxLines,
                keyboardType:
                    isPhoneNumber ? TextInputType.number : TextInputType.text,
                inputFormatters: isPhoneNumber
                    ? [FilteringTextInputFormatter.digitsOnly]
                    : null,
                style: TextStyle(
                  fontSize: sixteen,
                  height: 1.3,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  color: Color(0xff66696D),
                ),
                decoration: InputDecoration(
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(vertical: ten),
                  hintText: hintText,
                  border: InputBorder.none,
                  hintStyle: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: sixteen,
                    height: 1.3,
                    color: Color(0xFFAAADB1),
                  ),
                ),
              ),
            ),
          ),
          GestureDetector(
            onTap: _isApplyButtonLoading ? null : _applyFromTextField,
            child: IntrinsicWidth(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  _isApplyButtonLoading
                      ? SizedBox(
                          width: 12,
                          height: 12,
                          child: CircularProgressIndicator(
                            strokeWidth: 1.5,
                            color: Color(0xff414346),
                          ),
                        )
                      : Text(
                          'Apply',
                          style: TextStyle(
                            fontFamily: 'Suisse Intl',
                            fontWeight: FontWeight.w400,
                            fontSize: twelve,
                            height: 12 / twelve,
                            letterSpacing: 0.02,
                            decoration: TextDecoration.none,
                            color: Color(0xff414346),
                          ),
                        ),
                  SizedBox(height: 2),
                  if (!_isApplyButtonLoading)
                    Container(
                      height: 1.2,
                      color: Color(0xff414346),
                    ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildCouponContainer(Data coupon) {
    return Container(
      margin: EdgeInsets.only(bottom: sixteen / 2),
      padding: EdgeInsets.all(sixteen),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Color(0xffB9B6AD)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              DottedBorder(
                borderType: BorderType.RRect,
                radius: Radius.circular(4),
                strokeWidth: 1,
                dashPattern: [4, 2],
                color: Color(0xffD2D4D7),
                padding: EdgeInsets.zero,
                child: Container(
                  color: Color(0xfffffef3),
                  padding: EdgeInsets.symmetric(
                      horizontal: ten / 2, vertical: ten / 5),
                  child: Text(
                    coupon.couponCode ?? '',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: forteen,
                      height: 20 / forteen,
                      letterSpacing: 0,
                      color: Color(0xff1F2122),
                    ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () =>
                    _applyFromAvailableCoupons(coupon.couponCode ?? ''),
                child: IntrinsicWidth(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        'Apply',
                        style: TextStyle(
                          fontFamily: 'Suisse Intl',
                          fontWeight: FontWeight.w400,
                          fontSize: twelve,
                          height: 12 / twelve,
                          letterSpacing: 0.02,
                          decoration: TextDecoration.none,
                          color: Color(0xff414346),
                        ),
                      ),
                      SizedBox(height: 2),
                      Container(
                        height: 1,
                        color: Color(0xff414346),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
          SizedBox(height: ten),
          Text(
            coupon.discountName ?? '',
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              fontSize: twelve,
              height: 16 / twelve,
              letterSpacing: 0,
              color: Color(0xff1F2122),
            ),
          ),
          SizedBox(height: sixteen / 2),
          Text(
            coupon.description ?? '',
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              fontSize: twelve,
              height: 16 / twelve,
              letterSpacing: 0,
              color: Color(0xff1F2122),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: CircularProgressIndicator(
        color: Color(0xff414346),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Color(0xff414346),
          ),
          SizedBox(height: sixteen),
          Text(
            'Failed to get coupons',
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w500,
              fontSize: sixteen,
              color: Color(0xff414346),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessState(List<Data> coupons) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLabel('Enter coupon'),
        _buildTextField(
          'Enter coupon',
          controller: _couponController,
        ),
        Container(
          height: 1,
          color: Color(0xffE1E3E6),
          margin: EdgeInsets.only(bottom: twenty),
        ),
        Text(
          'Available Coupons',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: sixteen,
            height: 16 / sixteen,
            letterSpacing: 0.02,
            color: Color(0xff1F2122),
          ),
        ),
        SizedBox(height: 35),
        coupons.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.local_offer_outlined,
                      size: 48,
                      color: Color(0xff414346),
                    ),
                    SizedBox(height: sixteen),
                    Text(
                      'No coupons available',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: sixteen,
                        color: Color(0xff414346),
                      ),
                    ),
                  ],
                ),
              )
            : ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: coupons.length,
                itemBuilder: (context, index) {
                  return _buildCouponContainer(coupons[index]);
                },
              ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xfff6f3ec),
      appBar: AppBar(
        backgroundColor: Color(0xfff6f3ec),
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(0xff1F2122)),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Apply Coupon',
          style: TextStyle(
            fontFamily: 'Suisse Intl',
            fontWeight: FontWeight.w600,
            fontSize: eighteen,
            height: 1.28,
            letterSpacing: 0,
            color: Color(0xff1F2122),
          ),
        ),
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        child: Container(
          width: double.infinity,
          height: MediaQuery.of(context).size.height -
              AppBar().preferredSize.height -
              MediaQuery.of(context).padding.top -
              32, // Adjust for margins
          margin:
              EdgeInsets.only(bottom: sixteen, left: sixteen, right: sixteen),
          padding: EdgeInsets.all(sixteen),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: BlocConsumer<PromocodeBloc, PromocodeState>(
            listener: (context, state) {
              // Handle CheckPromocode states
              if (state is CheckPromoCodeLoading) {
                setState(() {
                  _isApplyButtonLoading = true;
                });
              } else if (state is CheckPromoCodeSuccess) {
                setState(() {
                  _isApplyButtonLoading = false;
                });
                // Show success toast and pop with coupon code
                _showAppliedToast(_couponController.text);
                Navigator.pop(context, _couponController.text);
              } else if (state is CheckPromoCodeFailed) {
                setState(() {
                  _isApplyButtonLoading = false;
                });
                // Show error toast with the message from state
                _showErrorToast(state.message ?? 'Invalid coupon code');
              }
            },
            buildWhen: (previous, current) {
              // Only rebuild for EligiblePromoCode states, not CheckPromoCode states
              return current is EligiblePromoCodeLoading ||
                  current is EligiblePromoCodeSuccess ||
                  current is EligiblePromoCodeFailed;
            },
            builder: (context, state) {
              if (state is EligiblePromoCodeLoading) {
                return _buildLoadingState();
              } else if (state is EligiblePromoCodeFailed) {
                return _buildErrorState();
              } else if (state is EligiblePromoCodeSuccess) {
                return _buildSuccessState(state.data ?? []);
              }
              // Default state
              return _buildLoadingState();
            },
          ),
        ),
      ),
    );
  }
}
