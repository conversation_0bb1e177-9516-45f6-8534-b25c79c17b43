import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/bloc/newmealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/guesthome/listaddressmodel.dart';
import 'package:db_eats/data/models/meal_plan/deliverytimemodel.dart';
import 'package:db_eats/data/models/meal_plan/dropoffoptionmodel.dart';
import 'package:db_eats/data/models/new_meal_plan/mealplanprogresslatest.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/cart/select_address.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:db_eats/ui/meal_plan_new/curated/curated_chef_lists.dart';
import 'package:db_eats/ui/meal_plan_new/persoanilized/new_persoanilied_chef.dart';
import 'package:db_eats/ui/paymentgateway/paymentwebview.dart';
import 'package:db_eats/utils/customtoggle.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';

class MealplanCheckoutPage extends StatefulWidget {
  final int mealPlanId;
  final List<Map<String, dynamic>>? selectedChefsWithDetails;

  const MealplanCheckoutPage({
    super.key,
    required this.mealPlanId,
    this.selectedChefsWithDetails,
  });

  @override
  State<MealplanCheckoutPage> createState() => _MealplanCheckoutPageState();
}

class _MealplanCheckoutPageState extends State<MealplanCheckoutPage> {
  String? dropOffOption;
  String? instructions;
  String? selectedDeliveryOption;
  bool _useWalletCredits = false;
  MealPlanProgressLatestData? mealPlanData;
  List<DropoffOptionItem> _dropoffOptions = [];
  List<DeliveryTimeItem> _deliveryTimeOptions = [];
  DropoffOptionItem? _selectedDropoffOption;
  DeliveryTimeItem? _selectedDeliveryTime;
  String? _dropOffInstructions;
  bool _showDropoffError = false;
  bool _showDeliveryTimeError = false;
  bool _isPlacingOrder = false;
  late TextEditingController _instructionsController;
  AddressData? currentAddressData;
  String? currentAddress;
  DateTime selectedDate = DateTime.now();
  String selectedTime = '';
  int _closePopupCount = 0;

  Map<String, dynamic>? appliedCoupon;

  String _getCurrentAddress(List<AddressData>? addresses) {
    if (addresses == null || addresses.isEmpty) return 'No address';
    final address = addresses.firstWhere(
      (address) => address.isCurrent == true,
      orElse: () => addresses.first,
    );
    currentAddressData = address;
    return address.addressText ?? 'No address';
  }

  @override
  void initState() {
    super.initState();
    _instructionsController = TextEditingController();
    context.read<MealplanBloc>().add(ListDropoffOptionEvent());
    context.read<MealplanBloc>().add(ListDeliveryTimeEvent());
    context.read<NewmealplanBloc>().add(ListNewMealPlanSummaryEvent(
          data: {"meal_plan_id": widget.mealPlanId, 'is_summary': true},
        ));
    context.read<AccountBloc>().add(ListAddressesEvent());
  }

  bool value = false;
  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final screenWidth = MediaQuery.of(context).size.width;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  void dispose() {
    _instructionsController.dispose();
    super.dispose();
  }

  String _formatTime(String? time) {
    if (time == null) return '00:00 AM';
    final parts = time.split(':');
    if (parts.length < 2) return '00:00 AM';
    int hour = int.tryParse(parts[0]) ?? 0;
    String minute = parts[1];
    String period = hour >= 12 ? 'PM' : 'AM';
    if (hour > 12) hour -= 12;
    if (hour == 0) hour = 12;
    return '$hour:$minute $period';
  }

  bool _isValidPaymentUrl(String url) {
    if (url.isEmpty) return false;
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  void _saveDeliveryDetails() {
    if (_selectedDropoffOption == null) {
      setState(() => _showDropoffError = true);
      return;
    }
    if (_selectedDeliveryTime == null) {
      setState(() => _showDeliveryTimeError = true);
      return;
    }
    final mealPlanId = mealPlanData?.id;
    if (mealPlanId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Missing meal plan ID'),
          backgroundColor: Color(0xFFE11900),
        ),
      );
      return;
    }

    final orderData = <String, dynamic>{
      "meal_plan_id": mealPlanId,
      "drop_off_option_id": _selectedDropoffOption!.id,
      "delivery_time_id": _selectedDeliveryTime!.id,
      "address_id": currentAddressData!.id,
    };

    if (_dropOffInstructions != null && _dropOffInstructions!.isNotEmpty) {
      orderData["drop_off_instructions"] = _dropOffInstructions;
    }

    if (_useWalletCredits) {
      orderData["use_wallet_credits"] = true;
    } else {
      orderData["use_wallet_credits"] = false;
    }

    context.read<NewmealplanBloc>().add(NewCheckoutMealPlanEvent(orderData));
  }

  Widget _buildDropoffOptionDropdown(Size size) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Drop-Off Options',
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: forteen,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF1F2122),
          ),
        ),
        SizedBox(height: size.height * 0.015),
        Container(
          height: size.height * 0.05,
          padding: EdgeInsets.symmetric(horizontal: size.width * 0.045),
          decoration: BoxDecoration(
            border: Border.all(
              color: _showDropoffError ? Colors.red : const Color(0xFFE1E3E6),
            ),
            borderRadius: BorderRadius.circular(size.width * 0.0875),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<DropoffOptionItem>(
              value: _selectedDropoffOption,
              isExpanded: true,
              hint: Text(
                'Select drop-off option',
                style: TextStyle(
                  color:
                      _showDropoffError ? Colors.red : const Color(0xFF66696D),
                  fontSize: sixteen,
                ),
              ),
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: const Color(0xFF1F2122),
                size: size.width * 0.06,
              ),
              items: _dropoffOptions
                  .map<DropdownMenuItem<DropoffOptionItem>>((item) {
                return DropdownMenuItem<DropoffOptionItem>(
                  value: item,
                  child: Text(
                    item.name ?? '',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: size.width * 0.04,
                      color: const Color(0xFF66696D),
                    ),
                  ),
                );
              }).toList(),
              onChanged: (DropoffOptionItem? newValue) {
                setState(() {
                  _selectedDropoffOption = newValue;
                  _showDropoffError = false;
                });
              },
            ),
          ),
        ),
        if (_showDropoffError)
          Padding(
            padding: EdgeInsets.only(top: size.height * 0.01),
            child: Text(
              'Please select a drop-off option',
              style: TextStyle(
                color: Colors.red,
                fontSize: size.width * 0.03,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDeliveryTimeOptions(Size size) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Delivery Time',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: sixteen,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF1F2122),
              ),
            ),
            Text(
              '45–50 Minutes',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: sixteen,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF1F2122),
              ),
            ),
          ],
        ),
        SizedBox(height: size.height * 0.015),
        ..._deliveryTimeOptions.map((option) => Padding(
              padding: EdgeInsets.only(bottom: size.height * 0.01),
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: _selectedDeliveryTime?.id == option.id
                        ? const Color(0xFF1F2122)
                        : _showDeliveryTimeError
                            ? Colors.red
                            : const Color(0xFFE1E3E6),
                  ),
                  color: _selectedDeliveryTime?.id == option.id
                      ? const Color(0xFFE1DDD5)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(size.width * 0.02),
                ),
                child: RadioListTile<DeliveryTimeItem>(
                  value: option,
                  groupValue: _selectedDeliveryTime,
                  onChanged: (DeliveryTimeItem? value) {
                    setState(() {
                      _selectedDeliveryTime = value;
                      _showDeliveryTimeError = false;
                    });
                    context
                        .read<NewmealplanBloc>()
                        .add(ListNewMealPlanSummaryEvent(data: {
                          "meal_plan_id": widget.mealPlanId,
                          "delivery_time_id": value?.id,
                          "is_summary": true,
                        }));
                  },
                  title: Text(
                    option.name ?? '',
                    style: TextStyle(
                        fontSize: forteen, fontWeight: FontWeight.w600),
                  ),
                  subtitle: Text(
                    option.description ?? '',
                    style: TextStyle(
                        fontSize: twelve, fontWeight: FontWeight.w400),
                  ),
                  secondary: Text(
                    '+\$${option.cost ?? '0.00'}',
                    style: TextStyle(
                        fontSize: forteen, fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            )),
        if (_showDeliveryTimeError)
          Padding(
            padding: EdgeInsets.only(top: size.height * 0.01),
            child: Text(
              'Please select a delivery time',
              style: TextStyle(
                color: Colors.red,
                fontSize: size.width * 0.03,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPlaceOrderButton(Size size) {
    final bool hasError = mealPlanData?.errorMessage != null &&
        mealPlanData!.errorMessage!.isNotEmpty;
    return SizedBox(
      width: double.infinity,
      height: twentyFour + twenty,
      child: ElevatedButton(
        onPressed: (_isPlacingOrder || hasError) ? null : _saveDeliveryDetails,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1F2122),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(size.width * 0.07),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: _isPlacingOrder ? size.width * 0.08 : size.width * 0.05,
            vertical: size.height * 0.01,
          ),
        ),
        child: _isPlacingOrder
            ? SizedBox(
                width: size.width * 0.06,
                height: size.width * 0.06,
                child: const CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                'Place Order',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  fontSize: sixteen,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  void _handleEditDay(int dayId, String dayOfWeek, {int? chefId}) {
    context.read<MealplanBloc>().add(ViewDayEvent(dayId));

    final mealSelectionType = mealPlanData?.mealSelectionType;

    if (mealSelectionType == "CURATED") {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CuratedChefLists(
            mealPlanId: widget.mealPlanId,
            dayId: dayId,
            date: dayOfWeek,
            isEditing: true,
          ),
        ),
      );
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => NewPersoaniliedChef(
            mealPlanId: widget.mealPlanId,
            currentday: dayId,
            isEditing: true,
            editDayId: dayId,
            editDate: dayOfWeek,
            selectedChefId: chefId,
          ),
        ),
      );
    }
  }

  void _showPaymentSuccessDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        final size = MediaQuery.of(dialogContext).size;
        final isTablet = size.width >= 600;

        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: isTablet ? size.width * 0.6 : size.width * 0.9,
            constraints: BoxConstraints(
              maxHeight: size.height * 0.7,
              maxWidth: isTablet ? 500 : size.width * 0.9,
            ),
            padding: EdgeInsets.all(size.width * 0.04),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  alignment: Alignment.center,
                  width: size.width * 0.5,
                  height: size.width * 0.5,
                  constraints: const BoxConstraints(
                    maxWidth: 200,
                    maxHeight: 200,
                  ),
                  child: Lottie.asset(
                    'assets/success.json',
                    fit: BoxFit.contain,
                    repeat: false,
                    animate: true,
                  ),
                ),
                SizedBox(height: size.height * 0.02),
                Text(
                  'Meal Plan Order Placed Successfully!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isTablet ? 22 : 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1F2122),
                  ),
                ),
                SizedBox(height: size.height * 0.015),
                Text(
                  'Your meal plan order has been placed successfully. We\'re getting your meals ready!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isTablet ? 16 : 14,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFF66696D),
                    height: 1.4,
                  ),
                ),
                SizedBox(height: size.height * 0.03),
                SizedBox(
                  width: double.infinity,
                  height: size.height * 0.06,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(dialogContext).pop();
                      Navigator.pushAndRemoveUntil(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const MainNavigationScreen(),
                        ),
                        (route) => false,
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1F2122),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Continue',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: isTablet ? 18 : 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showPaymentFailedDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        final size = MediaQuery.of(dialogContext).size;
        final isTablet = size.width >= 600;

        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: isTablet ? size.width * 0.6 : size.width * 0.9,
            constraints: BoxConstraints(
              maxHeight: size.height * 0.7,
              maxWidth: isTablet ? 500 : size.width * 0.9,
            ),
            padding: EdgeInsets.all(size.width * 0.04),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  alignment: Alignment.center,
                  width: size.width * 0.5,
                  height: size.width * 0.5,
                  constraints: const BoxConstraints(
                    maxWidth: 200,
                    maxHeight: 200,
                  ),
                  child: Lottie.asset(
                    'assets/failed.json',
                    fit: BoxFit.contain,
                    repeat: false,
                    animate: true,
                  ),
                ),
                SizedBox(height: size.height * 0.02),
                Text(
                  'Payment Failed!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isTablet ? 22 : 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1F2122),
                  ),
                ),
                SizedBox(height: size.height * 0.015),
                Text(
                  'Your payment could not be processed. Please try again or use a different payment method.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isTablet ? 16 : 14,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFF66696D),
                    height: 1.4,
                  ),
                ),
                SizedBox(height: size.height * 0.03),
                Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: size.height * 0.06,
                        child: OutlinedButton(
                          onPressed: () {
                            Navigator.of(dialogContext).pop();
                          },
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(
                              color: Color(0xFF1F2122),
                              width: 1.5,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            foregroundColor: const Color(0xFF1F2122),
                          ),
                          child: Text(
                            'Cancel',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: isTablet ? 18 : 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: size.width * 0.03),
                    Expanded(
                      child: SizedBox(
                        height: size.height * 0.06,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(dialogContext).pop();
                            _saveDeliveryDetails();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF1F2122),
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            'Try Again',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: isTablet ? 18 : 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;
    return MultiBlocListener(
      listeners: [
        BlocListener<AccountBloc, AccountState>(
          listener: (context, state) {
            if (state is ListAddressesSuccess) {
              setState(() {
                currentAddress = _getCurrentAddress(state.data);
              });
            }
          },
        ),
        BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is ListDropoffOptionSuccess) {
              final dropoffData = state.data as DropoffOptionsModel;
              setState(() {
                _dropoffOptions = dropoffData.data?.data ?? [];
              });
            } else if (state is ListDeliveryTimeSuccess) {
              final deliveryTimeData = state.data as DeliveryTimeModel;
              setState(() {
                _deliveryTimeOptions = deliveryTimeData.data?.data ?? [];
              });
            }
          },
        ),
        BlocListener<NewmealplanBloc, NewMealPlanState>(
          listener: (context, state) {
            if (state is NewMealPlanSummaryLoaded) {
              setState(() {
                mealPlanData = Initializer.mealPlanProgressLatestModel.data;
                _dropOffInstructions = _instructionsController.text;
              });
            } else if (state is NewMealPlanSummaryLoadFailed) {
              _showSummaryFailedDialog(state.error);
            }
            // else if (state is NewMealPlanSummaryLoadFailed) {
            //   ScaffoldMessenger.of(context).showSnackBar(
            //     SnackBar(
            //       content: Text(state.error),
            //       backgroundColor: const Color(0xFFE11900),
            //     ),
            //   );
            // }
            else if (state is NewCheckoutMealPlanStateLoading) {
              setState(() {
                _isPlacingOrder = true;
              });
            } else if (state is NewCheckoutMealPlanStateSuccess) {
              setState(() {
                _isPlacingOrder = false;
              });

              if (state.total > 0) {
                final paymentUrl = state.paymentUrl ?? '';

                if (paymentUrl.isEmpty || !_isValidPaymentUrl(paymentUrl)) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content:
                          Text('Payment URL not available. Please try again.'),
                      backgroundColor: Color(0xFFE11900),
                    ),
                  );
                  return;
                }

                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PaymentWebView(
                      paymentUrl: paymentUrl,
                      onPaymentComplete: () {
                        _showPaymentSuccessDialog();
                      },
                      onPaymentCancelled: () {
                        _showPaymentFailedDialog();
                      },
                    ),
                  ),
                );
              } else {
                _showPaymentSuccessDialog();
              }
            } else if (state is NewCheckoutMealPlanStateLoadFailed) {
              setState(() {
                _isPlacingOrder = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.error),
                  backgroundColor: const Color(0xFFE11900),
                ),
              );
            }
          },
        ),
      ],
      child: BlocBuilder<MealplanBloc, MealPlanState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: const Color(0xFFF6F3EC),
            appBar: AppBar(
              backgroundColor: const Color(0xFFF6F3EC),
              scrolledUnderElevation: 0,
              surfaceTintColor: Colors.transparent,
              elevation: 0,
              automaticallyImplyLeading: false,
              centerTitle: false,
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: const Color(0xFF1F2122),
                  size: size.width * 0.06,
                ),
                onPressed: () => Navigator.of(context).pop(),
              ),
              title: Text(
                'Checkout',
                style: TextStyle(
                  color: const Color(0xFF1F2122),
                  fontSize: isTablet ? size.width * 0.045 : size.width * 0.05,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Inter',
                ),
              ),
            ),
            body: SingleChildScrollView(
              padding: EdgeInsets.only(
                  top: ten, left: sixteen, right: sixteen, bottom: sixteen),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(size.width * 0.025),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: size.width * 0.01,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    padding: EdgeInsets.all(size.width * 0.04),
                    child:
                        //  state is MealPlanProgressLoading
                        //     ? Column(
                        //         crossAxisAlignment: CrossAxisAlignment.start,
                        //         children: List.generate(
                        //           5,
                        //           (index) => Padding(
                        //             padding: EdgeInsets.symmetric(
                        //                 vertical: size.height * 0.01),
                        //             child: _buildLoadingShimmer(size),
                        //           ),
                        //         ),
                        //       )
                        //     :
                        Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Delivery Details',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: twenty,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                        SizedBox(height: size.height * 0.02),
                        Text(
                          'Address',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: forteen,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                        SizedBox(height: size.height * 0.01875),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.location_on_outlined,
                                    size: sixteen,
                                    color: const Color(0xFF414346),
                                  ),
                                  SizedBox(width: size.width * 0.02),
                                  Expanded(
                                    child: Text(
                                      currentAddress ?? 'No address',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w500,
                                        color: const Color(0xFF1F2122),
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            TextButton(
                                onPressed: () async {
                                  final selectedAddress =
                                      await Navigator.push<AddressData>(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => SelectAddressPage(
                                        currentAddress: currentAddressData,
                                      ),
                                    ),
                                  );

                                  if (selectedAddress != null) {
                                    setState(() {
                                      currentAddressData = selectedAddress;
                                      currentAddress =
                                          selectedAddress.addressText ??
                                              'No address';
                                    });

                                    context
                                        .read<NewmealplanBloc>()
                                        .add(ListNewMealPlanSummaryEvent(data: {
                                          "meal_plan_id": widget.mealPlanId,
                                          "delivery_time_id":
                                              _selectedDeliveryTime?.id,
                                          "address_id": selectedAddress.id,
                                          "is_summary": true,
                                        }));
                                  }
                                },
                                style: TextButton.styleFrom(
                                  padding: EdgeInsets.zero,
                                  minimumSize: Size(
                                      size.width * 0.075, size.height * 0.025),
                                  tapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                ),
                                child: IntrinsicWidth(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        'Change',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontWeight: FontWeight.w600,
                                          fontSize: twelve,
                                          height: 1.0,
                                          color: const Color(0xFF1F2122),
                                          letterSpacing: 0.5,
                                          decoration: TextDecoration.none,
                                        ),
                                      ),
                                      SizedBox(height: 1),
                                      Container(
                                        height: 1,
                                        width: double.infinity,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ],
                                  ),
                                )),
                          ],
                        ),
                        // SizedBox(height: size.height * 0.02),
                        // Text(
                        //   'Delivery',
                        //   style: TextStyle(
                        //     fontFamily: 'Inter',
                        //     fontSize: forteen,
                        //     fontWeight: FontWeight.w500,
                        //     color: const Color(0xFF1F2122),
                        //   ),
                        // ),
                        // SizedBox(height: size.height * 0.01875),
                        // Row(
                        //   children: [
                        //     Icon(
                        //       Icons.access_time_rounded,
                        //       size: forteen,
                        //       color: const Color(0xFF1F2122),
                        //     ),
                        //     SizedBox(width: size.width * 0.02),
                        //     _buildDeliveryTimeText(size),
                        //   ],
                        // ),
                        SizedBox(height: size.height * 0.01),
                        Divider(
                          color: const Color(0xFFE1E3E6),
                          thickness: 1,
                        ),
                        SizedBox(height: size.height * 0.01),
                        _buildDropoffOptionDropdown(size),
                        SizedBox(height: size.height * 0.02),
                        Text(
                          'Drop-Off Instructions',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: forteen,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                        SizedBox(height: size.height * 0.01),
                        TextField(
                          controller: _instructionsController,
                          onChanged: (value) {
                            _dropOffInstructions = value;
                          },
                          textAlign: TextAlign.left,
                          decoration: InputDecoration(
                            contentPadding: EdgeInsets.all(size.width * 0.03),
                            hintText:
                                'Example: Doorbell is broken, please knock',
                            hintStyle: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w400,
                              fontSize: sixteen,
                              color: const Color(0xFF66696D),
                            ),
                            border: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.circular(size.width * 0.02),
                              borderSide:
                                  const BorderSide(color: Color(0xFFE1E3E6)),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.circular(size.width * 0.02),
                              borderSide:
                                  const BorderSide(color: Color(0xFFE1E3E6)),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.circular(size.width * 0.02),
                              borderSide: const BorderSide(
                                  color: Color(0xFF1F2122), width: 1.5),
                            ),
                          ),
                          minLines: 5,
                          maxLines: 8,
                          cursorColor: const Color(0xFF1F2122),
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            fontSize: size.width * 0.04,
                            color: const Color(0xFF66696D),
                          ),
                        ),
                        SizedBox(height: size.height * 0.02),
                        Divider(
                          color: const Color(0xFFE1E3E6),
                          thickness: 1,
                        ),
                        SizedBox(height: size.height * 0.02),
                        _buildDeliveryTimeOptions(size),
                      ],
                    ),
                  ),
                  SizedBox(height: size.height * 0.03),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(size.width * 0.025),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: size.width * 0.01,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    padding: EdgeInsets.all(size.width * 0.04),
                    child:
                        //  state is MealPlanProgressLoading
                        //     ? Column(
                        //         crossAxisAlignment: CrossAxisAlignment.start,
                        //         children: List.generate(
                        //           6,
                        //           (index) => Padding(
                        //             padding: EdgeInsets.symmetric(
                        //                 vertical: size.height * 0.01),
                        //             child: _buildLoadingShimmer(size),
                        //           ),
                        //         ),
                        //       )
                        //     :
                        Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                          Text(
                            'Order Summary',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: eighteen,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF1F2122),
                            ),
                          ),
                          SizedBox(height: size.height * 0.02),
                          Row(
                            children: List.generate(
                              50,
                              (index) => Container(
                                width: size.width * 0.00625,
                                height: 1,
                                margin: EdgeInsets.symmetric(
                                    horizontal: size.width * 0.005),
                                color: const Color(0xFFE1E3E6),
                              ),
                            ),
                          ),
                          SizedBox(height: size.height * 0.02),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Delivery Time Slot',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              Row(
                                children: [
                                  Icon(
                                    Icons.access_time_rounded,
                                    size: sixteen,
                                    color: const Color(0xFF1F2122),
                                  ),
                                  SizedBox(width: size.width * 0.01),
                                  Text(
                                    mealPlanData?.timeSlot?.startTime != null &&
                                            mealPlanData?.timeSlot?.endTime !=
                                                null
                                        ? '${_formatTime(mealPlanData!.timeSlot!.startTime)} - ${_formatTime(mealPlanData!.timeSlot!.endTime)}'
                                        : 'Time slot not selected',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          SizedBox(height: size.height * 0.02),
                          Row(
                            children: List.generate(
                              50,
                              (index) => Container(
                                width: size.width * 0.00625,
                                height: 1,
                                margin: EdgeInsets.symmetric(
                                    horizontal: size.width * 0.005),
                                color: const Color(0xFFE1E3E6),
                              ),
                            ),
                          ),
                          SizedBox(height: size.height * 0.02),
                          Text(
                            'Order Details (${(mealPlanData?.mealSelectionType == "CURATED" ? mealPlanData?.mealPlanDays?.length : mealPlanData?.personalizedDays?.length) ?? 0} Days)',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: forteen,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF1F2122),
                            ),
                          ),
                          SizedBox(height: size.height * 0.02),
                          ...(() {
                            if (mealPlanData?.mealSelectionType == "CURATED") {
                              return mealPlanData?.mealPlanDays?.map((day) {
                                    print(
                                        "DEBUG: Processing CURATED day with dayOfWeek='${day.dayOfWeek}'");
                                    // For CURATED, use price field, format to 2 decimal places
                                    final dayTotal = day.price != null
                                        ? day.price!.toStringAsFixed(2)
                                        : '0.00';
                                    return _buildDailyOrderItem(
                                      date: '${day.date}, ${day.dayOfWeek}',
                                      dayOfWeek: day.dayOfWeek ?? '',
                                      chef:
                                          '${day.chef?.firstName ?? ''} ${day.chef?.lastName ?? ''}',
                                      dayTotal: dayTotal,
                                      chefImage: day.chef?.profilePhoto,
                                      size: size,
                                      items: day.items,
                                      serves: mealPlanData?.servingSize?.serves,
                                    );
                                  }).toList() ??
                                  [];
                            } else {
                              return mealPlanData?.personalizedDays?.map((day) {
                                    print(
                                        "DEBUG: Processing PERSONALIZED day with dayOfWeek='${day.dayOfWeek}'");
                                    // For PERSONALIZED, use dayTotal field, format to 2 decimal places
                                    final dayTotal = day.dayTotal != null
                                        ? day.dayTotal!.toStringAsFixed(2)
                                        : '0.00';
                                    return _buildDailyOrderItem(
                                      date: '${day.date}, ${day.dayOfWeek}',
                                      dayOfWeek: day.dayOfWeek ?? '',
                                      chef:
                                          '${day.chef?.firstName ?? ''} ${day.chef?.lastName ?? ''}',
                                      dayTotal: dayTotal,
                                      chefImage: day.chef?.profilePhoto,
                                      size: size,
                                      items: day.items,
                                      serves: mealPlanData?.servingSize?.serves,
                                    );
                                  }).toList() ??
                                  [];
                            }
                          })(),
                          SizedBox(height: size.height * 0.02),
                          Row(
                            children: List.generate(
                              50,
                              (index) => Container(
                                width: size.width * 0.00625,
                                height: 1,
                                margin: EdgeInsets.symmetric(
                                    horizontal: size.width * 0.005),
                                color: const Color(0xFFE1E3E6),
                              ),
                            ),
                          ),
                          SizedBox(height: size.height * 0.02),
                          // appliedCoupon == null
                          //     ? Row(
                          //         mainAxisAlignment:
                          //             MainAxisAlignment.spaceBetween,
                          //         children: [
                          //           Row(
                          //             children: [
                          //               Icon(
                          //                 Icons.percent,
                          //                 size: twentyFour,
                          //                 color: const Color(0xFF1F2122),
                          //               ),
                          //               SizedBox(width: size.width * 0.01),
                          //               Text(
                          //                 'Add promo code',
                          //                 style: TextStyle(
                          //                   fontFamily: 'Inter',
                          //                   fontSize: forteen,
                          //                   fontWeight: FontWeight.w500,
                          //                   color: const Color(0xFF1F2122),
                          //                 ),
                          //               ),
                          //             ],
                          //           ),
                          //           TextButton(
                          //             onPressed: () {
                          //               // Navigator.push(
                          //               //   context,
                          //               //   MaterialPageRoute(
                          //               //       builder: (context) =>
                          //               //           ApplyCouponPage(chefid: 83,)),
                          //               // );
                          //               setState(() {
                          //                 appliedCoupon = {
                          //                   'code': 'SAVE20',
                          //                 };
                          //               });
                          //             },
                          //             style: TextButton.styleFrom(
                          //               minimumSize: Size.zero,
                          //               padding: EdgeInsets.zero,
                          //               tapTargetSize:
                          //                   MaterialTapTargetSize.shrinkWrap,
                          //             ),
                          //             child: Row(
                          //               children: [
                          //                 Icon(
                          //                   Icons.add,
                          //                   size: size.width * 0.0375,
                          //                   color: const Color(0xFF1F2122),
                          //                 ),
                          //                 SizedBox(width: size.width * 0.01),
                          //                 IntrinsicWidth(
                          //                   child: Column(
                          //                     children: [
                          //                       Text(
                          //                         'Add',
                          //                         style: TextStyle(
                          //                           fontFamily: 'Inter',
                          //                           fontWeight: FontWeight.w600,
                          //                           fontSize: size.width * 0.03,
                          //                           color:
                          //                               const Color(0xFF414346),
                          //                         ),
                          //                       ),
                          //                       SizedBox(
                          //                           height:
                          //                               size.height * 0.00125),
                          //                       Container(
                          //                         height: 1,
                          //                         width: double
                          //                             .infinity, // Will match the text width exactly
                          //                         color:
                          //                             const Color(0xFF1F2122),
                          //                       ),
                          //                     ],
                          //                   ),
                          //                 )
                          //               ],
                          //             ),
                          //           ),
                          //         ],
                          //       )
                          //     : Column(
                          //         crossAxisAlignment: CrossAxisAlignment.start,
                          //         children: [
                          //           // First Row: Coupon code + Remove Coupon
                          //           Row(
                          //             mainAxisAlignment:
                          //                 MainAxisAlignment.spaceBetween,
                          //             children: [
                          //               Text(
                          //                 appliedCoupon![
                          //                     'code'], // Replace with actual value
                          //                 style: TextStyle(
                          //                   fontFamily: 'Inter-medium',
                          //                   fontWeight: FontWeight.w400,
                          //                   fontSize: 14,
                          //                   height: 20 / 14,
                          //                   letterSpacing: 0,
                          //                   color: Color(0xFF1F2122),
                          //                 ),
                          //               ),
                          //               TextButton(
                          //                 onPressed: () {
                          //                   setState(() {
                          //                     appliedCoupon = null;
                          //                   });
                          //                 },
                          //                 style: TextButton.styleFrom(
                          //                   minimumSize: Size.zero,
                          //                   padding: EdgeInsets.zero,
                          //                   tapTargetSize: MaterialTapTargetSize
                          //                       .shrinkWrap,
                          //                 ),
                          //                 child: Row(
                          //                   children: [
                          //                     IntrinsicWidth(
                          //                       child: Column(
                          //                         mainAxisSize:
                          //                             MainAxisSize.min,
                          //                         crossAxisAlignment:
                          //                             CrossAxisAlignment.center,
                          //                         children: [
                          //                           Text(
                          //                             'Remove Coupon',
                          //                             style: TextStyle(
                          //                               fontFamily: 'Inter',
                          //                               fontWeight:
                          //                                   FontWeight.w600,
                          //                               fontSize:
                          //                                   size.width * 0.03,
                          //                               height: 1.0,
                          //                               letterSpacing: 0.24,
                          //                               color: const Color(
                          //                                   0xFF414346),
                          //                             ),
                          //                           ),
                          //                           SizedBox(
                          //                               height: size.height *
                          //                                   0.002),
                          //                           Container(
                          //                             height: 1,
                          //                             width: double
                          //                                 .infinity, // will now match text width due to IntrinsicWidth
                          //                             color: const Color(
                          //                                 0xFF1F2122),
                          //                           ),
                          //                         ],
                          //                       ),
                          //                     )
                          //                   ],
                          //                 ),
                          //               ),
                          //             ],
                          //           ),

                          //     SizedBox(height: 4),
                          //     Text(
                          //       'Coupon Applied',
                          //       style: TextStyle(
                          //         fontFamily: 'Inter',
                          //         fontWeight: FontWeight.w400,
                          //         fontSize: 12,
                          //         height: 16 / 12,
                          //         letterSpacing: 0,
                          //         color: Color(0xFF007A4D),
                          //       ),
                          //     ),
                          //   ],
                          // ),
                          // SizedBox(height: size.height * 0.02),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding:
                                    EdgeInsets.only(top: size.height * 0.004),
                                child: Container(
                                  width: size.width * 0.06,
                                  height: size.height * 0.02125,
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF1F2122),
                                    borderRadius: BorderRadius.circular(
                                        size.width * 0.01),
                                  ),
                                  alignment: Alignment.center,
                                  child: Text(
                                    'DB',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: size.width * 0.025,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),

                              SizedBox(width: size.width * 0.02),

                              // Column with labels
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'DB Wallet Credits',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w400,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                    Text(
                                      '\$${(mealPlanData?.walletBalance ?? 0.0)}',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: twelve,
                                        fontWeight: FontWeight.w400,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              CustomToggle(
                                value: _useWalletCredits,
                                onChanged: (bool newValue) {
                                  if (newValue &&
                                      (mealPlanData?.walletBalance == null ||
                                          mealPlanData!.walletBalance! <= 0)) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                            "No available credits in your Dabba wallet"),
                                        duration: Duration(seconds: 2),
                                        backgroundColor:
                                            Color.fromARGB(255, 230, 74, 53),
                                        behavior: SnackBarBehavior.floating,
                                        margin: EdgeInsets.symmetric(
                                            horizontal: 16, vertical: 16),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(8)),
                                        ),
                                      ),
                                    );
                                    return;
                                  }

                                  setState(() {
                                    _useWalletCredits = newValue;
                                  });

                                  context
                                      .read<NewmealplanBloc>()
                                      .add(ListNewMealPlanSummaryEvent(
                                        data: {
                                          "meal_plan_id": widget.mealPlanId,
                                          "is_summary": true,
                                          "use_wallet_credits": newValue,
                                          "delivery_time_id":
                                              _selectedDeliveryTime?.id,
                                        },
                                      ));
                                },
                              ),
                            ],
                          ),
                          SizedBox(height: size.height * 0.02),
                          Row(
                            children: List.generate(
                              50,
                              (index) => Container(
                                width: size.width * 0.00625,
                                height: 1,
                                margin: EdgeInsets.symmetric(
                                    horizontal: size.width * 0.005),
                                color: const Color(0xFFE1E3E6),
                              ),
                            ),
                          ),
                          SizedBox(height: size.height * 0.018),
                          Column(
                            children: [
                              Row(
                                children: [
                                  Text(
                                    'Order Total',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: sixteen,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: size.height * 0.014),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Subtotal',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  Text(
                                    '\$${(mealPlanData?.subtotal ?? 0).toStringAsFixed(2)}',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: twelve,
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF414346),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: size.height * 0.01),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Delivery fee',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  Text(
                                    '\$${mealPlanData?.deliveryFee ?? '_'}',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: twelve,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: size.height * 0.01),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Discounts',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  Text(
                                    '-\$${mealPlanData?.discount ?? '_'}',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: twelve,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFFD31510),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: size.height * 0.01),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'DB Wallet Credits',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  Text(
                                    '-\$${mealPlanData?.walletCredits ?? '_'}',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: twelve,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFFD31510),
                                    ),
                                  ),
                                ],
                              ),
                              // SizedBox(height: size.height * 0.01),
                              // Row(
                              //   mainAxisAlignment:
                              //       MainAxisAlignment.spaceBetween,
                              //   children: [
                              //     Text(
                              //       'Service Fees',
                              //       style: TextStyle(
                              //         fontFamily: 'Inter',
                              //         fontSize: forteen,
                              //         fontWeight: FontWeight.w400,
                              //         color: const Color(0xFF1F2122),
                              //       ),
                              //     ),
                              //     Text(
                              //       '\$${(mealPlanData?.deliveryFee ?? 0).toStringAsFixed(2)}',
                              //       style: TextStyle(
                              //         fontFamily: 'Inter',
                              //         fontSize: twelve,
                              //         fontWeight: FontWeight.w500,
                              //         color: const Color(0xFF1F2122),
                              //       ),
                              //     ),
                              //   ],
                              // ),
                              SizedBox(height: size.height * 0.01),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Sales Taxes',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  Text(
                                    '\$${mealPlanData?.taxesAndFees ?? '_'}',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: twelve,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: size.height * 0.01),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Packaging Cost',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  Text(
                                    '\$${mealPlanData?.packagingFee != null ? mealPlanData!.packagingFee!.toStringAsFixed(2) : '_'}',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: twelve,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: size.height * 0.01),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Employment Cost',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  Text(
                                    '\$${mealPlanData?.employmentFee != null ? mealPlanData!.employmentFee!.toStringAsFixed(2) : '_'}',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: twelve,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: size.height * 0.01),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Transaction Fee',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  Text(
                                    '\$${mealPlanData?.transactionFee != null ? mealPlanData!.transactionFee!.toStringAsFixed(2) : '_'}',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: twelve,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: size.height * 0.005),
                              Row(
                                children: List.generate(
                                  50,
                                  (index) => Container(
                                    width: size.width * 0.00625,
                                    height: 1,
                                    margin: EdgeInsets.symmetric(
                                        horizontal: size.width * 0.005),
                                    color: const Color(0xFFE1E3E6),
                                  ),
                                ),
                              ),
                              SizedBox(height: size.height * 0.01),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Total',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: sixteen,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  Text(
                                    '\$${mealPlanData?.total != null ? mealPlanData!.total!.toStringAsFixed(2) : '_'}',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: sixteen,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: size.height * 0.02),
                              // Error message if any
                              if (mealPlanData?.errorMessage != null)
                                if (mealPlanData!.errorMessage!.isNotEmpty)
                                  Text(
                                    mealPlanData!.errorMessage!,
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w400,
                                      color: const Color(0xFFE11900),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                              SizedBox(height: size.height * 0.02),
                              _buildPlaceOrderButton(size),
                              SizedBox(height: size.height * 0.0225),
                              Text(
                                "By clicking the 'Place Order' button, a one-time payment for the ${(mealPlanData?.mealSelectionType == "CURATED" ? mealPlanData?.mealPlanDays?.length : mealPlanData?.personalizedDays?.length) ?? 0}-day meal plan will be charged.",
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFF66696D),
                                ),
                              ),
                              SizedBox(height: size.height * 0.03125),
                            ],
                          ),
                        ]),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDailyOrderItem({
    required String date,
    required String dayOfWeek,
    required String chef,
    required String dayTotal,
    String? chefImage,
    required Size size,
    required List<dynamic>? items,
    required int? serves,
  }) {
    String rawDate = date;

    String datePart = rawDate.split(',')[0];

    DateTime parsedDate = DateTime.parse(datePart);

    String formattedDate = DateFormat('MMM d, yyyy, E').format(parsedDate);
    final totalQuantity = items?.length ?? 0;

    final dateOnly = date.split(',')[0].trim();

    return Container(
      margin: EdgeInsets.only(bottom: size.height * 0.015),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: size.height * 0.01),
          Text(
            formattedDate,
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: twelve,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2122),
            ),
          ),
          SizedBox(height: size.height * 0.01),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                children: [
                  Container(
                    width: twentyFour,
                    height: twentyFour,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xFFE1DDD5),
                    ),
                    child: ClipOval(
                      child: chefImage != null && chefImage.isNotEmpty
                          ? FadeInImage.assetNetwork(
                              placeholder: 'img/chef_placeholder.png',
                              image: '${ServerHelper.imageUrl}$chefImage',
                              fit: BoxFit.cover,
                              imageErrorBuilder: (context, error, stackTrace) {
                                return Image.asset(
                                  'img/chef_placeholder.png',
                                  fit: BoxFit.cover,
                                );
                              },
                            )
                          : Image.asset(
                              'img/chef_placeholder.png',
                              fit: BoxFit.cover,
                            ),
                    ),
                  ),
                  SizedBox(height: size.height * 0.01),
                  if (totalQuantity > 0)
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: size.width * 0.015,
                        vertical: size.height * 0.0025,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFFE1E3E6),
                        borderRadius: BorderRadius.circular(size.width * 0.02),
                      ),
                      child: Text(
                        '$totalQuantity×',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: forteen,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                    ),
                ],
              ),
              SizedBox(width: size.width * 0.025),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Chef $chef',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: sixteen,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(height: size.height * 0.01),
                    Padding(
                      padding: EdgeInsets.only(left: size.width * 0.0225),
                      child: Text(
                        '${serves ?? 1} serving${(serves ?? 1) > 1 ? 's' : ''}',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: forteen,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                    ),
                    SizedBox(height: size.height * 0.01),
                    Padding(
                      padding: EdgeInsets.only(left: size.width * 0.0225),
                      child: TextButton(
                          onPressed: () {
                            dynamic dayData;

                            if (mealPlanData?.mealSelectionType == "CURATED") {
                              try {
                                dayData =
                                    mealPlanData?.mealPlanDays?.firstWhere(
                                  (day) => day.date == dateOnly,
                                );
                              } catch (e) {
                                dayData = null;
                              }
                            } else {
                              try {
                                dayData =
                                    mealPlanData?.personalizedDays?.firstWhere(
                                  (day) => day.date == dateOnly,
                                );
                              } catch (e) {
                                dayData = null;
                              }
                            }

                            if (dayData?.id != null) {
                              _handleEditDay(dayData!.id!, dayOfWeek,
                                  chefId: dayData!.chef?.id);
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Day ID not found'),
                                  backgroundColor: Color(0xFFE11900),
                                ),
                              );
                            }
                          },
                          style: TextButton.styleFrom(
                            minimumSize: Size.zero,
                            padding: EdgeInsets.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          ),
                          child: IntrinsicWidth(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'Edit',
                                  style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: twelve,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF414346),
                                      decoration: TextDecoration
                                          .none, // remove default underline
                                      height: 1.2),
                                ),
                                Container(
                                  height: 1,
                                  width: double.infinity,
                                  color: const Color(0xFF414346),
                                ),
                              ],
                            ),
                          )),
                    ),
                  ],
                ),
              ),
              Text(
                '\$${double.tryParse(dayTotal)?.toStringAsFixed(2) ?? dayTotal}',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: forteen,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF1F2122),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showSummaryFailedDialog(String error) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          title: const Text('Error'),
          content: Text(error),
          actions: [
            TextButton(
              onPressed: () {
                _closePopupCount++;
                Navigator.of(context).pop();
                if (_closePopupCount >= 2) {
                  Navigator.of(context).maybePop();
                  _closePopupCount = 0; // Reset for future use
                }
              },
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }
}