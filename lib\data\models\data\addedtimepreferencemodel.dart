class AddedTimePreferenceModel {
  bool? status;
  int? statusCode;
  AddedTimePreferenceData? data;

  AddedTimePreferenceModel({this.status, this.statusCode, this.data});

  AddedTimePreferenceModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    statusCode = json['status_code'];
    data = json['data'] != null
        ? new AddedTimePreferenceData.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class AddedTimePreferenceData {
  bool? isDeliverNow;
  TimePreference? timePreference;

  AddedTimePreferenceData({this.isDeliverNow, this.timePreference});

  AddedTimePreferenceData.fromJson(Map<String, dynamic> json) {
    isDeliverNow = json['is_deliver_now'];
    timePreference = json['timePreference'] != null
        ? new TimePreference.fromJson(json['timePreference'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_deliver_now'] = this.isDeliverNow;
    if (this.timePreference != null) {
      data['timePreference'] = this.timePreference!.toJson();
    }
    return data;
  }
}

class TimePreference {
  int? id;
  String? startTime;
  String? endTime;

  TimePreference({this.id, this.startTime, this.endTime});

  TimePreference.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['start_time'] = this.startTime;
    data['end_time'] = this.endTime;
    return data;
  }
}
