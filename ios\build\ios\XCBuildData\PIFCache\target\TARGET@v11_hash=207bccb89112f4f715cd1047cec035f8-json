{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a1299aa917988dde2066b8a92f10efbd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980aafbdabb793129a61a9dab575c02d0b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983faeac33dc3e6aa7f75eacf7a5b1c7c7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c8171572110bf2789970485eec8d8b18", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983faeac33dc3e6aa7f75eacf7a5b1c7c7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c5dbe8c85b1858464e947cd68e197246", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9831541c66897e10d2f2c70ac716af5915", "guid": "bfdfe7dc352907fc980b868725387e98eb514fba1557b008e304dbe8a281e04e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc3b8c75c4c46793edc485415f8c4ff9", "guid": "bfdfe7dc352907fc980b868725387e985e76abc21c93ee4e8daf5dcc22215f30", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cb55ac03285ba39b94c9fd7030cbc98", "guid": "bfdfe7dc352907fc980b868725387e98b9806a1b96bc6088da3e5ad4957ee6df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6d4e378d399f2a63dbe5772f1da1029", "guid": "bfdfe7dc352907fc980b868725387e9846ca6bb7292dba60a061a292074a41cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892ef6e7a7ab2e5b852ed763300806428", "guid": "bfdfe7dc352907fc980b868725387e98e30f402c909f1867a84d61a8c7b0e2b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986182de47dace08ae726091120cb9ed07", "guid": "bfdfe7dc352907fc980b868725387e98853842c1d883a7d829a59fd8c302f11e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d3f0c7c277e2a5b5c78607d19c7e666", "guid": "bfdfe7dc352907fc980b868725387e98c36b6e8621c53706d854ad4a3965be8c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876516fc09d87de216139abdebba1d39c", "guid": "bfdfe7dc352907fc980b868725387e98219200859cabda59ca137ff134f0e137", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e81533698895f0dd52f161d6ba1cf10d", "guid": "bfdfe7dc352907fc980b868725387e98b5090a740428ad7069b78deecbfae9c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879de865eb7c0849ded1c42d84f37d2c4", "guid": "bfdfe7dc352907fc980b868725387e985d47c94e9aa7ed05b1a9975ac3cc0390", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0c1a3113764775f7cc2e84a21240838", "guid": "bfdfe7dc352907fc980b868725387e989bcb7cad74c0fb46f6fc857f2c7b786c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3be412b3ab9b4f40c934b678c77dddc", "guid": "bfdfe7dc352907fc980b868725387e98547c5c46f309e9d81846efc280adcf57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0b44f7a49bc49ca4b4c965c963c88c0", "guid": "bfdfe7dc352907fc980b868725387e98bf61aea75f3ed5390783b897fa4195f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98952807383b9748e5c6f08421e09ea6b8", "guid": "bfdfe7dc352907fc980b868725387e987aad8964d6361eefad0759274d5f1cc8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802b2911fe016a96ef34c446c485ae062", "guid": "bfdfe7dc352907fc980b868725387e9810eb182ea8adb50ae8fc2c2d8f26e7cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802352500155ad1a6df48419991d1c0a2", "guid": "bfdfe7dc352907fc980b868725387e98f53597b78fcc5d2311ede109d2d268a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc31b0c84bc8432cf67f312fdbf2d38e", "guid": "bfdfe7dc352907fc980b868725387e985e334a7338975c1c12941fa4b9273810", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f794d014e8f95ab9def66d6aa1a017c7", "guid": "bfdfe7dc352907fc980b868725387e9868295b10c6c05549da492241e188d16f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6e0d56647d577ea006dca7f266314ad", "guid": "bfdfe7dc352907fc980b868725387e984f5fd30f33e49842824c84728d0938bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b664aae7a6e5d4240ee74c87d77278a0", "guid": "bfdfe7dc352907fc980b868725387e988d75f0a2828965feb8d0a7e273ec4238", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c99e90576dd77afebbeeca4f6f6361c2", "guid": "bfdfe7dc352907fc980b868725387e9875d27da374aefac8d445d55ecfd4e57e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f866e1db60ad0daf7a6e1eca6be7a31f", "guid": "bfdfe7dc352907fc980b868725387e9874f3455166050ea5353afaab3f26654d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc24cf5a474584508027ecd28a04b6c3", "guid": "bfdfe7dc352907fc980b868725387e98711461bc8deaa9b3d1aad33f7a76557c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a73b3b87c475d571aea4d162c3f4c70", "guid": "bfdfe7dc352907fc980b868725387e98cbac897618871926b5ab6a1842c5b6d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f68b445fcf234fc73cbb8f1ad1dd26bf", "guid": "bfdfe7dc352907fc980b868725387e98364de3f0d03fb94e2c1dba4a035f3f4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865e1d8cb6829ba42cc3d8c5aee128177", "guid": "bfdfe7dc352907fc980b868725387e98e99869b03210195fd98058b60beaf4bb"}], "guid": "bfdfe7dc352907fc980b868725387e9881f4929f65f10b81ec2024db41fa211f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fc55a9378f26d41878bc7a97c3cbd94d", "guid": "bfdfe7dc352907fc980b868725387e98dfcc04da9988ab2874430f6f92f80c9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca657d0582eea6fa50501087f76c878e", "guid": "bfdfe7dc352907fc980b868725387e988d10784b3eaffcf49763b7a648cd3f04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da416ca7b2f64364f66d46815ffd2e52", "guid": "bfdfe7dc352907fc980b868725387e986be02a109df7390029446b3c05b806c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c30c410829e4a49173291db550f7afda", "guid": "bfdfe7dc352907fc980b868725387e9853ef0f71ef46c7885a8f1be4e0901cdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3214b09af20cacd837c367c7e5e217d", "guid": "bfdfe7dc352907fc980b868725387e980f567f5fe838d3fa560201ed607a1b94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ca1a76a08ef10b618af84902958319b", "guid": "bfdfe7dc352907fc980b868725387e987709218c9d7ffb090e5069fba9465eeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a2bb7e7a2930d6c351d52e983f568ec", "guid": "bfdfe7dc352907fc980b868725387e9818682152e7560d622b5828c6b7af4e15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837cde83227a982d2e812af35447e6f0f", "guid": "bfdfe7dc352907fc980b868725387e9865d4be66dd23dcc17c25c946f4d9e273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ac551213eb5080c29cc635179a4a352", "guid": "bfdfe7dc352907fc980b868725387e9806b779d515b013764a512dc3995a03b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98269db2c28f910a22d67c526369ee5a92", "guid": "bfdfe7dc352907fc980b868725387e98d3151363742152cf1ea13bf99f81d81d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dc0531b67df3ce9377c62e97d3fcaad", "guid": "bfdfe7dc352907fc980b868725387e988e2c67dcaab83b712243be573b34f78f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ebff26aee3487f1570e9b8523014491", "guid": "bfdfe7dc352907fc980b868725387e98437ea65b809f32c251c4c1b8d9fdc75c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb611a3ab567266b3dab650f91161160", "guid": "bfdfe7dc352907fc980b868725387e98708eef72b1169c76daebdc116660b209"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9a99295eff7fcfe529ee67a4d942723", "guid": "bfdfe7dc352907fc980b868725387e982b06555cd5e19ed93525e636048d86ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac70ff5b61ebf2b5ba54b5e5889b9e1f", "guid": "bfdfe7dc352907fc980b868725387e989cdc8ab75aba188fe6e4b899dd0de79e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c18f69b063f29c26a863672696faf724", "guid": "bfdfe7dc352907fc980b868725387e982cb07588fe60ab0cde918dffef25c30d"}], "guid": "bfdfe7dc352907fc980b868725387e98d7bfd99f796a0605af8cd090b0cd32c4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e987c5963e7f6d66f8ea69780b04e1d4047"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e985b602068d478aeaf654886bf26fa9c88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e28c510bcdc3834514b09ab79a067e08", "guid": "bfdfe7dc352907fc980b868725387e98da7326d7fb7d65ee7d91942044924baa"}], "guid": "bfdfe7dc352907fc980b868725387e983f39e404f67bb29ba260cd7d027a4a71", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9800880c6f9af33a49251be9c277f86232", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98b030b057869578e537ca248d2c72bf74", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}