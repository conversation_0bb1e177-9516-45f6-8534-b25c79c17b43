class SettingsInfoModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  SettingsInfoModel({this.status, this.message, this.statusCode, this.data});

  SettingsInfoModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['status_code'] = statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  bool? pushNotification;
  bool? smsOffers;
  bool? emailOffers;

  Data({this.pushNotification, this.smsOffers, this.emailOffers});

  Data.fromJson(Map<String, dynamic> json) {
    pushNotification = json['push_notification'];
    smsOffers = json['sms_offers'];
    emailOffers = json['email_offers'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['push_notification'] = pushNotification;
    data['sms_offers'] = smsOffers;
    data['email_offers'] = emailOffers;
    return data;
  }
}
