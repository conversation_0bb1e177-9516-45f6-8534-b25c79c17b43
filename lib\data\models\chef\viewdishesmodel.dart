class DishesListModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  DishesListModel({this.status, this.message, this.statusCode, this.data});

  DishesListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<FeaturedList>? featuredList;
  List<CategoryBasedList>? categoryBasedList;

  Data({this.featuredList, this.categoryBasedList});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['featured_list'] != null) {
      featuredList = <FeaturedList>[];
      json['featured_list'].forEach((v) {
        featuredList!.add(new FeaturedList.fromJson(v));
      });
    }
    if (json['category_based_list'] != null) {
      categoryBasedList = <CategoryBasedList>[];
      json['category_based_list'].forEach((v) {
        categoryBasedList!.add(new CategoryBasedList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.featuredList != null) {
      data['featured_list'] =
          this.featuredList!.map((v) => v.toJson()).toList();
    }
    if (this.categoryBasedList != null) {
      data['category_based_list'] =
          this.categoryBasedList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class FeaturedList {
  int? id;
  String? name;
  int? chefCategoryId;
  String? photo;
  bool? isFeatured;
  List<ServingSizePrices>? servingSizePrices;
  List<CartItem>? cartItems;
  bool? inCart;

  FeaturedList(
      {this.id,
      this.name,
      this.chefCategoryId,
      this.photo,
      this.isFeatured,
      this.servingSizePrices,
      this.cartItems,
      this.inCart});

  FeaturedList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    chefCategoryId = json['chef_category_id'];
    photo = json['photo'];
    isFeatured = json['is_featured'];
    if (json['serving_size_prices'] != null) {
      servingSizePrices = <ServingSizePrices>[];
      json['serving_size_prices'].forEach((v) {
        servingSizePrices!.add(new ServingSizePrices.fromJson(v));
      });
    }
    // Added cart items parsing
    if (json['cart_items'] != null) {
      cartItems = <CartItem>[];
      json['cart_items'].forEach((v) {
        cartItems!.add(new CartItem.fromJson(v));
      });
    }
    inCart = json['in_cart']; // Added
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['chef_category_id'] = this.chefCategoryId;
    data['photo'] = this.photo;
    data['is_featured'] = this.isFeatured;
    if (this.servingSizePrices != null) {
      data['serving_size_prices'] =
          this.servingSizePrices!.map((v) => v.toJson()).toList();
    }
    if (this.cartItems != null) {
      data['cart_items'] = this.cartItems!.map((v) => v.toJson()).toList();
    }
    data['in_cart'] = this.inCart;
    return data;
  }
}

class ServingSizePrices {
  int? id;
  int? servingSizeId;
  String? price;
  ServingSize? servingSize;

  ServingSizePrices(
      {this.id, this.servingSizeId, this.price, this.servingSize});

  ServingSizePrices.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    servingSizeId = json['serving_size_id'];
    price = json['price'];
    servingSize = json['serving_size'] != null
        ? new ServingSize.fromJson(json['serving_size'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['serving_size_id'] = this.servingSizeId;
    data['price'] = this.price;
    if (this.servingSize != null) {
      data['serving_size'] = this.servingSize!.toJson();
    }
    return data;
  }
}

class ServingSize {
  int? id;
  String? title;
  int? serves;

  ServingSize({this.id, this.title, this.serves});

  ServingSize.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    serves = json['serves'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['serves'] = this.serves;
    return data;
  }
}

class CartItem {
  int? chefDishId;
  int? servingSizeId;
  int? quantity;

  CartItem({this.chefDishId, this.servingSizeId, this.quantity});

  CartItem.fromJson(Map<String, dynamic> json) {
    chefDishId = json['chef_dish_id'];
    servingSizeId = json['serving_size_id'];
    quantity = json['quantity'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['chef_dish_id'] = this.chefDishId;
    data['serving_size_id'] = this.servingSizeId;
    data['quantity'] = this.quantity;
    return data;
  }
}

class CategoryBasedList {
  Category? category;
  List<DishList>? dishList;

  CategoryBasedList({this.category, this.dishList});

  CategoryBasedList.fromJson(Map<String, dynamic> json) {
    category = json['category'] != null
        ? new Category.fromJson(json['category'])
        : null;
    if (json['dish_list'] != null) {
      dishList = <DishList>[];
      json['dish_list'].forEach((v) {
        dishList!.add(new DishList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.category != null) {
      data['category'] = this.category!.toJson();
    }
    if (this.dishList != null) {
      data['dish_list'] = this.dishList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Category {
  int? id;
  String? name;

  Category({this.id, this.name});

  Category.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}

class DishList {
  int? id;
  String? name;
  int? chefCategoryId;
  String? photo;
  bool? isFeatured;
  List<ServingSizePrices>? servingSizePrices;
  List<CartItem>? cartItems;
  bool? inCart;

  DishList({
    this.id,
    this.name,
    this.chefCategoryId,
    this.photo,
    this.isFeatured,
    this.servingSizePrices,
    this.cartItems,
    this.inCart,
  });

  DishList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    chefCategoryId = json['chef_category_id'];
    photo = json['photo'];
    isFeatured = json['is_featured'];
    if (json['serving_size_prices'] != null) {
      servingSizePrices = <ServingSizePrices>[];
      json['serving_size_prices'].forEach((v) {
        servingSizePrices!.add(ServingSizePrices.fromJson(v));
      });
    }
    // Added cart items parsing
    if (json['cart_items'] != null) {
      cartItems = <CartItem>[];
      json['cart_items'].forEach((v) {
        cartItems!.add(CartItem.fromJson(v));
      });
    }
    inCart = json['in_cart']; // Added
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['chef_category_id'] = chefCategoryId;
    data['photo'] = photo;
    data['is_featured'] = isFeatured;
    if (servingSizePrices != null) {
      data['serving_size_prices'] =
          servingSizePrices!.map((v) => v.toJson()).toList();
    }

    if (cartItems != null) {
      data['cart_items'] = cartItems!.map((v) => v.toJson()).toList();
    }
    data['in_cart'] = inCart;
    return data;
  }
}
