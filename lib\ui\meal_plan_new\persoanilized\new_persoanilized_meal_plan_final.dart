import 'package:db_eats/bloc/newmealplan_bloc.dart';
import 'package:db_eats/data/models/new_meal_plan/mealplanprogresslatest.dart';
import 'package:db_eats/ui/meal_plan_new/mealplan_checkout_page.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:flutter/services.dart';

class NewPersoanilizedMealPlanFinal extends StatefulWidget {
  final int mealPlanId;

  const NewPersoanilizedMealPlanFinal({
    Key? key,
    required this.mealPlanId,
  }) : super(key: key);

  @override
  _NewPersoanilizedMealPlanFinalState createState() =>
      _NewPersoanilizedMealPlanFinalState();
}

class _NewPersoanilizedMealPlanFinalState
    extends State<NewPersoanilizedMealPlanFinal> {
  int currentday = 0;
  Map<String, Map<String, dynamic>> mealdata = {};
  List<String> dates = [];

  String _timeSlot = '';

  bool _dataProcessed = false;

  @override
  void initState() {
    super.initState();
    print(
        "PersonailizedMealplanFinal initState called - mealPlanId: ${widget.mealPlanId}");
    _resetData();

    context.read<NewmealplanBloc>().add(
        ListNewMealPlanSummaryEvent(data: {'meal_plan_id': widget.mealPlanId}));
  }

  // @override
  // void didUpdateWidget(NewPersoanilizedMealPlanFinal oldWidget) {
  //   super.didUpdateWidget(oldWidget);
  //   print(
  //       "didUpdateWidget called - old: ${oldWidget.mealPlanId}, new: ${widget.mealPlanId}");
  //   if (oldWidget.mealPlanId != widget.mealPlanId) {
  //     _resetData();
  //     context.read<NewmealplanBloc>().add(ListNewMealPlanSummaryEvent(
  //         data: {'meal_plan_id': widget.mealPlanId}));
  //   }
  // }

  void _resetData() {
    setState(() {
      mealdata.clear();
      dates.clear();
      currentday = 0;
      _timeSlot = '';
      _dataProcessed = false;
    });
  }

  String formatDate(String date) {
    DateTime parsedDate = DateTime.parse(date);
    String formattedDate = DateFormat('EEE, MMM dd, yyyy').format(parsedDate);
    return formattedDate;
  }

  String _formatTimeWithAMPM(String time) {
    final parts = time.split(':');
    int hours = int.parse(parts[0]);
    final minutes = parts[1];
    final period = hours >= 12 ? 'PM' : 'AM';
    hours = hours > 12 ? hours - 12 : hours;
    hours = hours == 0 ? 12 : hours;
    return '$hours:$minutes$period';
  }

  void processData(MealPlanProgressLatestData mealPlanData) {
    print(
        "Processing data - meal_plan_days count: ${mealPlanData.mealPlanDays?.length}");
    print(
        "Raw meal_plan_days data: ${mealPlanData.mealPlanDays?.map((e) => e.date).toList()}");
    setState(() {
      final Set<String> allDates = {};
      if (mealPlanData.mealPlanDays != null) {
        for (var day in mealPlanData.mealPlanDays!) {
          if (day.date != null && day.date!.isNotEmpty) {
            allDates.add(day.date!);
          }
        }
      }
      dates = allDates.toList()..sort();
      print("All processed dates: $dates");
      mealdata.clear();
      for (var date in dates) {
        final mealPlanDay = mealPlanData.mealPlanDays
            ?.where((day) => day.date == date)
            .firstOrNull;
        if (mealPlanDay != null) {
          mealdata[date] = {
            'selectedDishes': (mealPlanDay.items ?? []).map((dynamic item) {
              // Check if item is a Map or an object
              if (item is Map) {
                return {
                  'name': item['menu_item']?['name'] ?? '',
                  'price':
                      double.tryParse(item['price']?.toString() ?? '0') ?? 0.0,
                  'photo': item['menu_item']?['photo'] ?? '',
                  'servings': mealPlanData.servingSize?.serves ?? 1,
                  'quantity': item['quantity'] ?? 1,
                };
              } else {
                // Try to access properties as an object
                try {
                  return {
                    'name': item.menuItem?.name ?? '',
                    'price':
                        double.tryParse(item.price?.toString() ?? '0') ?? 0.0,
                    'photo': item.menuItem?.photo ?? '',
                    'servings': mealPlanData.servingSize?.serves ?? 1,
                    'quantity': item.quantity ?? 1,
                  };
                } catch (e) {
                  // If accessing properties fails, try as a Map using dynamic access
                  print("Error parsing meal item: $e");
                  try {
                    final dynamic menuItem = item.menu_item;
                    return {
                      'name': menuItem?['name'] ?? '',
                      'price':
                          double.tryParse(item.price?.toString() ?? '0') ?? 0.0,
                      'photo': menuItem?['photo'] ?? '',
                      'servings': mealPlanData.servingSize?.serves ?? 1,
                      'quantity': item.quantity ?? 1,
                    };
                  } catch (e) {
                    print("Fallback error: $e");
                    return {
                      'name': 'Unknown Item',
                      'price': 0.0,
                      'photo': '',
                      'servings': 1,
                      'quantity': 1,
                    };
                  }
                }
              }
            }).toList(),
            'chefDetails': {
              'name':
                  '${mealPlanDay.chef?.firstName ?? ''} ${mealPlanDay.chef?.lastName ?? ''}'
                      .trim(),
              'photo': mealPlanDay.chef?.profilePhoto ?? '',
            },
            'dayTotal':
                (mealPlanDay.items ?? []).fold(0.0, (double sum, dynamic item) {
              try {
                final double price = double.tryParse(
                        (item is Map ? item['price'] : item.price)
                                ?.toString() ??
                            '0') ??
                    0.0;
                final int quantity =
                    (item is Map ? item['quantity'] : item.quantity) ?? 1;
                return sum + (price * quantity);
              } catch (e) {
                print("Error calculating item price: $e");
                return sum;
              }
            }),
            'discount': mealPlanDay.discount ?? 0,
          };
        }
      }
      final startTime =
          mealPlanData.timeSlot?.startTime?.substring(0, 5) ?? '12:00';
      final endTime =
          mealPlanData.timeSlot?.endTime?.substring(0, 5) ?? '15:00';
      _timeSlot =
          '${_formatTimeWithAMPM(startTime)}-${_formatTimeWithAMPM(endTime)}';
      if (currentday >= dates.length) {
        currentday = 0;
      }
      _dataProcessed = true;
    });
  }

  List<Map<String, dynamic>> _getCurrentDayMeals(String currentDate) {
    return List<Map<String, dynamic>>.from(
        mealdata[currentDate]?['selectedDishes'] ?? []);
  }

  void _showCannotGoBackDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: EdgeInsets.all(MediaQuery.sizeOf(context).width * 0.05),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "Cannot Go Back",
                  style: TextStyle(
                    fontSize: MediaQuery.sizeOf(context).width * 0.045,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1F2122),
                  ),
                ),
                SizedBox(height: MediaQuery.sizeOf(context).height * 0.02),
                Text(
                  "You cannot go back from this page. Please continue to checkout.",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: MediaQuery.sizeOf(context).width * 0.035,
                    color: const Color(0xFF414346),
                  ),
                ),
                SizedBox(height: MediaQuery.sizeOf(context).height * 0.03),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF1F2122),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(100),
                    ),
                    minimumSize: Size.fromHeight(
                        MediaQuery.sizeOf(context).height * 0.06),
                  ),
                  child: Text(
                    "OK",
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: MediaQuery.sizeOf(context).width * 0.04,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        _showCannotGoBackDialog();
        return false;
      },
      child: BlocConsumer<NewmealplanBloc, NewMealPlanState>(
        listener: (context, state) {
          print("BLoC state changed: ${state.runtimeType}");
          if (state is NewMealPlanSummaryLoaded) {
            print("Success state - data null: ${state.response == null}");
            if (state.response != null) {
              print(
                  "meal_plan_days count: ${state.response.mealPlanDays?.length}");
              processData(state.response);
            }
          } else if (state is NewMealPlanSummaryLoadFailed) {
            print("Error state: ${state.toString()}");
          }
        },
        builder: (context, state) {
          print(
              "Building widget - state: ${state.runtimeType}, dataProcessed: $_dataProcessed, dates: ${dates.length}");
          if (state is ListNewMealPlanSummaryLoading) {
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );
          }
          if (state is NewMealPlanSummaryLoadFailed) {
            return Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('Error loading meal plan data'),
                    SizedBox(height: MediaQuery.sizeOf(context).height * 0.02),
                    ElevatedButton(
                      onPressed: () {
                        context.read<NewmealplanBloc>().add(
                            ListNewMealPlanSummaryEvent(
                                data: {'meal_plan_id': widget.mealPlanId}));
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            );
          }
          if (state is NewMealPlanSummaryLoaded && !_dataProcessed) {
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );
          }
          if (!_dataProcessed || mealdata.isEmpty || dates.isEmpty) {
            return const Scaffold(
              body: Center(child: Text('No meal plan data available')),
            );
          }
          if (currentday >= dates.length) {
            currentday = 0;
          }
          String currentDate = dates[currentday];
          List<Map<String, dynamic>> currentDayMeals =
              _getCurrentDayMeals(currentDate);
          double subtotal = currentDayMeals.fold(
              0.0, (sum, meal) => sum + (meal['price'] as num).toDouble());

          return Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              backgroundColor: Colors.white,
              elevation: 0,
              scrolledUnderElevation: 0,
              centerTitle: false,
              leading: IconButton(
                icon: const Icon(Icons.close, color: Colors.black),
                onPressed: _showCannotGoBackDialog,
                padding: EdgeInsets.zero,
                splashRadius: 20,
              ),

              // leading:
              // IconButton(
              //   icon: Image.asset('assets/icons/close.png',
              //       width: 24, height: 24),
              //   onPressed: () => _showCannotGoBackDialog(),
              //   padding: EdgeInsets.zero,
              // ),
              title: Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Text(
                  "Meal Plan",
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width * 0.045,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1F2122),
                  ),
                ),
              ),
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(1),
                child: Container(
                  color: const Color(0xFFE1E3E6),
                  height: 1,
                ),
              ),
            ),
            body: LayoutBuilder(
              builder: (context, constraints) {
                return SingleChildScrollView(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: constraints.maxHeight,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.all(
                              MediaQuery.sizeOf(context).width * 0.04),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                formatDate(dates[currentday]),
                                style: TextStyle(
                                  fontSize:
                                      MediaQuery.sizeOf(context).width * 0.045,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(
                                  height:
                                      MediaQuery.sizeOf(context).height * 0.01),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  IconButton(
                                    icon: currentday == 0
                                        ? Image.asset(
                                            'assets/icons/arrow_left.png',
                                            width: MediaQuery.sizeOf(context)
                                                    .width *
                                                0.08,
                                            height: MediaQuery.sizeOf(context)
                                                    .width *
                                                0.08,
                                            color: Colors.grey)
                                        : Transform.rotate(
                                            angle: 3.1416,
                                            child: Image.asset(
                                                'assets/icons/arrow_right.png',
                                                width:
                                                    MediaQuery.sizeOf(context)
                                                            .width *
                                                        0.08,
                                                height:
                                                    MediaQuery.sizeOf(context)
                                                            .width *
                                                        0.08),
                                          ),
                                    onPressed: currentday == 0
                                        ? null
                                        : () {
                                            setState(() {
                                              if (currentday > 0) currentday--;
                                            });
                                          },
                                    padding: EdgeInsets.all(
                                        MediaQuery.sizeOf(context).width *
                                            0.02),
                                    constraints: BoxConstraints(
                                      minWidth:
                                          MediaQuery.sizeOf(context).width *
                                              0.12,
                                      minHeight:
                                          MediaQuery.sizeOf(context).width *
                                              0.12,
                                    ),
                                    splashRadius:
                                        MediaQuery.sizeOf(context).width * 0.06,
                                  ),
                                  Expanded(
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(Icons.access_time,
                                            size: MediaQuery.sizeOf(context)
                                                    .width *
                                                0.04),
                                        SizedBox(
                                            width: MediaQuery.sizeOf(context)
                                                    .width *
                                                0.01),
                                        Text(
                                          _timeSlot,
                                          style: TextStyle(
                                            fontSize: MediaQuery.sizeOf(context)
                                                    .width *
                                                0.035,
                                            color: const Color(0xFF414346),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  IconButton(
                                    icon: currentday >= dates.length - 1
                                        ? Transform.rotate(
                                            angle: 3.1416,
                                            child: Image.asset(
                                                'assets/icons/arrow_left.png',
                                                width:
                                                    MediaQuery.sizeOf(context)
                                                            .width *
                                                        0.08,
                                                height:
                                                    MediaQuery.sizeOf(context)
                                                            .width *
                                                        0.08,
                                                color: Colors.grey),
                                          )
                                        : Image.asset(
                                            'assets/icons/arrow_right.png',
                                            width: MediaQuery.sizeOf(context)
                                                    .width *
                                                0.08,
                                            height: MediaQuery.sizeOf(context)
                                                    .width *
                                                0.08),
                                    onPressed: currentday >= dates.length - 1
                                        ? null
                                        : () {
                                            setState(() {
                                              if (currentday <
                                                  dates.length - 1) {
                                                currentday++;
                                              }
                                            });
                                          },
                                    padding: EdgeInsets.all(
                                        MediaQuery.sizeOf(context).width *
                                            0.02),
                                    constraints: BoxConstraints(
                                      minWidth:
                                          MediaQuery.sizeOf(context).width *
                                              0.12,
                                      minHeight:
                                          MediaQuery.sizeOf(context).width *
                                              0.12,
                                    ),
                                    splashRadius:
                                        MediaQuery.sizeOf(context).width * 0.06,
                                  ),
                                ],
                              ),
                              SizedBox(
                                  height:
                                      MediaQuery.sizeOf(context).height * 0.01),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  _buildChefAvatar(mealdata[currentDate]
                                      ?['chefDetails']['photo']),
                                  SizedBox(
                                      width: MediaQuery.sizeOf(context).width *
                                          0.02),
                                  Text(
                                    mealdata[currentDate]?['chefDetails']
                                            ['name'] ??
                                        '',
                                    style: TextStyle(
                                      fontSize:
                                          MediaQuery.sizeOf(context).width *
                                              0.04,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const Divider(
                            height: 1, thickness: 1, color: Color(0xFFE1E3E6)),
                        Padding(
                          padding: EdgeInsets.all(
                              MediaQuery.sizeOf(context).width * 0.04),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Meals (${mealdata[dates[currentday]]?['selectedDishes']?.length ?? 0})",
                                style: TextStyle(
                                  fontSize:
                                      MediaQuery.sizeOf(context).width * 0.04,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(
                                  height: MediaQuery.sizeOf(context).height *
                                      0.015),
                              ...List<Map<String, dynamic>>.from(
                                      mealdata[dates[currentday]]
                                              ?['selectedDishes'] ??
                                          [])
                                  .map((meal) => _buildMealItem(meal)),
                            ],
                          ),
                        ),
                        SizedBox(
                            height: MediaQuery.sizeOf(context).height * 0.02),
                        Padding(
                          padding: EdgeInsets.all(
                              MediaQuery.sizeOf(context).width * 0.04),
                          child: Column(
                            children: [
                              ...currentDayMeals.map((meal) => _buildPriceRow(
                                    meal['name'],
                                    (meal['price'] as num).toDouble(),
                                  )),
                              if (currentDayMeals.isNotEmpty)
                                SizedBox(
                                    height: MediaQuery.sizeOf(context).height *
                                        0.02),
                              _buildPriceRow("Subtotal", subtotal,
                                  isTotal: true),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            bottomNavigationBar: Container(
              padding: EdgeInsets.only(
                bottom: MediaQuery.sizeOf(context).width * 0.04,
                left: MediaQuery.sizeOf(context).width * 0.04,
                right: MediaQuery.sizeOf(context).width * 0.04,
              ),
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => MealplanCheckoutPage(
                        mealPlanId: widget.mealPlanId,
                      ),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1F2122),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(100),
                  ),
                  padding: EdgeInsets.symmetric(
                      vertical: MediaQuery.sizeOf(context).height * 0.02),
                  minimumSize:
                      Size.fromHeight(MediaQuery.sizeOf(context).height * 0.07),
                ),
                child: Text(
                  "Continue to Checkout",
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                    fontSize: MediaQuery.sizeOf(context).width * 0.04,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    SystemChannels.platform.setMethodCallHandler((call) async {
      if (call.method == "SystemNavigator.pop") {
        _showCannotGoBackDialog();
        return Future.value(false);
      }
      return null;
    });
  }

  Widget _buildChefAvatar(String? imagePath) {
    final size = MediaQuery.sizeOf(context).width * 0.03;
    if (imagePath == null || imagePath.isEmpty) {
      return CircleAvatar(
        backgroundColor: const Color(0xFFE0E0E0),
        radius: size,
        child: Icon(Icons.person, size: size, color: const Color(0xFF9E9E9E)),
      );
    }
    return CircleAvatar(
      backgroundImage: NetworkImage(ServerHelper.imageUrl + imagePath),
      backgroundColor: Colors.grey[200],
      radius: size,
      onBackgroundImageError: (_, __) {
        debugPrint(
            "Error loading chef image: ${ServerHelper.imageUrl + imagePath}");
      },
    );
  }

  Widget _buildMealItem(Map<String, dynamic> meal) {
    return Container(
      margin:
          EdgeInsets.only(bottom: MediaQuery.sizeOf(context).height * 0.015),
      padding: EdgeInsets.all(MediaQuery.sizeOf(context).width * 0.03),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE1E3E6)),
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              ServerHelper.imageUrl + (meal['photo'] ?? ''),
              width: MediaQuery.sizeOf(context).width * 0.12,
              height: MediaQuery.sizeOf(context).width * 0.12,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                width: MediaQuery.sizeOf(context).width * 0.12,
                height: MediaQuery.sizeOf(context).width * 0.12,
                color: Colors.grey[200],
                child:
                    const Icon(Icons.restaurant_menu, color: Color(0xFF9E9E9E)),
              ),
            ),
          ),
          SizedBox(width: MediaQuery.sizeOf(context).width * 0.03),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  meal['name'],
                  style: TextStyle(
                    fontSize: MediaQuery.sizeOf(context).width * 0.04,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1F2122),
                  ),
                ),
                SizedBox(height: MediaQuery.sizeOf(context).height * 0.005),
                Row(
                  children: [
                    Text(
                      "\$${meal['price'].toStringAsFixed(2)}",
                      style: TextStyle(
                        fontSize: MediaQuery.sizeOf(context).width * 0.035,
                        color: const Color(0xFF414346),
                      ),
                    ),
                    SizedBox(width: MediaQuery.sizeOf(context).width * 0.02),
                    Text(
                      "• ${meal['servings']} Servings",
                      style: TextStyle(
                        fontSize: MediaQuery.sizeOf(context).width * 0.035,
                        color: const Color(0xFF414346),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(String title, double amount, {bool isTotal = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: MediaQuery.sizeOf(context).height * 0.005),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: isTotal
                  ? MediaQuery.sizeOf(context).width * 0.04
                  : MediaQuery.sizeOf(context).width * 0.035,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
              color: const Color(0xFF1F2122),
            ),
          ),
          Text(
            "\$${amount.toStringAsFixed(2)}",
            style: TextStyle(
              fontSize: isTotal
                  ? MediaQuery.sizeOf(context).width * 0.04
                  : MediaQuery.sizeOf(context).width * 0.035,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
              color: const Color(0xFF1F2122),
            ),
          ),
        ],
      ),
    );
  }
}
