class AvailableCouponsModel {
  bool? status;
  List<Data>? data;
  int? statusCode;

  AvailableCouponsModel({this.status, this.data, this.statusCode});

  AvailableCouponsModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['status_code'] = this.statusCode;
    return data;
  }
}

class Data {
  String? couponCode;
  String? discountName;
  String? description;
  int? discountType;
  String? discountPercentage;
  String? discountAmount;
  String? maxCapPercentageOff;
  String? minimumSpendAmount;
  bool? isFirstOrder;
  bool? isMinimumOrderAmount;
  bool? isSelectedDishes;
  bool? isSelectedCategories;
  bool? isSelectedChefs;
  String? startDate;
  String? endDate;
  bool? isAutoApply;
  num? discount;

  Data(
      {this.couponCode,
      this.discountName,
      this.description,
      this.discountType,
      this.discountPercentage,
      this.discountAmount,
      this.maxCapPercentageOff,
      this.minimumSpendAmount,
      this.isFirstOrder,
      this.isMinimumOrderAmount,
      this.isSelectedDishes,
      this.isSelectedCategories,
      this.isSelectedChefs,
      this.startDate,
      this.endDate,
      this.isAutoApply,
      this.discount});

  Data.fromJson(Map<String, dynamic> json) {
    couponCode = json['coupon_code'];
    discountName = json['discount_name'];
    description = json['description'];
    discountType = json['discount_type'];
    discountPercentage = json['discount_percentage'];
    discountAmount = json['discount_amount'];
    maxCapPercentageOff = json['max_cap_percentage_off'];
    minimumSpendAmount = json['minimum_spend_amount'];
    isFirstOrder = json['is_first_order'];
    isMinimumOrderAmount = json['is_minimum_order_amount'];
    isSelectedDishes = json['is_selected_dishes'];
    isSelectedCategories = json['is_selected_categories'];
    isSelectedChefs = json['is_selected_chefs'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    isAutoApply = json['is_auto_apply'];
    discount = json['discount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['coupon_code'] = this.couponCode;
    data['discount_name'] = this.discountName;
    data['description'] = this.description;
    data['discount_type'] = this.discountType;
    data['discount_percentage'] = this.discountPercentage;
    data['discount_amount'] = this.discountAmount;
    data['max_cap_percentage_off'] = this.maxCapPercentageOff;
    data['minimum_spend_amount'] = this.minimumSpendAmount;
    data['is_first_order'] = this.isFirstOrder;
    data['is_minimum_order_amount'] = this.isMinimumOrderAmount;
    data['is_selected_dishes'] = this.isSelectedDishes;
    data['is_selected_categories'] = this.isSelectedCategories;
    data['is_selected_chefs'] = this.isSelectedChefs;
    data['start_date'] = this.startDate;
    data['end_date'] = this.endDate;
    data['is_auto_apply'] = this.isAutoApply;
    data['discount'] = this.discount;
    return data;
  }
}
