import 'dart:math';

import 'package:db_eats/bloc/catering_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/catering/catering_dishdetails.dart';
import 'package:db_eats/ui/catering/revieworder.dart';
import 'package:db_eats/widgets/subtotalbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CateringChefInfoPage extends StatefulWidget {
  final int cateringRequestId;
  final int id;
  final String title;
  final String cuisineTitle;
  final int peopleCount;
  final List<int>? cuisineIds;
  final List<int>? subCuisineIds;
  final List<int>? localCuisineIds;
  final int? packagingTypeId;
  final int? dietaryPreferenceId;
  final int? spiceLevelId;

  const CateringChefInfoPage({
    required this.cateringRequestId,
    super.key,
    required this.id,
    required this.title,
    required this.cuisineTitle,
    required this.peopleCount,
    this.cuisineIds,
    this.subCuisineIds,
    this.localCuisineIds,
    this.packagingTypeId,
    this.dietaryPreferenceId,
    this.spiceLevelId,
  });

  @override
  State<CateringChefInfoPage> createState() => _CateringChefInfoPageState();
}

class _CateringChefInfoPageState extends State<CateringChefInfoPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController; // Add ScrollController
  String _totalPrice = '0.00';
  List<GlobalKey> _categoryKeys = []; // List to store GlobalKeys for categories

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _scrollController = ScrollController(); // Initialize ScrollController

    // Load chef details
    context.read<HomeBloc>().add(ViewChefDetailsEvent(
          data: {
            "chef_id": widget.id.toString(),
            "latitude": Initializer.latitude.toString(),
            "longitude": Initializer.longitude.toString(),
          },
        ));

    // Load dishes
    context.read<CateringBloc>().add(
          ListDishesInCatering({
            "chef_id": widget.id.toString(),
            "search_keyword": "",
            "packaging_type_id": widget.packagingTypeId,
            "cuisine_ids": widget.cuisineIds,
            "sub_cuisine_ids": widget.subCuisineIds,
            "local_cuisine_ids": widget.localCuisineIds,
            "dietary_preference_id": widget.dietaryPreferenceId,
            "spice_level_id": widget.spiceLevelId,
            "catering_id": widget.cateringRequestId,
          }),
        );

    // Load catering request details
    context.read<CateringBloc>().add(
          ViewCateringRequest(widget.cateringRequestId),
        );

    // Add listener to TabController for scrolling
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        _scrollToCategory(_tabController.index);
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;

    // Initialize GlobalKeys after categories are known
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _categoryKeys = List.generate(
          1 +
              (Initializer
                      .cateringDishListModel.data?.categoryBasedList?.length ??
                  0),
          (_) => GlobalKey(),
        );
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose(); // Dispose ScrollController
    super.dispose();
  }

  void _scrollToCategory(int index) {
    if (_categoryKeys.isEmpty || index >= _categoryKeys.length) return;

    final RenderObject? renderObject =
        _categoryKeys[index].currentContext?.findRenderObject();
    if (renderObject != null && renderObject is RenderBox) {
      final position = renderObject.localToGlobal(Offset.zero).dy;
      final scrollOffset = _scrollController.offset +
          position -
          (screenHeight * 0.1); // Adjust for visibility
      _scrollController.animateTo(
        scrollOffset,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _submit() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
          builder: (context) => ReviewOrderPage(
                cateringId: widget.cateringRequestId,
              )),
    );

    if (result != null && result['updated'] == true) {
      context
          .read<CateringBloc>()
          .add(ViewCateringRequest(widget.cateringRequestId));

      context.read<CateringBloc>().add(
            ListDishesInCatering({
              "chef_id": widget.id.toString(),
              "search_keyword": "",
              "packaging_type_id": widget.packagingTypeId,
              "cuisine_ids": widget.cuisineIds,
              "sub_cuisine_ids": widget.subCuisineIds,
              "local_cuisine_ids": widget.localCuisineIds,
              "dietary_preference_id": widget.dietaryPreferenceId,
              "spice_level_id": widget.spiceLevelId,
              "catering_id": widget.cateringRequestId,
            }),
          );
    }
  }

  String _formatOperationTimes(List<dynamic>? operationTimes) {
    if (operationTimes == null || operationTimes.isEmpty) return 'Open: N/A';
    var time = operationTimes.first;
    String startTime = time.timing?.startTime ?? 'N/A';
    String endTime = time.timing?.endTime ?? 'N/A';
    String formattedStart = _formatTimeString(startTime);
    String formattedEnd = _formatTimeString(endTime);
    return 'Open $formattedStart-$formattedEnd';
  }

  String _formatOperationDays(List<dynamic>? operationDays) {
    if (operationDays == null || operationDays.isEmpty) return 'N/A';
    Map<String, String> dayAbbreviations = {
      'mon': 'M',
      'tue': 'T',
      'wed': 'W',
      'thu': 'Th',
      'fri': 'F',
      'sat': 'Sat',
      'sun': 'Sun',
    };
    List<String> formattedDays = operationDays
        .map((day) {
          String? dayName = day.day?.name?.toLowerCase();
          return dayAbbreviations[dayName] ?? '';
        })
        .where((day) => day.isNotEmpty)
        .toList();
    List<String> dayOrder = ['M', 'T', 'W', 'Th', 'F', 'Sat', 'Sun'];
    formattedDays
        .sort((a, b) => dayOrder.indexOf(a).compareTo(dayOrder.indexOf(b)));
    return formattedDays.isEmpty ? 'N/A' : formattedDays.join(', ');
  }

  String _formatTimeString(String timeStr) {
    if (timeStr == 'N/A') return timeStr;
    try {
      List<String> parts = timeStr.split(':');
      if (parts.length < 2) return timeStr;
      int hour = int.parse(parts[0]);
      bool isPM = hour >= 12;
      if (hour > 12) hour -= 12;
      if (hour == 0) hour = 12;
      return '$hour${isPM ? 'PM' : 'AM'}';
    } catch (e) {
      return timeStr;
    }
  }

  Widget _buildMetricContainer(BuildContext context, double screenWidth,
      {required IconData icon, required String text}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: ten / 2, vertical: 0),
      constraints: BoxConstraints(
          minWidth: screenWidth * 0.1, minHeight: twelve + twenty),
      decoration: BoxDecoration(
        color: const Color(0xFFF6F3EC),
        borderRadius: BorderRadius.circular(screenWidth * 0.025),
        border: Border.all(color: const Color(0xFFB9B6AD)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: sixteen, color: const Color(0xFF414346)),
          SizedBox(width: screenWidth * 0.01),
          Text(
            text,
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: forteen,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF1F2122),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, chefState) {
        return BlocConsumer<CateringBloc, CateringState>(
          listener: (context, state) {
            if (state is AddDishToCateringCartSuccess) {
              setState(() {});
            } else if (state is ViewCateringRequestSuccess) {
              _totalPrice =
                  Initializer.viewCateringRequestModel.data?.totalPrice ??
                      '0.00';
              setState(() {});
            } else if (state is ListDishesInCateringSuccess) {
              setState(() {
                _categoryKeys = List.generate(
                  1 + ((state.data?.categoryBasedList?.length ?? 0) as int),
                  (_) => GlobalKey(),
                );
              });
            }
          },
          builder: (context, state) {
            if (state is ViewCateringRequestSuccess) {
              _totalPrice =
                  Initializer.viewCateringRequestModel.data?.totalPrice ??
                      '0.00';
            }

            if (chefState is LoadingChefDetails ||
                state is ListDishesInCateringLoading ||
                state is ViewCateringRequestLoading) {
              return const Scaffold(
                body: Center(
                  child: CupertinoActivityIndicator(),
                ),
              );
            }

            if (chefState is ChefDetailsSuccess &&
                (state is ListDishesInCateringSuccess ||
                    state is ViewCateringRequestSuccess ||
                    state is AddDishToCateringCartSuccess ||
                    state is UpdateDishQuantitySuccess ||
                    state is RemoveDishFromRequestSuccess ||
                    (Initializer.cateringDishListModel.data != null))) {
              final chefData = chefState.data;
              final dishData = Initializer.cateringDishListModel.data;

              if (!(chefData.data?.chef?.availableForCatering ?? false)) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _showUnavailableModal();
                });
                return const Scaffold(
                  body: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              final categories = [
                'Featured Items',
                ...?dishData?.categoryBasedList
                    ?.map((category) => category.category?.name ?? '')
              ];

              return Scaffold(
                backgroundColor: const Color(0xFFF6F3EC),
                appBar: AppBar(
                  backgroundColor: const Color(0xFFf6f3ec),
                  centerTitle: false,
                  scrolledUnderElevation: 0,
                  titleSpacing: 03,
                  automaticallyImplyLeading: false,
                  leading: IconButton(
                    icon: Image.asset(
                      'assets/icons/backbutton.png',
                      width: twenty + twelve,
                      height: twenty + twelve,
                    ),
                    onPressed: () => Navigator.pop(context),
                  ),
                  title: Text(
                    '${widget.cuisineTitle} for ${widget.peopleCount} People',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: sixteen,
                      color: Colors.black,
                    ),
                  ),
                  elevation: 0,
                ),
                body: SingleChildScrollView(
                  controller: _scrollController, // Attach ScrollController
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: screenWidth * 0.035,
                          vertical: screenHeight * 0.01,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Cover and Profile Image
                            SizedBox(
                              height: screenHeight * 0.18,
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  Container(
                                    width: double.infinity,
                                    height: screenHeight * 0.148,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(
                                          screenWidth * 0.04),
                                      image: DecorationImage(
                                        image: NetworkImage(ServerHelper
                                                .imageUrl +
                                            (chefData.data?.chef?.coverPhoto ??
                                                '')),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  Align(
                                    alignment: Alignment.bottomRight,
                                    child: FractionalTranslation(
                                      translation: const Offset(-0.21, -0.03),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: Color(0xFFF6F3EC),
                                            width: screenWidth * 0.015,
                                          ),
                                        ),
                                        child: CircleAvatar(
                                          radius: screenWidth * 0.1,
                                          backgroundImage: NetworkImage(
                                              ServerHelper.imageUrl +
                                                  (chefData.data?.chef
                                                          ?.profilePhoto ??
                                                      '')),
                                          backgroundColor: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "${chefData.data?.chef?.chef?.firstName ?? ''} ${chefData.data?.chef?.chef?.lastName ?? ''}",
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: eighteen,
                                          fontWeight: FontWeight.w600,
                                          height: 1.24,
                                          letterSpacing: -1,
                                          color: Color(0xFF1F2122),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: screenHeight * 0.017),
                            Row(
                              children: [
                                _buildMetricContainer(
                                  context,
                                  screenWidth,
                                  icon: Icons.star,
                                  text: chefData.data?.chef?.averageRating ??
                                      '0.0',
                                ),
                                SizedBox(width: screenWidth * 0.02),
                                _buildMetricContainer(context, screenWidth,
                                    icon: Icons.location_on_outlined,
                                    // text:
                                    //     "${_calculateDistance(chefData.data?.chef?.location?.coordinates)} KM",
                                    text: _formatDistance(
                                        (chefData.data?.chef?.distance)
                                            ?.toDouble())),
                                SizedBox(width: screenWidth * 0.018),
                                Container(
                                    width: twenty + twelve,
                                    height: twenty + twelve,
                                    constraints: BoxConstraints(
                                      minHeight: twenty + twelve,
                                      minWidth: twenty + twelve,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(ten),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.05),
                                          blurRadius: 4,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: IconButton(
                                      icon: Image.asset(
                                        'assets/icons/chat.png',
                                        width: twenty,
                                        height: twenty,
                                        color: Color(0xFF1F2122),
                                      ),
                                      onPressed: () {},
                                      padding: EdgeInsets.zero,
                                    )),
                              ],
                            ),
                            SizedBox(height: screenHeight * 0.016),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 0, vertical: 0),
                              child: Wrap(
                                alignment: WrapAlignment.start,
                                spacing: screenWidth * 0.03,
                                runSpacing: screenHeight * 0.005,
                                children:
                                    (chefData.data?.chef?.searchTags ?? [])
                                        .map((tag) {
                                  return Container(
                                    padding: const EdgeInsets.only(right: 12),
                                    child: Text(
                                      tag,
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w400,
                                        color: Color(0xFF414346),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                            SizedBox(height: screenHeight * 0.02),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 0, vertical: 0),
                              child: Text(
                                chefData.data?.chef?.description ?? "",
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFF414346),
                                ),
                                textAlign: TextAlign.justify,
                              ),
                            ),
                            SizedBox(height: screenHeight * 0.017),
                            Row(
                              children: [
                                Image.asset(
                                  'assets/icons/calender_3.png',
                                  width: screenWidth * 0.05,
                                  height: screenWidth * 0.045,
                                  color: const Color(0xFF414346),
                                ),
                                SizedBox(width: screenWidth * 0.04),
                                Text(
                                  _formatOperationTimes(chefData
                                      .data?.chef?.chef?.operationTimes),
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w600,
                                    fontSize: forteen,
                                  ),
                                ),
                                SizedBox(width: screenWidth * 0.04),
                                Text(
                                  _formatOperationDays(
                                      chefData.data?.chef?.chef?.operationDays),
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w400,
                                    fontSize: forteen,
                                    letterSpacing: 1,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: screenHeight * 0.017),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.035),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: screenHeight * 0.04,
                              child: ListView(
                                scrollDirection: Axis.horizontal,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 0),
                                children:
                                    categories.asMap().entries.map((entry) {
                                  final index = entry.key;
                                  final category = entry.value;
                                  final isSelected =
                                      _tabController.index == index;

                                  return GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        _tabController.animateTo(index);
                                        _scrollToCategory(index);
                                      });
                                    },
                                    child: Container(
                                      margin: EdgeInsets.only(
                                          right: screenWidth * 0.02),
                                      padding: EdgeInsets.symmetric(
                                        horizontal: screenWidth * 0.03,
                                        vertical: screenHeight * 0.005,
                                      ),
                                      decoration: BoxDecoration(
                                        color: isSelected
                                            ? const Color(0xFFB9B6AD)
                                            : const Color(0xFFE1DDD5),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Center(
                                        child: Text(
                                          category,
                                          style: TextStyle(
                                            fontFamily: 'Inter',
                                            fontSize: forteen,
                                            fontWeight: FontWeight.w500,
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                            SizedBox(height: screenHeight * 0.033),
                            if (dishData?.featuredList?.isNotEmpty ??
                                false) ...[
                              Container(
                                key: _categoryKeys[0],
                                padding: EdgeInsets.symmetric(
                                    horizontal: 0, vertical: 0),
                                child: Text(
                                  "Featured Items",
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: twenty,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                              ),
                              SizedBox(height: screenHeight * 0.017),
                              SizedBox(
                                height: ten * 26 + sixteen,
                                child: dishData!.featuredList!.isEmpty
                                    ? _buildNoDataMessage()
                                    : ListView.builder(
                                        scrollDirection: Axis.horizontal,
                                        padding: EdgeInsets.only(
                                            right: screenWidth * 0.04),
                                        itemCount:
                                            dishData.featuredList?.length ?? 0,
                                        itemBuilder: (context, index) {
                                          final dish =
                                              dishData.featuredList![index];
                                          return Container(
                                            width: ten * 20 + eighteen,
                                            margin: EdgeInsets.only(
                                                right: screenWidth * 0.04),
                                            child: _buildDishCard(context, {
                                              'name': dish.title,
                                              'image': ServerHelper.imageUrl +
                                                  (dish.photo ?? ''),
                                              'servings': dish.servingSize,
                                              'price': double.tryParse(
                                                  dish.price ?? '0'),
                                              'id': dish.id,
                                              'is_added': dish.isadded ?? false,
                                            }),
                                          );
                                        },
                                      ),
                              ),
                            ],
                            SizedBox(height: screenHeight * 0.01),
                            ...dishData?.categoryBasedList
                                    ?.asMap()
                                    .entries
                                    .map((entry) {
                                  final index = entry.key;
                                  final category = entry.value;
                                  return Column(
                                    key: _categoryKeys[index + 1],
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical: screenHeight * 0.01),
                                        child: Text(
                                          category.category?.name ?? '',
                                          style: TextStyle(
                                            fontFamily: 'Inter',
                                            fontSize: twenty,
                                            fontWeight: FontWeight.w600,
                                            color: Color(0xFF1F2122),
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: screenHeight * 0.01),
                                      if (category.cateringDishList == null ||
                                          category.cateringDishList!.isEmpty)
                                        _buildNoDataMessage()
                                      else
                                        SizedBox(
                                          height: ten * 26 + sixteen,
                                          child: ListView.builder(
                                            scrollDirection: Axis.horizontal,
                                            padding: EdgeInsets.only(
                                                right: screenWidth * 0.04),
                                            itemCount: category
                                                    .cateringDishList?.length ??
                                                0,
                                            itemBuilder: (context, index) {
                                              final dish = category
                                                  .cateringDishList![index];
                                              return Container(
                                                width: ten * 20 + eighteen,
                                                margin: EdgeInsets.only(
                                                    right: screenWidth * 0.04),
                                                child: _buildDishCard(context, {
                                                  'name': dish.title,
                                                  'image':
                                                      ServerHelper.imageUrl +
                                                          (dish.photo ?? ''),
                                                  'servings': dish.servingSize,
                                                  'price': double.tryParse(
                                                      dish.price ?? '0'),
                                                  'id': dish.id,
                                                  'is_added':
                                                      dish.isadded ?? false,
                                                }),
                                              );
                                            },
                                          ),
                                        ),
                                    ],
                                  );
                                }).toList() ??
                                [],
                            SizedBox(height: screenHeight * 0.03),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                bottomNavigationBar: SubtotalBar(
                    amount: double.tryParse(_totalPrice) ?? 0.00,
                    onSubmit: _submit,
                    screenWidth: screenWidth,
                    screenHeight: screenHeight),
              );
            }

            if (chefState is ChefDetailsFailed) {
              return Scaffold(
                body: Center(
                  child: Text(
                    chefState.message,
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 16,
                      color: Color(0xFF414346),
                    ),
                  ),
                ),
              );
            }

            if (chefState is ChefDetailsSuccess) {
              return Scaffold(
                backgroundColor: const Color(0xFFF6F3EC),
                appBar: AppBar(
                  backgroundColor: const Color(0xFFf6f3ec),
                  centerTitle: false,
                  scrolledUnderElevation: 0,
                  titleSpacing: 03,
                  automaticallyImplyLeading: false,
                  leading: IconButton(
                    icon: Image.asset(
                      'assets/icons/backbutton.png',
                      width: twenty + twelve,
                      height: twenty + twelve,
                    ),
                    onPressed: () => Navigator.pop(context),
                  ),
                  title: Text(
                    '${widget.cuisineTitle} for ${widget.peopleCount} People',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: sixteen,
                      color: Colors.black,
                    ),
                  ),
                  elevation: 0,
                ),
                body: const Center(
                  child: CupertinoActivityIndicator(),
                ),
                bottomNavigationBar: SubtotalBar(
                    amount: double.tryParse(_totalPrice) ?? 0.00,
                    onSubmit: _submit,
                    screenWidth: screenWidth,
                    screenHeight: screenHeight),
              );
            }

            if ((state is ListDishesInCateringSuccess &&
                (state.data?.featuredList?.isEmpty ?? true) &&
                (state.data?.categoryBasedList?.every(
                        (cat) => cat.cateringDishList?.isEmpty ?? true) ??
                    true))) {
              return Scaffold(
                backgroundColor: const Color(0xFFF6F3EC),
                appBar: AppBar(
                  backgroundColor: const Color(0xFFf6f3ec),
                  centerTitle: false,
                  scrolledUnderElevation: 0,
                  titleSpacing: 03,
                  automaticallyImplyLeading: false,
                  leading: IconButton(
                    icon: Image.asset(
                      'assets/icons/backbutton.png',
                      width: 32,
                      height: 32,
                    ),
                    onPressed: () => Navigator.pop(context),
                  ),
                  title: Text(
                    '${widget.cuisineTitle} for ${widget.peopleCount} People',
                    style: const TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      fontSize: 24,
                      color: Colors.black,
                    ),
                  ),
                  elevation: 0,
                ),
                body: Center(
                  child: _buildNoDataMessage(),
                ),
              );
            }

            return const Scaffold(
              body: Center(
                child: Text('Something went wrong'),
              ),
            );
          },
        );
      },
    );
  }

  double _calculateDistance(List<double>? coordinates) {
    if (coordinates == null || coordinates.length != 2) {
      return 0.0;
    }

    final currentLat = Initializer().getLatitude ?? 0.0;
    final currentLng = Initializer().getLongitude ?? 0.0;

    final chefLat = coordinates[1];
    final chefLng = coordinates[0];

    const double earthRadius = 6371;

    final dLat = _toRadians(chefLat - currentLat);
    final dLng = _toRadians(chefLng - currentLng);

    final a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_toRadians(currentLat)) *
            cos(_toRadians(chefLat)) *
            sin(dLng / 2) *
            sin(dLng / 2);

    final c = 2 * atan2(sqrt(a), sqrt(1 - a));
    final distance = earthRadius * c;

    return double.parse(distance.toStringAsFixed(1));
  }

  double _toRadians(double degree) {
    return degree * pi / 180;
  }

  void _showUnavailableModal() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                'assets/icons/no_data.png',
                width: 64,
                height: 64,
              ),
              const SizedBox(height: 16),
              const Text(
                'Chef is not available for catering right now',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF1F2122),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
              child: const Text(
                'Cancel',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1F2122),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDishCard(BuildContext context, Map<String, dynamic> dish) {
    const rating = "90";
    return InkWell(
      onTap: () {
        Navigator.of(context)
            .push(
          MaterialPageRoute(
            builder: (context) => BlocProvider(
              create: (context) => CateringBloc(),
              child: CateringDishdetails(
                cateringRequestId: widget.cateringRequestId,
                dishId: dish['id'].toString(),
              ),
            ),
          ),
        )
            .then((_) {
          context.read<HomeBloc>().add(ViewChefDetailsEvent(
                data: {
                  "chef_id": widget.id.toString(),
                  "latitude": Initializer.latitude.toString(),
                  "longitude": Initializer.longitude.toString(),
                },
              ));

          context.read<CateringBloc>().add(
                ListDishesInCatering({
                  "chef_id": widget.id.toString(),
                  "search_keyword": "",
                  "packaging_type_id": widget.packagingTypeId,
                  "cuisine_ids": widget.cuisineIds,
                  "sub_cuisine_ids": widget.subCuisineIds,
                  "local_cuisine_ids": widget.localCuisineIds,
                  "dietary_preference_id": widget.dietaryPreferenceId,
                  "spice_level_id": widget.spiceLevelId,
                  "catering_id": widget.cateringRequestId,
                }),
              );

          context.read<CateringBloc>().add(
                ViewCateringRequest(widget.cateringRequestId),
              );
        });
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(screenWidth * 0.04),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.vertical(
                  top: Radius.circular(screenWidth * 0.04)),
              child: Image.network(
                dish['image'],
                height: ten * 15 + twelve,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: ten * 15 + twelve,
                    color: Colors.grey[200],
                    child: const Center(child: Icon(Icons.error)),
                  );
                },
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                  top: screenWidth * 0.025,
                  bottom: screenWidth * 0.015,
                  right: screenWidth * 0.04,
                  left: screenWidth * 0.04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    dish['name'] ?? '',
                    style: TextStyle(
                      fontSize: forteen,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1F2122),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: screenHeight * 0.015),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Wrap(
                              spacing: screenWidth * 0.02,
                              runSpacing: screenHeight * 0.01,
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: screenWidth * 0.015,
                                    vertical: screenWidth * 0.005,
                                  ),
                                  decoration: BoxDecoration(
                                    color:
                                        const Color.fromRGBO(225, 227, 230, 1),
                                    borderRadius: BorderRadius.circular(
                                        screenWidth * 0.03),
                                  ),
                                  child: Text(
                                    "${dish['servings']} Servings",
                                    style: TextStyle(
                                      fontSize: ten,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter',
                                      letterSpacing: 0.2,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: screenHeight * 0.02),
                            Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: '\u0024',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: twelve,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xFF1F2122),
                                    ),
                                  ),
                                  TextSpan(
                                    text: (dish['price'] ?? 0.0)
                                        .toStringAsFixed(2),
                                    style: TextStyle(
                                      fontFamily: 'Roboto',
                                      fontSize: twelve,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 4),
                          ],
                        ),
                      ),
                      Container(
                        alignment: Alignment.bottomRight,
                        child: (dish['is_added'] == true)
                            ? Container(
                                width: forteen + ten,
                                height: forteen + ten,
                                decoration: BoxDecoration(
                                  color: const Color.fromARGB(255, 13, 14, 13),
                                  borderRadius: BorderRadius.circular(
                                      (forteen + ten) / 2),
                                ),
                                child: Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: forteen,
                                ),
                              )
                            : Image.asset(
                                'assets/icons/add.png',
                                width: forteen + ten,
                                height: forteen + ten,
                              ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDataMessage() {
    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              'assets/icons/no_data.png',
              width: screenWidth * 0.5,
              height: screenWidth * 0.5,
            ),
            SizedBox(height: screenHeight * 0.02),
            Text(
              'No dishes available',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: screenWidth * 0.045,
                fontWeight: FontWeight.w500,
                color: Color(0xFF1F2122),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDistance(double? distance) {
    if (distance == null) return 'Unknown';
    final kilometers = distance / 1000;
    return '${kilometers.toStringAsFixed(1)} KM';
  }
}
