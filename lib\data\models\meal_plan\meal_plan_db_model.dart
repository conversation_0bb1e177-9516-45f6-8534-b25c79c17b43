class MealPlanDbModel {
  final int mealPlanId;
  final String date;
  final int chefId;
  final int menuItemId;
  final String chefName;
  final String chefImage;
  final String itemName;
  final String itemImage;
  final double price;
  final String servings;

  MealPlanDbModel({
    required this.mealPlanId,
    required this.date,
    required this.chefId,
    required this.menuItemId,
    required this.chefName,
    required this.chefImage,
    required this.itemName,
    required this.itemImage,
    required this.price,
    required this.servings,
  });

  Map<String, dynamic> toMap() {
    return {
      'meal_plan_id': mealPlanId,
      'date': date,
      'chef_id': chefId,
      'menu_item_id': menuItemId,
      'chef_name': chefName,
      'chef_image': chefImage,
      'item_name': itemName,
      'item_image': itemImage,
      'price': price,
      'servings': servings,
    };
  }

  factory MealPlanDbModel.fromMap(Map<String, dynamic> map) {
    return MealPlanDbModel(
      mealPlanId: map['meal_plan_id'],
      date: map['date'],
      chefId: map['chef_id'],
      menuItemId: map['menu_item_id'],
      chefName: map['chef_name'],
      chefImage: map['chef_image'],
      itemName: map['item_name'],
      itemImage: map['item_image'],
      price: map['price'],
      servings: map['servings'],
    );
  }
}
