class MealPlanDetailsModel {
  bool? status;
  String? message;
  int? statusCode;
  MealPlanDetailsData? data;

  MealPlanDetailsModel({this.status, this.message, this.statusCode, this.data});

  MealPlanDetailsModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null
        ? new MealPlanDetailsData.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class MealPlanDetailsData {
  MealPlan? mealPlan;

  MealPlanDetailsData({this.mealPlan});

  MealPlanDetailsData.fromJson(Map<String, dynamic> json) {
    mealPlan = json['meal_plan'] != null
        ? new MealPlan.fromJson(json['meal_plan'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.mealPlan != null) {
      data['meal_plan'] = this.mealPlan!.toJson();
    }
    return data;
  }
}

class MealPlan {
  int? id;
  int? customerId;
  String? orderNumber;
  int? mealPlanDuration;
  String? startDate;
  String? endDate;
  int? timeSlotId;
  int? servingSizeId;
  int? dishesPerDay;
  String? mealSelectionType;
  int? dietaryPreferenceId;
  int? spiceLevelId;
  String? comments;
  String? personalizationTags;
  int? dropOffOptionId;
  String? dropOffInstructions;
  int? deliveryTimeId;
  String? addressText;
  Location? location;
  String? buildingType;
  String? houseNumber;
  String? landmark;
  String? subtotal;
  String? deliveryFee;
  String? discount;
  String? walletCredits;
  String? taxesAndFees;
  String? total;
  String? status;
  String? paymentStatus;
  String? createdAt;
  TimeSlot? timeSlot;
  ServingSize? servingSize;
  DietaryPreference? dietaryPreference;
  DietaryPreference? spiceLevel;
  List<MealPlanCuisines>? mealPlanCuisines;
  List<MealPlanSubCuisines>? mealPlanSubCuisines;
  List<MealPlanLocalCuisines>? mealPlanLocalCuisines;
  DietaryPreference? dropOffOption;
  DeliveryTime? deliveryTime;
  List<MealPlanDays>? mealPlanDays;

  MealPlan(
      {this.id,
      this.customerId,
      this.orderNumber,
      this.mealPlanDuration,
      this.startDate,
      this.endDate,
      this.timeSlotId,
      this.servingSizeId,
      this.dishesPerDay,
      this.mealSelectionType,
      this.dietaryPreferenceId,
      this.spiceLevelId,
      this.comments,
      this.personalizationTags,
      this.dropOffOptionId,
      this.dropOffInstructions,
      this.deliveryTimeId,
      this.addressText,
      this.location,
      this.buildingType,
      this.houseNumber,
      this.landmark,
      this.subtotal,
      this.deliveryFee,
      this.discount,
      this.walletCredits,
      this.taxesAndFees,
      this.total,
      this.status,
      this.paymentStatus,
      this.createdAt,
      this.timeSlot,
      this.servingSize,
      this.dietaryPreference,
      this.spiceLevel,
      this.mealPlanCuisines,
      this.mealPlanSubCuisines,
      this.mealPlanLocalCuisines,
      this.dropOffOption,
      this.deliveryTime,
      this.mealPlanDays});

  MealPlan.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    customerId = json['customer_id'];
    orderNumber = json['order_number'];
    mealPlanDuration = json['meal_plan_duration'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    timeSlotId = json['time_slot_id'];
    servingSizeId = json['serving_size_id'];
    dishesPerDay = json['dishes_per_day'];
    mealSelectionType = json['meal_selection_type'];
    dietaryPreferenceId = json['dietary_preference_id'];
    spiceLevelId = json['spice_level_id'];
    comments = json['comments'];
    personalizationTags = json['personalization_tags'];
    dropOffOptionId = json['drop_off_option_id'];
    dropOffInstructions = json['drop_off_instructions'];
    deliveryTimeId = json['delivery_time_id'];
    addressText = json['address_text'];
    location = json['location'] != null
        ? new Location.fromJson(json['location'])
        : null;
    buildingType = json['building_type'];
    houseNumber = json['house_number'];
    landmark = json['landmark'];
    subtotal = json['subtotal'];
    deliveryFee = json['delivery_fee'];
    discount = json['discount'];
    walletCredits = json['wallet_credits'];
    taxesAndFees = json['taxes_and_fees'];
    total = json['total'];
    status = json['status'];
    paymentStatus = json['payment_status'];
    createdAt = json['created_at'];
    timeSlot = json['timeSlot'] != null
        ? new TimeSlot.fromJson(json['timeSlot'])
        : null;
    servingSize = json['servingSize'] != null
        ? new ServingSize.fromJson(json['servingSize'])
        : null;
    dietaryPreference = json['dietaryPreference'] != null
        ? new DietaryPreference.fromJson(json['dietaryPreference'])
        : null;
    spiceLevel = json['spiceLevel'] != null
        ? new DietaryPreference.fromJson(json['spiceLevel'])
        : null;
    if (json['mealPlanCuisines'] != null) {
      mealPlanCuisines = <MealPlanCuisines>[];
      json['mealPlanCuisines'].forEach((v) {
        mealPlanCuisines!.add(new MealPlanCuisines.fromJson(v));
      });
    }
    if (json['mealPlanSubCuisines'] != null) {
      mealPlanSubCuisines = <MealPlanSubCuisines>[];
      json['mealPlanSubCuisines'].forEach((v) {
        mealPlanSubCuisines!.add(new MealPlanSubCuisines.fromJson(v));
      });
    }
    if (json['mealPlanLocalCuisines'] != null) {
      mealPlanLocalCuisines = <MealPlanLocalCuisines>[];
      json['mealPlanLocalCuisines'].forEach((v) {
        mealPlanLocalCuisines!.add(new MealPlanLocalCuisines.fromJson(v));
      });
    }
    dropOffOption = json['dropOffOption'] != null
        ? new DietaryPreference.fromJson(json['dropOffOption'])
        : null;
    deliveryTime = json['deliveryTime'] != null
        ? new DeliveryTime.fromJson(json['deliveryTime'])
        : null;
    if (json['mealPlanDays'] != null) {
      mealPlanDays = <MealPlanDays>[];
      json['mealPlanDays'].forEach((v) {
        mealPlanDays!.add(new MealPlanDays.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['customer_id'] = this.customerId;
    data['order_number'] = this.orderNumber;
    data['meal_plan_duration'] = this.mealPlanDuration;
    data['start_date'] = this.startDate;
    data['end_date'] = this.endDate;
    data['time_slot_id'] = this.timeSlotId;
    data['serving_size_id'] = this.servingSizeId;
    data['dishes_per_day'] = this.dishesPerDay;
    data['meal_selection_type'] = this.mealSelectionType;
    data['dietary_preference_id'] = this.dietaryPreferenceId;
    data['spice_level_id'] = this.spiceLevelId;
    data['comments'] = this.comments;
    data['personalization_tags'] = this.personalizationTags;
    data['drop_off_option_id'] = this.dropOffOptionId;
    data['drop_off_instructions'] = this.dropOffInstructions;
    data['delivery_time_id'] = this.deliveryTimeId;
    data['address_text'] = this.addressText;
    if (this.location != null) {
      data['location'] = this.location!.toJson();
    }
    data['building_type'] = this.buildingType;
    data['house_number'] = this.houseNumber;
    data['landmark'] = this.landmark;
    data['subtotal'] = this.subtotal;
    data['delivery_fee'] = this.deliveryFee;
    data['discount'] = this.discount;
    data['wallet_credits'] = this.walletCredits;
    data['taxes_and_fees'] = this.taxesAndFees;
    data['total'] = this.total;
    data['status'] = this.status;
    data['payment_status'] = this.paymentStatus;
    data['created_at'] = this.createdAt;
    if (this.timeSlot != null) {
      data['timeSlot'] = this.timeSlot!.toJson();
    }
    if (this.servingSize != null) {
      data['servingSize'] = this.servingSize!.toJson();
    }
    if (this.dietaryPreference != null) {
      data['dietaryPreference'] = this.dietaryPreference!.toJson();
    }
    if (this.spiceLevel != null) {
      data['spiceLevel'] = this.spiceLevel!.toJson();
    }
    if (this.mealPlanCuisines != null) {
      data['mealPlanCuisines'] =
          this.mealPlanCuisines!.map((v) => v.toJson()).toList();
    }
    if (this.mealPlanSubCuisines != null) {
      data['mealPlanSubCuisines'] =
          this.mealPlanSubCuisines!.map((v) => v.toJson()).toList();
    }
    if (this.mealPlanLocalCuisines != null) {
      data['mealPlanLocalCuisines'] =
          this.mealPlanLocalCuisines!.map((v) => v.toJson()).toList();
    }
    if (this.dropOffOption != null) {
      data['dropOffOption'] = this.dropOffOption!.toJson();
    }
    if (this.deliveryTime != null) {
      data['deliveryTime'] = this.deliveryTime!.toJson();
    }
    if (this.mealPlanDays != null) {
      data['mealPlanDays'] = this.mealPlanDays!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Location {
  Crs? crs;
  String? type;
  List<double>? coordinates;

  Location({this.crs, this.type, this.coordinates});

  Location.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? new Crs.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates'].cast<double>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.crs != null) {
      data['crs'] = this.crs!.toJson();
    }
    data['type'] = this.type;
    data['coordinates'] = this.coordinates;
    return data;
  }
}

class Crs {
  String? type;
  Properties? properties;

  Crs({this.type, this.properties});

  Crs.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'] != null
        ? new Properties.fromJson(json['properties'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    if (this.properties != null) {
      data['properties'] = this.properties!.toJson();
    }
    return data;
  }
}

class Properties {
  String? name;

  Properties({this.name});

  Properties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    return data;
  }
}

class TimeSlot {
  int? id;
  String? startTime;
  String? endTime;

  TimeSlot({this.id, this.startTime, this.endTime});

  TimeSlot.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['start_time'] = this.startTime;
    data['end_time'] = this.endTime;
    return data;
  }
}

class ServingSize {
  int? id;
  String? title;
  int? serves;

  ServingSize({this.id, this.title, this.serves});

  ServingSize.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    serves = json['serves'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['serves'] = this.serves;
    return data;
  }
}

class DietaryPreference {
  int? id;
  String? name;

  DietaryPreference({this.id, this.name});

  DietaryPreference.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}

class MealPlanCuisines {
  int? cuisineId;
  DietaryPreference? cuisine;

  MealPlanCuisines({this.cuisineId, this.cuisine});

  MealPlanCuisines.fromJson(Map<String, dynamic> json) {
    cuisineId = json['cuisine_id'];
    cuisine = json['cuisine'] != null
        ? new DietaryPreference.fromJson(json['cuisine'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['cuisine_id'] = this.cuisineId;
    if (this.cuisine != null) {
      data['cuisine'] = this.cuisine!.toJson();
    }
    return data;
  }
}

class MealPlanSubCuisines {
  int? subcuisineId;
  DietaryPreference? subcuisine;

  MealPlanSubCuisines({this.subcuisineId, this.subcuisine});

  MealPlanSubCuisines.fromJson(Map<String, dynamic> json) {
    subcuisineId = json['subcuisine_id'];
    subcuisine = json['subcuisine'] != null
        ? new DietaryPreference.fromJson(json['subcuisine'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['subcuisine_id'] = this.subcuisineId;
    if (this.subcuisine != null) {
      data['subcuisine'] = this.subcuisine!.toJson();
    }
    return data;
  }
}

class MealPlanLocalCuisines {
  int? localCuisineId;
  DietaryPreference? localCuisine;

  MealPlanLocalCuisines({this.localCuisineId, this.localCuisine});

  MealPlanLocalCuisines.fromJson(Map<String, dynamic> json) {
    localCuisineId = json['local_cuisine_id'];
    localCuisine = json['local_cuisine'] != null
        ? new DietaryPreference.fromJson(json['local_cuisine'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['local_cuisine_id'] = this.localCuisineId;
    if (this.localCuisine != null) {
      data['local_cuisine'] = this.localCuisine!.toJson();
    }
    return data;
  }
}

class DeliveryTime {
  int? id;
  String? name;
  String? description;
  String? cost;

  DeliveryTime({this.id, this.name, this.description, this.cost});

  DeliveryTime.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    cost = json['cost'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['description'] = this.description;
    data['cost'] = this.cost;
    return data;
  }
}

class MealPlanDays {
  int? id;
  int? mealPlanDay;
  String? date;
  String? dayOfWeek;
  String? subtotal;
  String? deliveryFee;
  String? discount;
  String? walletCredits;
  String? taxesAndFees;
  String? dayTotal;
  String? status;
  String? createdAt;
  Chef? chef;
  List<Items>? items;
  List<Timelines>? timelines;

  MealPlanDays({
    this.id,
    this.mealPlanDay,
    this.date,
    this.dayOfWeek,
    this.subtotal,
    this.deliveryFee,
    this.discount,
    this.walletCredits,
    this.taxesAndFees,
    this.dayTotal,
    this.status,
    this.createdAt,
    this.chef,
    this.items,
    this.timelines,
  });

  MealPlanDays.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    mealPlanDay = json['meal_plan_day'];
    date = json['date'];
    dayOfWeek = json['day_of_week'];
    subtotal = json['subtotal'];
    deliveryFee = json['delivery_fee'];
    discount = json['discount'];
    walletCredits = json['wallet_credits'];
    taxesAndFees = json['taxes_and_fees'];
    dayTotal = json['day_total'];
    status = json['status'];
    createdAt = json['created_at'];
    chef = json['chef'] != null ? Chef.fromJson(json['chef']) : null;
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(Items.fromJson(v));
      });
    }
    if (json['timelines'] != null) {
      timelines = <Timelines>[];
      json['timelines'].forEach((v) {
        timelines!.add(Timelines.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['meal_plan_day'] = mealPlanDay;
    data['date'] = date;
    data['day_of_week'] = dayOfWeek;
    data['subtotal'] = subtotal;
    data['delivery_fee'] = deliveryFee;
    data['discount'] = discount;
    data['wallet_credits'] = walletCredits;
    data['taxes_and_fees'] = taxesAndFees;
    data['day_total'] = dayTotal;
    data['status'] = status;
    data['created_at'] = createdAt;
    if (chef != null) {
      data['chef'] = chef!.toJson();
    }
    if (items != null) {
      data['items'] = items!.map((v) => v.toJson()).toList();
    }
    if (timelines != null) {
      data['timelines'] = timelines!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Chef {
  int? id;
  String? firstName;
  String? lastName;
  Profile? profile;

  Chef({this.id, this.firstName, this.lastName, this.profile});

  Chef.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    profile =
        json['profile'] != null ? new Profile.fromJson(json['profile']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['first_name'] = this.firstName;
    data['last_name'] = this.lastName;
    if (this.profile != null) {
      data['profile'] = this.profile!.toJson();
    }
    return data;
  }
}

class Profile {
  int? id;
  String? profilePhoto;
  List<String>? searchTags;

  Profile({this.id, this.profilePhoto, this.searchTags});

  Profile.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    profilePhoto = json['profile_photo'];
    searchTags = json['search_tags'] != null
        ? List<String>.from(json['search_tags'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['profile_photo'] = this.profilePhoto;
    data['search_tags'] = this.searchTags;
    return data;
  }
}

class Items {
  int? id;
  int? chefMenuItemId;
  int? quantity;
  String? price;
  DietaryPreference? menuItem;

  Items(
      {this.id, this.chefMenuItemId, this.quantity, this.price, this.menuItem});

  Items.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    chefMenuItemId = json['chef_menu_item_id'];
    quantity = json['quantity'];
    price = json['price'];
    menuItem = json['menuItem'] != null
        ? new DietaryPreference.fromJson(json['menuItem'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['chef_menu_item_id'] = this.chefMenuItemId;
    data['quantity'] = this.quantity;
    data['price'] = this.price;
    if (this.menuItem != null) {
      data['menuItem'] = this.menuItem!.toJson();
    }
    return data;
  }
}

class Timelines {
  int? action;
  String? remarks;
  String? createdAt;

  Timelines({this.action, this.remarks, this.createdAt});

  Timelines.fromJson(Map<String, dynamic> json) {
    action = json['action'];
    remarks = json['remarks'];
    createdAt = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['action'] = this.action;
    data['remarks'] = this.remarks;
    data['created_at'] = this.createdAt;
    return data;
  }
}
