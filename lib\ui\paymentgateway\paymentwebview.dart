import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class PaymentWebView extends StatefulWidget {
  final String paymentUrl;
  final VoidCallback onPaymentComplete;
  final VoidCallback onPaymentCancelled; // New callback for cancellation

  const PaymentWebView({
    super.key,
    required this.paymentUrl,
    required this.onPaymentComplete,
    required this.onPaymentCancelled,
  });

  @override
  State<PaymentWebView> createState() => _PaymentWebViewState();
}

class _PaymentWebViewState extends State<PaymentWebView> {
  WebViewController? _controller;
  bool _isLoading = true;
  bool _hasError = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    log('Payment URL: ${widget.paymentUrl}');
    _initializeWebView();
  }

  void _initializeWebView() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      await Future.delayed(const Duration(milliseconds: 800));

      if (!mounted) return;

      _controller = WebViewController();

      await _controller!.setJavaScriptMode(JavaScriptMode.unrestricted);
      await _controller!.setBackgroundColor(const Color(0x00000000));

      await _controller!.setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            log('Page started loading: $url');
            if (mounted) {
              setState(() {
                _isLoading = true;
                _hasError = false;
              });
            }
          },
          onPageFinished: (String url) {
            log('Page finished loading: $url');
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
            }
          },
          onNavigationRequest: (NavigationRequest request) {
            log('Navigation request: ${request.url}');
            if (_isSuccessUrl(request.url)) {
              Navigator.of(context).pop();
              widget.onPaymentComplete();
              return NavigationDecision.prevent;
            } else if (_isCancelUrl(request.url)) {
              Navigator.of(context).pop();
              widget.onPaymentCancelled();
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
          onUrlChange: (UrlChange change) {
            final url = change.url ?? '';
            log('URL changed: $url');
            if (_isSuccessUrl(url)) {
              Navigator.of(context).pop();
              widget.onPaymentComplete();
            } else if (_isCancelUrl(url)) {
              Navigator.of(context).pop();
              widget.onPaymentCancelled();
            }
          },
          onWebResourceError: (WebResourceError error) {
            log('WebView error: ${error.description}');
            if (error.errorType == WebResourceErrorType.hostLookup ||
                error.errorType == WebResourceErrorType.timeout ||
                error.errorType == WebResourceErrorType.connect) {
              if (mounted) {
                setState(() {
                  _hasError = true;
                  _isLoading = false;
                });
              }
            }
          },
        ),
      );

      await Future.delayed(const Duration(milliseconds: 300));

      if (mounted && _controller != null) {
        setState(() {
          _isInitialized = true;
        });
        await _controller!.loadRequest(Uri.parse(widget.paymentUrl));
      }
    } catch (e) {
      log('WebView initialization error: $e');
      if (mounted) {
        setState(() {
          _hasError = true;
          _isLoading = false;
        });
      }
    }
  }

  bool _isSuccessUrl(String url) {
    return url.contains('payment_success') ||
        url.contains('success') ||
        url.contains('success=true') ||
        url.contains('status=success') ||
        url.contains('payment_status=completed');
  }

  bool _isCancelUrl(String url) {
    return url.contains('payment_cancel') ||
        url.contains('cancel') ||
        url.contains('failure') ||
        url.contains('status=failed') ||
        url.contains('status=cancelled');
       
  }

  @override
  void dispose() {
    _controller = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor:  Colors.white,
         centerTitle: false,
            title: const Text(
              "Payment",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1F2122),
              ),
            ),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Color(0xFF1F2122)),
              onPressed: () => Navigator.pop(context),
            ),
      ),
      body: Stack(
        children: [
          if (_hasError)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  const Text('Failed to load payment page'),
                  const SizedBox(height: 8),
                  Text(
                    'Please check your internet connection',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      _initializeWebView();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            )
          else if (_isInitialized && _controller != null)
            WebViewWidget(controller: _controller!)
          else
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: Color(0xFFCFD0D0),
                  ),
                  SizedBox(height: 16),
                  Text('Initializing payment...'),
                ],
              ),
            ),
          if (_isLoading && !_hasError && _isInitialized)
            const Center(
              child: CircularProgressIndicator(
                color: Color(0xFFCFD0D0),
              ),
            ),
        ],
      ),
    );
  }
}