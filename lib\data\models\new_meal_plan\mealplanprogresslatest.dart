class MealPlanProgressLatestModel {
  bool? status;
  MealPlanProgressLatestData? data;
  int? statusCode;

  MealPlanProgressLatestModel({this.status, this.data, this.statusCode});

  MealPlanProgressLatestModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null
        ? MealPlanProgressLatestData.fromJson(json['data'])
        : null;
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['status_code'] = statusCode;
    return data;
  }
}

class MealPlanProgressLatestData {
  int? id;
  int? stepProgress;
  String? createdAt;
  int? customerId;
  int? mealPlanDuration;
  String? endDate;
  String? startDate;
  int? timeSlotId;
  int? servingSizeId;
  int? dishesPerDay;
  String? mealSelectionType;
  int? dietaryPreferenceId;
  int? spiceLevelId;
  int? dropOffOptionId;
  String? dropOffInstructions;
  num? deliveryTimeId;
  num? subtotal;
  num? deliveryFee;
  num? discount;
  num? walletCredits;
  num? taxesAndFees;
  num? total;
  String? status;
  List<String>? selectedWeekdays;
  TimeSlot? timeSlot;
  ServingSize? servingSize;
  DietaryPreference? dietaryPreference;
  SpiceLevel? spiceLevel;
  List<Cuisine>? cuisines;
  List<Subcuisine>? subcuisines;
  List<Localcuisine>? localcuisines;
  List<MealPlanDay>? mealPlanDays;
  num? totalDiscount;
  // Added missing fields from JSON
  num? taxPercentage;
  num? packagingFee;
  num? employmentFee;
  num? transactionFee;
  num? walletBalance;
  String? errorMessage;
  List<MealPlanDay>? personalizedDays;

  MealPlanProgressLatestData({
    this.id,
    this.stepProgress,
    this.createdAt,
    this.customerId,
    this.mealPlanDuration,
    this.endDate,
    this.startDate,
    this.timeSlotId,
    this.servingSizeId,
    this.dishesPerDay,
    this.mealSelectionType,
    this.dietaryPreferenceId,
    this.spiceLevelId,
    this.dropOffOptionId,
    this.dropOffInstructions,
    this.deliveryTimeId,
    this.subtotal,
    this.deliveryFee,
    this.discount,
    this.walletCredits,
    this.taxesAndFees,
    this.total,
    this.status,
    this.selectedWeekdays,
    this.timeSlot,
    this.servingSize,
    this.dietaryPreference,
    this.spiceLevel,
    this.cuisines,
    this.subcuisines,
    this.localcuisines,
    this.mealPlanDays,
    this.totalDiscount,
    this.taxPercentage,
    this.packagingFee,
    this.employmentFee,
    this.transactionFee,
    this.walletBalance,
    this.errorMessage,
    this.personalizedDays,
  });

  MealPlanProgressLatestData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    stepProgress = json['step_progress'];
    createdAt = json['created_at'];
    customerId = json['customer_id'];
    mealPlanDuration = json['meal_plan_duration'];
    endDate = json['end_date'];
    startDate = json['start_date'];
    timeSlotId = json['time_slot_id'];
    servingSizeId = json['serving_size_id'];
    dishesPerDay = json['dishes_per_day'];
    mealSelectionType = json['meal_selection_type'];
    dietaryPreferenceId = json['dietary_preference_id'];
    spiceLevelId = json['spice_level_id'];
    dropOffOptionId = json['drop_off_option_id'];
    dropOffInstructions = json['drop_off_instructions'];
    deliveryTimeId = json['delivery_time_id'];
    subtotal = json['subtotal'];
    deliveryFee = json['delivery_fee'];
    discount = json['discount'];
    walletCredits = json['wallet_credits'];
    taxesAndFees = json['taxes_and_fees'];
    total = json['total'];
    status = json['status'];
    selectedWeekdays = json['selected_weekdays']?.cast<String>();
    timeSlot =
        json['timeSlot'] != null ? TimeSlot.fromJson(json['timeSlot']) : null;
    servingSize = json['servingSize'] != null
        ? ServingSize.fromJson(json['servingSize'])
        : null;
    dietaryPreference = json['dietaryPreference'] != null
        ? DietaryPreference.fromJson(json['dietaryPreference'])
        : null;
    spiceLevel = json['spiceLevel'] != null
        ? SpiceLevel.fromJson(json['spiceLevel'])
        : null;

    if (json['cuisines'] != null) {
      cuisines = <Cuisine>[];
      json['cuisines'].forEach((v) {
        cuisines!.add(Cuisine.fromJson(v));
      });
    }

    if (json['subcuisines'] != null) {
      subcuisines = <Subcuisine>[];
      json['subcuisines'].forEach((v) {
        subcuisines!.add(Subcuisine.fromJson(v));
      });
    }

    if (json['localcuisines'] != null) {
      localcuisines = <Localcuisine>[];
      json['localcuisines'].forEach((v) {
        localcuisines!.add(Localcuisine.fromJson(v));
      });
    }

    if (json['meal_plan_days'] != null) {
      mealPlanDays = <MealPlanDay>[];
      json['meal_plan_days'].forEach((v) {
        mealPlanDays!.add(MealPlanDay.fromJson(v));
      });
    }

    if (json['personalized_days'] != null) {
      personalizedDays = <MealPlanDay>[];
      json['personalized_days'].forEach((v) {
        personalizedDays!.add(MealPlanDay.fromJson(v));
      });
    }

    totalDiscount = json['total_discount'];
    taxPercentage = json['tax_percentage'];
    packagingFee = json['packaging_fee'];
    employmentFee = json['employment_fee'];
    transactionFee = json['transaction_fee'];
    walletBalance = json['wallet_balance'];
    errorMessage = json['error_message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['step_progress'] = stepProgress;
    data['created_at'] = createdAt;
    data['customer_id'] = customerId;
    data['meal_plan_duration'] = mealPlanDuration;
    data['end_date'] = endDate;
    data['start_date'] = startDate;
    data['time_slot_id'] = timeSlotId;
    data['serving_size_id'] = servingSizeId;
    data['dishes_per_day'] = dishesPerDay;
    data['meal_selection_type'] = mealSelectionType;
    data['dietary_preference_id'] = dietaryPreferenceId;
    data['spice_level_id'] = spiceLevelId;
    data['drop_off_option_id'] = dropOffOptionId;
    data['drop_off_instructions'] = dropOffInstructions;
    data['delivery_time_id'] = deliveryTimeId;
    data['subtotal'] = subtotal;
    data['delivery_fee'] = deliveryFee;
    data['discount'] = discount;
    data['wallet_credits'] = walletCredits;
    data['taxes_and_fees'] = taxesAndFees;
    data['total'] = total;
    data['status'] = status;
    data['selected_weekdays'] = selectedWeekdays;
    if (timeSlot != null) {
      data['timeSlot'] = timeSlot!.toJson();
    }
    if (servingSize != null) {
      data['servingSize'] = servingSize!.toJson();
    }
    if (dietaryPreference != null) {
      data['dietaryPreference'] = dietaryPreference!.toJson();
    }
    if (spiceLevel != null) {
      data['spiceLevel'] = spiceLevel!.toJson();
    }
    if (cuisines != null) {
      data['cuisines'] = cuisines!.map((v) => v.toJson()).toList();
    }
    if (subcuisines != null) {
      data['subcuisines'] = subcuisines!.map((v) => v.toJson()).toList();
    }
    if (localcuisines != null) {
      data['localcuisines'] = localcuisines!.map((v) => v.toJson()).toList();
    }
    if (mealPlanDays != null) {
      data['meal_plan_days'] = mealPlanDays!.map((v) => v.toJson()).toList();
    }
    if (personalizedDays != null) {
      data['personalized_days'] =
          personalizedDays!.map((v) => v.toJson()).toList();
    }
    data['total_discount'] = totalDiscount;
    data['tax_percentage'] = taxPercentage;
    data['packaging_fee'] = packagingFee;
    data['employment_fee'] = employmentFee;
    data['transaction_fee'] = transactionFee;
    data['wallet_balance'] = walletBalance;
    data['error_message'] = errorMessage;
    return data;
  }
}

class TimeSlot {
  int? id;
  String? startTime;
  String? endTime;

  TimeSlot({this.id, this.startTime, this.endTime});

  TimeSlot.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    return data;
  }
}

class ServingSize {
  int? id;
  String? title;
  int? serves;

  ServingSize({this.id, this.title, this.serves});

  ServingSize.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    serves = json['serves'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['serves'] = serves;
    return data;
  }
}

class DietaryPreference {
  int? id;
  String? name;

  DietaryPreference({this.id, this.name});

  DietaryPreference.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class SpiceLevel {
  int? id;
  String? name;

  SpiceLevel({this.id, this.name});

  SpiceLevel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class Cuisine {
  int? id;
  String? name;

  Cuisine({this.id, this.name});

  Cuisine.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class Subcuisine {
  int? id;
  String? name;

  Subcuisine({this.id, this.name});

  Subcuisine.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class Localcuisine {
  int? id;
  String? name;

  Localcuisine({this.id, this.name});

  Localcuisine.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class MealPlanDay {
  int? id;
  String? date;
  String? dayOfWeek;
  Chef? chef;
  int? dayNumber;
  num? dayTotal;
  num? price; // Added for CURATED meal plans
  num? discount;
  List<MealPlanItem>? items;
  // Additional fields from JSON
  num? deliveryFee;
  num? distance;
  int? duration;
  String? durationText;
  num? taxPercentage;
  num? tax;

  MealPlanDay({
    this.id,
    this.date,
    this.dayOfWeek,
    this.chef,
    this.dayNumber,
    this.dayTotal,
    this.price,
    this.discount,
    this.items,
    this.deliveryFee,
    this.distance,
    this.duration,
    this.durationText,
    this.taxPercentage,
    this.tax,
  });

  MealPlanDay.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    date = json['date'];
    dayOfWeek = json['day_of_week'];
    chef = json['chef'] != null ? Chef.fromJson(json['chef']) : null;
    dayNumber = json['day_number'];
    dayTotal = json['day_total'] is String
        ? num.tryParse(json['day_total'])
        : json['day_total'];
    price =
        json['price'] is String ? num.tryParse(json['price']) : json['price'];
    discount = json['discount'] is String
        ? int.tryParse(json['discount'])
        : json['discount'];
    deliveryFee = json['delivery_fee'];
    distance = json['distance'];
    duration = json['duration'];
    durationText = json['duration_text'];
    taxPercentage = json['tax_percentage'];
    tax = json['tax'];

    if (json['items'] != null) {
      items = <MealPlanItem>[];
      json['items'].forEach((v) {
        items!.add(MealPlanItem.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['date'] = date;
    data['day_of_week'] = dayOfWeek;
    if (chef != null) {
      data['chef'] = chef!.toJson();
    }
    data['day_number'] = dayNumber;
    data['day_total'] = dayTotal;
    data['price'] = price;
    data['discount'] = discount;
    data['delivery_fee'] = deliveryFee;
    data['distance'] = distance;
    data['duration'] = duration;
    data['duration_text'] = durationText;
    data['tax_percentage'] = taxPercentage;
    data['tax'] = tax;
    if (items != null) {
      data['items'] = items!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class MealPlanItem {
  int? id;
  int? chefMenuItemId;
  int? quantity;
  num? price;
  MenuItem? menuItem;

  MealPlanItem({
    this.id,
    this.chefMenuItemId,
    this.quantity,
    this.price,
    this.menuItem,
  });

  MealPlanItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    chefMenuItemId = json['chef_menu_item_id'];
    quantity = json['quantity'];
    price =
        json['price'] is String ? num.tryParse(json['price']) : json['price'];
    menuItem =
        json['menu_item'] != null ? MenuItem.fromJson(json['menu_item']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['chef_menu_item_id'] = chefMenuItemId;
    data['quantity'] = quantity;
    data['price'] = price;
    if (menuItem != null) {
      data['menu_item'] = menuItem!.toJson();
    }
    return data;
  }
}

class MenuItem {
  int? id;
  String? name;
  String? photo;

  MenuItem({this.id, this.name, this.photo});

  MenuItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['photo'] = photo;
    return data;
  }
}

class Chef {
  int? id;
  String? firstName;
  String? lastName;
  String? profilePhoto;
  List<String>? searchTags;
  num? averageRating;
  int? totalRatings;
  int? ratingPercentage;
  Location? location;
  num? deliveryRadius;

  Chef({
    this.id,
    this.firstName,
    this.lastName,
    this.profilePhoto,
    this.searchTags,
    this.averageRating,
    this.totalRatings,
    this.ratingPercentage,
    this.location,
    this.deliveryRadius,
  });

  Chef.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    profilePhoto = json['profile_photo'];
    searchTags = json['search_tags']?.cast<String>();
    averageRating = json['average_rating'] is String
        ? num.tryParse(json['average_rating'])
        : json['average_rating'];
    totalRatings = json['total_ratings'];
    ratingPercentage = json['rating_percentage'];
    location =
        json['location'] != null ? Location.fromJson(json['location']) : null;
    deliveryRadius = json['delivery_radius'] is String
        ? num.tryParse(json['delivery_radius'])
        : json['delivery_radius'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    data['profile_photo'] = profilePhoto;
    data['search_tags'] = searchTags;
    data['average_rating'] = averageRating;
    data['total_ratings'] = totalRatings;
    data['rating_percentage'] = ratingPercentage;
    if (location != null) {
      data['location'] = location!.toJson();
    }
    data['delivery_radius'] = deliveryRadius;
    return data;
  }
}

class Location {
  Crs? crs;
  String? type;
  List<double>? coordinates;

  Location({this.crs, this.type, this.coordinates});

  Location.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? Crs.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates']?.cast<double>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (crs != null) {
      data['crs'] = crs!.toJson();
    }
    data['type'] = type;
    data['coordinates'] = coordinates;
    return data;
  }
}

class Crs {
  String? type;
  Properties? properties;

  Crs({this.type, this.properties});

  Crs.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'] != null
        ? Properties.fromJson(json['properties'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    if (properties != null) {
      data['properties'] = properties!.toJson();
    }
    return data;
  }
}

class Properties {
  String? name;

  Properties({this.name});

  Properties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    return data;
  }
}
