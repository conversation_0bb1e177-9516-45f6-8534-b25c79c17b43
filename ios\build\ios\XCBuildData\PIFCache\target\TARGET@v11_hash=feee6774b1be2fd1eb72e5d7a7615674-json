{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988cc75553b5015dc6dbb70ca075ac73ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98091251a1270ae39983daa99a85b10ab1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ef74ad3cc973f5d4d4d61b492444f6c4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9888cdf6caa1f92985ba7e6002d853af1f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ef74ad3cc973f5d4d4d61b492444f6c4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981687822ebb6e465db2434a7a5012ed14", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c834098312d540c7cf6154da2e15001e", "guid": "bfdfe7dc352907fc980b868725387e98fe7a4f091cd0a8f821de03ea46e0ee90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfa3629c63acb316a43443b57be72056", "guid": "bfdfe7dc352907fc980b868725387e9875669d1afb480b102c762bdeeda93028", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f19bba1e634ac885dca8a02892f340f6", "guid": "bfdfe7dc352907fc980b868725387e985a30b4df5febcee0f3e32fefc2fdbc73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13f19e171c19c2e6547d6e3cf4d4069", "guid": "bfdfe7dc352907fc980b868725387e985ad6613eb1966b06b8cadaf3e1346c57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98340779a5d3e4f7ec641086904f3342db", "guid": "bfdfe7dc352907fc980b868725387e985f4f98436cbcc031607aaea195495d31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec6f72065e3c1421b0cf5c5c7acacb30", "guid": "bfdfe7dc352907fc980b868725387e9842542015088a6621eea69c5ac0ddc8ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833836f2141e029193648528f417ef6d1", "guid": "bfdfe7dc352907fc980b868725387e980f05e2d2d4372832ca1e9abb8653309a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccc414de96e2be74d3f7731adb21a6c4", "guid": "bfdfe7dc352907fc980b868725387e98ebcc9a8b1a2ccfbd47d2a3d89017c537"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2dde1b6ce29308bb43d5e187bff7b2b", "guid": "bfdfe7dc352907fc980b868725387e98f22f0b5b5d2f16588c2e674712f49056", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988bcd7df9c7023856421a9978d612fda0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a148bb39a902962f0eb7eb321606d98d", "guid": "bfdfe7dc352907fc980b868725387e98405ce8942ce04b116a7ac59331c286d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2c48df34fafdc1fffb38e1f7ae25412", "guid": "bfdfe7dc352907fc980b868725387e98d242e689bef10f7b8c763f06be8997a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98812a7fc667d2db3a77430771b72c45d5", "guid": "bfdfe7dc352907fc980b868725387e9811ff33ca75b9f016880be84a548190a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2f69039ef0968a2e5832cc721a086b3", "guid": "bfdfe7dc352907fc980b868725387e98db6591a1f2f6c4c6e46be0ea04c4b724"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af97c19117b604d0860308336c2981a4", "guid": "bfdfe7dc352907fc980b868725387e98fd6570c21709c986e7ebd017eff1897b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b29d6ff932b5349a531f4458bd0b4378", "guid": "bfdfe7dc352907fc980b868725387e98907129c8c222ed9dc37e918dac6496e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810bec7a6e1975dd5459c466aa700a0e4", "guid": "bfdfe7dc352907fc980b868725387e9815f057b3bfc65280f4dd1b08f6c95347"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff09ec59d348a5117fc9f13699e03edd", "guid": "bfdfe7dc352907fc980b868725387e98b413c6ec217c826a31507e684af8da8c"}], "guid": "bfdfe7dc352907fc980b868725387e98d9a5ee40ffac5fe194569a14288f7fb7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e984fd052c9d45114348a5b20f9870ef4e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd407fd61ceab3d35c8d0217eda0e40", "guid": "bfdfe7dc352907fc980b868725387e988c00dc5e602878e853b462237ef8d605"}], "guid": "bfdfe7dc352907fc980b868725387e98be2f432ccbf8c6a291b5329d4ed158ab", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9898161030888121b8c240a44f3a1ed7b1", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e988a967fdf75ef7eab528e600e3ce386a0", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e9888147bea1e3422d1e2ab26d671033a3b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}