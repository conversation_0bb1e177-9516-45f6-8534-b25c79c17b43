import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/bloc/newmealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/new_meal_plan/newfilteredchefsmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/meal_plan_new/persoanilized/new_persoanilized_meal_plan_final.dart';
import 'package:db_eats/ui/meal_plan_new/persoanilized/new_persoanilized_select_meals.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';

class NewPersoaniliedChef extends StatefulWidget {
  final List<String>? dates;
  final int currentday;
  final int mealPlanId;
  final Map<String, Map<String, dynamic>>? mealdata;
  final bool isEditing;
  final int? editDayId;
  final String? editDate;
  final int? selectedChefId;

  const NewPersoaniliedChef({
    super.key,
    this.dates,
    required this.mealPlanId,
    required this.currentday,
    this.mealdata,
    this.isEditing = false,
    this.editDayId,
    this.editDate,
    this.selectedChefId,
  });

  @override
  State<NewPersoaniliedChef> createState() => _NewPersoaniliedChefState();
}

class _NewPersoaniliedChefState extends State<NewPersoaniliedChef> {
  List<Chefs> _chefs = [];
  bool _isLoading = true;
  String _displayStartDate = '';
  int _maxDishesPerDay = 0;
  late int _servingSizeId;
  late int _timeSlotId;
  int _currentDay = 0;

  Color kBlack = Color(0xFF1F2122);
  Color kSecondBlack = Color(0xFF414346);

  String _startDate = '';
  String _endDate = '';
  int _mealPlanDuration = 5; // default duration

  int? _selectedChefId;

  // Add new property to store view day response
  Map<String, dynamic>? viewDayData;

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  void initState() {
    super.initState();

    // Always fetch meal plan summary first to get updated values
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NewmealplanBloc>().add(ListNewMealPlanSummaryEvent(
          data: {'meal_plan_id': widget.mealPlanId}));
    });
  }

  int _findNextIncompleteDay() {
    final summaryData = Initializer.mealPlanProgressLatestModel.data;
    final selectedWeekdays = summaryData?.selectedWeekdays ?? [];

    print("Finding next incomplete day - selectedWeekdays: $selectedWeekdays");

    if (selectedWeekdays.isEmpty) {
      return 1; // Default to first day if no weekdays selected
    }

    // Check each selected weekday for completion
    final mealPlanDays = summaryData?.mealPlanDays ?? [];
    print(
        "Meal plan days: ${mealPlanDays.map((d) => '${d.dayOfWeek}: ${d.date}')}");

    for (int i = 0; i < selectedWeekdays.length; i++) {
      final weekday = selectedWeekdays[i];

      // Check if this weekday is completed by looking at meal_plan_days
      final isWeekdayCompleted = mealPlanDays.any((day) =>
          day.dayOfWeek == weekday &&
          day.items != null &&
          day.items!.isNotEmpty);

      print("Weekday $weekday (day ${i + 1}) completed: $isWeekdayCompleted");

      if (!isWeekdayCompleted) {
        print("Found incomplete weekday: $weekday at position ${i + 1}");
        return i + 1; // Return 1-based index for first incomplete weekday
      }
    }

    // If all weekdays completed, return last weekday index
    print("All weekdays completed, returning ${selectedWeekdays.length}");
    return selectedWeekdays.length;
  }

  String _getSelectedDate(int dayIndex) {
    // For weekday-based meal plans, we don't need specific dates
    // Return start date for filtering purposes
    return _startDate;
  }

  void _loadFilteredChefs() {
    // Get weekday from selected weekdays or edit date
    String weekdayName = '';

    // In edit mode, use the editDate which contains the day of week
    if (widget.isEditing && widget.editDate != null) {
      weekdayName = widget.editDate!.toUpperCase();
    } else {
      // Non-edit mode logic
      final summaryData = Initializer.mealPlanProgressLatestModel.data;
      final selectedWeekdays = summaryData?.selectedWeekdays ?? [];

      if (selectedWeekdays.isNotEmpty &&
          _currentDay > 0 &&
          _currentDay <= selectedWeekdays.length) {
        weekdayName = selectedWeekdays[_currentDay - 1];
      } else {
        weekdayName = 'MONDAY'; // Default fallback
      }
    }

    Map<String, dynamic> requestData = {
      'search_keyword': '',
      'latitude': Initializer.latitude ?? '0',
      'longitude': Initializer.longitude ?? '0',
      'serving_size_id': _servingSizeId,
      'time_slot_id': _timeSlotId,
      'start_date': _startDate,
      'end_date': _endDate,
      'weekday': weekdayName,
      "meal_plan_id": widget.mealPlanId,
      "meal_plan_duration": _mealPlanDuration,
    };

    // print("Loading chefs with data: $requestData"); // Add debug log
    // print(
    //     "Current _servingSizeId: $_servingSizeId, _timeSlotId: $_timeSlotId, weekday: $weekdayName"); // Add debug log
    context.read<NewmealplanBloc>().add(NewFilterChefsEvent(requestData));
  }

  String _formatDayName() {
    // In edit mode, use the editDate which contains the day of week
    if (widget.isEditing && widget.editDate != null) {
      // widget.editDate in edit mode contains the day of week like "MONDAY", "TUESDAY", etc.
      final editDayOfWeek = widget.editDate!;
      print("DEBUG: In edit mode, editDate = '$editDayOfWeek'"); // Debug log

      // Convert weekday to display format
      switch (editDayOfWeek.toUpperCase()) {
        case 'MONDAY':
          return 'Monday';
        case 'TUESDAY':
          return 'Tuesday';
        case 'WEDNESDAY':
          return 'Wednesday';
        case 'THURSDAY':
          return 'Thursday';
        case 'FRIDAY':
          return 'Friday';
        case 'SATURDAY':
          return 'Saturday';
        case 'SUNDAY':
          return 'Sunday';
        default:
          return editDayOfWeek; // fallback to original string
      }
    }

    final summaryData = Initializer.mealPlanProgressLatestModel.data;
    final selectedWeekdays = summaryData?.selectedWeekdays ?? [];

    // Get the current weekday name from selected weekdays
    if (selectedWeekdays.isNotEmpty &&
        _currentDay > 0 &&
        _currentDay <= selectedWeekdays.length) {
      final weekday = selectedWeekdays[_currentDay - 1];

      // Convert weekday to display format
      switch (weekday) {
        case 'MONDAY':
          return 'Monday';
        case 'TUESDAY':
          return 'Tuesday';
        case 'WEDNESDAY':
          return 'Wednesday';
        case 'THURSDAY':
          return 'Thursday';
        case 'FRIDAY':
          return 'Friday';
        case 'SATURDAY':
          return 'Saturday';
        case 'SUNDAY':
          return 'Sunday';
        default:
          return weekday;
      }
    }

    // Fallback: derive day name from display date
    if (_displayStartDate.isNotEmpty) {
      try {
        final DateTime dateTime = DateTime.parse(_displayStartDate);
        final days = [
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday',
          'Sunday'
        ];
        return days[dateTime.weekday - 1];
      } catch (e) {
        return "Unknown Day";
      }
    }

    return "Loading...";
  }

  bool _areAllDaysCompleted() {
    final summaryData = Initializer.mealPlanProgressLatestModel.data;
    final selectedWeekdays = summaryData?.selectedWeekdays ?? [];
    final mealPlanDays = summaryData?.mealPlanDays ?? [];

    print('[DEBUG] _areAllDaysCompleted - selectedWeekdays: $selectedWeekdays');
    print(
        '[DEBUG] _areAllDaysCompleted - mealPlanDays count: ${mealPlanDays.length}');

    // If no selected weekdays, return false as we need at least one day
    if (selectedWeekdays.isEmpty) {
      print(
          '[DEBUG] _areAllDaysCompleted - No selected weekdays, returning false');
      return false;
    }

    // Check if all selected weekdays are completed
    for (String weekday in selectedWeekdays) {
      final isWeekdayCompleted = mealPlanDays.any((day) =>
          day.dayOfWeek == weekday &&
          day.items != null &&
          day.items!.isNotEmpty);

      print(
          '[DEBUG] _areAllDaysCompleted - $weekday completed: $isWeekdayCompleted');
      if (!isWeekdayCompleted) return false;
    }

    print('[DEBUG] _areAllDaysCompleted - All days completed, returning true');
    return true;
  }

  Widget _buildChefShimmerGrid() {
    return Column(
      children: List.generate(
        3,
        (index) => Padding(
          padding: EdgeInsets.fromLTRB(sixteen, 0, sixteen, sixteen),
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(sixteen),
              ),
              child: Padding(
                padding: EdgeInsets.all(sixteen),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: twelve),
                        Container(
                          width: 150,
                          height: 20,
                          color: Colors.white,
                        ),
                      ],
                    ),
                    SizedBox(height: twelve),
                    Container(
                      width: 80,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(twelve),
                      ),
                    ),
                    SizedBox(height: twelve),
                    Container(
                      width: double.infinity,
                      height: sixteen,
                      color: Colors.white,
                    ),
                    SizedBox(height: eighteen),
                    Container(
                      width: 120,
                      height: forteen,
                      color: Colors.white,
                    ),
                    SizedBox(height: twelve),
                    Container(
                      width: double.infinity,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(28),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoChefMessage() {
    return Container(
      margin: EdgeInsets.fromLTRB(sixteen, 32, sixteen, sixteen),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(sixteen),
        border: Border.all(color: const Color(0xFFE1DDD5)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            'assets/images/cooking.png',
            width: 90,
            height: 90,
          ),
          SizedBox(height: sixteen),
          Text(
            'No Chefs Available',
            style: TextStyle(
              fontSize: eighteen,
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
              color: Color(0xFF1F2122),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Sorry, there are no chefs available in your area at the moment.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: forteen,
              fontWeight: FontWeight.w400,
              fontFamily: 'Inter',
              color: Color(0xFF414346),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateTimeShimmer() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: sixteen),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: sixteen,
                  height: sixteen,
                  color: Colors.white,
                ),
                const SizedBox(width: 8),
                Container(
                  width: 150,
                  height: forteen,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const Spacer(),
                Row(
                  children: List.generate(
                    5,
                    (index) => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: Container(
                        width: 25,
                        height: 25,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  width: sixteen,
                  height: sixteen,
                  color: Colors.white,
                ),
                const SizedBox(width: 8),
                Container(
                  width: 100,
                  height: forteen,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector() {
    final summaryData = Initializer.mealPlanProgressLatestModel.data;
    final selectedWeekdays = summaryData?.selectedWeekdays ?? [];
    final dayCount = selectedWeekdays.isNotEmpty
        ? selectedWeekdays.length
        : _mealPlanDuration;

    // Get time slot from meal plan summary
    final timeSlot = summaryData?.timeSlot;
    final startTime = timeSlot?.startTime;
    final endTime = timeSlot?.endTime;

    // Debug log
    print("Chef Listing - selectedWeekdays: $selectedWeekdays");
    print("Chef Listing - dayCount: $dayCount");
    print("Chef Listing - mealPlanDuration: $_mealPlanDuration");

    // Get mealPlanDays for completion logic
    final mealPlanDays = summaryData?.mealPlanDays ?? [];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: sixteen, vertical: twelve),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.calendar_today,
                            size: sixteen, color: kSecondBlack),
                        SizedBox(width: 8),
                        Text(
                          _formatDayName(),
                          style: TextStyle(
                            fontSize: forteen,
                            fontWeight: FontWeight.w500,
                            color: kBlack,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    if (startTime != null && endTime != null)
                      Row(
                        children: [
                          Icon(Icons.access_time,
                              size: sixteen, color: kSecondBlack),
                          SizedBox(width: 8),
                          Text(
                            "${_formatTimeToAmPm(startTime)} - ${_formatTimeToAmPm(endTime)}",
                            style: TextStyle(
                              fontSize: twelve,
                              color: kSecondBlack,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
              // Day indicator row with connecting lines based on selected weekdays
              Row(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(
                  dayCount * 2 - 1,
                  (index) {
                    if (index.isOdd) {
                      // Draw connecting line
                      return Container(
                        width: sixteen,
                        height: 1,
                        color: const Color(0xFFE1DDD5),
                      );
                    } else {
                      // Draw circle with day number
                      final dayNumber = (index ~/ 2) + 1;
                      final isSelected = dayNumber == _currentDay;
                      // Determine if this weekday is completed
                      bool isCompleted = false;
                      if (selectedWeekdays.isNotEmpty &&
                          dayNumber <= selectedWeekdays.length) {
                        final weekday = selectedWeekdays[dayNumber - 1];
                        isCompleted = mealPlanDays.any((day) =>
                            day.dayOfWeek == weekday &&
                            day.items != null &&
                            day.items!.isNotEmpty);
                      }
                      String displayText = '$dayNumber';
                      return GestureDetector(
                        onTap: () {
                          onDayTapped(dayNumber);
                        },
                        child: Container(
                          width: 22,
                          height: 22,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isSelected
                                ? kBlack
                                : isCompleted
                                    ? const Color(0xFF1F2122)
                                    : Colors.transparent,
                            border: Border.all(
                              color: isSelected || isCompleted
                                  ? kBlack
                                  : const Color(0xFFE1DDD5),
                              width: 1,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              displayText,
                              style: TextStyle(
                                fontSize: forteen,
                                fontWeight: FontWeight.w500,
                                color: isSelected || isCompleted
                                    ? Colors.white
                                    : kSecondBlack,
                              ),
                            ),
                          ),
                        ),
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (widget.isEditing) {
          context.read<NewmealplanBloc>().add(ListNewMealPlanSummaryEvent(
                data: {"meal_plan_id": widget.mealPlanId, 'is_summary': true},
              ));
        }
        return true; // allow pop
      },
      child: MultiBlocListener(
        listeners: [
          BlocListener<NewmealplanBloc, NewMealPlanState>(
            listener: (context, state) {
              print("Current state: $state"); // Add debug log
              if (state is NewMealPlanSummaryLoaded) {
                print("NewMealPlanSummaryLoaded: " +
                    (Initializer.mealPlanProgressLatestModel.data
                            ?.toJson()
                            .toString() ??
                        'null'));

                // Update serving size and time slot IDs from the summary data in Initializer
                if (Initializer.mealPlanProgressLatestModel.data != null) {
                  final summaryData =
                      Initializer.mealPlanProgressLatestModel.data!;
                  _servingSizeId = summaryData.servingSizeId ?? _servingSizeId;
                  _timeSlotId = summaryData.timeSlotId ?? _timeSlotId;
                  _maxDishesPerDay =
                      summaryData.dishesPerDay ?? _maxDishesPerDay;
                  _startDate = summaryData.startDate ?? _startDate;
                  _endDate = summaryData.endDate ?? _endDate;
                  final selectedWeekdays = summaryData.selectedWeekdays ?? [];
                  _mealPlanDuration = selectedWeekdays.length;
                  _currentDay = _findNextIncompleteDay();
                  print(
                      "Updated from summary - _servingSizeId: $_servingSizeId, _timeSlotId: $_timeSlotId, _startDate: $_startDate, _endDate: $_endDate, selectedWeekdays: $selectedWeekdays, duration: $_mealPlanDuration");
                  // If all days are completed, auto-navigate to final page
                  final mealPlanDays = summaryData.mealPlanDays ?? [];
                  bool allCompleted = true;
                  for (String weekday in selectedWeekdays) {
                    final isWeekdayCompleted = mealPlanDays.any((day) =>
                        day.dayOfWeek == weekday &&
                        day.items != null &&
                        day.items!.isNotEmpty);
                    if (!isWeekdayCompleted) {
                      allCompleted = false;
                      break;
                    }
                  }
                  // Only auto-navigate to final page if not in edit mode
                  // if (allCompleted &&
                  //     selectedWeekdays.isNotEmpty &&
                  //     !widget.isEditing) {
                  //   // Delay navigation to avoid setState during build
                  //   WidgetsBinding.instance.addPostFrameCallback((_) {
                  //     Navigator.of(context).pushReplacement(
                  //       MaterialPageRoute(
                  //         builder: (context) => NewPersoanilizedMealPlanFinal(
                  //           mealPlanId: widget.mealPlanId,
                  //         ),
                  //       ),
                  //     );
                  //   });
                  //   return;
                  // }
                }

                if (widget.isEditing) {
                  final summaryData =
                      Initializer.mealPlanProgressLatestModel.data;
                  final chefId = summaryData?.mealPlanDays?.isNotEmpty == true
                      ? summaryData!.mealPlanDays!.first.chef?.id
                      : null;
                  final dayDate = summaryData?.mealPlanDays?.isNotEmpty == true
                      ? summaryData!.mealPlanDays!.first.date
                      : widget.editDate;

                  setState(() {
                    _selectedChefId = chefId;
                    _displayStartDate = dayDate ?? widget.editDate ?? '';
                    viewDayData = summaryData?.mealPlanDays?.isNotEmpty == true
                        ? {
                            'chef': {'id': chefId},
                            'date': dayDate,
                            'time_slot': {
                              'start_time': summaryData?.timeSlot?.startTime,
                              'end_time': summaryData?.timeSlot?.endTime,
                            }
                          }
                        : null;
                  });
                }

                // Always load filtered chefs after getting updated IDs
                _loadFilteredChefs();
              } else if (state is NewFilterChefsStateSuccess) {
                setState(() {
                  _chefs = Initializer.newFilteredChefModel.data?.chefs ?? [];
                  _isLoading = false;
                  if (_selectedChefId != null) {
                    final selectedChefExists =
                        _chefs.any((c) => c.chefId == _selectedChefId);
                    if (!selectedChefExists) {
                      _selectedChefId = null;
                    }
                  }
                });
              } else if (state is NewFilterChefsStateLoadFailed) {
                setState(() {
                  _isLoading = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.error),
                    backgroundColor: const Color(0xFFE11900),
                  ),
                );
              }
            },
          ),
        ],
        child: BlocBuilder<HomeBloc, HomeState>(
          builder: (context, state) {
            return Scaffold(
              backgroundColor: const Color(0xFFF6F3EC),
              appBar: AppBar(
                backgroundColor: const Color(0xFFF6F3EC),
                elevation: 0,
                scrolledUnderElevation: 0,
                foregroundColor: const Color(0xFF1F2122),
                surfaceTintColor: Colors.transparent,
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Color(0xFF1F2122)),
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (widget.isEditing) {
                      context
                          .read<NewmealplanBloc>()
                          .add(ListNewMealPlanSummaryEvent(
                            data: {
                              "meal_plan_id": widget.mealPlanId,
                              'is_summary': true
                            },
                          ));
                    }
                  },
                ),
                title: const Text(''),
                centerTitle: false,
              ),
              body: ListView(
                children: [
                  Padding(
                    padding: EdgeInsets.fromLTRB(sixteen, 0, sixteen, 4),
                    child: Text(
                      "Select Chef",
                      style: TextStyle(
                        fontSize: eighteen,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                        color: Color(0xFF1F2122),
                      ),
                    ),
                  ),
                  // const SizedBox(height: twelve),
                  _isLoading ? _buildDateTimeShimmer() : _buildDateSelector(),
                  // Padding(
                  //   padding: const EdgeInsets.fromLTRB(sixteen, 8, sixteen, sixteen),
                  //   child: _isLoading
                  //       ? Shimmer.fromColors(
                  //           baseColor: Colors.grey[300]!,
                  //           highlightColor: Colors.grey[100]!,
                  //           child: Container(
                  //             height: 52,
                  //             decoration: BoxDecoration(
                  //               color: Colors.white,
                  //               borderRadius: BorderRadius.circular(28),
                  //             ),
                  //           ),
                  //         )
                  //       : Container(
                  //           height: 48,
                  //           decoration: BoxDecoration(
                  //             borderRadius: BorderRadius.circular(28),
                  //             border: Border.all(color: const Color(0xFF1F2122)),
                  //           ),
                  //           child: InkWell(
                  //             borderRadius: BorderRadius.circular(28),
                  //             onTap: () {
                  //               // Handle filter action
                  //             },
                  //             child: Row(
                  //               mainAxisAlignment: MainAxisAlignment.center,
                  //               children: const [
                  //                 Icon(Icons.tune,
                  //                     size: sixteen, color: Color(0xFF1F2122)),
                  //                 SizedBox(width: 8),
                  //                 Text(
                  //                   "View Filters",
                  //                   style: TextStyle(
                  //                     fontSize: twelve
                  //                     fontWeight: FontWeight.w600,
                  //                     fontFamily: 'Inter',
                  //                     color: Color(0xFF1F2122),
                  //                   ),
                  //                 ),
                  //               ],
                  //             ),
                  //           ),
                  //         ),
                  // ),
                  SizedBox(height: ten),
                  _isLoading
                      ? _buildChefShimmerGrid()
                      : _chefs.isEmpty
                          ? _buildNoChefMessage()
                          : Column(
                              children: _chefs
                                  .map((chef) => _buildChefCard(chef))
                                  .toList(),
                            ),
                ],
              ),
              // bottomNavigationBar: _areAllDaysCompleted() && !widget.isEditing
              //     ? Container(
              //         padding: EdgeInsets.all(sixteen),
              //         decoration: const BoxDecoration(
              //           color: Colors.white,
              //           border: Border(
              //             top: BorderSide(color: Color(0xFFE1E3E6)),
              //           ),
              //         ),
              //         child: ElevatedButton(
              //           onPressed: () {
              //             Navigator.of(context).pushReplacement(
              //               MaterialPageRoute(
              //                 builder: (context) =>
              //                     NewPersoanilizedMealPlanFinal(
              //                   mealPlanId: widget.mealPlanId,
              //                 ),
              //               ),
              //             );
              //           },
              //           style: ElevatedButton.styleFrom(
              //             backgroundColor: const Color(0xFF1F2122),
              //             shape: RoundedRectangleBorder(
              //               borderRadius: BorderRadius.circular(100),
              //             ),
              //             padding: EdgeInsets.symmetric(vertical: eighteen),
              //           ),
              //           child: Text(
              //             'Continue to Checkout',
              //             style: TextStyle(
              //               fontSize: sixteen,
              //               fontWeight: FontWeight.w600,
              //               color: Colors.white,
              //             ),
              //           ),
              //         ),
              //       )
              //     : const SizedBox.shrink(),
            );
          },
        ),
      ),
    );
  }

  // String _getSelectedDate(int dayIndex) {
  //   // Convert from 1-based to 0-based index
  //   int index = dayIndex - 1;

  //   // First try to get from working days array
  //   if (_workingDays.isNotEmpty && index >= 0 && index < _workingDays.length) {
  //     return _workingDays[index];
  //   }

  //   // Fallback to dates from widget if available
  //   if (widget.dates.isNotEmpty && index >= 0 && index < widget.dates.length) {
  //     return widget.dates[index];
  //   }

  //   // If no valid date found, calculate next working day from start date
  //   if (_startDate.isNotEmpty) {
  //     DateTime baseDate = DateTime.parse(_startDate);
  //     int workingDaysToAdd = index;

  //     while (workingDaysToAdd > 0) {
  //       baseDate = baseDate.add(const Duration(days: 1));
  //       if (baseDate.weekday != DateTime.saturday &&
  //           baseDate.weekday != DateTime.sunday) {
  //         workingDaysToAdd--;
  //       }
  //     }

  //     return _formatStorageDate(baseDate);
  //   }

  //   return '';
  // }

  // Usage example when day number is tapped
  void onDayTapped(int dayNumber) {
    setState(() {
      _currentDay = dayNumber;
      _displayStartDate = _getSelectedDate(dayNumber);
      // Trigger chef loading for the new date
      _loadFilteredChefs();
    });
  }

  Future<bool> _showChangeChefConfirmation(Chefs newChef) async {
    return await showDialog(
          context: context,
          builder: (context) => Dialog(
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(sixteen)),
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Change Chef?',
                    style: TextStyle(
                      fontSize: eighteen,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                      color: Color(0xFF1F2122),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Are you sure you want to change the chef for this day? Your current meal selections will be cleared.',
                    style: TextStyle(
                      fontSize: forteen,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: Color(0xFF414346),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context, false),
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                              horizontal: sixteen, vertical: twelve),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            fontSize: forteen,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Inter',
                            color: Color(0xFF414346),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () => Navigator.pop(context, true),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF1F2122),
                          padding: EdgeInsets.symmetric(
                              horizontal: twentyFour, vertical: twelve),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(24),
                          ),
                        ),
                        child: Text(
                          'Change Chef',
                          style: TextStyle(
                            fontSize: forteen,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Inter',
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ) ??
        false;
  }

  Widget _buildChefCard(Chefs chef) {
    final bool isSelected = chef.chefId == _selectedChefId;

    return InkWell(
        onTap: () async {
          if (widget.isEditing && !isSelected) {
            final shouldChange = await _showChangeChefConfirmation(chef);
            if (!shouldChange) return;
          }

          setState(() {
            _selectedChefId = chef.chefId;
          });

          // In edit mode, always navigate to meal selection when a chef is tapped
          // In non-edit mode, only navigate if not all days are completed
          if (widget.isEditing || !_areAllDaysCompleted()) {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => NewPersoanilizedSelectMeals(
                  mealPlanId: widget.mealPlanId,
                  id: chef.chefId ?? 0,
                  selectedDay: _currentDay,
                  selectedDate: _displayStartDate,
                  mealData: widget.mealdata ?? {},
                  isEditing: widget.isEditing,
                  editDayId: widget.isEditing ? widget.editDayId : null,
                  editDate: widget.isEditing ? widget.editDate : null,
                  viewDayData: widget.isEditing ? viewDayData : null,
                ),
              ),
            );
          }
        },
        child: Container(
            margin: EdgeInsets.fromLTRB(sixteen, 0, sixteen, sixteen),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(sixteen),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color:
                    isSelected ? const Color(0xFF1F2122) : Colors.transparent,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(sixteen),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: twelve,
                        backgroundImage: chef.profilePhoto != null
                            ? NetworkImage(
                                ServerHelper.imageUrl + chef.profilePhoto!)
                            : const AssetImage(
                                    'assets/images/no_image_avatar.png')
                                as ImageProvider,
                      ),
                      SizedBox(width: twelve),
                      Expanded(
                        child: Text(
                          '${chef.chef?.firstName} ${chef.chef?.lastName}',
                          style: TextStyle(
                            fontSize: sixteen,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Inter',
                            color: Color(0xFF1F2122),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFFE1E3E6),
                      borderRadius: BorderRadius.circular(twelve),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Image.asset(
                          'assets/icons/thump.png',
                          width: ten,
                          height: ten,
                          color: const Color(0xFF1F2122),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          "${chef.ratingPercentage ?? '0'}% (${chef.totalRatings ?? 0})",
                          style: TextStyle(
                            fontSize: ten,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Inter',
                            color: Color(0xFF1F2122),
                            height: 1.0,
                            letterSpacing: 0.02,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    chef.searchTags?.join(", ") ?? '',
                    style: TextStyle(
                      fontSize: twelve,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: Color(0xFF414346),
                      height: 20 / 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Image.asset(
                        'assets/icons/calender_2.png',
                        width: ten,
                        height: ten,
                        color: Colors.black54,
                      ),
                      SizedBox(width: 2),
                      Text(
                        chef.chef?.operationDays
                                ?.map((day) =>
                                    day.day?.name?.substring(0, 1) ?? '')
                                .join(", ") ??
                            '',
                        style: TextStyle(
                          fontSize: twelve,
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => NewPersoanilizedSelectMeals(
                            mealPlanId: widget.mealPlanId,
                            id: chef.chefId ?? 0,
                            selectedDay: _currentDay,
                            selectedDate: _displayStartDate,
                            mealData: widget.mealdata ?? {},
                            isEditing: widget.isEditing,
                            editDayId:
                                widget.isEditing ? widget.editDayId : null,
                            editDate: widget.isEditing ? widget.editDate : null,
                            viewDayData: widget.isEditing ? viewDayData : null,
                          ),
                        ),
                      );
                    },
                    style: OutlinedButton.styleFrom(
                      minimumSize: Size(twelve, twenty + twelve),
                      padding: EdgeInsets.symmetric(horizontal: twelve),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(28),
                      ),
                      side: const BorderSide(color: Color(0xFF1F2122)),
                    ),
                    child: Text(
                      (widget.isEditing && isSelected)
                          ? 'Edit Meals'
                          : 'View Chef Menu',
                      style: TextStyle(
                        fontSize: twelve,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: const Color(0xFF1F2122),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: ten * 12 + sixteen,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: chef.chef?.dishes?.length ?? 0,
                      itemBuilder: (context, index) {
                        final dish = chef.chef?.dishes?[index];
                        return Container(
                          width: ten * 15 + eighteen,
                          margin: EdgeInsets.only(
                            right: index == (chef.chef?.dishes?.length ?? 0) - 1
                                ? 0
                                : 8,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: const Color(0xFFE1DDD5)),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ClipRRect(
                                borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(8),
                                ),
                                child: dish?.photo != null
                                    ? Image.network(
                                        '${ServerHelper.imageUrl}${dish?.photo}',
                                        height: ten * 8 + twelve,
                                        width: double.infinity,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return Container(
                                            // height: 120,
                                            color: const Color(0xFFE1DDD5),
                                            child: const Center(
                                              child: Icon(Icons.restaurant_menu,
                                                  color: Color(0xFF1F2122)),
                                            ),
                                          );
                                        },
                                      )
                                    : Container(
                                        height: ten * 12,
                                        color: const Color(0xFFE1DDD5),
                                        child: const Center(
                                          child: Icon(Icons.restaurant_menu,
                                              color: Color(0xFF1F2122)),
                                        ),
                                      ),
                              ),
                              Padding(
                                padding: EdgeInsets.all(twelve),
                                child: Text(
                                  dish?.name ?? '',
                                  style: TextStyle(
                                    fontSize: twelve,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF1F2122),
                                    height: 1.43,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            )));
  }

  String _formatTimeToAmPm(String? time) {
    if (time == null) return '';
    try {
      final timeParts = time.split(':');
      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);
      String period = hour >= 12 ? 'PM' : 'AM';
      hour = hour > 12 ? hour - 12 : hour;
      hour = hour == 0 ? 12 : hour;
      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}$period';
    } catch (e) {
      return time;
    }
  }
}
