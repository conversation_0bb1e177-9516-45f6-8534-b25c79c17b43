{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b912ea3eb8b3d2a2dbfdd3bdca72298", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7a99df6e6a4873f55b3ad33955c73d7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989f1eedc46f240487faf150eebcdc6688", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98961e7c9baafe5b4e26a5c59132394731", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989f1eedc46f240487faf150eebcdc6688", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5e2f4ec47caf1c35a822cf8398efdf7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c2e696a844e3b76d55c99c2ee88e90ab", "guid": "bfdfe7dc352907fc980b868725387e983a7f56a8bda7f4a32b582f3e3543ec6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821b9d557d5636b3bd8387f9873feb06b", "guid": "bfdfe7dc352907fc980b868725387e98eefa1fbb52d5820fb2f74287e89fa5be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2740ccf5ea14d3cf8e9c8048d6ab972", "guid": "bfdfe7dc352907fc980b868725387e98731bf14971647468f98880f5ab2ecebd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985952a0d219d2b8f834283080061b1daa", "guid": "bfdfe7dc352907fc980b868725387e985188a1987f7cf2a34653743fc646f39e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989967fab42c0073ea7095f486a4e48cfd", "guid": "bfdfe7dc352907fc980b868725387e988f117cd19bea713411f7d886e3ee57f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f435da06a55fb938fe63ba6e58c70f5a", "guid": "bfdfe7dc352907fc980b868725387e98f7013b8d08955f03cd9ad217b6575ec4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e337a46f2be9b8f7ae0f689e8b262558", "guid": "bfdfe7dc352907fc980b868725387e98c3717fce461d8c3c727f6ab1e1ef8dd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f2da2fbe9a455db5f4aab32cf15ab3c", "guid": "bfdfe7dc352907fc980b868725387e98971d704d92f697bb1c3431227aeca581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f787b37c774e43061be69b2fe8ef665e", "guid": "bfdfe7dc352907fc980b868725387e987fb0a97b5514da31c86f2442dd9c5c3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822e1dc1183f58197086f4b29c5ad9434", "guid": "bfdfe7dc352907fc980b868725387e98557eecafb08f3d4c91d7a27a04cbc98f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98599af5271519a362c42e17af99352882", "guid": "bfdfe7dc352907fc980b868725387e9831a692eeee4b67a14159ea381967273a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bd71c7abf4cac01ac53f2b3038ecde4", "guid": "bfdfe7dc352907fc980b868725387e986a533507042bc1b488b8d411b4e467dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836b7f83ddc890ec2d0becfbf17bf55d1", "guid": "bfdfe7dc352907fc980b868725387e98ae7871cec43b2a430799debd34abfca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853088f95cb56e5add7f10b53ea45c119", "guid": "bfdfe7dc352907fc980b868725387e9843848d640b95d66ae7b0609d40822a18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ad43792a78bebd533c55138a3eb9383", "guid": "bfdfe7dc352907fc980b868725387e985df37ff259fbebfcef0385a6ec8da199"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce6650f13e4f5fea99ff9f7e27dee8c3", "guid": "bfdfe7dc352907fc980b868725387e98f8b9a4332a9d35d417da9e60a68e1e5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988098d37a18760e5810e6251141fa03a9", "guid": "bfdfe7dc352907fc980b868725387e987016ef18ef8e69d7bc59d3c64c1062eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98901918dc12788c9a18148f3da3f661a1", "guid": "bfdfe7dc352907fc980b868725387e98638fdbf659b7db656daaabefeb0fa1e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c06cacd4b2c020084f7d6d444caad16f", "guid": "bfdfe7dc352907fc980b868725387e982366610e765f0b5c67b62d5d700c6ccd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98394e3a1647b53c05319f8f884f6cd63d", "guid": "bfdfe7dc352907fc980b868725387e98201a1ca6306b21611dc8f609d62d8e10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98553fa1585b79187e8f9e2637f9c65854", "guid": "bfdfe7dc352907fc980b868725387e9805d1768fe792537334f391c041c31f35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988046db50053a8d9d5c11d690c9b22f80", "guid": "bfdfe7dc352907fc980b868725387e981ca2893936b4e0ab83db00de99e3fe4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e83f33811274f46ae91574bf590bbbf6", "guid": "bfdfe7dc352907fc980b868725387e98df05095938a6e6a95d183f2debb7eeca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864861c164e3e65d70fcfb2566a9ee1d8", "guid": "bfdfe7dc352907fc980b868725387e988f3992c3226d3fffed029e44a32d785f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989eb938ffbf30bad66de44807c914bfed", "guid": "bfdfe7dc352907fc980b868725387e98325b799f84feca7cce3ae0c158b3d6d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ae193b6f2310fa23538feed1e09607f", "guid": "bfdfe7dc352907fc980b868725387e98d73be45a5c15dbc80c5e29415079a21a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce34cd154f7a3a0021fde39be25f474c", "guid": "bfdfe7dc352907fc980b868725387e9895e853d89407c6a61308b8ed634f6c83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879423a73f65a274f6ffc11d10ffb5b2e", "guid": "bfdfe7dc352907fc980b868725387e983f07250a53c4dcc69dfbcf67ebdc325b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985135b86391ce9d448f530db849a5e22f", "guid": "bfdfe7dc352907fc980b868725387e987481fc11365e35e19c58f07f6f4b732c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dadaa4e640bbe9ea8c4d072d7560764", "guid": "bfdfe7dc352907fc980b868725387e985b6c7b86e2e3ddab6f8772c5362a6fba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5649ff2dedb3e94e3ecb460c622e111", "guid": "bfdfe7dc352907fc980b868725387e988919c6af1499779a90e7598a5e84fabe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8d4d1e489209191f7c6feb2a46166e5", "guid": "bfdfe7dc352907fc980b868725387e98d6d0fe9c6b6c404e0d62b0cbcd05dfdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fc37b5ac045be86db281fef07f443c1", "guid": "bfdfe7dc352907fc980b868725387e9810d41d30cf3c158f9509077744b70505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98505d64d905e928c636df4ffd7be45961", "guid": "bfdfe7dc352907fc980b868725387e9865d6cb4f29a80f2f2ce4e60e0090a0bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985806c99bbc86e0cd3edff2907769c633", "guid": "bfdfe7dc352907fc980b868725387e98ea323f1a49908e40b0b598e61db59b71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1d86d815f850a564225191c985ae0cc", "guid": "bfdfe7dc352907fc980b868725387e9866a81d67491e84a3ef0e7b3befdbdaba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98916e49dd4b48bbfd681344c142b3b42b", "guid": "bfdfe7dc352907fc980b868725387e98f9a04fb7607f394362f9bff3a632b600", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d01d5f0834e12c5c1048e9416e3aa365", "guid": "bfdfe7dc352907fc980b868725387e98c1b9cbe39894801763b170085b67c5ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983408354f3c1c20570c89a3dd2fe8b8c5", "guid": "bfdfe7dc352907fc980b868725387e984e87e4b3e6b500cceaa46ede0ec13dfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcb04a769a2d91fb17e048bb679a16d3", "guid": "bfdfe7dc352907fc980b868725387e9804e24ee381ecd94ecdaeb870ed0114ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f89aa8fa6efef8766aafa96a6ada6f", "guid": "bfdfe7dc352907fc980b868725387e987e8729dc670ba4e8dc2e7ab782a73fb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e5753250fb3d421a6a6741e97912c88", "guid": "bfdfe7dc352907fc980b868725387e980673b86a47f0fe84859c2176137bfbd7"}], "guid": "bfdfe7dc352907fc980b868725387e98dc3f6c017f50c3291e26862a62071ad6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b3a2692b816542c3a6ab0ad293d48859", "guid": "bfdfe7dc352907fc980b868725387e981251f36c01e0bb324ca1cf7a8d9bddb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983802d098da6072b1d3f718edd5f44705", "guid": "bfdfe7dc352907fc980b868725387e98d8db050f1c7a5fd0ec0a4596c587f92c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98299b5de0e004139d0852a7effa0984fe", "guid": "bfdfe7dc352907fc980b868725387e98251d465f7239177f9eb05186be427f1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4da978e834b0a6d3f83c0d58c920908", "guid": "bfdfe7dc352907fc980b868725387e98b9bf91278debb41b3833f220f1b435bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6c4bda2ec0f168dbcee531b4d5a6d7a", "guid": "bfdfe7dc352907fc980b868725387e98af769f021239e440439d2f37c2a8b47a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cfcc1f211e279899887ef0e279ee227", "guid": "bfdfe7dc352907fc980b868725387e98f4410e2eefcc5e3073d1057d8a440acc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac6b977767f90c60f5c62f959d76620c", "guid": "bfdfe7dc352907fc980b868725387e981c659a32af5c88dd60c9e7e92e503c73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851b9a244c4c9f41dde58c408afdf69c7", "guid": "bfdfe7dc352907fc980b868725387e98385e9f712da19f3fdeab81bd33335d87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9683e23352bc14dc3762d7ae927036e", "guid": "bfdfe7dc352907fc980b868725387e98a44cf29d16204615eb6acc6a177ed90d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980972f8dcacdc37646483d829ebf26f2c", "guid": "bfdfe7dc352907fc980b868725387e981759ea69ef13d9431585b07246de10e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f9d9c0500a0f1eafae2056dc4830ae2", "guid": "bfdfe7dc352907fc980b868725387e98c7a014a0f2696eb65fdde5d95ff8a377"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832d813a8c6e83c56198de1a19cacfeb9", "guid": "bfdfe7dc352907fc980b868725387e987d3c88b047f31c79453e480da1cee745"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824aed41e3d8a8211e7b2345fd9c05a7a", "guid": "bfdfe7dc352907fc980b868725387e98754dd466afb74dc55b83de8dbd18dd4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854666f7692af0b9c175d2e777226f339", "guid": "bfdfe7dc352907fc980b868725387e98d346057307b65a0c4cb63b7ce2cfbc65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b640bdcd835ca499d2f3cb1b11c4136c", "guid": "bfdfe7dc352907fc980b868725387e9831a5a7cf4456b84e8b2fb59d15681b76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a46a855b366f900a17cdff445c53ccab", "guid": "bfdfe7dc352907fc980b868725387e98630b01bae14ac405faf80a562b3c32b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804d11c4c071d0852f44ef68f87860ba3", "guid": "bfdfe7dc352907fc980b868725387e98c9f34912719e77ba96475461238e0e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b7d873e55564a072f3866e77678adec", "guid": "bfdfe7dc352907fc980b868725387e984867af3440057818079d08ea0c4d6195"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e54a9131eb868c49da71917ad2449bc", "guid": "bfdfe7dc352907fc980b868725387e9804dbc6bc4a995fc03805e33bbdc61583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e188624cd92a3c745a492fe6f7a3770", "guid": "bfdfe7dc352907fc980b868725387e98d92cafd93ccc407f0b67993abcefdce5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820a4974140e42b3d4b7d4a4e101fbe3b", "guid": "bfdfe7dc352907fc980b868725387e9820143a2be1e220fac8de61a11087c01f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877821fc8c0e374933f1dacd42c7bcc99", "guid": "bfdfe7dc352907fc980b868725387e98f5c29ea9255f8220e21e5404dc5a3f8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b4555b1137007434667b06b7687a537", "guid": "bfdfe7dc352907fc980b868725387e982693cc42ebb05a72e63217341d71bd9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dc07028627d0f4669dfedcbcce184fb", "guid": "bfdfe7dc352907fc980b868725387e985d0530ef6188ebe958e1f0d3f3298d82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aae8f86f93284b2c4efe2c87d10c4267", "guid": "bfdfe7dc352907fc980b868725387e9829041b1e039eb006c470ff92591bf911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880db3026f2cf55fee66fee72243eb6b1", "guid": "bfdfe7dc352907fc980b868725387e98b92b9cf860855796967f283ce7415d09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982afdbaf54739f4dcbc71f8b7b93c958d", "guid": "bfdfe7dc352907fc980b868725387e98debe9957367259f62596ede328df84af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871e9e4fd7fac37e0f507756ee67932ea", "guid": "bfdfe7dc352907fc980b868725387e98ec8f6bff25068400792a0da62301d62a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ef9f324392680279a5e8e3833ecff51", "guid": "bfdfe7dc352907fc980b868725387e981808cef69632857ddb48af8a1b5faf76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825b7dcb2c0f71bf272895c21ec0cf66d", "guid": "bfdfe7dc352907fc980b868725387e980f230ea024b72dbc683a1fabead8d6bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f800cda99bb1d17e28a1a89e5e12091", "guid": "bfdfe7dc352907fc980b868725387e98deca3a41ff15842151e1be841cc3fff0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe42474971bfc9fc5cacf2537e9695f6", "guid": "bfdfe7dc352907fc980b868725387e98f21ff164321f2d57d89d63e9fa8d5d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee023309a36225728856014b5b01b70b", "guid": "bfdfe7dc352907fc980b868725387e981618ab1789cddc3783305bb624a1280e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f7250a01fffcaf102af521b567f2761", "guid": "bfdfe7dc352907fc980b868725387e98c367e72e8bce33e83d8a76f05c126e9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983654709b62eea887983f48281bc39f47", "guid": "bfdfe7dc352907fc980b868725387e98c36efdc476e07ebbd7263ace396ae758"}], "guid": "bfdfe7dc352907fc980b868725387e98053600d677a0ee3a6e8294a5f6c29926", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ceea8cccb36705d2531da7286ed87033", "guid": "bfdfe7dc352907fc980b868725387e98a7f5c11a3ddc6c6109c703c9daffc90f"}], "guid": "bfdfe7dc352907fc980b868725387e986ef21295949b1adf438aaa172cba7c15", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987799c6ff721a2f90b939c7d64edb78cc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e985c8c1a45791dbc15ae7565c9ac08e62e", "name": "AppCheckCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}